version: '3.8'

# Docker Compose extension for logging configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.logging.yml up

services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"        # Maximum size of each log file
        max-file: "7"          # Keep 7 days of logs
        compress: "true"       # Compress rotated logs
        labels: "service"      # Include service labels in logs
    volumes:
      - ./logs:/app/logs:rw    # Mount logs directory for persistent storage
    environment:
      # Configure <PERSON><PERSON> to write to both stdout and file
      LOG_FILE: "/app/logs/app.log"
      LOG_LEVEL: "${LOG_LEVEL:-info}"

  # Optional: Loki for log aggregation (self-hosted)
  # Uncomment to enable local log aggregation with Grafana
  # loki:
  #   image: grafana/loki:2.9.0
  #   container_name: emailconnect-loki
  #   ports:
  #     - "3100:3100"
  #   command: -config.file=/etc/loki/local-config.yaml
  #   volumes:
  #     - ./deploy/loki-config.yaml:/etc/loki/local-config.yaml:ro
  #     - loki-data:/loki
  #   networks:
  #     - emailconnect-network

  # promtail:
  #   image: grafana/promtail:2.9.0
  #   container_name: emailconnect-promtail
  #   volumes:
  #     - ./logs:/var/log/emailconnect:ro
  #     - ./deploy/promtail-config.yaml:/etc/promtail/config.yml:ro
  #     - /var/run/docker.sock:/var/run/docker.sock:ro
  #   command: -config.file=/etc/promtail/config.yml
  #   depends_on:
  #     - loki
  #   networks:
  #     - emailconnect-network

volumes:
  loki-data:

networks:
  emailconnect-network:
    external: true
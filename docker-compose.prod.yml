networks:
  emailconnect:
    driver: bridge

services:
  app:
    networks:
      - emailconnect
    image: ${IMAGE_URI:-ghcr.io/xadi-hq/emailconnect-app:latest}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - URL=${URL:-https://emailconnect.eu}
      - DB_HOST=${DB_HOST:-postgres}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${URL}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - CSP_REPORT_URI=${CSP_REPORT_URI:-https://emailconnect.eu/csp-report}
      - ENCRYPTION_MASTER_KEY=${ENCRYPTION_MASTER_KEY}
      - REDIS_URL=${REDIS_URL:-redis://:${REDIS_PASSWORD}@redis-master:6379}
      - DNS_VERIFICATION_TIMEOUT_MS=${DNS_VERIFICATION_TIMEOUT_MS:-5000}
      - DNS_VERIFICATION_CACHE_TTL_MS=${DNS_VERIFICATION_CACHE_TTL_MS:-300000}
      - DNS_VERIFICATION_RETRY_ATTEMPTS=${DNS_VERIFICATION_RETRY_ATTEMPTS:-3}
      - EMAIL_RETENTION_DAYS=${EMAIL_RETENTION_DAYS:-30}
      - LOG_RETENTION_DAYS=${LOG_RETENTION_DAYS:-7}
      - MAX_EMAIL_SIZE_MB=${MAX_EMAIL_SIZE_MB:-25}
      - DEFAULT_MONTHLY_EMAIL_LIMIT=${DEFAULT_MONTHLY_EMAIL_LIMIT:-50}
      - FREE_PLAN_EMAIL_LIMIT=${FREE_PLAN_EMAIL_LIMIT:-50}
      - PRO_PLAN_EMAIL_LIMIT=${PRO_PLAN_EMAIL_LIMIT:-1000}
      - ENTERPRISE_PLAN_EMAIL_LIMIT=${ENTERPRISE_PLAN_EMAIL_LIMIT:-10000}
      - WEBHOOK_TIMEOUT_MS=${WEBHOOK_TIMEOUT_MS:-30000}
      - WEBHOOK_RETRY_ATTEMPTS=${WEBHOOK_RETRY_ATTEMPTS:-3}
      - PROCESS_EMAIL_SCRIPT=/opt/emailconnect-app/scripts/production/process-email.js
      - MOLLIE_API_KEY=${MOLLIE_API_KEY}
      - MOLLIE_WEBHOOK_SECRET=${MOLLIE_WEBHOOK_SECRET}
      - MOLLIE_WEBHOOK_URL=${MOLLIE_WEBHOOK_URL}
      - MOLLIE_TEST_MODE=${MOLLIE_TEST_MODE:-false}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-https://emailconnect.eu, https://webhooktest.eu}
      - MAX_INLINE_ATTACHMENT_SIZE_KB=${MAX_INLINE_ATTACHMENT_SIZE_KB:-128}
      - DEFAULT_ATTACHMENT_RETENTION_HOURS=${DEFAULT_ATTACHMENT_RETENTION_HOURS:-1}
      - PAID_ATTACHMENT_RETENTION_HOURS=${PAID_ATTACHMENT_RETENTION_HOURS:-24}
      - WEBHOOKTEST_API_URL=${WEBHOOKTEST_API_URL:-https://webhooktest.eu}
      - WEBHOOKTEST_JWT_SECRET=${WEBHOOKTEST_JWT_SECRET}
      - NOTION_API_KEY=${NOTION_API_KEY}
      - NOTION_ANNOUNCEMENTS_DB_ID=${NOTION_ANNOUNCEMENTS_DB_ID:-251f0d21f6f180538e95c541be93903c}
      - NOTION_FAQ_DB_ID=${NOTION_FAQ_DB_ID:-206f0d21f6f18020af7feff431a7cd60}
      - NOTION_CACHE_TTL_SECONDS=${NOTION_CACHE_TTL_SECONDS:-300}
      - NOTION_REDIRECT_HOST_WHITELIST=${NOTION_REDIRECT_HOST_WHITELIST}
      - S3_REGION=${S3_REGION:-nl-ams}
      - S3_BUCKET=${S3_BUCKET:-eceu-data}
      - S3_ACCESS_KEY_ID=${S3_ACCESS_KEY_ID}
      - S3_SECRET_ACCESS_KEY=${S3_SECRET_ACCESS_KEY}
      - S3_ENDPOINT=${S3_ENDPOINT:-https://eceu-data.s3.nl-ams.scw.cloud}
      - SCALEWAY_ACCESS_KEY=${SCALEWAY_ACCESS_KEY}
      - SCALEWAY_SECRET_KEY=${SCALEWAY_SECRET_KEY}
      - SCALEWAY_PROJECT_ID=${SCALEWAY_PROJECT_ID:-00000000-0000-0000-0000-000000000000}
      - SCALEWAY_REGION=${SCALEWAY_REGION:-fr-par}
      - SCALEWAY_FROM_EMAIL=${SCALEWAY_FROM_EMAIL:-<EMAIL>}
      - SCALEWAY_FROM_NAME=${SCALEWAY_FROM_NAME:-EmailConnect}
      - SENTRY_ENABLED=${SENTRY_ENABLED:-true}
      - SENTRY_DSN=${SENTRY_DSN}
      - SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE:-0}
      - SENTRY_REPLAYS_SESSION_SAMPLE_RATE=${SENTRY_REPLAYS_SESSION_SAMPLE_RATE:-0}
      - SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE=${SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE:-1.0}
      - HEARTBEAT_URL=${HEARTBEAT_URL}
      - SIGNED_LINK_SECRET=${SIGNED_LINK_SECRET}
      - HEARTBEAT_INTERVAL_MINUTES=${HEARTBEAT_INTERVAL_MINUTES:-5}
      - TELEGRAM_ENABLED=${TELEGRAM_ENABLED:-true}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_ADMIN_CHAT_ID=${TELEGRAM_ADMIN_CHAT_ID}
      - ENHANCED_WEBHOOK_PROCESSOR=${ENHANCED_WEBHOOK_PROCESSOR:-false}
      - USE_REDIS_SENTINEL=${USE_REDIS_SENTINEL:-false}
    depends_on:
      postgres:
        condition: service_healthy
      redis-master:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - eu_email_scripts:/opt/emailconnect-app/scripts:ro
      - invoice_storage:/opt/emailconnect-app/storage:rw
    command: sh -c "dockerize -wait tcp://postgres:5432 -wait tcp://redis-master:6379 -timeout 60s && npx prisma migrate deploy && npm start"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # 🎯 POSTFIX MANAGER REMOVED - Now using PostgreSQL direct integration

  postgres:
    image: postgres:17-alpine
    networks:
      - emailconnect
    environment:
      - POSTGRES_USER=${DB_USER:-eceu_user}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME:-eceu_db}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-eceu_user} -d ${DB_NAME:-eceu_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    ports:
      - "127.0.0.1:5432:5432"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    shm_size: 64mb

  # Redis Master for high availability
  redis-master:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:6379:6379"  # Primary Redis on default port
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 128mb 
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Redis Replica for failover (only deployed when Sentinel is enabled)
  redis-replica:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:6380:6379"  # Replica on different port
    volumes:
      - redis_replica_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - redis-master
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 128mb
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD}
      --replicaof redis-master 6379
      --masterauth ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'
    profiles:
      - sentinel  # Only deployed when needed

  # Redis Sentinel for automatic failover
  redis-sentinel:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:26379:26379"
    depends_on:
      - redis-master
      - redis-replica
    volumes:
      - ./config/redis-sentinel.conf:/etc/redis/sentinel.conf:ro
      - ./config/sentinel-entrypoint.sh:/sentinel-entrypoint.sh:ro
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "26379", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 15s
    restart: unless-stopped
    entrypoint: ["/bin/sh"]
    command: ["/sentinel-entrypoint.sh"]
    deploy:
      resources:
        limits:
          memory: 32M
          cpus: '0.05'
    profiles:
      - sentinel  # Only deployed when needed

  # 🎯 SCRIPT INITIALIZATION SERVICE
  script-init:
    image: ghcr.io/xadi-hq/emailconnect-app:${IMAGE_TAG:-latest}
    networks:
      - emailconnect
    volumes:
      - eu_email_scripts:/opt/emailconnect-app/scripts:rw
    command: sh -c "cp -r /app/scripts/* /opt/emailconnect-app/scripts/ && chmod +x /opt/emailconnect-app/scripts/*.js"
    restart: "no"
    profiles:
      - init

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  redis_replica_data:
    driver: local
  eu_email_scripts:
    driver: local
  invoice_storage:
    driver: local

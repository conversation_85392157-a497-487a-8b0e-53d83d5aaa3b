#!/usr/bin/env node

/**
 * Simple test script to verify admin impersonation functionality
 * This script tests the impersonation flow without requiring a full test setup
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testImpersonation() {
  console.log('🧪 Testing Admin Impersonation Functionality\n');

  try {
    // Step 1: Test the impersonation endpoint
    console.log('1. Testing impersonation endpoint...');
    
    const impersonateResponse = await fetch(`${BASE_URL}/api/auth/admin/impersonate-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // In a real test, you'd need valid admin session cookies here
        'Cookie': 'better-auth.session=admin-session-token'
      },
      body: JSON.stringify({
        userId: 'target-user-id'
      })
    });

    console.log(`   Status: ${impersonateResponse.status}`);
    
    if (impersonateResponse.ok) {
      const data = await impersonateResponse.json();
      console.log(`   ✅ Success: ${data.message}`);
      console.log(`   👤 Target user: ${data.user.email}`);
      
      // Extract session cookies from response
      const setCookieHeaders = impersonateResponse.headers.raw()['set-cookie'] || [];
      console.log(`   🍪 Set ${setCookieHeaders.length} cookies`);
      
    } else {
      const error = await impersonateResponse.text();
      console.log(`   ❌ Failed: ${error}`);
    }

    // Step 2: Test auth status during impersonation
    console.log('\n2. Testing auth status during impersonation...');
    
    const authStatusResponse = await fetch(`${BASE_URL}/api/auth-status`, {
      headers: {
        // In a real test, you'd use the impersonation session token here
        'Cookie': 'better-auth.session=impersonation-session-token'
      }
    });

    if (authStatusResponse.ok) {
      const authData = await authStatusResponse.json();
      console.log(`   ✅ Auth status retrieved`);
      console.log(`   👤 Current user: ${authData.user?.email || 'Unknown'}`);
      console.log(`   🎭 Is impersonating: ${authData.user?.isImpersonating || false}`);
      console.log(`   👨‍💼 Impersonator: ${authData.user?.impersonatorEmail || 'None'}`);
    } else {
      console.log(`   ❌ Auth status failed: ${authStatusResponse.status}`);
    }

    // Step 3: Test stopping impersonation
    console.log('\n3. Testing stop impersonation...');
    
    const stopResponse = await fetch(`${BASE_URL}/api/auth/admin/stop-impostering`, {
      method: 'POST',
      headers: {
        'Cookie': 'better-auth.session=impersonation-session-token;original_token=admin-session-token'
      }
    });

    console.log(`   Status: ${stopResponse.status}`);
    
    if (stopResponse.ok) {
      const data = await stopResponse.json();
      console.log(`   ✅ Success: ${data.message}`);
    } else {
      const error = await stopResponse.text();
      console.log(`   ❌ Failed: ${error}`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('\n🏁 Test completed');
}

// Run the test if this script is executed directly
if (require.main === module) {
  testImpersonation();
}

module.exports = { testImpersonation };

/**
 * Test fixtures for attachment processing tests
 */

export const testAttachments = {
  smallTextFile: {
    filename: 'test.txt',
    contentType: 'text/plain',
    size: 1024, // 1KB
    content: Buffer.from('This is a test text file content for unit testing.', 'utf-8')
  },
  
  smallJsonFile: {
    filename: 'config.json',
    contentType: 'application/json',
    size: 512, // 0.5KB
    content: Buffer.from(JSON.stringify({ test: true, data: 'sample' }), 'utf-8')
  },
  
  mediumPdfFile: {
    filename: 'document.pdf',
    contentType: 'application/pdf',
    size: 1536 * 1024, // 1.5MB
    content: Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF', 'utf-8')
  },
  
  largePdfFile: {
    filename: 'large-report.pdf',
    contentType: 'application/pdf',
    size: 5 * 1024 * 1024, // 5MB
    content: Buffer.alloc(5 * 1024 * 1024, 'x') // Dummy large content
  },
  
  executableFile: {
    filename: 'malware.exe',
    contentType: 'application/octet-stream',
    size: 2048, // 2KB
    content: Buffer.from('MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff', 'binary')
  },
  
  imageFile: {
    filename: 'photo.jpg',
    contentType: 'image/jpeg',
    size: 512 * 1024, // 512KB
    content: Buffer.from('\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb', 'binary')
  },
  
  csvFile: {
    filename: 'data.csv',
    contentType: 'text/csv',
    size: 256, // 256 bytes
    content: Buffer.from('name,email,age\nJohn Doe,<EMAIL>,30\nJane Smith,<EMAIL>,25', 'utf-8')
  }
};

export const mockUserFileTypeRules = {
  freeUser: [
    {
      id: 'rule1',
      userId: 'free-user-123',
      category: 'text',
      mimeTypes: ['text/plain', 'text/csv'],
      maxSizeMB: 0.1,
      handling: 'sync',
      syncThresholdMB: 0.1
    }
  ],
  
  proUser: [
    {
      id: 'rule1',
      userId: 'pro-user-123',
      category: 'documents',
      mimeTypes: ['application/pdf', 'text/plain', 'application/json'],
      maxSizeMB: 10,
      handling: 'storage',
      syncThresholdMB: 2
    },
    {
      id: 'rule2',
      userId: 'pro-user-123',
      category: 'images',
      mimeTypes: ['image/jpeg', 'image/png'],
      maxSizeMB: 5,
      handling: 'storage',
      syncThresholdMB: 1
    }
  ],
  
  restrictiveUser: [
    {
      id: 'rule1',
      userId: 'restrictive-user-123',
      category: 'text-only',
      mimeTypes: ['text/plain'],
      maxSizeMB: 0.05,
      handling: 'sync',
      syncThresholdMB: 0.05
    }
  ]
};

export const mockS3Responses = {
  successfulUpload: {
    uploadStatus: 'COMPLETED' as const,
    s3Key: 'email-attachments/msg123/test-file.txt',
    downloadUrl: 'https://emailconnect.eu/attachments/af_123abc456def/download',
    uploadedAt: new Date('2023-12-01T10:00:00Z')
  },
  
  failedUpload: {
    uploadStatus: 'FAILED' as const,
    s3Key: null,
    downloadUrl: null,
    error: 'S3 access denied'
  }
};

export const sampleRawEmailWithAttachments = `From: <EMAIL>
To: <EMAIL>
Subject: Test Email with Attachments
Date: Fri, 01 Dec 2023 10:00:00 +0000
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="boundary123"
X-Original-To: <EMAIL>

--boundary123
Content-Type: text/plain

This is the email body with attachments.

--boundary123
Content-Type: text/plain; name="test.txt"
Content-Disposition: attachment; filename="test.txt"
Content-Transfer-Encoding: base64

VGhpcyBpcyBhIHRlc3QgdGV4dCBmaWxlIGNvbnRlbnQgZm9yIHVuaXQgdGVzdGluZy4=

--boundary123
Content-Type: application/pdf; name="document.pdf"
Content-Disposition: attachment; filename="document.pdf"
Content-Transfer-Encoding: base64

JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPT4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQo+PgplbmRvYmoKeHJlZgowIDQKMDAwMDAwMDAwMCA2NTUzNSBmIAowMDAwMDAwMDA5IDAwMDAwIG4gCjAwMDAwMDAwNzQgMDAwMDAgbiAKMDAwMDAwMDEyMCAwMDAwMCBuIAp0cmFpbGVyCjw8Ci9TaXplIDQKL1Jvb3QgMSAwIFIKPj4Kc3RhcnR4cmVmCjE3OQolJUVPRg==

--boundary123--
`;

export const sampleAdvancedProcessingHeaders = {
  'x-advanced-processing': 'true',
  'x-attachment-count': '2',
  'x-attachment-metadata': JSON.stringify({
    attachments: [
      {
        filename: 'test.txt',
        contentType: 'text/plain',
        size: 1024,
        downloadUrl: 'https://emailconnect.eu/attachments/af_123abc456def/download',
        status: 'completed',
        storage: 's3',
        uploadType: 'sync'
      },
      {
        filename: 'large-report.pdf',
        contentType: 'application/pdf',
        size: 5242880,
        downloadUrl: 'https://emailconnect.eu/attachments/af_789ghi012jkl/download',
        status: 'pending',
        storage: 's3',
        uploadType: 'async'
      }
    ],
    totalCount: 2,
    processedCount: 2,
    excludedCount: 0,
    syncCount: 1,
    asyncCount: 1,
    rejectedCount: 0
  })
};
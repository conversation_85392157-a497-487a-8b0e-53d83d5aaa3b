import { sanitizeS3Folder } from '../../src/backend/services/storage/s3-storage.service';

describe('sanitizeS3Folder', () => {
  test('returns empty string for null/undefined/empty', () => {
    expect(sanitizeS3Folder(undefined)).toBe('');
    expect(sanitizeS3Folder(null as any)).toBe('');
    expect(sanitizeS3Folder('')).toBe('');
    expect(sanitizeS3Folder('   ')).toBe('');
  });

  test('normalizes backslashes and trims edges', () => {
    expect(sanitizeS3Folder('\\a\\b\\c')).toBe('a/b/c');
    expect(sanitizeS3Folder('/a/b/c/')).toBe('a/b/c');
  });

  test('collapses duplicate slashes', () => {
    expect(sanitizeS3Folder('a//b///c')).toBe('a/b/c');
  });

  test('replaces disallowed characters with underscore', () => {
    expect(sanitizeS3Folder('a b*c?d|e')).toBe('a_b_c_d_e');
  });

  test('removes dot segments', () => {
    expect(sanitizeS3Folder('a/./b/../c')).toBe('a/c');
  });

  test('caps length to 200 chars', () => {
    const long = 'a/'.repeat(150) + 'end';
    const out = sanitizeS3Folder(long);
    expect(out.length).toBeLessThanOrEqual(200);
  });
});


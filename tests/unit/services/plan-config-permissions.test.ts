import { describe, it, expect } from '@jest/globals';
import { PlanConfigService, PLAN_PERMISSIONS } from '../../../src/backend/services/billing/plan-config.service.js';

describe('PlanConfigService - Permission System', () => {
  describe('PLAN_PERMISSIONS configuration', () => {
    it('should have correct permissions for free plan', () => {
      expect(PLAN_PERMISSIONS.free).toEqual([]);
    });

    it('should have correct permissions for pro plan', () => {
      expect(PLAN_PERMISSIONS.pro).toContain('custom_headers');
      expect(PLAN_PERMISSIONS.pro).toContain('priority_support');
      expect(PLAN_PERMISSIONS.pro).toContain('spam_filtering');
      expect(PLAN_PERMISSIONS.pro).toContain('attachment_samples');
      expect(PLAN_PERMISSIONS.pro).toContain('s3_storage');
      expect(PLAN_PERMISSIONS.pro).not.toContain('email_analytics');
      expect(PLAN_PERMISSIONS.pro).not.toContain('custom_integrations');
    });

    it('should have correct permissions for enterprise plan', () => {
      expect(PLAN_PERMISSIONS.enterprise).toContain('custom_headers');
      expect(PLAN_PERMISSIONS.enterprise).toContain('priority_support');
      expect(PLAN_PERMISSIONS.enterprise).toContain('email_analytics');
      expect(PLAN_PERMISSIONS.enterprise).toContain('custom_integrations');
      expect(PLAN_PERMISSIONS.enterprise).toContain('spam_filtering');
      expect(PLAN_PERMISSIONS.enterprise).toContain('attachment_samples');
      expect(PLAN_PERMISSIONS.enterprise).toContain('s3_storage');
    });
  });

  describe('userHasPermission', () => {
    it('should return false for free plan with any permission', () => {
      expect(PlanConfigService.userHasPermission('free', 'custom_headers')).toBe(false);
      expect(PlanConfigService.userHasPermission('free', 'priority_support')).toBe(false);
    });

    it('should return true for pro plan with allowed permissions', () => {
      expect(PlanConfigService.userHasPermission('pro', 'custom_headers')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 'priority_support')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 'spam_filtering')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 'attachment_samples')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 's3_storage')).toBe(true);
    });

    it('should return false for pro plan with enterprise-only permissions', () => {
      expect(PlanConfigService.userHasPermission('pro', 'email_analytics')).toBe(false);
      expect(PlanConfigService.userHasPermission('pro', 'custom_integrations')).toBe(false);
    });

    it('should return true for enterprise plan with all permissions', () => {
      expect(PlanConfigService.userHasPermission('enterprise', 'custom_headers')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'priority_support')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'email_analytics')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'custom_integrations')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'spam_filtering')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'attachment_samples')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 's3_storage')).toBe(true);
    });

    it('should return false for unknown plan types', () => {
      expect(PlanConfigService.userHasPermission('unknown', 'custom_headers')).toBe(false);
      expect(PlanConfigService.userHasPermission('', 'custom_headers')).toBe(false);
    });
  });

  describe('getPlanPermissions', () => {
    it('should return empty array for free plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('free');
      expect(permissions).toEqual([]);
    });

    it('should return correct permissions for pro plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('pro');
      expect(permissions).toContain('custom_headers');
      expect(permissions).toContain('priority_support');
      expect(permissions).toContain('spam_filtering');
      expect(permissions).toContain('attachment_samples');
      expect(permissions).toContain('s3_storage');
      expect(permissions).toHaveLength(5);
    });

    it('should return all permissions for enterprise plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('enterprise');
      expect(permissions).toHaveLength(7);
      expect(permissions).toContain('custom_headers');
      expect(permissions).toContain('priority_support');
      expect(permissions).toContain('email_analytics');
      expect(permissions).toContain('custom_integrations');
      expect(permissions).toContain('spam_filtering');
      expect(permissions).toContain('attachment_samples');
      expect(permissions).toContain('s3_storage');
    });

    it('should return empty array for unknown plan types', () => {
      expect(PlanConfigService.getPlanPermissions('unknown')).toEqual([]);
    });
  });

  describe('userHasAnyPermission', () => {
    it('should return false for free plan with any permissions', () => {
      expect(PlanConfigService.userHasAnyPermission('free', ['custom_headers', 'priority_support'])).toBe(false);
    });

    it('should return true for pro plan with at least one allowed permission', () => {
      expect(PlanConfigService.userHasAnyPermission('pro', ['custom_headers'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('pro', ['s3_storage'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('pro', ['email_analytics'])).toBe(false);
    });

    it('should return true for enterprise plan with any permissions', () => {
      expect(PlanConfigService.userHasAnyPermission('enterprise', ['custom_headers'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('enterprise', ['email_analytics'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('enterprise', ['s3_storage'])).toBe(true);
    });
  });

  describe('getMissingPermissions', () => {
    it('should return all permissions for free plan', () => {
      const missing = PlanConfigService.getMissingPermissions('free', ['custom_headers', 'priority_support']);
      expect(missing).toEqual(['custom_headers', 'priority_support']);
    });

    it('should return only missing permissions for pro plan', () => {
      const missing = PlanConfigService.getMissingPermissions('pro', ['custom_headers']);
      expect(missing).toEqual([]);
    });

    it('should return empty array for enterprise plan with any permissions', () => {
      const missing = PlanConfigService.getMissingPermissions('enterprise', ['custom_headers']);
      expect(missing).toEqual([]);
    });
  });

  describe('S3 Storage Permission Validation', () => {
    it('should allow S3 storage for Pro plan', () => {
      expect(PlanConfigService.userHasPermission('pro', 's3_storage')).toBe(true);
    });

    it('should allow S3 storage for Enterprise plan', () => {
      expect(PlanConfigService.userHasPermission('enterprise', 's3_storage')).toBe(true);
    });

    it('should deny S3 storage for Free plan', () => {
      expect(PlanConfigService.userHasPermission('free', 's3_storage')).toBe(false);
    });

    it('should validate S3 storage scope requirements', () => {
      const proValidation = PlanConfigService.validateScopeForPlan('storage:s3', 'pro');
      expect(proValidation.allowed).toBe(true);

      const enterpriseValidation = PlanConfigService.validateScopeForPlan('storage:s3', 'enterprise');
      expect(enterpriseValidation.allowed).toBe(true);

      const freeValidation = PlanConfigService.validateScopeForPlan('storage:s3', 'free');
      expect(freeValidation.allowed).toBe(false);
      expect(freeValidation.requiredPlan).toBe('pro');
      expect(freeValidation.missingPermissions).toContain('s3_storage');
    });
  });

  describe('Attachment Samples Permission Validation', () => {
    it('should allow attachment samples for Pro plan', () => {
      expect(PlanConfigService.userHasPermission('pro', 'attachment_samples')).toBe(true);
    });

    it('should validate webhook test attachments scope', () => {
      const proValidation = PlanConfigService.validateScopeForPlan('webhook:test_attachments', 'pro');
      expect(proValidation.allowed).toBe(true);

      const freeValidation = PlanConfigService.validateScopeForPlan('webhook:test_attachments', 'free');
      expect(freeValidation.allowed).toBe(false);
      expect(freeValidation.missingPermissions).toContain('attachment_samples');
    });
  });

  describe('getCreditPricing with permissions', () => {
    it('should return standard pricing for free plan', () => {
      const pricing = PlanConfigService.getCreditPricing('free');
      expect(pricing.pricePerHundred).toBe(1.00);
      expect(pricing.currency).toBe('EUR');
    });

    it('should return discounted pricing for pro plan (has priority_support)', () => {
      const pricing = PlanConfigService.getCreditPricing('pro');
      expect(pricing.pricePerHundred).toBe(0.80);
      expect(pricing.currency).toBe('EUR');
    });

    it('should return discounted pricing for enterprise plan (has priority_support)', () => {
      const pricing = PlanConfigService.getCreditPricing('enterprise');
      expect(pricing.pricePerHundred).toBe(0.80);
      expect(pricing.currency).toBe('EUR');
    });
  });
});

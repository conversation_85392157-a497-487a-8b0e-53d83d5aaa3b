/**
 * Unit tests for PostfixSyncService
 * Tests the advanced_processing logic and system domain restrictions
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import { PostfixSyncService } from '../../../src/backend/services/postfix-sync.service.js';
import { setupTestDatabase, prisma, createTestUser } from '../../setup/test-db-setup.js';

// Mock external dependencies
jest.mock('child_process');

setupTestDatabase();

describe('PostfixSyncService', () => {
  let postfixSyncService: PostfixSyncService;

  beforeEach(() => {
    postfixSyncService = new PostfixSyncService();
  });

  describe('System Domain Restrictions', () => {
    test('should always set advanced_processing = false for system domain', async () => {
      const user = await createTestUser({
        planType: 'pro' // Even pro users can't use advanced processing on system domain
      });

      // Sync system domain
      await postfixSyncService.syncDomainToPostfix('user.emailconnect.eu', user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'user.emailconnect.eu' }
      });

      expect(postfixDomain).toBeTruthy();
      expect(postfixDomain?.domain).toBe('user.emailconnect.eu');
      expect(postfixDomain?.advancedProcessing).toBe(false);
      expect(postfixDomain?.active).toBe(true);
    });

    test('should force system domain to basic processing even with spam filtering enabled', async () => {
      const user = await createTestUser({
        planType: 'pro' // Pro plan has spam filtering
      });

      // Even though pro users have spam filtering, system domain should use basic processing
      await postfixSyncService.syncDomainToPostfix('user.emailconnect.eu', user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'user.emailconnect.eu' }
      });

      expect(postfixDomain?.spamFiltering).toBe(true); // Pro users have spam filtering
      expect(postfixDomain?.advancedProcessing).toBe(false); // But system domain never uses advanced processing
    });

    test('should maintain system domain restriction when updating spam filtering', async () => {
      const user = await createTestUser({
        planType: 'pro'
      });

      // First sync the domain
      await postfixSyncService.syncDomainToPostfix('user.emailconnect.eu', user.id, true);

      // Then update spam filtering (this would normally enable advanced processing)
      await postfixSyncService.updateDomainSpamFiltering('user.emailconnect.eu', true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'user.emailconnect.eu' }
      });

      expect(postfixDomain?.spamFiltering).toBe(true);
      expect(postfixDomain?.advancedProcessing).toBe(false); // Still forced to false
    });
  });

  describe('Custom Domain Advanced Processing Logic', () => {
    test('should enable advanced_processing for custom domains with spam filtering', async () => {
      const user = await createTestUser({
        planType: 'pro' // Pro plan has spam filtering
      });

      const customDomain = 'test-spam.example.com';

      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.domain).toBe(customDomain);
      expect(postfixDomain?.spamFiltering).toBe(true);
      expect(postfixDomain?.advancedProcessing).toBe(true); // Should be true for spam filtering
    });

    test('should enable advanced_processing for custom domains with storage aliases', async () => {
      const user = await createTestUser({
        planType: 'pro'
      });

      const customDomain = 'storage-test.example.com';

      // Create domain in main tables first
      const domain = await prisma.domain.create({
        data: {
          domain: customDomain,
          userId: user.id,
          verificationStatus: 'VERIFIED'
        }
      });

      const webhook = await prisma.webhook.create({
        data: {
          userId: user.id,
          name: 'Test Webhook',
          url: 'https://test.example.com/webhook',
          active: true,
          verified: true
        }
      });

      // Create alias with storage handling
      await prisma.alias.create({
        data: {
          email: `support@${customDomain}`,
          domainId: domain.id,
          webhookId: webhook.id,
          active: true,
          configuration: {
            allowAttachments: true,
            attachmentHandling: 'storage',
            includeEnvelope: false
          }
        }
      });

      // Now sync domain to postfix (this should detect the storage alias)
      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.domain).toBe(customDomain);
      expect(postfixDomain?.advancedProcessing).toBe(true); // Should be true due to storage alias
    });

    test('should disable advanced_processing when no special features required', async () => {
      const user = await createTestUser({
        planType: 'free' // Free plan has no spam filtering
      });

      const customDomain = 'basic.example.com';

      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.domain).toBe(customDomain);
      expect(postfixDomain?.spamFiltering).toBe(false);
      expect(postfixDomain?.advancedProcessing).toBe(false); // No special features needed
    });

    test('should update advanced_processing when spam filtering changes', async () => {
      const user = await createTestUser({
        planType: 'free'
      });

      const customDomain = 'update-test.example.com';

      // Initially sync without spam filtering
      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      let postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.spamFiltering).toBe(false);
      expect(postfixDomain?.advancedProcessing).toBe(false);

      // Enable spam filtering
      await postfixSyncService.updateDomainSpamFiltering(customDomain, true);

      postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.spamFiltering).toBe(true);
      expect(postfixDomain?.advancedProcessing).toBe(true); // Should now be true

      // Disable spam filtering
      await postfixSyncService.updateDomainSpamFiltering(customDomain, false);

      postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      expect(postfixDomain?.spamFiltering).toBe(false);
      expect(postfixDomain?.advancedProcessing).toBe(false); // Should be false again
    });
  });

  describe('Dual-Write Strategy', () => {
    test('should write both spam_filtering and advanced_processing fields', async () => {
      const user = await createTestUser({
        planType: 'pro'
      });

      const customDomain = 'dual-write.example.com';

      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });

      // Both fields should be written
      expect(postfixDomain?.spamFiltering).toBeDefined();
      expect(postfixDomain?.advancedProcessing).toBeDefined();
      
      // For pro user with spam filtering, both should be true
      expect(postfixDomain?.spamFiltering).toBe(true);
      expect(postfixDomain?.advancedProcessing).toBe(true);
    });

    test('should handle domain removal correctly', async () => {
      const user = await createTestUser();
      const customDomain = 'removal-test.example.com';

      // First create the domain
      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      let postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });
      expect(postfixDomain).toBeTruthy();

      // Then remove it
      await postfixSyncService.removeDomainFromPostfix(customDomain);

      postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: customDomain }
      });
      expect(postfixDomain).toBeNull();
    });
  });

  describe('Alias Management', () => {
    test('should create catch-all aliases for domains', async () => {
      const user = await createTestUser();
      const customDomain = 'catchall-test.example.com';

      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      const catchAllAlias = await prisma.postfixVirtualAlias.findUnique({
        where: { email: `@${customDomain}` }
      });

      expect(catchAllAlias).toBeTruthy();
      expect(catchAllAlias?.email).toBe(`@${customDomain}`);
      expect(catchAllAlias?.domain).toBe(customDomain);
      expect(catchAllAlias?.active).toBe(true);
    });

    test('should manage specific aliases correctly', async () => {
      const user = await createTestUser();
      const customDomain = 'alias-test.example.com';

      // First create the domain
      await postfixSyncService.syncDomainToPostfix(customDomain, user.id, true);

      // Then add a specific alias
      const specificEmail = `support@${customDomain}`;
      await postfixSyncService.syncAliasToPostfix(specificEmail, customDomain, true);

      const specificAlias = await prisma.postfixVirtualAlias.findUnique({
        where: { email: specificEmail }
      });

      expect(specificAlias).toBeTruthy();
      expect(specificAlias?.email).toBe(specificEmail);
      expect(specificAlias?.domain).toBe(customDomain);
      expect(specificAlias?.active).toBe(true);

      // Remove the specific alias
      await postfixSyncService.removeAliasFromPostfix(specificEmail);

      const removedAlias = await prisma.postfixVirtualAlias.findUnique({
        where: { email: specificEmail }
      });
      expect(removedAlias).toBeNull();
    });
  });

  describe('Health Check', () => {
    test('should return healthy status when database accessible', async () => {
      const healthResult = await postfixSyncService.healthCheck();

      expect(healthResult.healthy).toBe(true);
      expect(healthResult.details).toHaveProperty('totalDomains');
      expect(healthResult.details).toHaveProperty('totalAliases');
      expect(healthResult.details).toHaveProperty('activeDomains');
      expect(healthResult.details).toHaveProperty('timestamp');
      
      expect(typeof healthResult.details.totalDomains).toBe('number');
      expect(typeof healthResult.details.totalAliases).toBe('number');
      expect(typeof healthResult.details.activeDomains).toBe('number');
    });
  });

  describe('Error Handling', () => {
    test('should handle missing user gracefully', async () => {
      const nonExistentUserId = 'non-existent-user-id';
      const customDomain = 'error-test.example.com';

      await expect(
        postfixSyncService.syncDomainToPostfix(customDomain, nonExistentUserId, true)
      ).rejects.toThrow('User non-existent-user-id not found');
    });

    test('should handle missing domain for alias sync', async () => {
      const nonExistentDomain = 'missing-domain.example.com';
      const email = `test@${nonExistentDomain}`;

      await expect(
        postfixSyncService.syncAliasToPostfix(email, nonExistentDomain, true)
      ).rejects.toThrow(`Domain ${nonExistentDomain} not found in Postfix PostgreSQL tables`);
    });
  });

  describe('Configuration Queries', () => {
    test('should return list of configured domains', async () => {
      const user = await createTestUser();
      const domains = ['test1.example.com', 'test2.example.com', 'test3.example.com'];

      // Create multiple domains
      for (const domain of domains) {
        await postfixSyncService.syncDomainToPostfix(domain, user.id, true);
      }

      const configuredDomains = await postfixSyncService.getConfiguredDomains();

      expect(configuredDomains).toEqual(expect.arrayContaining(domains));
      expect(configuredDomains.length).toBeGreaterThanOrEqual(domains.length);
    });

    test('should only return active domains', async () => {
      const user = await createTestUser();
      const activeDomain = 'active.example.com';
      const inactiveDomain = 'inactive.example.com';

      // Create active domain
      await postfixSyncService.syncDomainToPostfix(activeDomain, user.id, true);
      
      // Create inactive domain
      await postfixSyncService.syncDomainToPostfix(inactiveDomain, user.id, false);

      const configuredDomains = await postfixSyncService.getConfiguredDomains();

      expect(configuredDomains).toContain(activeDomain);
      expect(configuredDomains).not.toContain(inactiveDomain);
    });
  });
});
import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import {
  setupTestDatabase,
  prisma,
  createTestFastifyApp
} from '../setup/test-db-setup.js';
import { PlanConfigService } from '../../src/backend/services/billing/plan-config.service';
import axios from 'axios';

// Mock WebhookTest integration for testing
jest.mock('../../src/backend/services/webhooktest-integration.service.js', () => ({
  WebhookTestIntegrationService: {
    createWebhookTestEndpoint: jest.fn().mockResolvedValue({
      success: true,
      webhookEndpoint: 'https://webhooktest.eu/endpoint/test-journey-12345',
      testId: 'test-journey-12345',
      verificationCode: 'verify-journey-12345'
    })
  }
}));

describe('Test Webhook User Journey (E2E)', () => {
  setupTestDatabase();

  let app: FastifyInstance;
  let authToken: string;
  let userId: string;
  let testEmail: string;

  let realAxiosPost: any;

  beforeAll(async () => {
    app = await createTestFastifyApp();

    // Register necessary routes
    await app.register(async function (fastify) {
      await fastify.register((await import('../../src/backend/routes/better-auth.routes.js')).betterAuthRoutes, { prefix: '/api' });
      await fastify.register((await import('../../src/backend/routes/email.js')).emailRoutes, { prefix: '/api/email' });
      await fastify.register((await import('../../src/backend/routes/logs.routes.js')).logsRoutes, { prefix: '/api' });
    });

    await app.ready();

    // Mock axios.post only for webhooktest.eu to return 200 in tests
    realAxiosPost = axios.post.bind(axios);
    jest.spyOn(axios, 'post').mockImplementation((url: any, data: any, config: any) => {
      const target = typeof url === 'string' ? url : (url?.toString?.() || '');
      if (typeof target === 'string' && target.includes('webhooktest.eu')) {
        return Promise.resolve({ status: 200, data: { ok: true } });
      }
      return realAxiosPost(url, data, config);
    });
  });

  afterAll(async () => {
    // Restore axios.post
    (axios.post as any).mockRestore?.();
    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.email.deleteMany({
      where: { isTestWebhook: true }
    });
  });

  test.skip('complete user journey: signup → get test email → send email → view logs', async () => {
    // Step 1: Create user directly in database (auth flow tested separately)
    const bcrypt = await import('bcrypt');
    const hashedPassword = await bcrypt.hash('securepassword123', 10);
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User',
        planType: 'free'
      }
    });
    
    userId = user.id;
    
    // Generate auth token for testing
    const { UserAuthService } = await import('../../src/backend/services/auth/user-auth.service.js');
    const userAuthService = new UserAuthService();
    const tokenResult = userAuthService.generateToken({ userId: user.id, email: user.email });
    authToken = tokenResult.token!;

    // Verify webhook was auto-created during registration
    const webhook = await prisma.webhook.findFirst({
      where: { userId }
    });
    expect(webhook).toBeTruthy();
    expect(webhook?.url).toContain('webhooktest.eu');

    // Step 2: User gets their test email address
    const userIdSuffix = userId.slice(-8);
    testEmail = `${userIdSuffix}+<EMAIL>`;
    
    // Step 3: User sends an email to their test address
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: My First Test Email
Date: ${new Date().toUTCString()}
Message-ID: <first-test-${Date.now()}@gmail.com>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

Hello!

I'm testing the webhook functionality. This is my first email to see how it works.

The email should appear in my logs with the full JSON payload.

Best regards,
Test User`;

    const emailResponse = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain',
        'X-Email-Source': 'test-journey'
      }
    });

    expect(emailResponse.statusCode).toBe(202);
    const emailBody = JSON.parse(emailResponse.body);
    expect(emailBody.success).toBe(true);
    expect(emailBody.status).toBe('queued'); // Now queued through normal webhook system
    expect(emailBody.isTestWebhook).toBe(true);

    // Step 4: User checks their logs
    const logsResponse = await app.inject({
      method: 'GET',
      url: '/api/logs',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(logsResponse.statusCode).toBe(200);
    const logsBody = JSON.parse(logsResponse.body);
    expect(logsBody.logs).toBeDefined();
    expect(logsBody.logs.length).toBeGreaterThan(0);

    // Find our test email in the logs
    const testEmailLog = logsBody.logs.find((log: any) => 
      log.messageId === emailBody.messageId
    );

    expect(testEmailLog).toBeDefined();
    expect(testEmailLog.fromAddress).toBe('<EMAIL>');
    expect(testEmailLog.subject).toBe('My First Test Email');
    expect(['PENDING', 'DELIVERED', 'RETRYING']).toContain(testEmailLog.deliveryStatus); // Allow quick queue transitions in tests

    // Step 5: User views the webhook payload (simulating frontend click)
    const emailRecord = await prisma.email.findUnique({
      where: { messageId: emailBody.messageId }
    });

    expect(emailRecord).toBeTruthy();
    expect(emailRecord!.webhookPayload).toBeTruthy();
    expect(emailRecord!.userId).toBe(userId); // Should have userId set
    expect(emailRecord!.domainId).toBeNull(); // No domain for user.emailconnect.eu

    const payload = emailRecord!.webhookPayload as any;
    
    // Verify the payload structure that user would see
    expect(payload.message.sender.email).toBe('<EMAIL>');
    expect(payload.message.recipient.email).toBe(testEmail);
    expect(payload.message.subject).toBe('My First Test Email');
    expect(payload.message.content.text).toContain('testing the webhook functionality');
    expect(payload.envelope.messageId).toBe(emailBody.messageId);
    expect(payload.envelope.processed.domain).toBe('user.emailconnect.eu');

    // Step 6: Verify user's usage was NOT tracked (test webhooks don't count)
    const updatedUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { currentMonthEmails: true, planType: true }
    });

    expect(updatedUser!.currentMonthEmails).toBe(0); // Should remain 0 for test webhooks
    const planConfig = PlanConfigService.getPlanConfig(updatedUser!.planType || 'free');
    expect(planConfig.monthlyEmailLimit).toBe(50); // Default limit

    console.log('✅ Complete user journey test passed!');
    console.log(`📧 Test email: ${testEmail}`);
    console.log(`📊 Usage: ${updatedUser!.currentMonthEmails}/${planConfig.monthlyEmailLimit}`);
    console.log(`🔍 Message ID: ${emailBody.messageId}`);
  });

  test('user can send multiple test emails and see them all', async () => {
    // Create user directly in database
    const bcrypt = await import('bcrypt');
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Multi Test User',
        planType: 'free'
      }
    });

    // Generate auth token
    const { UserAuthService } = await import('../../src/backend/services/auth/user-auth.service.js');
    const userAuthService = new UserAuthService();
    const tokenResult = userAuthService.generateToken({ userId: user.id, email: user.email });
    const token = tokenResult.token!;
    
    const testEmail = `${user.id.slice(-8)}+<EMAIL>`;

    // Send multiple emails
    const emailSubjects = [
      'First Test Email',
      'Second Test Email', 
      'Third Test Email'
    ];

    const messageIds: string[] = [];

    for (let i = 0; i < emailSubjects.length; i++) {
      const rawEmail = `From: sender${i}@example.com
To: ${testEmail}
Subject: ${emailSubjects[i]}
Date: ${new Date().toUTCString()}
Message-ID: <multi-test-${i}-${Date.now()}@example.com>

This is test email number ${i + 1}.`;

      const response = await app.inject({
        method: 'POST',
        url: '/api/email/process',
        payload: rawEmail,
        headers: { 'Content-Type': 'text/plain' }
      });

      expect(response.statusCode).toBe(202);
      const body = JSON.parse(response.body);
      messageIds.push(body.messageId);

      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // Check logs show all emails
    const logsResponse = await app.inject({
      method: 'GET',
      url: '/api/logs',
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const logsBody = JSON.parse(logsResponse.body);
    
    // Should have all 3 emails
    expect(logsBody.logs.length).toBe(3);
    
    // Verify all message IDs are present
    const logMessageIds = logsBody.logs.map((log: any) => log.messageId);
    for (const messageId of messageIds) {
      expect(logMessageIds).toContain(messageId);
    }

    // Verify they're ordered by creation time (newest first)
    const timestamps = logsBody.logs.map((log: any) => new Date(log.createdAt).getTime());
    for (let i = 1; i < timestamps.length; i++) {
      expect(timestamps[i-1]).toBeGreaterThanOrEqual(timestamps[i]);
    }

    // Verify usage tracking (test webhooks don't count toward usage)
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { currentMonthEmails: true }
    });

    expect(updatedUser!.currentMonthEmails).toBe(0); // Should remain 0 for test webhooks
  });

  test('user hits email limit and gets proper error', async () => {
    // Create user directly in database
    const bcrypt = await import('bcrypt');
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Limit Test User',
        planType: 'free'
      }
    });
    
    // Set user to be at limit
    await prisma.user.update({
      where: { id: user.id },
      data: {
        currentMonthEmails: 50, // At the default limit
      }
    });

    const testEmail = `${user.id.slice(-8)}+<EMAIL>`;

    // Try to send email when at limit
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Should Be Rejected
Date: ${new Date().toUTCString()}
Message-ID: <limit-test-${Date.now()}@example.com>

This should be rejected.`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: { 'Content-Type': 'text/plain' }
    });

    expect(response.statusCode).toBe(429);
    const body = JSON.parse(response.body);
    expect(body.error).toBe('Too Many Requests');
    expect(body.message).toContain('Email quota exhausted. Purchase credits or upgrade your plan.');
    expect(body.currentUsage).toBe(50);
    expect(body.monthlyLimit).toBe(50);
  }, 15000);
});

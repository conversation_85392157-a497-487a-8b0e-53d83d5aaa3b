#!/usr/bin/env node

/**
 * Test script for webhook secret functionality
 * Tests HMAC signature generation and verification
 */

import crypto from 'crypto';

// Test payload (similar to what would be sent to webhooks)
const testPayload = {
  messageId: '<EMAIL>',
  envelope: {
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    messageId: 'test-msg-123',
  },
  message: {
    sender: { email: '<EMAIL>', name: 'Test Sender' },
    recipient: { email: '<EMAIL>', name: 'Test Recipient' },
    subject: 'Test Email',
    textBody: 'This is a test email.',
    htmlBody: '<p>This is a test email.</p>',
    attachments: [],
    headers: [],
    date: new Date().toISOString(),
  },
  domain: 'test.com',
  timestamp: new Date().toISOString(),
};

// Test webhook secret
const webhookSecret = 'test-secret-key-12345';

function generateSignature(payload, secret) {
  const payloadString = JSON.stringify(payload);
  const signature = crypto
    .createHmac('sha256', secret)
    .update(payloadString, 'utf8')
    .digest('hex');
  
  return `sha256=${signature}`;
}

function verifySignature(payload, signature, secret) {
  const expectedSignature = generateSignature(payload, secret);
  return signature === expectedSignature;
}

console.log('🧪 Testing webhook secret functionality...\n');

// Test 1: Generate signature
console.log('1. Generating HMAC signature...');
const signature = generateSignature(testPayload, webhookSecret);
console.log(`   Generated signature: ${signature}`);

// Test 2: Verify signature
console.log('\n2. Verifying signature...');
const isValid = verifySignature(testPayload, signature, webhookSecret);
console.log(`   Signature valid: ${isValid ? '✅' : '❌'}`);

// Test 3: Verify with wrong secret
console.log('\n3. Testing with wrong secret...');
const wrongSecret = 'wrong-secret';
const isInvalid = verifySignature(testPayload, signature, wrongSecret);
console.log(`   Signature valid with wrong secret: ${isInvalid ? '❌ FAIL' : '✅ PASS'}`);

// Test 4: Verify with tampered payload
console.log('\n4. Testing with tampered payload...');
const tamperedPayload = { ...testPayload, message: { ...testPayload.message, subject: 'Tampered Subject' } };
const isTampered = verifySignature(tamperedPayload, signature, webhookSecret);
console.log(`   Signature valid with tampered payload: ${isTampered ? '❌ FAIL' : '✅ PASS'}`);

console.log('\n🎉 Webhook secret tests completed!');

// Example headers that would be sent
console.log('\n📋 Example webhook headers:');
console.log(`   Content-Type: application/json`);
console.log(`   User-Agent: EmailConnect/1.0`);
console.log(`   X-Email-Webhook: true`);
console.log(`   X-Webhook-Signature: ${signature}`);
console.log(`   X-Webhook-Timestamp: ${Math.floor(Date.now() / 1000)}`);

console.log('\n📖 Webhook verification example:');
console.log(`
// Webhook endpoint verification example (Node.js/Express)
app.post('/webhook', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const timestamp = req.headers['x-webhook-timestamp'];
  const payload = req.body;
  
  // Verify timestamp (optional - prevent replay attacks)
  const now = Math.floor(Date.now() / 1000);
  if (Math.abs(now - parseInt(timestamp)) > 300) { // 5 minutes
    return res.status(400).send('Request too old');
  }
  
  // Verify signature
  const expectedSignature = crypto
    .createHmac('sha256', '${webhookSecret}')
    .update(JSON.stringify(payload), 'utf8')
    .digest('hex');
  
  if (signature !== \`sha256=\${expectedSignature}\`) {
    return res.status(401).send('Invalid signature');
  }
  
  // Process webhook payload
  console.log('Verified webhook:', payload);
  res.status(200).send('OK');
});
`);

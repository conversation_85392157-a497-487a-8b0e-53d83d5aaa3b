import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestFastifyApp
} from '../setup/test-db-setup.js';

describe('Test Webhook Usage Exclusion', () => {
  setupTestDatabase();

  let app: FastifyInstance;
  let testUser: any;
  let userIdSuffix: string;
  let testEmail: string;

  beforeAll(async () => {
    app = await createTestFastifyApp();

    // Register the email processing route
    await app.register(async function (fastify) {
      await fastify.register((await import('../../src/backend/routes/email.js')).emailRoutes, { prefix: '/api/email' });
    });

    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.email.deleteMany({
      where: { isTestWebhook: true }
    });

    // Create a test user
    testUser = await createTestUser({
      email: '<EMAIL>',
      password: 'password123',
      currentMonthEmails: 0
    });

    // Get the last 8 characters of user ID for test email
    userIdSuffix = testUser.id.slice(-8);
    testEmail = `${userIdSuffix}+<EMAIL>`;
  });

  test('test webhook emails should NOT increment usage count', async () => {
    // Verify initial usage is 0
    const initialUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });
    expect(initialUser!.currentMonthEmails).toBe(0);

    // Send a test webhook email
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Test Webhook Email
Date: ${new Date().toUTCString()}
Message-ID: <test-usage-${Date.now()}@example.com>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

This is a test webhook email that should NOT count toward usage limits.
`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    expect(response.statusCode).toBe(202);
    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe(true);
    // Note: isTestWebhook may or may not be in response, but the important thing is usage tracking

    // Verify that usage count has NOT been incremented
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });
    expect(updatedUser!.currentMonthEmails).toBe(0); // Should still be 0

    // Verify the email was stored with isTestWebhook: true
    const storedEmail = await prisma.email.findFirst({
      where: { 
        messageId: responseBody.messageId,
        isTestWebhook: true
      }
    });
    expect(storedEmail).toBeTruthy();
    expect(storedEmail!.isTestWebhook).toBe(true);
  });

  test('regular emails would increment usage count (but test webhooks do not)', async () => {
    // Note: This test demonstrates that test webhooks are treated differently
    // We can't easily test regular email processing in this isolated test
    // because it requires complex domain/webhook setup, but we can verify
    // that test webhooks specifically do NOT increment usage

    // Verify initial usage is 0
    const initialUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });
    expect(initialUser!.currentMonthEmails).toBe(0);

    // Send a test webhook email (should NOT increment usage)
    const rawEmail = `From: <EMAIL>
To: ${testEmail}
Subject: Test Webhook Email
Date: ${new Date().toUTCString()}
Message-ID: <test-no-usage-${Date.now()}@example.com>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8

This is a test webhook email that should NOT count toward usage limits.
`;

    const response = await app.inject({
      method: 'POST',
      url: '/api/email/process',
      payload: rawEmail,
      headers: {
        'Content-Type': 'text/plain'
      }
    });

    expect(response.statusCode).toBe(202);
    const responseBody = JSON.parse(response.body);
    expect(responseBody.success).toBe(true);

    // The response should include isTestWebhook for test webhook emails
    if (responseBody.isTestWebhook !== undefined) {
      expect(responseBody.isTestWebhook).toBe(true);
    }

    // Verify that usage count has NOT been incremented
    const updatedUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });
    expect(updatedUser!.currentMonthEmails).toBe(0); // Should still be 0

    // Verify the email was stored with isTestWebhook: true
    const storedEmail = await prisma.email.findFirst({
      where: {
        messageId: responseBody.messageId
      }
    });
    expect(storedEmail).toBeTruthy();
    expect(storedEmail!.isTestWebhook).toBe(true);
  });

  test('multiple test webhook emails should not accumulate usage', async () => {
    // Send multiple test webhook emails
    for (let i = 0; i < 3; i++) {
      const rawEmail = `From: sender${i}@example.com
To: ${testEmail}
Subject: Test Webhook Email ${i + 1}
Date: ${new Date().toUTCString()}
Message-ID: <test-multiple-${i}-${Date.now()}@example.com>

Test email ${i + 1} - should not count toward usage.
`;

      const response = await app.inject({
        method: 'POST',
        url: '/api/email/process',
        payload: rawEmail,
        headers: {
          'Content-Type': 'text/plain'
        }
      });

      expect(response.statusCode).toBe(202);
    }

    // Verify usage is still 0 after multiple test emails
    const finalUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true }
    });
    expect(finalUser!.currentMonthEmails).toBe(0);

    // Verify all emails were stored as test webhooks
    const testEmails = await prisma.email.count({
      where: { 
        toAddresses: { has: testEmail },
        isTestWebhook: true
      }
    });
    expect(testEmails).toBe(3);
  });
});

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { initializeQueue, queueWebhookDelivery, getQueue } from '../../../src/backend/services/queue.js';
import { prisma } from '../../../src/backend/lib/prisma.js';
import { setupTestDatabase, createTestUser } from '../../setup/test-db-setup.js';
import http from 'http';

setupTestDatabase();

// Mock webhook server
let mockServer: http.Server;
let mockServerPort: number;
let webhookRequests: any[] = [];
let shouldFail = true;

beforeAll(async () => {
  // Initialize the queue
  await initializeQueue();

  // Start mock webhook server
  mockServer = http.createServer((req, res) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      const requestData = {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: body ? JSON.parse(body) : null,
        timestamp: new Date().toISOString()
      };
      
      webhookRequests.push(requestData);
      
      if (shouldFail) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Mock webhook failure' }));
      } else {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true }));
      }
    });
  });

  // Start server on random port
  await new Promise<void>((resolve) => {
    mockServer.listen(0, () => {
      mockServerPort = (mockServer.address() as any).port;
      resolve();
    });
  });
});

afterAll(async () => {
  if (mockServer) {
    await new Promise<void>((resolve) => {
      mockServer.close(() => resolve());
    });
  }
});

describe('Queue Retry Logic', () => {
  test('should transition from RETRYING to FAILED after 3 attempts', async () => {
    // Reset state
    webhookRequests = [];
    shouldFail = true;

    // Create test user first
    const testUser = await createTestUser({
      email: '<EMAIL>',
      name: 'Retry Test User'
    });

    // Create test email record
    const testEmail = await prisma.email.create({
      data: {
        messageId: 'test-retry-logic-001',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Test Retry Logic',
        deliveryStatus: 'PENDING',
        deliveryAttempts: 0,

        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        webhookPayload: {
          message: {
            sender: { email: '<EMAIL>', name: 'Test Sender' },
            recipient: { email: '<EMAIL>', name: null },
            subject: 'Test Retry Logic',
            content: { text: 'Test content', html: null },
            date: new Date().toISOString()
          },
          envelope: {
            messageId: 'test-retry-logic-001'
          }
        }
      }
    });

    // Queue webhook delivery to failing endpoint
    const webhookUrl = `http://localhost:${mockServerPort}/webhook`;
    const payload = testEmail.webhookPayload as any;
    
    await queueWebhookDelivery(webhookUrl, payload);

    // Wait for all retry attempts to complete
    // With exponential backoff starting at 2000ms, this should take about 6-8 seconds total
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Check final email status
    const finalEmail = await prisma.email.findUnique({
      where: { messageId: 'test-retry-logic-001' }
    });

    expect(finalEmail).toBeTruthy();
    expect(finalEmail!.deliveryStatus).toBe('FAILED');
    expect(finalEmail!.deliveryAttempts).toBe(3); // Should have made 3 attempts
    expect(finalEmail!.errorMessage).toContain('Request failed with status code 500');

    // Verify webhook was called 3 times
    expect(webhookRequests).toHaveLength(3);
    
    // Clean up
    await prisma.email.delete({ where: { messageId: 'test-retry-logic-001' } });
  }, 30000); // 30 second timeout

  test('should succeed on retry after initial failures', async () => {
    // Reset state
    webhookRequests = [];
    shouldFail = true;

    // Create test user first
    const testUser2 = await createTestUser({
      email: '<EMAIL>',
      name: 'Retry Success Test User'
    });

    // Create test email record
    const testEmail = await prisma.email.create({
      data: {
        messageId: 'test-retry-success-001',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Test Retry Success',
        deliveryStatus: 'PENDING',
        deliveryAttempts: 0,

        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        webhookPayload: {
          message: {
            sender: { email: '<EMAIL>', name: 'Test Sender' },
            recipient: { email: '<EMAIL>', name: null },
            subject: 'Test Retry Success',
            content: { text: 'Test content', html: null },
            date: new Date().toISOString()
          },
          envelope: {
            messageId: 'test-retry-success-001'
          }
        }
      }
    });

    // Queue webhook delivery
    const webhookUrl = `http://localhost:${mockServerPort}/webhook`;
    const payload = testEmail.webhookPayload as any;
    
    await queueWebhookDelivery(webhookUrl, payload);

    // Wait for first attempt to fail
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Make webhook succeed on next attempt
    shouldFail = false;

    // Wait for retry to succeed
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check final email status
    const finalEmail = await prisma.email.findUnique({
      where: { messageId: 'test-retry-success-001' }
    });

    expect(finalEmail).toBeTruthy();
    expect(finalEmail!.deliveryStatus).toBe('DELIVERED');
    expect(finalEmail!.deliveryAttempts).toBe(2); // Should have made 2 attempts
    expect(finalEmail!.deliveredAt).toBeTruthy();
    expect(finalEmail!.errorMessage).toBeNull(); // Error should be cleared on success

    // Verify webhook was called at least twice (1 failure + 1 success)
    expect(webhookRequests.length).toBeGreaterThanOrEqual(2);
    
    // Clean up
    await prisma.email.delete({ where: { messageId: 'test-retry-success-001' } });
  }, 30000); // 30 second timeout
});

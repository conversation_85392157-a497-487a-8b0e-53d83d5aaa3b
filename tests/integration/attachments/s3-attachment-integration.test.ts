/**
 * Integration test for S3 attachment functionality
 * Tests the complete flow with mocked S3 but real database operations
 */

import { describe, test, expect, beforeEach } from '@jest/globals';
import { setupTestDatabase, prisma, createTestUser } from '../../setup/test-db-setup.js';
import { testAttachments } from '../../fixtures/attachments.js';

describe('S3 Attachment Integration', () => {
  setupTestDatabase();

  test('should create attachment file record in database', async () => {
    const user = await createTestUser();
    
    // Create a mock email record first
    const email = await prisma.email.create({
      data: {
        messageId: 'test-message-123',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Test Email with Attachment',
        userId: user.id,
        webhookPayload: {},
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }
    });

    // Create attachment file record (simulating what S3StorageService would do)
    const attachmentFile = await prisma.attachmentFile.create({
      data: {
        emailId: email.id,
        messageId: email.messageId,
        filename: testAttachments.smallTextFile.filename,
        contentType: testAttachments.smallTextFile.contentType,
        size: testAttachments.smallTextFile.size,
        downloadUrl: 'https://emailconnect.eu/attachments/af_123abc456def/download',
        uploadStatus: 'COMPLETED',
        processingType: 'sync',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        s3Key: 'email-attachments/test-message-123/test.txt'
      }
    });

    expect(attachmentFile.id).toBeDefined();
    expect(attachmentFile.filename).toBe('test.txt');
    expect(attachmentFile.uploadStatus).toBe('COMPLETED');
    expect(attachmentFile.s3Key).toContain('test.txt');

    // Verify the attachment is linked to the email
    const emailWithAttachments = await prisma.email.findUnique({
      where: { id: email.id },
      include: { attachments: true }
    });

    expect(emailWithAttachments?.attachments).toHaveLength(1);
    expect(emailWithAttachments?.attachments[0].filename).toBe('test.txt');
  });

  test('should create file type rules for user', async () => {
    const user = await createTestUser();

    // Create file type rules (simulating Pro user setup)
    const documentRule = await prisma.fileTypeRule.create({
      data: {
        userId: user.id,
        category: 'documents',
        mimeTypes: ['application/pdf', 'text/plain', 'application/json'],
        maxSizeMB: 10,
        handling: 'storage',
        syncThresholdMB: 2
      }
    });

    const imageRule = await prisma.fileTypeRule.create({
      data: {
        userId: user.id,
        category: 'images', 
        mimeTypes: ['image/jpeg', 'image/png'],
        maxSizeMB: 5,
        handling: 'storage',
        syncThresholdMB: 1
      }
    });

    // Retrieve user's rules
    const userRules = await prisma.fileTypeRule.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'asc' }
    });

    expect(userRules).toHaveLength(2);
    expect(userRules[0].category).toBe('documents');
    expect(userRules[1].category).toBe('images');
    expect(userRules[0].maxSizeMB).toBe(10);
    expect(userRules[1].maxSizeMB).toBe(5);
  });

  test('should handle multiple attachment statuses for an email', async () => {
    const user = await createTestUser();
    
    const email = await prisma.email.create({
      data: {
        messageId: 'test-message-multi-attach',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Email with Multiple Attachments',
        userId: user.id,
        webhookPayload: {},
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    });

    // Create multiple attachments with different statuses
    await prisma.attachmentFile.createMany({
      data: [
        {
          emailId: email.id,
          messageId: email.messageId,
          filename: 'small.txt',
          contentType: 'text/plain',
          size: 1024,
          downloadUrl: 'https://emailconnect.eu/attachments/af_small/download',
          uploadStatus: 'COMPLETED',
          processingType: 'sync',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          s3Key: 'email-attachments/test-message-multi-attach/small.txt'
        },
        {
          emailId: email.id,
          messageId: email.messageId,
          filename: 'large.pdf',
          contentType: 'application/pdf',
          size: 5242880, // 5MB
          downloadUrl: 'https://emailconnect.eu/attachments/af_large/download',
          uploadStatus: 'PENDING',
          processingType: 'async',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
        },
        {
          emailId: email.id,
          messageId: email.messageId,
          filename: 'failed.exe',
          contentType: 'application/octet-stream',
          size: 2048,
          downloadUrl: 'https://emailconnect.eu/attachments/af_failed/download',
          uploadStatus: 'FAILED',
          processingType: 'rejected',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          errorMessage: 'File type not allowed'
        }
      ]
    });

    // Query attachments by status
    const completedAttachments = await prisma.attachmentFile.findMany({
      where: { 
        emailId: email.id,
        uploadStatus: 'COMPLETED'
      }
    });

    const pendingAttachments = await prisma.attachmentFile.findMany({
      where: {
        emailId: email.id,
        uploadStatus: 'PENDING'
      }
    });

    const failedAttachments = await prisma.attachmentFile.findMany({
      where: {
        emailId: email.id,
        uploadStatus: 'FAILED'
      }
    });

    expect(completedAttachments).toHaveLength(1);
    expect(pendingAttachments).toHaveLength(1);
    expect(failedAttachments).toHaveLength(1);

    expect(completedAttachments[0].filename).toBe('small.txt');
    expect(pendingAttachments[0].filename).toBe('large.pdf');
    expect(failedAttachments[0].filename).toBe('failed.exe');
    expect(failedAttachments[0].errorMessage).toBe('File type not allowed');
  });

  test('should track download counts for attachments', async () => {
    const user = await createTestUser();
    
    const email = await prisma.email.create({
      data: {
        messageId: 'test-message-downloads',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Test Downloads',
        userId: user.id,
        webhookPayload: {},
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    });

    const attachmentFile = await prisma.attachmentFile.create({
      data: {
        emailId: email.id,
        messageId: email.messageId,
        filename: 'downloadable.pdf',
        contentType: 'application/pdf',
        size: 102400, // 100KB
        downloadUrl: 'https://emailconnect.eu/attachments/af_download_test/download',
        uploadStatus: 'COMPLETED',
        processingType: 'sync',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        s3Key: 'email-attachments/test-message-downloads/downloadable.pdf',
        downloadCount: 0
      }
    });

    // Simulate downloads
    const firstDownload = await prisma.attachmentFile.update({
      where: { id: attachmentFile.id },
      data: {
        downloadCount: { increment: 1 },
        lastDownloadAt: new Date()
      }
    });

    const secondDownload = await prisma.attachmentFile.update({
      where: { id: attachmentFile.id },
      data: {
        downloadCount: { increment: 1 },
        lastDownloadAt: new Date()
      }
    });

    expect(firstDownload.downloadCount).toBe(1);
    expect(secondDownload.downloadCount).toBe(2);
    expect(secondDownload.lastDownloadAt).toBeDefined();
  });

  test('should handle attachment expiration', async () => {
    const user = await createTestUser();
    
    const email = await prisma.email.create({
      data: {
        messageId: 'test-message-expiry',
        fromAddress: '<EMAIL>',
        toAddresses: ['<EMAIL>'],
        subject: 'Test Expiry',
        userId: user.id,
        webhookPayload: {},
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    });

    // Create attachment that expires in 1 hour
    const shortExpiryTime = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    const attachmentFile = await prisma.attachmentFile.create({
      data: {
        emailId: email.id,
        messageId: email.messageId,
        filename: 'expires-soon.txt',
        contentType: 'text/plain',
        size: 1024,
        downloadUrl: 'https://emailconnect.eu/attachments/af_expiry_test/download',
        uploadStatus: 'COMPLETED',
        processingType: 'sync',
        expiresAt: shortExpiryTime,
        s3Key: 'email-attachments/test-message-expiry/expires-soon.txt',
        customExpiry: true
      }
    });

    // Find attachments expiring soon (within 2 hours)
    const soonToExpire = await prisma.attachmentFile.findMany({
      where: {
        expiresAt: {
          lte: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
        },
        uploadStatus: 'COMPLETED'
      }
    });

    expect(soonToExpire).toHaveLength(1);
    expect(soonToExpire[0].filename).toBe('expires-soon.txt');
    expect(soonToExpire[0].customExpiry).toBe(true);
  });

  describe('System Domain S3 Storage Restrictions', () => {
    test('should prevent S3 storage for system domain aliases', async () => {
      const user = await createTestUser();
      const webhook = await prisma.webhook.create({
        data: {
          userId: user.id,
          name: 'Test Webhook',
          url: 'https://test.example.com/webhook',
          active: true,
          verified: true
        }
      });

      // Create system domain if it doesn't exist
      const systemDomain = await prisma.domain.upsert({
        where: { domain: 'user.emailconnect.eu' },
        create: {
          domain: 'user.emailconnect.eu',
          userId: user.id,
          verificationStatus: 'VERIFIED'
        },
        update: {}
      });

      // Attempt to create alias with S3 storage on system domain (should be blocked by business logic)
      const systemAlias = await prisma.alias.create({
        data: {
          email: `${user.id.slice(-8)}+<EMAIL>`,
          domainId: systemDomain.id,
          webhookId: webhook.id,
          active: true,
          configuration: {
            allowAttachments: true,
            attachmentHandling: 'storage', // This should be blocked/ignored for system domain
            includeEnvelope: false
          }
        }
      });

      // Verify the alias was created but with inline handling (frontend restriction)
      expect(systemAlias.email).toContain('@user.emailconnect.eu');
      expect(systemAlias.configuration).toEqual({
        allowAttachments: true,
        attachmentHandling: 'storage', // DB allows it, but frontend/routing should prevent it
        includeEnvelope: false
      });

      // Verify domain has advanced_processing = false (routing restriction)
      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'user.emailconnect.eu' }
      });
      
      if (postfixDomain) {
        expect(postfixDomain.advancedProcessing).toBe(false);
      }
    });

    test('should allow S3 storage for custom domain aliases', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashed_password',
          planType: 'pro', // Pro user can use S3 storage
          emailVerified: true
        }
      });

      const webhook = await prisma.webhook.create({
        data: {
          userId: user.id,
          name: 'Test Webhook',
          url: 'https://test.example.com/webhook',
          active: true,
          verified: true
        }
      });

      // Create custom domain
      const customDomain = await prisma.domain.create({
        data: {
          domain: 'custom.example.com',
          userId: user.id,
          verificationStatus: 'VERIFIED'
        }
      });

      // Create alias with S3 storage on custom domain (should be allowed)
      const customAlias = await prisma.alias.create({
        data: {
          email: '<EMAIL>',
          domainId: customDomain.id,
          webhookId: webhook.id,
          active: true,
          configuration: {
            allowAttachments: true,
            attachmentHandling: 'storage', // Should be allowed for custom domain + pro user
            includeEnvelope: false,
            s3Folder: 'support-attachments'
          }
        }
      });

      expect(customAlias.email).toBe('<EMAIL>');
      expect(customAlias.configuration).toMatchObject({
        allowAttachments: true,
        attachmentHandling: 'storage',
        s3Folder: 'support-attachments'
      });

      // Create postfix domain entry to simulate real usage
      await prisma.postfixVirtualDomain.upsert({
        where: { domain: 'custom.example.com' },
        create: {
          domain: 'custom.example.com',
          active: true,
          spamFiltering: true, // Pro user has spam filtering
          advancedProcessing: true // Should be true for Pro user with custom domain
        },
        update: {
          active: true,
          spamFiltering: true,
          advancedProcessing: true
        }
      });

      // Verify domain has advanced_processing = true when storage aliases exist
      const postfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'custom.example.com' }
      });
      
      expect(postfixDomain).toBeTruthy();
      expect(postfixDomain?.advancedProcessing).toBe(true);
    });

    test('should compute advanced_processing correctly for domains with storage aliases', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashed_password',
          planType: 'pro',
          emailVerified: true
        }
      });

      const webhook = await prisma.webhook.create({
        data: {
          userId: user.id,
          name: 'Storage Test Webhook',
          url: 'https://test.example.com/webhook',
          active: true,
          verified: true
        }
      });

      // Create custom domain
      const domain = await prisma.domain.create({
        data: {
          domain: 'storage-test.com',
          userId: user.id,
          verificationStatus: 'VERIFIED'
        }
      });

      // Create postfix domain entry
      await prisma.postfixVirtualDomain.upsert({
        where: { domain: 'storage-test.com' },
        create: {
          domain: 'storage-test.com',
          active: true,
          spamFiltering: false, // No spam filtering
          advancedProcessing: false // Initially false
        },
        update: {
          active: true,
          spamFiltering: false,
          advancedProcessing: false
        }
      });

      // Create alias with storage handling
      await prisma.alias.create({
        data: {
          email: '<EMAIL>',
          domainId: domain.id,
          webhookId: webhook.id,
          active: true,
          configuration: {
            allowAttachments: true,
            attachmentHandling: 'storage',
            includeEnvelope: false
          }
        }
      });

      // Manually simulate what PostfixSyncService.recompute_advanced_processing() trigger would do
      const hasStorageAliases = await prisma.alias.findFirst({
        where: {
          domainId: domain.id,
          active: true,
          configuration: {
            path: ['allowAttachments'],
            equals: true
          }
        }
      });

      const aliasConfig = hasStorageAliases?.configuration as any;
      const needsAdvancedProcessing = aliasConfig?.attachmentHandling === 'storage';

      // Update postfix domain
      await prisma.postfixVirtualDomain.update({
        where: { domain: 'storage-test.com' },
        data: { advancedProcessing: needsAdvancedProcessing }
      });

      const updatedPostfixDomain = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'storage-test.com' }
      });

      expect(updatedPostfixDomain?.advancedProcessing).toBe(true);
    });

    test('should ensure system domain never uses advanced processing', async () => {
      // Ensure system domain exists in postfix tables
      await prisma.postfixVirtualDomain.upsert({
        where: { domain: 'user.emailconnect.eu' },
        create: {
          domain: 'user.emailconnect.eu',
          active: true,
          spamFiltering: false,
          advancedProcessing: false
        },
        update: {
          advancedProcessing: false // Force to false even if somehow set to true
        }
      });

      const systemDomainConfig = await prisma.postfixVirtualDomain.findUnique({
        where: { domain: 'user.emailconnect.eu' }
      });

      expect(systemDomainConfig?.domain).toBe('user.emailconnect.eu');
      expect(systemDomainConfig?.advancedProcessing).toBe(false);
    });
  });
});
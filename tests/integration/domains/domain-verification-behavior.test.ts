import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { setupTestDatabase, prisma, createTestUser, createTestWebhook, createTestDomainWithCatchAll } from '../../setup/test-db-setup';
import { DomainService } from '../../../src/backend/services/user/domain.service.js';
import { VerificationWorker } from '../../../src/backend/services/verification-worker.js';
import { DNSVerifier } from '../../../src/backend/services/dns-verifier.js';

// Helper to compare time windows (in ms)
const within = (actual: Date | null, targetMs: number, deltaMs: number) => {
  if (!actual) return false;
  const diff = Math.abs(actual.getTime() - targetMs);
  return diff <= deltaMs;
};

describe('Domain verification behavior', () => {
  setupTestDatabase();
  const domainService = new DomainService();
  const worker = new VerificationWorker();

  let dnsSpy: jest.SpiedFunction<DNSVerifier['verifyDomainOwnership']>;

  beforeEach(() => {
    // Spy on DNS verification to avoid real DNS lookups
    dnsSpy = jest.spyOn(DNSVerifier.prototype, 'verifyDomainOwnership');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('manual verification success logs audit, schedules next check, and notifies once', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const { domain } = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: `manual-success-${Date.now()}.example.com`,
      verified: false,
      verificationStatus: 'PENDING',
      verificationFailureCount: 3,
      active: true
    });

    dnsSpy.mockResolvedValue({
      verified: true,
      domain: domain.domain,
      expectedRecord: `verify-ec=${domain.domain}`,
      foundRecords: [
        `verify-ec=${domain.domain}`
      ],
      cached: false,
      timestamp: new Date().toISOString()
    } as any);

    const now = Date.now();
    const result = await domainService.verifyDomain(domain.id, user.id);
    expect(result.success).toBe(true);
    expect(result.domain.verified).toBe(true);
    expect(result.domain.verificationStatus).toBe('VERIFIED');

    const updated = await prisma.domain.findUnique({ where: { id: domain.id } });
    expect(updated?.verificationFailureCount).toBe(0);
    expect(updated?.nextVerificationCheck).not.toBeNull();
    // Next check ~24h later (+/- 5 minutes tolerance)
    expect(within(updated!.nextVerificationCheck!, now + 24 * 60 * 60 * 1000, 5 * 60 * 1000)).toBe(true);

    // Audit log created for manual verification
    const audit = await prisma.auditLog.findMany({
      where: { action: 'domain.verification.manual', resourceId: domain.id }
    });
    expect(audit.length).toBe(1);

    // Notification created once (verified)
    const notifications = await prisma.notification.findMany({
      where: { userId: user.id, type: 'DOMAIN_VERIFIED' }
    });
    expect(notifications.length).toBe(1);
  });

  test('manual verification failure auto-disables after >10 failures and logs audit + notifications', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const { domain } = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: `manual-fail-${Date.now()}.example.com`,
      verified: false,
      verificationStatus: 'PENDING',
      verificationFailureCount: 10,
      active: true
    });

    dnsSpy.mockResolvedValue({
      verified: false,
      domain: domain.domain,
      expectedRecord: `verify-ec=${domain.domain}`,
      foundRecords: [],
      cached: false,
      timestamp: new Date().toISOString(),
      error: 'not found'
    } as any);

    const result = await domainService.verifyDomain(domain.id, user.id);
    expect(result.success).toBe(true);
    expect(result.domain.verified).toBe(false);
    expect(result.domain.verificationStatus).toBe('FAILED');

    const updated = await prisma.domain.findUnique({ where: { id: domain.id } });
    expect(updated?.verificationFailureCount).toBe(11);
    expect(updated?.active).toBe(false);
    expect(updated?.nextVerificationCheck).toBeNull();

    // Audit log exists
    const audit = await prisma.auditLog.findMany({
      where: { action: 'domain.verification.manual', resourceId: domain.id }
    });
    expect(audit.length).toBe(1);

    // Check notifications - both failure and auto-disable use DOMAIN_FAILED type
    const allNotes = await prisma.notification.findMany({
      where: { userId: user.id, type: 'DOMAIN_FAILED' }
    });
    // Should have 2 notifications: one for failure, one for auto-disable
    expect(allNotes.length).toBe(2);
  });

  test('auto verification schedules verified domains with null schedule and logs audit', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const { domain } = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: `auto-schedule-${Date.now()}.example.com`,
      verified: true,
      verificationStatus: 'VERIFIED',
      verificationFailureCount: 0,
      active: true,
      nextVerificationCheck: null
    });

    dnsSpy.mockResolvedValue({
      verified: true,
      domain: domain.domain,
      expectedRecord: `verify-ec=${domain.domain}`,
      foundRecords: [`verify-ec=${domain.domain}`],
      cached: false,
      timestamp: new Date().toISOString()
    } as any);

    const now = Date.now();
    await worker.runVerificationCycle();

    const updated = await prisma.domain.findUnique({ where: { id: domain.id } });
    expect(updated?.verified).toBe(true);
    expect(updated?.verificationStatus).toBe('VERIFIED');
    expect(updated?.nextVerificationCheck).not.toBeNull();
    expect(within(updated!.nextVerificationCheck!, now + 24 * 60 * 60 * 1000, 5 * 60 * 1000)).toBe(true);

    const audit = await prisma.auditLog.findMany({
      where: { action: 'domain.verification.auto', resourceId: domain.id }
    });
    expect(audit.length).toBe(1);
  });

  test('auto verification disables after repeated failures and writes audit + notifications', async () => {
    const user = await createTestUser();
    const webhook = await createTestWebhook(user.id);
    const { domain } = await createTestDomainWithCatchAll(user.id, webhook.id, {
      domain: `auto-disable-${Date.now()}.example.com`,
      verified: false,
      verificationStatus: 'PENDING',
      verificationFailureCount: 10,
      active: true,
      nextVerificationCheck: new Date(Date.now() - 60 * 1000) // due now
    });

    dnsSpy.mockResolvedValue({
      verified: false,
      domain: domain.domain,
      expectedRecord: `verify-ec=${domain.domain}`,
      foundRecords: [],
      cached: false,
      timestamp: new Date().toISOString(),
      error: 'not found'
    } as any);

    await worker.runVerificationCycle();

    const updated = await prisma.domain.findUnique({ where: { id: domain.id } });
    expect(updated?.verificationStatus).toBe('FAILED');
    expect(updated?.active).toBe(false);
    expect(updated?.nextVerificationCheck).toBeNull();

    const audit = await prisma.auditLog.findMany({
      where: { action: 'domain.verification.auto', resourceId: domain.id }
    });
    expect(audit.length).toBe(1);

    // Check notifications - both failure and auto-disable use DOMAIN_FAILED type
    const allNotes = await prisma.notification.findMany({ 
      where: { userId: user.id, type: 'DOMAIN_FAILED' } 
    });
    // Should have 2 notifications: one for failure, one for auto-disable
    expect(allNotes.length).toBe(2);
  });
});


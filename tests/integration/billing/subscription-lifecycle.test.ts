import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
} from '../../setup/test-db-setup';
import { SubscriptionLifecycleService } from '../../../src/backend/services/billing/subscription-lifecycle.service';
import { SubscriptionStatusService } from '../../../src/backend/services/billing/subscription-status.service';
import { OneOffSubscriptionService } from '../../../src/backend/services/billing/one-off-subscription.service';

describe('Subscription Lifecycle Service', () => {
  setupTestDatabase();

  let lifecycleService: SubscriptionLifecycleService;
  let statusService: SubscriptionStatusService;
  let oneOffService: OneOffSubscriptionService;

  beforeEach(() => {
    lifecycleService = new SubscriptionLifecycleService();
    statusService = new SubscriptionStatusService();
    oneOffService = new OneOffSubscriptionService();
  });

  afterEach(() => {
    lifecycleService.stop();
  });

  describe('Service Management', () => {
    test('should start and stop service correctly', () => {
      expect(() => {
        lifecycleService.start();
        lifecycleService.stop();
      }).not.toThrow();
    });

    test('should handle starting already running service', () => {
      lifecycleService.start();
      expect(() => {
        lifecycleService.start(); // Should warn but not throw
      }).not.toThrow();
      lifecycleService.stop();
    });
  });

  describe('Subscription End-of-Period Processing', () => {
    test('should downgrade cancelled subscriptions past their end date', async () => {
      // Create user with Pro plan
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro',
        currentMonthEmails: 15
      });

      // Create cancelled subscription that's past its end date
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1); // Yesterday

      const subscription = await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_test123',
          status: 'CANCELLED',
          planType: 'pro',
          amount: 995, // €9.95 in cents
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: pastDate,
          startDate: new Date('2024-12-01'),
          // status: 'CANCELLED' already set above
        }
      });

      // Run the lifecycle check
      await lifecycleService.processEndOfPeriodDowngrades();

      // Verify user was downgraded to free
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('free');

      // Verify subscription status reflects the downgrade
      const hasActiveSubscription = (await SubscriptionStatusService.getSubscriptionStatus(user.id)).hasActiveSubscription;
      expect(hasActiveSubscription).toBe(false);
    });

    test('should not downgrade cancelled subscriptions before their end date', async () => {
      // Create user with Pro plan
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro'
      });

      // Create cancelled subscription with future end date
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 5); // 5 days from now

      await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_test456',
          status: 'CANCELLED',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: futureDate,
          startDate: new Date(),
          // status: 'CANCELLED' already set above
        }
      });

      // Run the lifecycle check
      await lifecycleService.processEndOfPeriodDowngrades();

      // Verify user remains on Pro plan
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('pro');
    });

    test('should not affect active subscriptions', async () => {
      // Create user with active Pro subscription
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro'
      });

      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 1);

      await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_active123',
          status: 'ACTIVE',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: futureDate,
          startDate: new Date(),
          // status: 'ACTIVE' already set above
        }
      });

      // Run the lifecycle check
      await lifecycleService.processEndOfPeriodDowngrades();

      // Verify user remains on Pro plan
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('pro');

      // Verify subscription status
      const hasActiveSubscription = (await SubscriptionStatusService.getSubscriptionStatus(user.id)).hasActiveSubscription;
      expect(hasActiveSubscription).toBe(true);
    });
  });

  describe('Data Preservation During Downgrade', () => {
    test('should preserve user data within free plan limits during downgrade', async () => {
      // Create user with Pro plan and data that exceeds free limits
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro',
        currentMonthEmails: 250 // Exceeds free limit
      });

      // Create multiple domains (assuming free plan has limits)
      const domains = await Promise.all([
        prisma.domain.create({
          data: {
            domain: `test1-${Date.now()}.example.com`,
            userId: user.id,
            verified: true,
            verificationStatus: 'VERIFIED',
            active: true
          }
        }),
        prisma.domain.create({
          data: {
            domain: `test2-${Date.now()}.example.com`,
            userId: user.id,
            verified: true,
            verificationStatus: 'VERIFIED',
            active: true
          }
        }),
        prisma.domain.create({
          data: {
            domain: `test3-${Date.now()}.example.com`,
            userId: user.id,
            verified: true,
            verificationStatus: 'VERIFIED',
            active: true
          }
        })
      ]);

      // Create cancelled subscription past end date
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_downgrade123',
          status: 'CANCELLED',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: pastDate,
          startDate: new Date(),
          // status: 'CANCELLED' already set above
        }
      });

      // Run the lifecycle check
      await lifecycleService.processEndOfPeriodDowngrades();

      // Verify user was downgraded
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true, currentMonthEmails: true }
      });
      expect(updatedUser?.planType).toBe('free');

      // Verify domains are preserved but possibly limited
      const activeDomains = await prisma.domain.count({
        where: { 
          userId: user.id,
          active: true
        }
      });
      
      // Check that at least some domains are preserved (implementation specific)
      expect(activeDomains).toBeGreaterThan(0);
    });
  });

  describe('Virtual Subscription Handling', () => {
    test('should handle virtual subscriptions (admin-created) correctly', async () => {
      // Create user with virtual subscription (no mollieId)
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro'
      });

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: `virtual_${user.id}_${Date.now()}`, // Virtual subscription
          status: 'CANCELLED',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: pastDate,
          startDate: new Date(),
          // status: 'CANCELLED' already set above
        }
      });

      // Run the lifecycle check
      await lifecycleService.processEndOfPeriodDowngrades();

      // Verify virtual subscriptions are processed like regular ones
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('free');
    });
  });

  describe('One-Off Subscription Integration', () => {
    test('should integrate with one-off subscription service', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'free'
      });

      // First create a test payment
      const payment = await prisma.payment.create({
        data: {
          mollieId: 'tr_oneoff123',
          status: 'PAID',
          amount: '9.95',
          currency: 'EUR',
          description: 'Test payment',
          userId: user.id
        }
      });

      // Create a one-off subscription
      const result = await OneOffSubscriptionService.createVirtualSubscription({
        userId: user.id,
        planType: 'pro',
        amount: 995,
        currency: 'EUR',
        billingPeriod: 'monthly',
        paymentId: payment.id
      });

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
      expect(result.status).toBe('ACTIVE');

      // Verify user has effective pro plan through subscription
      const status = await SubscriptionStatusService.getSubscriptionStatus(user.id);
      expect(status.effectivePlan).toBe('pro');

      // Verify subscription status
      const hasActiveSubscription = (await SubscriptionStatusService.getSubscriptionStatus(user.id)).hasActiveSubscription;
      expect(hasActiveSubscription).toBe(true);
    });

    test('should handle one-off subscription creation failure', async () => {
      // Test with invalid data
      await expect(OneOffSubscriptionService.createVirtualSubscription({
        userId: 'invalid-user-id',
        planType: 'pro',
        amount: 995,
        currency: 'EUR',
        billingPeriod: 'monthly',
        paymentId: 'tr_invalid123'
      })).rejects.toThrow();
    });
  });

  describe('Subscription Status Integration', () => {
    test('should correctly determine subscription status after lifecycle changes', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro'
      });

      // Create active subscription
      const subscription = await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_status123',
          status: 'ACTIVE',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          startDate: new Date(),
          // status: 'ACTIVE' already set above
        }
      });

      // Test active subscription status
      let hasActive = (await SubscriptionStatusService.getSubscriptionStatus(user.id)).hasActiveSubscription;
      expect(hasActive).toBe(true);

      let status = await SubscriptionStatusService.getSubscriptionStatus(user.id);
      expect(status.hasActiveSubscription).toBe(true);
      expect(status.effectivePlan).toBe('pro');

      // Cancel the subscription
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { 
          status: 'CANCELLED',
          // status: 'CANCELLED' already set above,
          nextPaymentDate: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
        }
      });

      // Run lifecycle check to process the cancellation
      await lifecycleService.processEndOfPeriodDowngrades();

      // Test cancelled subscription status after processing
      hasActive = (await SubscriptionStatusService.getSubscriptionStatus(user.id)).hasActiveSubscription;
      expect(hasActive).toBe(false);

      // Verify user plan was downgraded
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('free');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      // Mock a database error by creating invalid state
      await expect(async () => {
        await lifecycleService.processEndOfPeriodDowngrades();
      }).not.toThrow();
    });

    test('should handle subscriptions with null nextPaymentDate', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        planType: 'pro'
      });

      await prisma.subscription.create({
        data: {
          userId: user.id,
          mollieId: 'sub_nulldate123',
          status: 'CANCELLED',
          planType: 'pro',
          amount: 995,
          currency: 'EUR',
          interval: 'MONTHLY',
          nextPaymentDate: null,
          startDate: new Date(),
          // status: 'CANCELLED' already set above
        }
      });

      // Should not throw error
      await expect(async () => {
        await lifecycleService.processEndOfPeriodDowngrades();
      }).not.toThrow();
    });

    test.skip('should handle multiple subscriptions for same user', async () => {
      const user = await createTestUser({
      email: '<EMAIL>',
      planType: 'pro'
      });

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      // Create multiple cancelled subscriptions
      await Promise.all([
      prisma.subscription.create({
        data: {
        userId: user.id,
        mollieId: 'sub_multi1',
        status: 'CANCELLED',
        planType: 'pro',
        amount: 995,
        currency: 'EUR',
        interval: 'MONTHLY',
        nextPaymentDate: pastDate,
        startDate: new Date(),
        // status: 'CANCELLED' already set above
        }
      }),
      prisma.subscription.create({
        data: {
        userId: user.id,
        mollieId: 'sub_multi2',
        status: 'CANCELLED',
        planType: 'pro',
        amount: 995,
        currency: 'EUR',
        interval: 'MONTHLY',
        nextPaymentDate: pastDate,
        startDate: new Date(),
        // status: 'CANCELLED' already set above
        }
      })
      ]);

      // Should handle multiple subscriptions gracefully
      await expect(async () => {
      await lifecycleService.processEndOfPeriodDowngrades();
      }).not.toThrow();

      // User should be downgraded
      const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { planType: true }
      });
      expect(updatedUser?.planType).toBe('free');
    });
  });
});
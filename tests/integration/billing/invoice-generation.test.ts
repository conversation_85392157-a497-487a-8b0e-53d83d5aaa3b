import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestPayment,
} from '../../setup/test-db-setup';
import { InvoiceGenerationService } from '../../../src/backend/services/billing/invoice-generation.service';
import fs from 'fs';
import path from 'path';

// Mock S3 service to avoid real S3 operations in tests
jest.mock('../../../src/backend/services/storage/s3-storage.service', () => ({
  S3StorageService: jest.fn().mockImplementation(() => ({
    client: {
      send: jest.fn().mockResolvedValue({ success: true })
    }
  }))
}));

describe('Invoice Generation Service', () => {
  setupTestDatabase();

  beforeEach(() => {
    // No instance needed - service uses static methods
  });

  afterEach(() => {
    // Clean up any generated test files
    const testDir = path.join(process.cwd(), 'temp_test_invoices');
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
  });

  describe('Invoice Number Generation', () => {
    test('should generate unique sequential invoice numbers', async () => {
      const currentYear = new Date().getFullYear();
      
      // Generate first invoice number (using private method via type assertion)
      const invoiceNumber1 = await (InvoiceGenerationService as any).generateInvoiceNumber();
      expect(invoiceNumber1).toMatch(new RegExp(`^INV-${currentYear}-\\d+$`));

      // Create an invoice in database with this number
      const user1 = await createTestUser({ email: '<EMAIL>' });
      const payment1 = await createTestPayment(user1.id, { status: 'PAID', paidAt: new Date() });
      await prisma.invoice.create({
        data: {
          invoiceNumber: invoiceNumber1,
          paymentId: payment1.id,
          userId: user1.id,
          amount: 9.95,
          currency: 'EUR',
          description: 'Test invoice',
          s3Key: 'test-key-1'
        }
      });

      // Generate second invoice number - should be incremented
      const invoiceNumber2 = await (InvoiceGenerationService as any).generateInvoiceNumber();
      expect(invoiceNumber2).toMatch(new RegExp(`^INV-${currentYear}-\\d+$`));
      expect(invoiceNumber2).not.toBe(invoiceNumber1);

      // Extract numbers and verify increment
      const num1 = parseInt(invoiceNumber1.split('-')[2]);
      const num2 = parseInt(invoiceNumber2.split('-')[2]);
      expect(num2).toBe(num1 + 1);
    });

    test('should start from base number when no existing invoices', async () => {
      // Ensure clean state
      await prisma.invoice.deleteMany();
      
      const invoiceNumber = await (InvoiceGenerationService as any).generateInvoiceNumber();
      const currentYear = new Date().getFullYear();
      
      expect(invoiceNumber).toBe(`INV-${currentYear}-2984`);
    });
  });

  describe('Invoice Creation', () => {
    test('should create invoice for paid payment', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Invoice Test User'
      });

      const payment = await createTestPayment(user.id, {
        status: 'PAID',
        paidAt: new Date(),
        description: 'EmailConnect Pro - Monthly Subscription'
      });

      const invoiceId = await InvoiceGenerationService.createInvoice(payment.id);
      
      expect(invoiceId).toBeDefined();
      expect(typeof invoiceId).toBe('string');

      // Verify invoice was created in database
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId }
      });

      expect(invoice).toBeDefined();
      expect(invoice!.paymentId).toBe(payment.id);
      expect(invoice!.userId).toBe(user.id);
      expect(invoice!.s3Key).toBeDefined();
    });

    test('should prevent duplicate invoice creation', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Duplicate Test User'  
      });

      const payment = await createTestPayment(user.id, {
        status: 'PAID',
        paidAt: new Date(),
        description: 'EmailConnect Pro - Monthly Subscription'
      });

      // Create first invoice
      const invoiceId1 = await InvoiceGenerationService.createInvoice(payment.id);
      expect(invoiceId1).toBeDefined();

      // Try to create second invoice for same payment - should return existing
      const invoiceId2 = await InvoiceGenerationService.createInvoice(payment.id);
      expect(invoiceId2).toBe(invoiceId1);

      // Verify only one invoice exists
      const invoices = await prisma.invoice.findMany({
        where: { paymentId: payment.id }
      });
      expect(invoices).toHaveLength(1);
    });

    test('should throw error for unpaid payment', async () => {
      const user = await createTestUser({
        email: '<EMAIL>'
      });

      const payment = await createTestPayment(user.id, {
        status: 'PENDING', // Not paid
        paidAt: null,
        description: 'Unpaid subscription'
      });

      await expect(InvoiceGenerationService.createInvoice(payment.id))
        .rejects
        .toThrow('is not marked as paid');
    });

    test('should throw error for non-existent payment', async () => {
      await expect(InvoiceGenerationService.createInvoice('non-existent-payment-id'))
        .rejects
        .toThrow('not found');
    });
  });

  describe('Invoice Retrieval', () => {
    test('should get invoice by ID and user', async () => {
      const user = await createTestUser({
        email: '<EMAIL>',
        name: 'Get Invoice User'
      });

      const payment = await createTestPayment(user.id, {
        status: 'PAID',
        paidAt: new Date(),
        description: 'Test invoice retrieval'
      });

      const invoiceId = await InvoiceGenerationService.createInvoice(payment.id);
      
      const retrievedInvoice = await InvoiceGenerationService.getInvoice(invoiceId, user.id);
      
      expect(retrievedInvoice).toBeDefined();
      expect(retrievedInvoice.id).toBe(invoiceId);
      expect(retrievedInvoice.userId).toBe(user.id);
    });

    test('should return null for invoice access by wrong user', async () => {
      const user1 = await createTestUser({ email: '<EMAIL>' });
      const user2 = await createTestUser({ email: '<EMAIL>' });

      const payment = await createTestPayment(user1.id, {
        status: 'PAID',
        paidAt: new Date()
      });

      const invoiceId = await InvoiceGenerationService.createInvoice(payment.id);
      
      const retrievedInvoice = await InvoiceGenerationService.getInvoice(invoiceId, user2.id);
      expect(retrievedInvoice).toBeNull();
    });

    test('should get user invoices', async () => {
      const user = await createTestUser({
        email: '<EMAIL>'
      });

      // Create two payments and invoices
      const payment1 = await createTestPayment(user.id, { status: 'PAID', paidAt: new Date() });
      const payment2 = await createTestPayment(user.id, { status: 'PAID', paidAt: new Date() });

      await InvoiceGenerationService.createInvoice(payment1.id);
      await InvoiceGenerationService.createInvoice(payment2.id);

      const userInvoices = await InvoiceGenerationService.getUserInvoices(user.id);
      
      expect(userInvoices).toHaveLength(2);
      expect(userInvoices.every(inv => inv.userId === user.id)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle database connection errors gracefully', async () => {
      // This test would be complex to implement properly as it requires 
      // mocking Prisma, but we can at least verify the service handles 
      // invalid inputs properly
      await expect(InvoiceGenerationService.createInvoice(''))
        .rejects
        .toThrow();
    });

    test('should handle invalid user ID in getUserInvoices', async () => {
      const invoices = await InvoiceGenerationService.getUserInvoices('non-existent-user');
      expect(invoices).toEqual([]);
    });
  });
});
/**
 * Integration test for WebhookTest user linking functionality
 * Tests the new permanent user creation flow
 */
import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { WebhookTestIntegrationService } from '../../src/backend/services/webhooktest-integration.service.js';

// Use environment variables from .env file (don't override)
// WEBHOOKTEST_API_URL should be set in .env (e.g., https://localhost:3002)

/**
 * Check if WebhookTest service is available
 */
async function isWebhookTestServiceAvailable(): Promise<boolean> {
  try {
    const response = await fetch(`${process.env.WEBHOOKTEST_API_URL}/health`, { 
      signal: AbortSignal.timeout(2000) // 2 second timeout
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

describe('WebhookTest User Linking Integration', () => {
  let serviceAvailable = false;

  beforeAll(async () => {
    serviceAvailable = await isWebhookTestServiceAvailable();
    if (!serviceAvailable) {
      console.log(`⏭️  WebhookTest service is not available at ${process.env.WEBHOOKTEST_API_URL} - skipping integration tests`);
    }
  });
  const testUser = {
    userId: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User'
  };

  describe('Permanent User Creation', () => {
    it('should create permanent user account instead of temporary', async () => {
      if (!serviceAvailable) {
        console.log('⏭️  Skipping test - WebhookTest service not available');
        return;
      }

      try {
        const result = await WebhookTestIntegrationService.createWebhookTestEndpoint(
          testUser.userId,
          testUser.email,
          testUser.name
        );

        // Verify the response structure
        expect(result.success).toBe(true);
        expect(result.webhookEndpoint).toBeDefined();
        expect(result.testId).toBeDefined();
        expect(result.verificationCode).toBeDefined();
        
        // Verify user linking information is included
        expect(result.user).toBeDefined();
        expect(result.user?.isLinked).toBe(true);
        expect(result.user?.isPermanent).toBe(true);
        expect(result.user?.planType).toBe('pro');
        expect(result.user?.email).toBe(testUser.email);
        
        // Verify endpoint information
        expect(result.endpoint).toBeDefined();
        expect(result.endpoint?.friendlyUrl).toBeDefined();
        expect(result.endpoint?.createdAt).toBeDefined();
        
        // Verify no expiration (permanent account)
        expect(result.expiresAt).toBeUndefined();

        console.log('✅ Permanent user creation test passed:', {
          webhookEndpoint: result.webhookEndpoint,
          userLinked: result.user?.isLinked,
          isPermanent: result.user?.isPermanent,
          planType: result.user?.planType
        });

      } catch (error) {
        if (error instanceof Error && error.message.includes('WebhookTest integration not configured')) {
          console.log('⏭️  Skipping test - WebhookTest integration not configured');
          return;
        }
        
        if (error instanceof Error && (
          error.message.includes('ECONNREFUSED') || 
          error.message.includes('fetch failed')
        )) {
          console.log('⏭️  Skipping test - WebhookTest service not running');
          return;
        }

        throw error;
      }
    });

    it('should handle user information retrieval', async () => {
      if (!serviceAvailable) {
        console.log('⏭️  Skipping test - WebhookTest service not available');
        return;
      }

      try {
        const userInfo = await WebhookTestIntegrationService.getUserInfo(
          testUser.userId,
          testUser.email
        );

        // Verify user info structure
        expect(userInfo.success).toBe(true);
        expect(userInfo.user).toBeDefined();
        expect(userInfo.stats).toBeDefined();
        
        // Verify user data
        expect(userInfo.user.email).toBe(testUser.email);
        expect(userInfo.user.planType).toBe('pro');
        
        // Verify stats structure
        expect(typeof userInfo.stats.totalEndpoints).toBe('number');
        expect(typeof userInfo.stats.activeEndpoints).toBe('number');
        expect(typeof userInfo.stats.totalRequests).toBe('number');

        console.log('✅ User info retrieval test passed:', {
          userEmail: userInfo.user.email,
          planType: userInfo.user.planType,
          totalEndpoints: userInfo.stats.totalEndpoints,
          totalRequests: userInfo.stats.totalRequests
        });

      } catch (error) {
        if (error instanceof Error && error.message.includes('WebhookTest integration not configured')) {
          console.log('⏭️  Skipping test - WebhookTest integration not configured');
          return;
        }
        
        if (error instanceof Error && (
          error.message.includes('ECONNREFUSED') || 
          error.message.includes('fetch failed')
        )) {
          console.log('⏭️  Skipping test - WebhookTest service not running');
          return;
        }

        throw error;
      }
    });
  });


  describe('Configuration Validation', () => {
    it('should validate service configuration', () => {
      if (!serviceAvailable) {
        console.log('⏭️  Skipping configuration validation - WebhookTest service not available');
        return;
      }

      // Test that environment variables are properly structured
      expect(process.env.WEBHOOKTEST_API_URL).toBeDefined();
      expect(process.env.WEBHOOKTEST_JWT_SECRET).toBeDefined();
      
      // Test URL format
      const url = process.env.WEBHOOKTEST_API_URL;
      if (url) {
        expect(url.startsWith('http')).toBe(true);
        expect(() => new URL(url)).not.toThrow();
      }

      console.log('✅ Configuration validation passed:', {
        webhookTestUrl: process.env.WEBHOOKTEST_API_URL,
        secretConfigured: !!process.env.WEBHOOKTEST_JWT_SECRET
      });
    });
  });
});
// Jest global setup for all tests
import { jest } from '@jest/globals';

// Set test environment variables
process.env.NODE_ENV = 'test';

// Use separate test database (PostgreSQL with _test suffix)
// This will be overridden by the test:safe script if needed
if (!process.env.DATABASE_URL?.includes('_test')) {
  process.env.DATABASE_URL = process.env.DATABASE_URL?.replace(/\/([^\/]+)$/, '/$1_test') || 'postgresql://postgres:password@localhost:5432/eu_email_webhook_test';
}

// Use separate Redis database for tests
// Check if REDIS_URL is already set by test command (package.json), otherwise use test Redis
if (!process.env.REDIS_URL?.includes('6380')) {
  process.env.REDIS_URL = process.env.TEST_REDIS_URL || 'redis://localhost:6380';
}

// Set other required environment variables for tests
process.env.USER_JWT_SECRET = process.env.USER_JWT_SECRET || 'test-user-jwt-secret';

// Increase timeout for integration tests
jest.setTimeout(15000);

// Global test utilities
global.testUtils = {
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  generateTestEmail: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`,
  generateTestDomain: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.example.com`
};

// Ensure background resources are shut down after all tests

// Declare global types for TypeScript
declare global {
  // eslint-disable-next-line no-var
  var testUtils: {
    delay: (ms: number) => Promise<void>;
    generateTestEmail: () => string;
    generateTestDomain: () => string;
  };
}

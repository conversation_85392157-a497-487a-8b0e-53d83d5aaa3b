// Jest global setup: connect DB and initialize queues once per run
// Use dynamic import so ts-jest transpiles ESM properly

export default async function globalSetup() {
  process.env.NODE_ENV = 'test';
  const { connectDatabase } = await import('../../src/backend/lib/prisma.js');
  const { initializeQueue } = await import('../../src/backend/services/queue.js');
  await connectDatabase();
  await initializeQueue();
}


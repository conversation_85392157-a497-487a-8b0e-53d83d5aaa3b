import { PrismaClient } from '@prisma/client';
import { beforeAll, afterAll, beforeEach } from '@jest/globals';
import { FastifyInstance } from 'fastify';
import Fastify from 'fastify';
import cookie from '@fastify/cookie';
import formbody from '@fastify/formbody';
import { env } from '../../src/backend/config/env.js';
import { registerCommonSchemas } from '../../src/backend/schemas/common.js';
import { allSchemas } from '../../src/backend/schemas/openapi-schemas.js';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});


export { prisma };

// Test database setup and teardown
export const setupTestDatabase = () => {
  beforeAll(async () => {
    // Ensure Prisma client is connected to the test database
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean database before each test
    // Order matters due to foreign key constraints
    await prisma.alias.deleteMany();
    await prisma.email.deleteMany();
    await prisma.invoice.deleteMany();
    await prisma.payment.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.paymentMethod.deleteMany();
    await prisma.creditTransaction.deleteMany();
    await prisma.creditBatch.deleteMany();
    await prisma.notification.deleteMany();
    await prisma.apiKey.deleteMany();
    await prisma.domain.deleteMany();
    await prisma.webhook.deleteMany();
    await prisma.user.deleteMany();
    await prisma.auditLog.deleteMany();
  });
};

// Test data factories
export const createTestUser = async (data = {}) => {
  const bcrypt = await import('bcrypt');
  const defaultData = {
    email: `test-${Date.now()}@example.com`,
    password: await bcrypt.hash('TestPassword123!', 10),
    name: 'Test User',
    emailVerified: true,
    ...data,
  };
  
  return prisma.user.create({ data: defaultData });
};

export const createTestWebhook = async (userId: string, data = {}) => {
  const defaultData = {
    name: `Test Webhook ${Date.now()}`,
    url: `https://example-${Date.now()}.com/webhook`,
    description: 'Test webhook for integration tests',
    verified: true,
    user: {
      connect: { id: userId }
    },
    ...data,
  };
  
  return prisma.webhook.create({ data: defaultData });
};

export const createTestDomain = async (userId: string, data = {}) => {
  const defaultData = {
    domain: `test-${Date.now()}.example.com`,
    verified: true,
    verificationStatus: 'VERIFIED' as const,
    active: true,
    user: {
      connect: { id: userId }
    },
    ...data,
  };

  return prisma.domain.create({ data: defaultData });
};

export const createTestAlias = async (domainId: string, webhookId: string, data = {}) => {
  const timestamp = Date.now();
  const defaultData = {
    email: `alias-${timestamp}@test-${timestamp}.example.com`,
    active: true,
    domain: {
      connect: { id: domainId }
    },
    webhook: {
      connect: { id: webhookId }
    },
    ...data,
  };

  return prisma.alias.create({ data: defaultData });
};

/**
 * Create a domain with a catch-all alias (new architecture)
 */
export const createTestDomainWithCatchAll = async (userId: string, webhookId: string, data = {}) => {
  const domain = await createTestDomain(userId, data);

  // Create catch-all alias for the domain
  const catchAllAlias = await createTestAlias(domain.id, webhookId, {
    email: `*@${domain.domain}`,
    active: true
  });

  return { domain, catchAllAlias };
};

/**
 * Legacy compatibility function for tests that haven't been updated yet
 * This allows old tests to continue working while we update them
 */
export const createTestDomainLegacy = async (userId: string, webhookId: string, data = {}) => {
  return createTestDomainWithCatchAll(userId, webhookId, data);
};

/**
 * Create test subscription with proper defaults
 */
export const createTestSubscription = async (userId: string, data = {}) => {
  const defaultData = {
    status: 'ACTIVE' as const,
    planType: 'pro',
    interval: 'monthly',
    amount: '9.99',
    currency: 'EUR',
    description: 'Pro Plan Subscription',
    mollieId: `sub_test_${Date.now()}`,
    mollieCustomerId: `cst_test_${Date.now()}`,
    startDate: new Date(),
    nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    userId,
    ...data,
  };
  
  return prisma.subscription.create({ data: defaultData });
};

/**
 * Create test payment with proper defaults
 */
export const createTestPayment = async (userId: string, data = {}) => {
  const defaultData = {
    mollieId: `tr_test_${Date.now()}`,
    status: 'PAID' as const,
    amount: '9.99',
    currency: 'EUR',
    description: 'Test payment',
    method: 'creditcard',
    paidAt: new Date(),
    mollieCustomerId: `cst_test_${Date.now()}`,
    userId,
    ...data,
  };
  
  return prisma.payment.create({ data: defaultData });
};

/**
 * Create test invoice with proper defaults
 */
export const createTestInvoice = async (userId: string, paymentId: string, data = {}) => {
  const defaultData = {
    invoiceNumber: `INV-${Date.now()}`,
    amount: '9.99',
    currency: 'EUR',
    description: 'Test invoice',
    billingPeriod: '2025-01',
    s3Key: `invoices/test_${Date.now()}.pdf`,
    userId,
    paymentId,
    ...data,
  };
  
  return prisma.invoice.create({ data: defaultData });
};

/**
 * Create test credit batch with proper defaults
 */
export const createTestCreditBatch = async (userId: string, data = {}) => {
  const defaultData = {
    amount: 1000,
    remainingAmount: 1000,
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    isExpired: false,
    userId,
    ...data,
  };
  
  return prisma.creditBatch.create({ data: defaultData });
};

/**
 * Create a properly configured Fastify instance for testing
 */
export async function createTestFastifyApp(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: false, // Disable logging in tests
    ajv: {
      customOptions: {
        strict: false, // Allow unknown keywords like "example"
        removeAdditional: false,
        useDefaults: true,
        coerceTypes: true
      }
    }
  });

// Add all schemas from OpenAPI spec to Fastify for $ref resolution
  // Use precompiled allSchemas instead of importing openapi-spec (build artifact)
  for (const [id, schema] of Object.entries(allSchemas)) {
    // Remove example keywords that cause issues in tests
    const cleanSchema = JSON.parse(JSON.stringify(schema));
    removeExampleKeywords(cleanSchema);
    app.addSchema({ ...cleanSchema, $id: id });
  }

  // Register common schemas
  await registerCommonSchemas(app);

  // Register necessary plugins
  await app.register(formbody);
  await app.register(cookie, {
    secret: env.BETTER_AUTH_SECRET, // Use BetterAuth secret for cookie signing
    parseOptions: {}
  });

  // Initialize background queues to mirror production behavior
  // Use reinitializeQueue to ensure test Redis URL is used
  try {
    const { reinitializeQueue } = await import('../../src/backend/services/queue.js');
    await reinitializeQueue();
  } catch (e) {
    // If Redis is unavailable in a local test run, surface a clear error
    console.error('Failed to initialize queues for tests. Ensure REDIS_URL is set and Redis is running.', e);
    throw e;
  }

  return app;
}

/**
 * Recursively remove example keywords from schema objects
 */
function removeExampleKeywords(obj: any): void {
  if (typeof obj !== 'object' || obj === null) return;

  if (Array.isArray(obj)) {
    obj.forEach(removeExampleKeywords);
    return;
  }

  // Remove example keyword
  if ('example' in obj) {
    delete obj.example;
  }

  // Recursively process nested objects
  Object.values(obj).forEach(removeExampleKeywords);
}

// Cleanup utilities
export const cleanupTestData = async (patterns = []) => {
  // Default cleanup patterns for test data
  const defaultPatterns = [
    '<EMAIL>',
    'webhook-test%',
    'test-%.example.com',
  ];
  
  const allPatterns = [...defaultPatterns, ...patterns];
  
  // Clean up test users and related data
  for (const pattern of allPatterns) {
    if (pattern.includes('@')) {
      // Email pattern - clean users
      await prisma.user.deleteMany({
        where: {
          email: { contains: pattern.replace('%', '') }
        }
      });
    } else if (pattern.includes('.')) {
      // Domain pattern
      await prisma.domain.deleteMany({
        where: {
          domain: { contains: pattern.replace('%', '') }
        }
      });
    }
  }
};

// Better Auth test helpers
export const authenticateTestUser = async (app: FastifyInstance, email: string, password: string) => {
  const response = await app.inject({
    method: 'POST',
    url: '/api/auth/sign-in/email',
    payload: { email, password }
  });

  if (response.statusCode !== 200) {
    throw new Error(`Failed to authenticate user: ${response.body}`);
  }

  // Extract session cookie from response
  const sessionCookie = response.cookies.find(c => 
    c.name === 'ec.session_token' ||
    c.name === 'better-auth.session_token' || 
    c.name === 'session' ||
    c.name.includes('auth')
  );

  return {
    response,
    sessionCookie: sessionCookie?.value,
    cookies: response.cookies
  };
};

export const createAuthenticatedTestUser = async (app: FastifyInstance, userData = {}) => {
  const user = await createTestUser(userData);
  const password = 'TestPassword123!';
  
  // Update user with known password for testing
  const bcrypt = await import('bcrypt');
  await prisma.user.update({
    where: { id: user.id },
    data: { password: await bcrypt.hash(password, 10) }
  });

  const auth = await authenticateTestUser(app, user.email, password);
  
  return {
    user,
    auth,
    password
  };
};

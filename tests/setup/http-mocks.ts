// Global axios mock to prevent real outbound network calls in tests
// Allows localhost/127.0.0.1 calls (e.g., tests that spin up a local server)
// Provides override hooks per-test via globalThis.__axiosHandler

import type { AxiosRequestConfig, AxiosResponse } from 'axios';

// Extend the NodeJS global type for TypeScript
declare global {
  // eslint-disable-next-line no-var
  var __axiosHandler: ((method: string, url: string, data?: any, config?: any) => Promise<AxiosResponse<any>>) | undefined;
}

// Install the jest mock
jest.mock('axios', () => {
  // Use requireActual to delegate localhost traffic to real axios
  // We import lazily inside handlers to avoid ESM/CJS interop headaches
  const allowRealForLocalhost = async (
    method: string,
    url: string,
    data?: any,
    config?: any
  ): Promise<any> => {
    try {
      const parsed = new URL(url);
      if (parsed.hostname === 'localhost' || parsed.hostname === '127.0.0.1') {
        const realAxiosModule: any = jest.requireActual('axios');
        const realAxios: any = realAxiosModule.default ?? realAxiosModule;
        const m = method.toLowerCase();
        if (m === 'get') return realAxios.get(url, config);
        if (m === 'post') return realAxios.post(url, data, config);
        if (m === 'put') return realAxios.put(url, data, config);
        if (m === 'patch') return realAxios.patch(url, data, config);
        if (m === 'delete') return realAxios.delete(url, config);
        return realAxios.request({ url, method, data, ...(config || {}) });
      }
    } catch {
      // If URL parsing fails, fall back to mock
    }
    return undefined;
  };

  const makeOk = (status = 200, data: any = {}): AxiosResponse => ({
    status,
    statusText: String(status),
    headers: {},
    // Minimal config shape to satisfy axios types
    // Cast as any to avoid tying to internal axios types
    config: { headers: {} } as any,
    data,
  });

  const handler = async (method: string, url: string, data?: any, config?: any) => {
    // Per-test override via global handler
    if (global.__axiosHandler) {
      return global.__axiosHandler(method, url, data, config);
    }

    // Allow localhost to hit real servers spun up by tests
    const maybeReal = await allowRealForLocalhost(method, url, data, config);
    if (maybeReal) return maybeReal;

    // Default mock: 200 OK with empty body
    return makeOk(200, {});
  };

  // The mock exports axios-like API used in the codebase (mainly post)
  const axiosMock: any = (config: AxiosRequestConfig) => handler(config.method || 'get', config.url!, config.data, config);
  axiosMock.post = (url: string, data?: any, config?: any) => handler('post', url, data, config);
  axiosMock.get = (url: string, config?: any) => handler('get', url, undefined, config);
  axiosMock.put = (url: string, data?: any, config?: any) => handler('put', url, data, config);
  axiosMock.patch = (url: string, data?: any, config?: any) => handler('patch', url, data, config);
  axiosMock.delete = (url: string, config?: any) => handler('delete', url, undefined, config);
  axiosMock.request = (config: AxiosRequestConfig) => handler(config.method || 'get', config.url!, config.data, config);
  axiosMock.create = () => axiosMock;
  axiosMock.isAxiosError = (err: any) => !!(err && err.isAxiosError);
  axiosMock.defaults = {};

  return axiosMock;
});

export {};

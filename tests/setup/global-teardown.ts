// Jest global teardown to ensure background queues and DB connections are closed
export default async function globalTeardown() {
  try {
    const { shutdownQueues } = await import('../../src/backend/services/queue.js');
    await shutdownQueues();
  } catch (e) {
    // ignore teardown errors to avoid masking test results
  }
  try {
    const { disconnectDatabase } = await import('../../src/backend/lib/prisma.js');
    await disconnectDatabase();
  } catch (e) {
    // ignore
  }
}


#!/usr/bin/env node

/**
 * Test script for Alias API endpoints
 * Tests CRUD operations for aliases
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// Test configuration
const testConfig = {
  // These would need to be real values in a full test
  userEmail: '<EMAIL>',
  userPassword: 'testpassword123',
  testDomain: 'test-domain.com',
  testWebhookUrl: 'https://webhook.site/test-alias-webhook'
};

class AliasAPITester {
  constructor() {
    this.authToken = null;
    this.testDomainId = null;
    this.testWebhookId = null;
    this.testAliasId = null;
  }

  async login() {
    console.log('🔐 Logging in (BetterAuth)...');
    try {
      const response = await axios.post(`${BASE_URL}/api/auth/sign-in/email`, {
        email: testConfig.userEmail,
        password: testConfig.userPassword
      }, {
        // BetterAuth uses httpOnly cookies; allow axios to read set-cookie header
        validateStatus: () => true
      });

      if (response.status >= 400) {
        console.log('❌ Login failed:', response.data?.message || response.statusText);
        return false;
      }
      
      // Collect BetterAuth cookies for subsequent requests
      const setCookie = response.headers['set-cookie'];
      if (Array.isArray(setCookie) && setCookie.length > 0) {
        // Keep only session-related cookies (e.g., better-auth.session or __Secure-better-auth.session)
        const sessionCookies = setCookie
          .filter(c => c.includes('better-auth') || c.includes('ec.session'))
          .map(c => c.split(';')[0]);
        
        if (sessionCookies.length > 0) {
          this.sessionCookieHeader = sessionCookies.join('; ');
          console.log('✅ Login successful');
          return true;
        }
      }
      
      console.log('❌ Login failed - no session cookie received');
      return false;
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  getAuthHeaders() {
    return {
      'Cookie': this.sessionCookieHeader || '',
      'Content-Type': 'application/json'
    };
  }

  async setupTestData() {
    console.log('\n📋 Setting up test data...');
    
    try {
      // Get user's domains to find a verified one
      const domainsResponse = await axios.get(`${API_BASE}/domains`, {
        headers: this.getAuthHeaders()
      });
      
      const verifiedDomain = domainsResponse.data.domains?.find(d => d.verified);
      if (!verifiedDomain) {
        console.log('❌ No verified domains found. Please verify a domain first.');
        return false;
      }
      
      this.testDomainId = verifiedDomain.id;
      console.log(`✅ Using verified domain: ${verifiedDomain.domain} (${this.testDomainId})`);
      
      // Get user's webhooks
      const webhooksResponse = await axios.get(`${API_BASE}/webhooks`, {
        headers: this.getAuthHeaders()
      });
      
      if (!webhooksResponse.data.webhooks?.length) {
        console.log('❌ No webhooks found. Please create a webhook first.');
        return false;
      }
      
      this.testWebhookId = webhooksResponse.data.webhooks[0].id;
      console.log(`✅ Using webhook: ${webhooksResponse.data.webhooks[0].name} (${this.testWebhookId})`);
      
      return true;
    } catch (error) {
      console.log('❌ Failed to setup test data:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testGetAliases() {
    console.log('\n📋 Testing GET /api/aliases...');
    try {
      const response = await axios.get(`${API_BASE}/aliases`, {
        headers: this.getAuthHeaders()
      });
      
      console.log(`✅ GET /api/aliases successful`);
      console.log(`   Found ${response.data.total} aliases`);
      
      if (response.data.aliases.length > 0) {
        const alias = response.data.aliases[0];
        console.log(`   Sample alias: ${alias.email} -> ${alias.webhook.name}`);
      }
      
      return true;
    } catch (error) {
      console.log('❌ GET /api/aliases failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testCreateAlias() {
    console.log('\n➕ Testing POST /api/aliases...');
    try {
      // Get domain info to construct email
      const domainsResponse = await axios.get(`${API_BASE}/domains`, {
        headers: this.getAuthHeaders()
      });
      
      const domain = domainsResponse.data.domains.find(d => d.id === this.testDomainId);
      const testEmail = `test-alias-${Date.now()}@${domain.domain}`;
      
      const response = await axios.post(`${API_BASE}/aliases`, {
        email: testEmail,
        domainId: this.testDomainId,
        webhookId: this.testWebhookId,
        active: true
      }, {
        headers: this.getAuthHeaders()
      });
      
      this.testAliasId = response.data.alias.id;
      console.log(`✅ POST /api/aliases successful`);
      console.log(`   Created alias: ${response.data.alias.email} (${this.testAliasId})`);
      
      return true;
    } catch (error) {
      console.log('❌ POST /api/aliases failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testGetSpecificAlias() {
    if (!this.testAliasId) {
      console.log('⏭️  Skipping GET specific alias test - no alias created');
      return true;
    }
    
    console.log('\n🔍 Testing GET /api/aliases/:id...');
    try {
      const response = await axios.get(`${API_BASE}/aliases/${this.testAliasId}`, {
        headers: this.getAuthHeaders()
      });
      
      console.log(`✅ GET /api/aliases/${this.testAliasId} successful`);
      console.log(`   Alias: ${response.data.email}`);
      console.log(`   Domain: ${response.data.domain.domain}`);
      console.log(`   Webhook: ${response.data.webhook.name}`);
      
      return true;
    } catch (error) {
      console.log('❌ GET specific alias failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testUpdateAlias() {
    if (!this.testAliasId) {
      console.log('⏭️  Skipping UPDATE alias test - no alias created');
      return true;
    }
    
    console.log('\n✏️  Testing PUT /api/aliases/:id...');
    try {
      const response = await axios.put(`${API_BASE}/aliases/${this.testAliasId}`, {
        active: false
      }, {
        headers: this.getAuthHeaders()
      });
      
      console.log(`✅ PUT /api/aliases/${this.testAliasId} successful`);
      console.log(`   Updated alias active status: ${response.data.alias.active}`);
      
      return true;
    } catch (error) {
      console.log('❌ PUT alias failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testDeleteAlias() {
    if (!this.testAliasId) {
      console.log('⏭️  Skipping DELETE alias test - no alias created');
      return true;
    }
    
    console.log('\n🗑️  Testing DELETE /api/aliases/:id...');
    try {
      const response = await axios.delete(`${API_BASE}/aliases/${this.testAliasId}`, {
        headers: this.getAuthHeaders()
      });
      
      console.log(`✅ DELETE /api/aliases/${this.testAliasId} successful`);
      console.log(`   ${response.data.message}`);
      
      return true;
    } catch (error) {
      console.log('❌ DELETE alias failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async runTests() {
    console.log('🧪 Starting Alias API Tests...\n');
    
    const results = {
      login: false,
      setup: false,
      getAliases: false,
      createAlias: false,
      getSpecificAlias: false,
      updateAlias: false,
      deleteAlias: false
    };
    
    // Run tests in sequence
    results.login = await this.login();
    if (!results.login) return results;
    
    results.setup = await this.setupTestData();
    if (!results.setup) return results;
    
    results.getAliases = await this.testGetAliases();
    results.createAlias = await this.testCreateAlias();
    results.getSpecificAlias = await this.testGetSpecificAlias();
    results.updateAlias = await this.testUpdateAlias();
    results.deleteAlias = await this.testDeleteAlias();
    
    return results;
  }

  printResults(results) {
    console.log('\n📊 Test Results:');
    console.log('================');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test}`);
    });
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    
    console.log(`\n🎯 Summary: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All alias API tests passed!');
    } else {
      console.log('⚠️  Some tests failed. Check the output above for details.');
    }
  }
}

// Run the tests
const tester = new AliasAPITester();
tester.runTests().then(results => {
  tester.printResults(results);
}).catch(error => {
  console.error('💥 Test runner failed:', error.message);
  process.exit(1);
});

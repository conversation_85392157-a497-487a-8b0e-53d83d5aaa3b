# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Added
- Advanced email processing path with Postfix one-line routing to advanced-process-email
- Redis-based metadata handoff (advmeta:<messageId>) consumed by backend
- S3 attachment storage flow: sync for small files, async queue for large files
- Tracked download endpoint with presigned redirects
- S3 folder sanitizer (sanitizeS3Folder) and tests
- Admin impersonation: environment-driven enable/allow list with clear 401/403 semantics
- Extensive unit/integration tests for queue retry logic, webhook journey, impersonation, and sanitizer

### Changed
- Email route: computes folder per alias/user and passes to sync/async S3 uploads
- Queue worker: forwards folder through job data; improved logs
- Auth registration flow: respects test environment to avoid flaky side-effects
- Admin status route: returns 401 when feature disabled; 403 when enabled but user not allowed

### Fixed
- Flaky tests due to environment mutations mid-run (AdminPermissions now reads process.env at call time)
- Integration test interference from fallback webhook creation path during auth register

### Ops/Docs
- Deployment plan documented externally by maintainer (not in repo)
- Recommend updating docs: S3 rules, advanced processing behavior, admin impersonation env vars



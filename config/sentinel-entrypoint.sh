#!/bin/sh
# Wait for redis-master to be available
echo "Waiting for redis-master to be available..."

while ! nc -z redis-master 6379; do
  echo "Redis master not available yet, waiting..."
  sleep 2
done

# Get the IP address of redis-master
REDIS_MASTER_IP=$(getent hosts redis-master | awk '{ print $1 }')
echo "Redis master is available at IP: $REDIS_MASTER_IP"

# Create a temporary sentinel config with the resolved IP
cp /etc/redis/sentinel.conf /tmp/sentinel.conf
sed -i "s/redis-master/$REDIS_MASTER_IP/g" /tmp/sentinel.conf

echo "Starting sentinel with resolved IP configuration..."
exec redis-sentinel /tmp/sentinel.conf
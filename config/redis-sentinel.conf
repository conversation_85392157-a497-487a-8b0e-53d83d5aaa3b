# Redis Sentinel Configuration for EmailConnect Queue System
# Provides automatic failover for Redis master-replica setup

# Sentinel port
port 26379

# Monitor the master Redis instance
# sentinel monitor <master-name> <ip> <port> <quorum>
# quorum = minimum number of sentinels that need to agree for failover
# Monitor the master Redis instance
# Using container service name - works in both dev and production
sentinel monitor emailconnect-master redis-master 6379 1

# Master password
sentinel auth-pass emailconnect-master your_strong_redis_password_here

# How long (in milliseconds) the master should be unreachable for sentinel to start failover
sentinel down-after-milliseconds emailconnect-master 5000

# How long to wait for replica to sync with new master during failover
sentinel failover-timeout emailconnect-master 10000

# How many replicas can be reconfiguring simultaneously during failover
sentinel parallel-syncs emailconnect-master 1

# Logging
logfile /var/log/redis-sentinel.log
loglevel notice

# Security - disable dangerous commands
sentinel deny-scripts-reconfig yes

# Notification scripts (optional - can be used for alerting)
# sentinel notification-script emailconnect-master /path/to/notify.sh
# sentinel client-reconfig-script emailconnect-master /path/to/reconfig.sh

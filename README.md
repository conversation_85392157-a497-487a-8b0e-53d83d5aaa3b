# EU Email Webhook Service

A GDPR-compliant email-to-webhook service that processes incoming emails and delivers them as JSON payloads to configured HTTP endpoints. Built for EU data sovereignty requirements.

## Features

- 🇪🇺 **EU-compliant**: No US cloud services, full GDPR compliance
- 📧 **Multi-tenant**: Support multiple domains and custom webhook endpoints
- 🔄 **Reliable delivery**: Queue-based webhook delivery with retry logic
- 🛡️ **Security focused**: No email content stored, automatic data expiration
- 📊 **Monitoring**: Built-in delivery tracking and failure handling
- 🐳 **Docker ready**: Easy deployment with Docker Compose

## Quick Start

### Prerequisites

- Node.js 20+
- Docker & Docker Compose
- A server with port 25 open for email reception

### Local Development

1. **Clone and setup:**
```bash
cd /home/<USER>/webapps/emailconnect-app
npm install
cp .env.example .env
```

2. **Start services:**
```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Run database migrations
npx prisma migrate dev

# Start the development server
npm run dev
```

3. **Test the API:**
```bash
# Health check
curl http://localhost:3000/health

# Test webhook delivery
curl -X POST http://localhost:3000/api/webhook/test \
  -H "Content-Type: application/json" \
  -d '{"webhookUrl": "https://webhook.site/your-unique-id"}'
```

## Deployment

### Deployment Scripts Overview

The `deploy/` directory contains scripts to aid in deploying and maintaining the EU Email Webhook service:

- **[`deploy/initialize.sh`](deploy/initialize.sh:1)**: Prepares and validates the host system for Postfix compatibility *before* application deployment.
- **[`deploy/validate-deployment.sh`](deploy/validate-deployment.sh:1)**: Performs post-deployment checks to ensure the service is operating correctly.
- **[`deploy/backup_postfix.sh`](deploy/backup_postfix.sh:1)**: Backs up critical Postfix configuration files managed by the `postfix-manager` service.

### Production Deployment Steps

1.  **Host Server Preparation:**
    *   Deploy to an EU-based server (e.g., Hetzner, OVH).
    *   Ensure port 25 is open for SMTP traffic.
    *   Set up necessary DNS records (MX for mail routing, and TXT for domain validation if used).
    *   **Crucial:** Before deploying the application containers, run the [`deploy/initialize.sh`](deploy/initialize.sh:1) script on the host system. This script prepares and validates the host environment, ensuring it's ready for Postfix and the `postfix-manager` service. It checks for necessary dependencies, configurations, and permissions.

2.  **Application Deployment (Docker):**
    ```bash
    # Build and start all services using the production compose file
    docker-compose -f docker-compose.prod.yml up -d --build

    # Run database migrations
    docker-compose -f docker-compose.prod.yml exec app npx prisma migrate deploy
    ```
    *   After deployment, consider running [`deploy/validate-deployment.sh`](deploy/validate-deployment.sh:1) to perform basic checks.

## API Endpoints

### Email Processing
- `POST /api/email/process` - Process incoming email (called by mail server)
- `GET /api/email/status/:messageId` - Get delivery status

### Webhook Management
- `GET /api/webhook/stats` - Get delivery statistics
- `GET /api/webhook/failed` - List failed deliveries
- `POST /api/webhook/retry/:jobId` - Retry failed delivery
- `POST /api/webhook/test` - Send test webhook

### Domain Configuration
- `GET /api/domains` - List configured domains
- `POST /api/domains` - Add new domain
- `GET /api/domains/:domain` - Get domain config
- `PUT /api/domains/:domain` - Update domain config
- `DELETE /api/domains/:domain` - Remove domain
- `POST /api/domains/:domain/verify` - Verify domain ownership

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment | `development` |
| `PORT` | HTTP port | `3000` |
| `DATABASE_URL` | PostgreSQL connection | Required |
| `REDIS_URL` | Redis connection | Required |
| `WEBHOOK_TIMEOUT_MS` | Webhook timeout | `30000` |
| `WEBHOOK_RETRY_ATTEMPTS` | Max retry attempts | `3` |
| `EMAIL_RETENTION_DAYS` | Email data retention | `30` |

### Domain Setup

1. **Add domain configuration:**
```bash
curl -X POST http://localhost:3000/api/domains \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "yourdomain.com",
    "webhookUrl": "https://your-app.com/webhook",
    "active": true
  }'
```

2. **Configure DNS records:**
```
MX Record:
Type: MX
Name: @
Value: mail.your-service.com
Priority: 10

TXT Record (optional, for validation):
Type: TXT  
Name: @
Value: forward-email=yourdomain.com:https://your-app.com/webhook
```

## Email Processing Flow

1. **Email arrives** → Your mail server (Postfix/Postal)
2. **Email parsed** → Extracted into structured JSON
3. **Webhook queued** → Added to Redis queue for delivery
4. **Webhook delivered** → HTTP POST to configured endpoint
5. **Status tracked** → Success/failure logged with retry logic

## GDPR Compliance

- **Data minimization**: Only essential email metadata stored
- **Automatic expiration**: Emails auto-deleted after retention period
- **EU hosting**: Deploy only on EU servers
- **Audit logging**: Track all data processing activities
- **Right to erasure**: API endpoints for data deletion

## Development

### Project Structure
```
src/
├── config/         # Environment configuration
├── routes/         # API route handlers
├── services/       # Business logic (email parsing, queue)
├── types/          # TypeScript type definitions
├── utils/          # Utility functions
└── index.ts        # Application entry point
```

### Key Technologies
- **Fastify** - High-performance web framework
- **Bull** - Redis-based job queue
- **Prisma** - Type-safe database ORM
- **Mailparser** - Email parsing library
- **Zod** - Runtime type validation

### TODO Items
- [ ] Implement database operations in route handlers
- [ ] Add authentication/authorization
- [ ] DNS TXT record verification
- [ ] Email attachment handling
- [ ] Rate limiting and abuse prevention
- [ ] Admin dashboard UI
- [ ] Prometheus metrics export

## Backup and Restore

The [`deploy/backup_postfix.sh`](deploy/backup_postfix.sh:1) script is provided to back up critical Postfix configuration files (e.g., those located in `/etc/postfix/`) that are managed by the `postfix-manager` service.

This script creates timestamped archives of the configuration and supports rotation to manage disk space. It should be run regularly on the host system, for example, via a cron job.

For more detailed setup instructions for the backup script, including cron job configuration and restoration procedures, please refer to the [`deploy/README.md`](deploy/README.md:1) or [`docs/deployment-best-practices.md`](docs/deployment-best-practices.md:1).

## License

MIT - See LICENSE file for details.

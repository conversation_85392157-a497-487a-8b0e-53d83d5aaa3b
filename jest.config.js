export default {
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  testMatch: ['<rootDir>/tests/**/*.test.ts', '<rootDir>/tests/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/index.ts',
    '!src/**/*.d.ts'
  ],
  moduleNameMapper: {
    // Preserve .cjs and .mjs requires from dependencies (e.g., zod, mailparser)
    '^(\\.{1,2}/.*)\\.cjs$': '$1.cjs',
    '^(\\.{1,2}/.*)\\.mjs$': '$1.mjs',
    // Allow TypeScript ESM-style imports that end with .js in source to resolve without extension
    // Ensure we do NOT match .cjs by requiring a non-'.c' before .js
    '^(\\.{1,2}/.*[^c])\\.js$': '$1',
  },
  modulePaths: ['<rootDir>/src'],
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  transform: {
    '^.+\.tsx?$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'ESNext',
        target: 'ES2022'
      }
    }],
    '^.+\.jsx?$': ['babel-jest', {
      presets: [['@babel/preset-env', { targets: { node: 'current' } }]]
    }]
  },
  transformIgnorePatterns: [
    'node_modules/(?!(axios|@fastify|fastify)/)'
  ],
  setupFilesAfterEnv: ['\u003crootDir\u003e/tests/setup/jest-setup.ts','\u003crootDir\u003e/tests/setup/http-mocks.ts'],
  globalTeardown: '\u003crootDir\u003e/tests/setup/global-teardown.ts',
  testTimeout: 15000,
  forceExit: true,
  detectOpenHandles: true,
  // CI-specific configuration
  reporters: process.env.CI ? [
    'default',
    ['jest-junit', {
      outputDirectory: '.',
      outputName: 'junit.xml',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
      ancestorSeparator: ' › ',
      usePathForSuiteName: true
    }]
  ] : ['default'],
  coverageReporters: process.env.CI ?
    ['text', 'lcov', 'html', 'json-summary'] :
    ['text', 'html'],
  coverageDirectory: 'coverage',
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  }
};

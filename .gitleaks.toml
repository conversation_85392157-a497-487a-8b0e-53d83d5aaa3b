# Gitleaks configuration for EmailConnect
# This prevents accidental commit of secrets

title = "EmailConnect Gitleaks Config"

[allowlist]
description = "Global allowlist"
paths = [
  '''.env.example''',
  '''.env.test''',
  '''node_modules''',
  '''dist/''',
  '''*.min.js''',
  '''package-lock.json'''
]

[[rules]]
id = "jwt-secret"
description = "JWT Secret Keys"
regex = '''(?i)(jwt[_\-\s]?secret|jwt[_\-\s]?key|bearer[_\-\s]?token)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9+/]{32,})['"]?'''
tags = ["key", "JWT"]

[[rules]]
id = "api-key"
description = "Generic API Key"
regex = '''(?i)(api[_\-\s]?key|apikey)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9]{32,})['"]?'''
tags = ["key", "API"]

[[rules]]
id = "scaleway-access-key"
description = "Scaleway Access Key"
regex = '''(?i)(scaleway[_\-\s]?access[_\-\s]?key)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9]{32,})['"]?'''
tags = ["key", "Scaleway"]

[[rules]]
id = "database-url"
description = "Database Connection String with Password"
regex = '''(?i)(postgres|postgresql|mysql|mongodb|redis)://[^:]+:([^@]+)@'''
tags = ["database", "password"]

[[rules]]
id = "mollie-api-key"
description = "Mollie API Key"
regex = '''(?i)(mollie[_\-\s]?api[_\-\s]?key)['"]?\s*[:=]\s*['"]?(live_[a-zA-Z0-9]{30,}|test_[a-zA-Z0-9]{30,})['"]?'''
tags = ["key", "Mollie", "payment"]

[[rules]]
id = "encryption-key"
description = "Encryption Master Key"
regex = '''(?i)(encryption[_\-\s]?master[_\-\s]?key|encryption[_\-\s]?key)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9+/]{32,})['"]?'''
tags = ["key", "encryption"]

[[rules]]
id = "s3-secret"
description = "S3/Storage Secret Key"
regex = '''(?i)(s3[_\-\s]?secret|secret[_\-\s]?access[_\-\s]?key|scaleway[_\-\s]?secret)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9+/]{40,})['"]?'''
tags = ["key", "S3", "storage"]

[[rules]]
id = "webhook-secret"
description = "Webhook Signing Secret"
regex = '''(?i)(webhook[_\-\s]?secret|signing[_\-\s]?secret)['"]?\s*[:=]\s*['"]?([a-zA-Z0-9]{32,})['"]?'''
tags = ["key", "webhook"]

[[rules]]
id = "sentry-dsn"
description = "Sentry DSN with auth"
regex = '''https://[a-f0-9]{32}@[a-z0-9]+\.ingest\.sentry\.io/[0-9]+'''
tags = ["key", "Sentry", "monitoring"]

# Allow test values and examples
[allowlist.regexes]
patterns = [
  '''test_[a-zA-Z0-9]{10,}''',  # Test API keys
  '''your-secret-here''',
  '''xxxxx''',
  '''<YOUR_.*>''',
  '''\$\{.*\}''',  # Template variables
  '''process\.env\.[A-Z_]+'''  # Environment variable references
]

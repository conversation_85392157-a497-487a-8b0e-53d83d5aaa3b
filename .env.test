# Test environment variables
NODE_ENV=test
PORT=3000
HOST=0.0.0.0
URL=http://localhost:3000

# Test Database
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=eu_email_webhook_test
DATABASE_URL=postgresql://postgres:password@localhost:5432/eu_email_webhook_test

# Redis (no password for testing)
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_URL=redis://localhost:6379
REDIS_HOST_URL=redis://127.0.0.1:6379


# Admin impersonation 

# SPAMC
SPAMC_PATH=/usr/bin/spamc

# S3 (mocked in tests)
S3_ACCESS_KEY_ID=test-access-key
S3_SECRET_ACCESS_KEY=test-secret-key
S3_REGION=eu-west-1
S3_BUCKET=test-bucket
S3_ENDPOINT=http://localhost:9000

# <PERSON><PERSON> (mocked in tests)
MOLLIE_API_KEY=test_molliekey

# Webhooktest (mocked in tests)
WEBHOOKTEST_API_URL=http://localhost:4000
WEBHOOKTEST_JWT_SECRET=test-webhooktest-secret

# Email processing
MAX_EMAIL_SIZE_MB=25
WEBHOOK_TIMEOUT_MS=5000
WEBHOOK_RETRY_ATTEMPTS=3

# DNS verification
DNS_VERIFICATION_TIMEOUT_MS=1000
DNS_VERIFICATION_CACHE_TTL_MS=5000
DNS_VERIFICATION_RETRY_ATTEMPTS=2

# GDPR compliance
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90

# Sentry (disabled for tests)
SENTRY_DSN=
SENTRY_ENABLED=false
SENTRY_ENVIRONMENT=test
---
title: Testing webhooks with WebhookTest
slug: webhooktest-integration
excerpt: Test your email-to-webhook flows instantly with integrated WebhookTest.eu
category: integrations
order: 2
---

# Testing webhooks with WebhookTest

WebhookTest is seamlessly integrated into EmailConnect, making it incredibly easy to test your email-to-webhook flows without switching between services or setting up external endpoints.

## What is WebhookTest?

WebhookTest (webhooktest.eu) is a free service that provides instant webhook endpoints for testing. When integrated with EmailConnect, it allows you to:
- See webhook payloads in real-time
- Test different response scenarios
- Debug your email processing logic
- Validate data before connecting to production systems

## Quick start

### Step 1: Create a test webhook
1. In your EmailConnect dashboard, go to any alias
2. Click "Add webhook"
3. Click the "Use WebhookTest" button
4. A unique test endpoint is automatically generated (e.g., `https://webhooktest.eu/u/abc123`)

### Step 2: Send a test email
Send an email to your configured alias. Within seconds, you'll see:
- The complete JSON payload in WebhookTest
- Response status and headers
- Processing time and metadata

### Step 3: Iterate and refine
Make adjustments to your webhook configuration and test again instantly. No deployment needed!

## Advanced testing scenarios

### Testing error handling
Use custom headers to simulate different response codes:
```
Header: X-Mock-Status
Value: 500
```

This will make WebhookTest return a 500 error, allowing you to test your retry logic.

### Testing timeouts
```
Header: X-Mock-Delay
Value: 5000
```

Delays the response by 5 seconds to test timeout handling.

### Testing different content types
```
Header: X-Mock-Content-Type
Value: application/xml
```

Tests how your system handles non-JSON responses.

## Real-world workflow

### Development phase
1. Create alias with WebhookTest endpoint
2. Send test emails with various formats
3. Verify the JSON structure matches your expectations
4. Test edge cases (large attachments, special characters, etc.)

### Integration phase
1. Keep WebhookTest as a secondary webhook
2. Add your production endpoint as primary
3. Compare responses between test and production
4. Remove WebhookTest when confident

### Debugging production issues
1. Temporarily add WebhookTest alongside your production webhook
2. Capture real-world payloads
3. Diagnose issues without affecting production
4. Remove when resolved

## Best practices

### Use descriptive webhook names
When creating webhooks, use names like:
- "WebhookTest - Development"
- "WebhookTest - Debugging Production Issue #123"
- "WebhookTest - Testing Attachments"

### Leverage webhook groups
Create separate webhook configurations for:
- Development (WebhookTest only)
- Staging (WebhookTest + staging endpoint)
- Production (production endpoint only)

### Document test scenarios
Keep track of test emails and expected outcomes:
```
Test: Large attachment handling
Email: <EMAIL>
Attachment: 10MB PDF
Expected: Base64 encoded in payload
```

## Tips and tricks

### 1. Bookmark your test endpoints
WebhookTest URLs are permanent. Bookmark them for quick access.

### 2. Use the WebhookTest dashboard
Visit your WebhookTest URL in a browser to see a full history of requests.

### 3. Test webhook signatures
If using webhook signatures, WebhookTest shows the exact headers received, making it easy to verify your HMAC calculations.

### 4. Mobile testing
WebhookTest works great on mobile devices. Test your email flows from anywhere.

## Common testing patterns

### Testing email parsing
```
Subject: Order #12345
Body: Please process this order urgently

Expected webhook payload:
{
  "subject": "Order #12345",
  "text": "Please process this order urgently",
  "metadata": { ... }
}
```

### Testing attachment handling
Send emails with various attachment types and verify they appear correctly in the webhook payload.

### Testing retry logic
1. Set X-Mock-Status: 500
2. Verify EmailConnect retries the webhook
3. Change to X-Mock-Status: 200
4. Confirm successful delivery

WebhookTest integration removes the friction from testing email automation flows, letting you focus on building great features instead of debugging infrastructure.
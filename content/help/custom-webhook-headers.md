---
title: Custom webhook headers
slug: custom-webhook-headers
excerpt: Configure custom HTTP headers for authentication, routing, and testing webhooks
category: configuration
order: 5
---

# Custom webhook headers

Custom headers in EmailConnect webhooks provide powerful capabilities for authentication, routing, and testing. Here's everything you need to know about configuring and using them effectively.

## What are custom headers?

Custom headers are additional HTTP headers sent with every webhook request. They can be used for:
- Authentication tokens
- API versioning
- Request routing
- Testing scenarios
- Metadata passing

## Adding custom headers

### In the webhook form
1. Navigate to your alias
2. Click "Add webhook" or edit existing
3. Find the "Custom headers" section
4. Click "Add header"
5. Enter header name and value
6. Save the webhook

### Header format
Headers follow standard HTTP conventions:
- **Name**: Case-insensitive, but typically formatted as `Header-Name`
- **Value**: Any string value, including tokens, JSON, etc.

## Common use cases

### Authentication
```
Header: Authorization
Value: Bearer your-api-token-here
```

```
Header: X-API-Key
Value: sk_live_abcd1234efgh5678
```

### Webhook signatures
```
Header: X-Webhook-Secret
Value: shared-secret-for-hmac-verification
```

### Content type override
```
Header: Content-Type
Value: application/x-www-form-urlencoded
```

### Custom routing
```
Header: X-Environment
Value: production
```

```
Header: X-Tenant-ID
Value: customer-123
```

## Testing with WebhookTest

WebhookTest offers special headers for testing scenarios without deploying code:

### Simulating HTTP errors
```
Header: X-Mock-Status
Value: 500
```

Returns a 500 Internal Server Error, perfect for testing retry logic.

### Adding response delays
```
Header: X-Mock-Delay
Value: 3000
```

Delays response by 3 seconds (3000ms) to test timeout handling.

### Custom response bodies
```
Header: X-Mock-Response
Value: {"status": "queued", "id": 12345}
```

Returns custom JSON response for testing parsers.

### Different content types
```
Header: X-Mock-Content-Type
Value: text/plain
```

Tests how your system handles non-JSON responses.

## Real-world examples

### Connecting to Slack
```
Header: Authorization
Value: Bearer xoxb-your-slack-token
```

### Zapier webhook
```
Header: X-Hook-Secret
Value: your-zapier-hook-secret
```

### Custom CRM integration
```
Header: X-Company-ID
Value: COMP-12345
Header: X-API-Version
Value: v2
Header: X-Source
Value: email-automation
```

### Multi-tenant SaaS
```
Header: X-Tenant
Value: acme-corp
Header: X-Workspace
Value: engineering
Header: X-Priority
Value: high
```

## Security best practices

### Don't expose sensitive data
- Use environment-specific values
- Rotate tokens regularly
- Never commit webhook configurations to version control

### Use HTTPS endpoints
- Always use HTTPS URLs for webhooks
- Headers are encrypted in transit with HTTPS
- Avoid HTTP endpoints for sensitive data

### Implement webhook signatures
Instead of static authentication:
1. Generate a signature using HMAC
2. Include timestamp to prevent replay attacks
3. Verify on the receiving end

## Advanced patterns

### Environment-based headers
Create separate webhooks for different environments:

**Development webhook:**
```
URL: https://dev.api.example.com/webhook
Header: X-Environment
Value: development
```

**Production webhook:**
```
URL: https://api.example.com/webhook
Header: X-Environment
Value: production
Header: Authorization
Value: Bearer prod-token-here
```

### Feature flags
```
Header: X-Feature-Flag
Value: new-parser-enabled
```

Your endpoint can use this to test new features gradually.

### Request tracing
```
Header: X-Request-ID
Value: webhook-{{timestamp}}-{{random}}
```

Note: Variable substitution coming in future updates.

## Troubleshooting

### Headers not appearing
1. Check header name doesn't conflict with reserved headers
2. Ensure no typos in header names
3. Verify webhook is saved after adding headers

### Authentication failing
1. Verify token/key is correct
2. Check if token has expired
3. Ensure header name matches API expectations
4. Test with curl to isolate issues

### Testing headers locally
Use curl to simulate EmailConnect webhooks:
```bash
curl -X POST https://your-endpoint.com/webhook \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-key" \
  -H "X-Custom-Header: test-value" \
  -d '{"test": "payload"}'
```

## Tips and tricks

### Header templates
Save common header combinations:
- Production auth headers
- Development/test headers
- Integration-specific headers

### Documentation
Document your headers:
```
# Webhook Headers
- Authorization: Bearer token for API access
- X-Webhook-Source: Always "emailconnect"
- X-Customer-ID: Dynamic based on sender domain
```

### Monitoring
Log custom headers on receiving end for debugging:
- Track which headers are present
- Monitor for missing headers
- Alert on authentication failures

Custom headers make your webhooks flexible and secure, enabling seamless integration with any HTTP endpoint.
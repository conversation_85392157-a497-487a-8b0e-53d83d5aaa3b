---
title: Using subdomains for email processing
slug: subdomain-strategy
excerpt: Keep your main email infrastructure intact while enabling powerful automation
category: getting-started
order: 2
---

# Using subdomains for email processing

Setting up email processing can be intimidating when it involves changing the MX records of your main domain. Here's a clever strategy that keeps your primary email infrastructure intact while enabling powerful automation capabilities.

## The subdomain approach

Instead of modifying your main domain's MX records, create a dedicated subdomain for email processing:

- `ai.yourdomain.com` - for AI-powered email processing
- `in.yourdomain.com` - for incoming webhook processing
- `automation.yourdomain.com` - for automated workflows

## Benefits of this approach

### 1. Zero disruption to existing email
Your main domain continues to function exactly as before. All existing email addresses and services remain untouched.

### 2. Easy forwarding setup
You can create simple email aliases in your existing email system:
- `<EMAIL>` → forwards to → `<EMAIL>`
- `<EMAIL>` → forwards to → `<EMAIL>`

### 3. Gradual migration
Test your automation workflows with the subdomain first. Once confident, you can gradually forward more addresses or eventually switch your main domain if desired.

## How to set it up

### Step 1: Create a subdomain in EmailConnect
1. Go to your dashboard
2. Click "Add domain"
3. Enter your subdomain (e.g., `in.yourdomain.com`)
4. Follow the DNS verification steps

### Step 2: Configure MX records
Add these MX records for your subdomain only:
```
in.yourdomain.com    MX    10    mail.emailconnect.eu
```

### Step 3: Create aliases
In EmailConnect, create aliases for the subdomain:
- `<EMAIL>`
- `<EMAIL>`
- etc.

### Step 4: Set up forwarding
In your main email system (Gmail, Office 365, etc.), create forwarding rules:
- From: `<EMAIL>`
- To: `<EMAIL>`

## Real-world example

Let's say you run an e-commerce store at `mystore.com`:

1. Keep your main email (`<EMAIL>`) on your current provider
2. Create `automation.mystore.com` for processing orders
3. Forward `<EMAIL>` to `<EMAIL>`
4. Configure webhooks to update your inventory system automatically

Your customers still email `<EMAIL>`, but the emails are automatically processed through EmailConnect without touching your main email infrastructure.

## Tips for success

- Start with one or two non-critical email addresses
- Test thoroughly before forwarding important emails
- Use descriptive subdomain names that indicate their purpose
- Document which addresses forward where for your team

This approach gives you all the benefits of email automation while maintaining complete control and flexibility over your email infrastructure.
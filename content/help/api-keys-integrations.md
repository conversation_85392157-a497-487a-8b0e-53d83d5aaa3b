---
title: API keys and integrations
slug: api-keys-integrations
excerpt: Create and manage API keys for powerful integrations with automation platforms
category: integrations
order: 1
---

# API keys and integrations

EmailConnect's REST API enables powerful integrations with automation platforms, custom applications, and third-party services. Here's how to create and manage API keys for seamless connectivity.

## Creating API keys

### Access API settings
1. Navigate to Settings (`/settings`)
2. Click on "API Keys" tab
3. Click "Create new API key"

### Configure key permissions

**Full scope (default)**
- Complete access to all API endpoints
- Read and write permissions
- Suitable for trusted applications

**Limited scopes**
Choose specific permissions:
- **Domains**: List and manage domains
- **Aliases**: Create and manage email aliases  
- **Webhooks**: Configure webhook endpoints
- **Emails**: Read email history and logs
- **Statistics**: Access usage statistics

### Generate and save
1. Choose key name (e.g., "Zapier Integration")
2. Select permissions
3. Click "Generate key"
4. **Important**: Save the key immediately - it won't be shown again

## API documentation

Access comprehensive API documentation at `/docs` when logged in:
- Interactive API explorer (Swagger UI)
- Request/response examples
- Authentication details
- Rate limits and quotas

## Popular integrations

### Zapier
Connect EmailConnect to 5,000+ apps:
1. Create API key with full scope
2. Add EmailConnect as a Zapier app
3. Use API key for authentication
4. Build zaps to process emails

**Example Zapier workflows:**
- Email → EmailConnect → Google Sheets
- Email → EmailConnect → Slack notification
- Email → EmailConnect → CRM update

### Make (formerly Integromat)
Visual automation platform:
1. Create API key in EmailConnect
2. Add HTTP module in Make
3. Configure with EmailConnect API endpoints
4. Process emails in complex workflows

**Example Make scenarios:**
- Parse invoices and update accounting software
- Extract leads from emails to CRM
- Trigger multi-step approval workflows

### n8n (self-hosted automation)
Open-source workflow automation:
1. Install official EmailConnect n8n node
2. Create API key with required scopes
3. Configure node with your API key
4. Build automation workflows

**n8n advantages:**
- Native EmailConnect integration
- Self-hosted for data privacy
- Complex workflow capabilities
- Cost-effective for high volume

## Common API operations

### List domains
```bash
GET https://api.emailconnect.eu/v1/domains
Authorization: Bearer your-api-key-here
```

### Create alias
```bash
POST https://api.emailconnect.eu/v1/aliases
Authorization: Bearer your-api-key-here
Content-Type: application/json

{
  "domain_id": "dom_123",
  "name": "support",
  "description": "Support ticket intake"
}
```

### Configure webhook
```bash
POST https://api.emailconnect.eu/v1/webhooks
Authorization: Bearer your-api-key-here
Content-Type: application/json

{
  "alias_id": "alias_456",
  "url": "https://your-app.com/webhook",
  "headers": {
    "X-Custom-Header": "value"
  }
}
```

### Get email logs
```bash
GET https://api.emailconnect.eu/v1/emails?alias_id=alias_456
Authorization: Bearer your-api-key-here
```

## Security best practices

### Key rotation
- Rotate keys every 90 days
- Use different keys per integration
- Revoke unused keys immediately

### Scope limitation
- Use minimum required permissions
- Create separate keys for read vs write
- Audit key usage regularly

### Environment separation
- Development: Limited scope, test domains only
- Production: Full scope, IP restrictions
- CI/CD: Temporary keys with expiration

### Storage guidelines
- Never commit keys to version control
- Use environment variables
- Encrypt keys in configuration files
- Use secret management services

## Rate limits

### Default limits
- 1000 requests per hour
- 10,000 requests per day
- Burst: 100 requests per minute

### Higher limits available
- Pro plan: 2x default limits
- Enterprise: Custom limits
- Contact support for increases

### Rate limit headers
Monitor your usage:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 850
X-RateLimit-Reset: 1640995200
```

## Integration examples

### Python
```python
import requests

API_KEY = "your-api-key-here"
BASE_URL = "https://api.emailconnect.eu/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# List all domains
response = requests.get(f"{BASE_URL}/domains", headers=headers)
domains = response.json()
```

### Node.js
```javascript
const axios = require('axios');

const API_KEY = 'your-api-key-here';
const BASE_URL = 'https://api.emailconnect.eu/v1';

const client = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// Create an alias
async function createAlias(domainId, name) {
  const response = await client.post('/aliases', {
    domain_id: domainId,
    name: name
  });
  return response.data;
}
```

### curl
```bash
# Get account statistics
curl -X GET https://api.emailconnect.eu/v1/statistics \
  -H "Authorization: Bearer your-api-key-here"
```

## Troubleshooting

### Authentication errors
- Verify API key is correct
- Check key hasn't been revoked
- Ensure Bearer prefix in header

### Permission denied
- Check key has required scopes
- Verify resource ownership
- Confirm account limits

### Rate limiting
- Implement exponential backoff
- Cache responses when possible
- Use webhooks instead of polling

## Advanced usage

### Webhook signature verification
Verify webhook authenticity:
```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const hash = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return hash === signature;
}
```

### Batch operations
Process multiple resources efficiently:
```bash
POST https://api.emailconnect.eu/v1/aliases/batch
Authorization: Bearer your-api-key-here

{
  "operations": [
    {"action": "create", "data": {...}},
    {"action": "update", "data": {...}}
  ]
}
```

### Pagination
Handle large result sets:
```bash
GET https://api.emailconnect.eu/v1/emails?page=2&limit=100
```

The API provides powerful programmatic access to all EmailConnect features, enabling seamless integration with your existing tools and workflows.
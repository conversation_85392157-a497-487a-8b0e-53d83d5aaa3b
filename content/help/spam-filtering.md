---
title: Spam filtering for domains
slug: spam-filtering
excerpt: Protect your webhooks from unwanted emails with built-in spam filtering
category: settings
order: 2
---

# Spam filtering for domains

EmailConnect provides built-in spam filtering to protect your webhooks from unwanted emails and reduce unnecessary processing costs.

## How spam filtering works

When enabled, EmailConnect uses advanced spam detection algorithms to:
- Analyze email headers and content
- Check sender reputation
- Identify common spam patterns
- Block emails before they trigger webhooks

## Enabling spam filtering

### When creating a new domain
1. Click "Add domain" in your dashboard
2. Enter your domain name
3. Toggle "Enable spam filtering" to ON
4. Complete the domain setup

### For existing domains
1. Go to your domain settings
2. Find the "Spam filtering" section
3. Toggle the setting to enable/disable
4. Changes take effect immediately

## What happens to spam emails?

When spam filtering is enabled:
- Suspected spam emails are blocked at the SMTP level
- No webhook is triggered
- No credits are consumed
- The sender receives a standard rejection message

## Spam filter sensitivity

EmailConnect uses a balanced approach that:
- Blocks obvious spam (Nigerian princes, fake pharmacies, etc.)
- Allows legitimate automated emails (receipts, notifications)
- Learns from global spam patterns
- Minimizes false positives

## Best practices

### When to enable spam filtering

**Recommended for:**
- Public-facing email addresses (support@, info@, contact@)
- Domains receiving high volumes of email
- Cost-sensitive applications
- Production environments

**Consider disabling for:**
- Internal automation addresses
- Testing environments
- When you need to receive all emails regardless of content

### Monitoring spam filtering

Check your domain statistics to see:
- Total emails received
- Emails blocked by spam filter
- Spam block percentage

This helps you understand if legitimate emails might be blocked.

## Common scenarios

### Customer support emails
Enable spam filtering for support@ addresses to avoid:
- SEO spam
- Link building requests
- Automated bot submissions

### Order processing
For orders@ or invoices@ addresses, you might want to:
- Disable spam filtering initially
- Monitor incoming emails
- Enable filtering once you understand traffic patterns

### Public contact forms
If your contact form sends to an EmailConnect address:
- Enable spam filtering
- Add form validation on your website
- Consider CAPTCHA for additional protection

## Troubleshooting

### Legitimate emails being blocked

If you suspect false positives:
1. Temporarily disable spam filtering
2. Ask the sender to retry
3. Check if the email arrives
4. Report false positives to support

### Too much spam getting through

If spam filtering isn't catching enough:
- Ensure it's enabled for the domain
- Check that emails are arriving at the correct aliases
- Contact support for filter adjustments

## Advanced configuration

### Whitelisting (coming soon)
Future updates will allow:
- Whitelisting specific sender domains
- Custom spam rules
- Regex-based filtering

### API-based filtering
Use the EmailConnect API to:
- Check spam filter status
- Enable/disable per domain
- View spam statistics

## FAQ

### Does spam filtering affect email delivery speed?
No, spam filtering happens in real-time with negligible impact on processing speed.

### Can I see which emails were blocked?
Currently, blocked emails are not stored. Only statistics are available.

### Is spam filtering included in all plans?
Yes, spam filtering is available on all plans at no extra cost.

### Can I customize spam sensitivity?
Not yet, but this feature is on our roadmap.

## Tips for reducing spam

Beyond EmailConnect's filtering:
1. Use obscured email addresses on websites
2. Implement form validation
3. Use subdomain strategies for public addresses
4. Rotate aliases periodically for high-spam addresses

Spam filtering helps you focus on legitimate emails while keeping your webhook endpoints clean and your costs predictable.
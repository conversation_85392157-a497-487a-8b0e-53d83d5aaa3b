---
title: Understanding alias options
slug: alias-options-explained
excerpt: Learn about attachment processing and envelope data options for email aliases
category: configuration
order: 4
---

# Understanding alias options: Attachments and envelope data

When creating or editing an alias in EmailConnect, you'll find two important options that control how email data is processed and delivered to your webhooks.

## Process attachments

This option determines how EmailConnect handles file attachments in incoming emails.

### Option 1: Inline (default)

When set to "Inline", attachments are:
- Converted to Base64 encoding
- Included directly in the webhook JSON payload
- Immediately available for processing

**Example payload with inline attachment:**
```json
{
  "subject": "Invoice for March",
  "attachments": [
    {
      "filename": "invoice-march.pdf",
      "contentType": "application/pdf",
      "size": 125840,
      "content": "JVBERi0xLjQKJeHp69MKMSAwIG9iago8PC9Ue..." // Base64 encoded content
    }
  ]
}
```

**Best for:**
- Small to medium attachments (under 10MB)
- When you need immediate access to file contents
- Simple architectures without external storage
- Quick prototypes and MVPs

### Option 2: S3 Storage

When set to "S3 Storage", attachments are:
- Uploaded to Amazon S3 (or compatible storage)
- Webhook receives URLs instead of file contents
- Files accessible via secure, time-limited URLs

**Example payload with S3 storage:**
```json
{
  "subject": "Invoice for March",
  "attachments": [
    {
      "filename": "invoice-march.pdf",
      "contentType": "application/pdf",
      "size": 125840,
      "url": "https://s3.eu-central-1.amazonaws.com/bucket/uuid/invoice-march.pdf?X-Amz-..."
    }
  ]
}
```

**Best for:**
- Large attachments (over 10MB)
- High-volume email processing
- When webhooks have size limits
- Long-term file storage needs

### Option 3: Custom S3 Storage (coming soon)

Configure your own S3 bucket for attachment storage:
- Full control over file retention
- Keep files in your own infrastructure
- Custom access policies
- Cost optimization for high volumes

## Include envelope data

This option adds technical email routing information to your webhook payload.

### What is envelope data?

Envelope data includes:
- **MAIL FROM**: The actual sender address (may differ from "From" header)
- **RCPT TO**: The actual recipient address (may differ from "To" header)
- **SMTP transaction details**: Routing and delivery information

### When enabled

Your webhook receives additional fields:
```json
{
  "subject": "Order confirmation",
  "from": "<EMAIL>",
  "to": "<EMAIL>",
  "envelope": {
    "mailFrom": "<EMAIL>",
    "rcptTo": ["<EMAIL>"],
    "remoteAddress": "************",
    "clientHostname": "mail.shop.com"
  }
}
```

### Why envelope data matters

**Email forwarding detection:**
- See the original recipient when emails are forwarded
- Track the actual delivery path
- Identify automated forwards

**Bounce handling:**
- The MAIL FROM address receives bounce notifications
- Different from the visible "From" address
- Critical for email deliverability tracking

**Security and validation:**
- Verify the sending server
- Detect spoofed emails
- Implement SPF/DKIM validation

### When to enable envelope data

**Enable for:**
- Email analytics applications
- Bounce processing systems
- Security-focused applications
- When debugging delivery issues
- Multi-tenant applications needing routing info

**Skip for:**
- Simple email-to-webhook conversions
- When you only need message content
- Applications that don't process technical headers

## Common configurations

### Support ticket system
- **Process attachments**: Inline (customers attach screenshots)
- **Include envelope**: No (not needed for tickets)

### Document processing system
- **Process attachments**: S3 Storage (large PDFs)
- **Include envelope**: No (focus on content)

### Email analytics platform
- **Process attachments**: S3 Storage (preserve originals)
- **Include envelope**: Yes (track routing)

### Simple notification system
- **Process attachments**: Inline (if any)
- **Include envelope**: No (keep it simple)

## Performance considerations

### Inline attachments
- Increases webhook payload size
- May hit size limits on receiving endpoints
- Faster for small files
- No additional API calls needed

### S3 storage
- Keeps webhook payloads small
- Requires additional HTTP requests to fetch files
- Better for large files
- Adds slight processing delay

### Envelope data
- Minimal impact on payload size
- No performance impact
- Useful debugging information
- Can be ignored if not needed

Choose the configuration that best matches your use case and architectural requirements.
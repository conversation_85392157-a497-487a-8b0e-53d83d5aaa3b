---
title: Data retention settings
slug: data-retention
excerpt: Configure how long EmailConnect stores your email metadata and logs
category: settings
order: 1
---

# Data retention settings

EmailConnect provides flexible data retention controls to help you balance data availability with privacy requirements and storage costs. Configure these settings at `/settings#retention`.

## Understanding data retention

Data retention determines how long EmailConnect stores:
- Processed email metadata
- Webhook delivery logs
- Failed webhook attempts
- Email processing history

## Why data retention matters

### Privacy compliance
- Meet GDPR requirements for data minimization
- Automatically delete old customer data
- Reduce liability from storing unnecessary information

### Storage optimization
- Lower storage costs by removing old data
- Improve query performance on active data
- Keep your account lean and efficient

### Business requirements
- Maintain audit trails for the required period
- Balance debugging needs with privacy
- Comply with industry-specific regulations

## Configuring retention settings

### Access retention settings
1. Navigate to Settings (`/settings`)
2. Click on the "Retention" tab
3. Choose your retention period
4. Save changes

### Available retention periods

**Free plan:**
- 7 days (default and maximum)

**Basic plan:**
- 7 days (default)
- 14 days
- 30 days

**Pro plan:**
- 7 days
- 14 days
- 30 days (default)
- 60 days
- 90 days

**Enterprise plan:**
- All standard options plus
- 180 days
- 365 days
- Custom periods available

## What gets retained

### Email metadata
- Subject, sender, recipient
- Processing timestamp
- Size and attachment count
- Delivery status

### Webhook logs
- Delivery attempts and timestamps
- HTTP response codes
- Response times
- Retry attempts

### What doesn't get stored
- Email body content (unless explicitly configured)
- Attachment contents (only metadata)
- Raw email data
- Personal identifying information beyond email addresses

## Retention lifecycle

### Day 0: Email received
- Email processed and webhook triggered
- Metadata stored in database
- Delivery logged

### During retention period
- Full access to logs and history
- Debugging information available
- Retry options for failed webhooks

### After retention period
- Data automatically deleted
- No manual intervention needed
- Deletion is permanent and irreversible

## Best practices by use case

### Transactional emails
**Recommendation: 30-60 days**
- Sufficient for debugging issues
- Covers most dispute periods
- Balances cost and utility

### Support tickets
**Recommendation: 14-30 days**
- Quick resolution expected
- Lower privacy concerns
- Reduces storage needs

### Financial notifications
**Recommendation: 90-365 days**
- Regulatory requirements
- Audit trail needs
- Dispute resolution periods

### Marketing automation
**Recommendation: 7-14 days**
- High volume, low individual value
- Quick feedback loops
- Cost optimization priority

## Changing retention settings

### Immediate effect
- New emails follow new retention period
- Existing data maintains original retention

### Retroactive changes
- Shortening retention doesn't delete existing data immediately
- Data expires based on original retention setting
- Extending retention only affects new data

## Compliance considerations

### GDPR compliance
- Set shortest practical retention period
- Document your retention policy
- Ensure automatic deletion works

### Industry regulations
- Financial services: May require longer retention
- Healthcare: Check HIPAA requirements
- E-commerce: Consider payment dispute periods

### Data subject requests
- Retention settings don't override deletion requests
- Manual deletion available via API
- Contact support for bulk deletions

## Monitoring data usage

### Retention dashboard
View in your settings:
- Current retention setting
- Data volume by age
- Upcoming deletions
- Storage trends

### Alerts and notifications
- Warning before high-volume deletions
- Monthly retention reports
- Storage quota notifications

## Advanced retention strategies

### Multi-domain approaches
Different retention per use case:
- `transactions.domain.com`: 90 days
- `notifications.domain.com`: 7 days
- `support.domain.com`: 30 days

### Archival options (Enterprise)
- Export data before deletion
- Backup to your own storage
- Compliance archives

### API-based management
```javascript
// Get current retention setting
GET /api/v1/settings/retention

// Update retention period
PUT /api/v1/settings/retention
{
  "days": 30
}
```

## Cost optimization

### Storage costs by retention
- 7 days: Baseline cost
- 30 days: ~4x baseline
- 90 days: ~12x baseline
- 365 days: ~50x baseline

### Optimization tips
1. Start with shortest reasonable period
2. Increase only when needed
3. Use different domains for different retention needs
4. Monitor actual data access patterns

## FAQ

### Can I recover deleted data?
No, deletion is permanent. Plan retention accordingly.

### Does retention affect webhook delivery?
No, webhooks are delivered immediately. Retention only affects historical logs.

### Can I export data before deletion?
Yes, use the API to export data. Enterprise plans have automated export options.

### How precise is deletion timing?
Data is deleted within 24 hours of retention expiry.

Configure your retention settings thoughtfully to balance business needs, compliance requirements, and cost optimization.
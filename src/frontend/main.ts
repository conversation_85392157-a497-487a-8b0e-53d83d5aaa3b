import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './style.css'
import './assets-manifest' // Import assets to ensure they're processed by Vite
import { initializeSentry } from './lib/sentry'

// Create and mount the consolidated dashboard app
const app = createApp(App)

// Initialize Sentry error tracking
initializeSentry(app, router)

// Disable console logging in production (keep errors and warnings)
if (import.meta.env.PROD) {
  console.log = () => {}
  console.debug = () => {}
  console.info = () => {}
  // Keep console.error and console.warn for critical issues
}

// Add global error handler for Vue
app.config.errorHandler = (err, _instance, info) => {
  // eslint-disable-next-line no-console
  console.error('Vue error:', err, info)
  // Error will be automatically captured by Sentry Vue integration
}

// Add Vue Router
app.use(router)

// Mount the single Vue app
app.mount('#app')

// Hide the loading spinner once Vue is mounted
const loadingElement = document.getElementById('app-loading')
if (loadingElement) {
  loadingElement.style.display = 'none'
}

// Back/forward cache optimization
window.addEventListener('pageshow', (event) => {
  if (event.persisted) {
    // Page was restored from bfcache - refresh dynamic content if needed
    router.replace(router.currentRoute.value.fullPath)
  }
})

// Prevent bfcache blocking by avoiding unload listeners
// and ensuring no pending network requests block the cache

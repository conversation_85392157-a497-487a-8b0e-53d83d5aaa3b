import { createRouter, createWebHistory } from 'vue-router'
import { authGuard, adminGuard } from './router/guards'

// Use dynamic imports for better code splitting
const DomainsView = () => import('./components/dashboard/DomainsView.vue')
const AliasesView = () => import('./components/dashboard/AliasesView.vue')
const WebhooksView = () => import('./components/dashboard/WebhooksView.vue')
const LogsView = () => import('./components/dashboard/LogsView.vue')
const SettingsPage = () => import('./components/settings/SettingsPage.vue')
const LoginPage = () => import('./components/auth/LoginPage.vue')
const ForgotPasswordPage = () => import('./components/auth/ForgotPasswordPage.vue')
const ResetPasswordPage = () => import('./components/auth/ResetPasswordPage.vue')
const LandingPage = () => import('./components/landing/LandingPage.vue')
const StaticPageLayout = () => import('./components/content/StaticPageLayout.vue')
const DynamicPageResolver = () => import('./components/content/DynamicPageResolver.vue')
const ChangelogPage = () => import('./components/content/ChangelogPage.vue')
const PricingPage = () => import('./components/pages/PricingPage.vue')
const HelpIndexPage = () => import('./components/content/HelpIndexPage.vue')
const HelpArticlePage = () => import('./components/content/HelpArticlePage.vue')
const AdminDashboard = () => import('./components/admin/AdminDashboard.vue')

const routes = [
  // Landing page (public)
  {
    path: '/',
    name: 'home',
    component: LandingPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Static pages (legal documents, etc.) - public
  // These are handled by the dynamic resolver, but we keep explicit routes for better SEO/navigation
  {
    path: '/terms-of-service',
    name: 'terms-of-service',
    component: StaticPageLayout,
    meta: { requiresAuth: false, layout: 'guest' },
    beforeEnter: (to, from, next) => {
      to.params.slug = 'terms-of-service'
      next()
    }
  },
  {
    path: '/privacy-policy',
    name: 'privacy-policy',
    component: StaticPageLayout,
    meta: { requiresAuth: false, layout: 'guest' },
    beforeEnter: (to, from, next) => {
      to.params.slug = 'privacy-policy'
      next()
    }
  },
  // Content pages (public)
  {
    path: '/pricing',
    name: 'pricing',
    component: PricingPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  {
    path: '/changelog',
    name: 'changelog',
    component: ChangelogPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  {
    path: '/help',
    name: 'help',
    component: HelpIndexPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  {
    path: '/help/:slug',
    name: 'help-article',
    component: HelpArticlePage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Auth routes (public)
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  {
    path: '/register',
    redirect: '/login'
  },
  {
    path: '/auth/forgot-password',
    name: 'forgot-password',
    component: ForgotPasswordPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  {
    path: '/auth/reset-password',
    name: 'reset-password',
    component: ResetPasswordPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  // Dashboard routes (protected) - clean URLs
  {
    path: '/dashboard',
    redirect: '/domains'
  },
  {
    path: '/domains',
    name: 'domains',
    component: DomainsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/aliases',
    name: 'aliases',
    component: AliasesView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/webhooks',
    name: 'webhooks',
    component: WebhooksView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/settings',
    redirect: '/settings/profile'
  },
  {
    path: '/settings/:section',
    name: 'settings',
    component: SettingsPage,
    meta: { requiresAuth: true, layout: 'user' },
    beforeEnter: (to, from, next) => {
      const validSections = ['profile', 'api-keys', 'account-linking', 'storage', 'retention', 'billing']
      const section = to.params.section as string
      if (validSections.includes(section)) {
        next()
      } else {
        next('/settings/profile')
      }
    }
  },
  // Admin routes
  {
    path: '/admin',
    name: 'admin',
    component: AdminDashboard,
    meta: { requiresAuth: true, requiresAdmin: true, layout: 'user' }
  },
  // 404 route for access denied / not found
  {
    path: '/404',
    name: '404',
    component: DynamicPageResolver,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Catch-all route for dynamic page resolution (static pages vs 404)
  // This must be last to avoid catching legitimate routes
  {
    path: '/:pathMatch(.*)*',
    name: 'dynamic-resolver',
    component: DynamicPageResolver,
    meta: { requiresAuth: false, layout: 'guest' }
  }
] as const

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Plausible Analytics: Track pageviews on every route change (SPA support)
declare global {
  interface Window {
    // eslint-disable-next-line no-unused-vars
    plausible?: (_eventName: string, _options?: {
      callback?: () => void;
      props?: Record<string, string | number>;
      interactive?: boolean;
    }) => void;
  }
}

router.afterEach(() => {
  if (window.plausible) {
    window.plausible('pageview');
  }
});

// Apply authentication guards
router.beforeEach(authGuard)

// Apply admin guards for admin routes
router.beforeEach(adminGuard)

export default router

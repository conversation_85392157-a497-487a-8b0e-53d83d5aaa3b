<template>
  <div class="min-h-screen bg-base-200/40">
    <!-- Protected content - authentication verified by router guards -->
      <!-- User Header Component -->
      <UserHeader />

      <!-- Dashboard Content with Metrics and Navigation -->
      <div class="bg-base-200 overflow-x-hidden">
        <!-- Metrics Pill and Tab Navigation (only for dashboard pages) -->
        <template v-if="!isSettingsPage">
          <!-- Metrics Pill -->
          <MetricsPill />

          <!-- Tab Navigation -->
          <TabNavigation
            :counts="counts"
            @create-action="handleCreateAction"
          />
        </template>

        <!-- Main Content -->
        <main class="bg-base-200/40 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-8">
          <slot />
        </main>

        <!-- Footer -->
        <footer>
          <div class="bg-base-100/40 backdrop-blur-sm border-t border-base-300/50">
            <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
              <p class="text-center text-sm text-base-content/70">&copy; 2025 - EmailConnect by <a href="https://xadi.nl" target="_blank" rel="noopener noreferrer" class="link link-primary">XADI</a></p>
              <div class="text-center mt-1">
                <a href="https://status.emailconnect.eu" target="_blank" rel="noopener noreferrer" class="text-sm text-base-content hover:underline">
                  <span class="hidden sm:inline">Service</span> Status
                </a>
                <router-link to="/help" class="text-sm text-base-content hover:underline ml-4 cursor-pointer">
                  Help <span class="hidden sm:inline">center</span>
                </router-link>
                <router-link to="/terms-of-service" class="text-sm text-base-content hover:underline ml-4 cursor-pointer">
                  Terms <span class="hidden sm:inline">of service</span>
                </router-link>
                <router-link to="/privacy-policy" class="text-sm text-base-content hover:underline ml-4 cursor-pointer">
                  Privacy <span class="hidden sm:inline">policy</span>
                </router-link>
              </div>
            </div>
          </div>
        </footer>
      </div>

      <!-- Onboarding assistant (persistent across all routes) -->
    <OnboardingAssistant />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserHeader from '../components/ui/UserHeader.vue'
import TabNavigation from '../components/dashboard/TabNavigation.vue'
import MetricsPill from '../components/dashboard/MetricsPill.vue'
import OnboardingAssistant from '../components/onboarding/OnboardingAssistant.vue'
import { useAuth } from '@composables/useAuth'
import { useMetrics } from '@composables/useMetrics'
import { useOnboarding } from '@composables/useOnboarding'
import { useWebSocket } from '@composables/useWebSocket'

// Vue Router
const route = useRoute()
const router = useRouter()

// Use auth composable - authentication is already verified by router guards
const { isAuthenticated } = useAuth()

// Use shared metrics composable instead of duplicate API call
const { counts, loadMetrics } = useMetrics()

// Use onboarding state to hide navigation during onboarding
const { shouldShowOnboarding } = useOnboarding()

// Initialize WebSocket for real-time updates (only for authenticated users)
// Since this is UserLayout, we know we're in an authenticated context
const { isConnected, connectionError } = useWebSocket()

// Detect if we're on the settings page using Vue Router
const isSettingsPage = computed(() => {
  return route.name === 'settings'
})

// Load dashboard data - router guards ensure we're authenticated
const loadDashboardData = async () => {
  try {
    await loadMetrics()
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

// Handle create actions from tab navigation
const handleCreateAction = (actionType: string) => {
  // Use the global modal system set up by App
  const tryOpenModal = () => {
    if ((window as any).openModal) {
      (window as any).openModal(actionType)
      return true
    } else if ((window as any).vueModal) {
      (window as any).vueModal.open(actionType)
      return true
    }
    return false
  }

  // Try immediately, if not available, wait a bit and try again
  if (!tryOpenModal()) {
    console.warn('Modal system not available yet, retrying...')
    setTimeout(() => {
      if (!tryOpenModal()) {
        console.error('Modal system still not available after retry')
      }
    }, 100)
  }
}

onMounted(() => {
  // Router guards ensure authentication - just load dashboard data
  loadDashboardData()
})
</script>

<style scoped>
/* User layout specific styles */
</style>

<script setup lang="ts">
import { ref } from 'vue'
import Logo from '@/components/ui/Logo.vue'

// Mobile menu state
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}
</script>

<template>
  <div class="min-h-screen bg-base-100">
    <!-- Navigation -->
    <nav class="bg-base-100/50 backdrop-blur-md shadow-sm border-b border-base-300 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Logo :show-text="false" />
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center justify-center flex-1">
            <!-- Centered Navigation Group -->
            <div class="flex items-center space-x-8">
              <a href="/#features" class="text-sm font-medium text-base-content hover:text-primary transition-colors">Product</a>
              
              <!-- Privacy Dropdown -->
              <div class="dropdown dropdown-hover">
                <div tabindex="0" role="button" class="text-sm font-medium text-base-content hover:text-primary transition-colors cursor-pointer">
                  Privacy
                  <svg class="w-3 h-3 inline-block ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <ul tabindex="0" class="dropdown-content z-[1] menu p-0 shadow-lg bg-base-100 rounded-lg w-72 border border-base-300/50">
                  <li>
                    <router-link to="/hidden-risks-email-automation" class="py-4 px-5 hover:bg-primary/5 rounded-none border-b border-base-300/30">
                      <div>
                        <div class="font-medium text-sm leading-tight">Hidden automation risks</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">Why Gmail + Zapier exposes your entire inbox history</div>
                      </div>
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/why-businesses-automate-email-first" class="py-4 px-5 hover:bg-primary/5 rounded-none border-b border-base-300/30">
                      <div>
                        <div class="font-medium text-sm leading-tight">The business case</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">60% cost reduction with faster response times</div>
                      </div>
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/selective-email-access-vs-full-mailbox" class="py-4 px-5 hover:bg-primary/5 rounded-none border-b border-base-300/30">
                      <div>
                        <div class="font-medium text-sm leading-tight">Selective vs full access</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">Technical comparison of two security models</div>
                      </div>
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/how-email-automation-works" class="py-4 px-5 hover:bg-primary/5 rounded-none border-b border-base-300/30">
                      <div>
                        <div class="font-medium text-sm leading-tight">How it works</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">Email to JSON webhook flow with examples</div>
                      </div>
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/built-for-european-privacy" class="py-4 px-5 hover:bg-primary/5 rounded-none border-b border-base-300/30">
                      <div>
                        <div class="font-medium text-sm leading-tight">EU compliance</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">GDPR-ready infrastructure and data sovereignty</div>
                      </div>
                    </router-link>
                  </li>
                  <li>
                    <router-link to="/privacy-policy" class="py-4 px-5 hover:bg-primary/5 rounded-t-none rounded-b-lg">
                      <div>
                        <div class="font-medium text-sm leading-tight">Privacy policy</div>
                        <div class="text-xs text-base-content/60 mt-1 leading-relaxed">Complete data protection and GDPR details</div>
                      </div>
                    </router-link>
                  </li>
                </ul>
              </div>
              
              <router-link to="/pricing" class="text-sm font-medium text-base-content hover:text-primary transition-colors">Pricing</router-link>
              <router-link to="/help" class="text-sm font-medium text-base-content hover:text-primary transition-colors">Docs</router-link>
            </div>
          </div>
          
          <!-- Actions Group -->
          <div class="hidden md:flex items-center">
            <router-link to="/login" class="bg-primary hover:bg-primary/90 text-primary-content px-4 py-2 rounded-md text-sm font-medium transition-colors">
              Start free
            </router-link>
          </div>

          <!-- Mobile Menu Button -->
          <div class="md:hidden flex items-center">
            <button
              @click="toggleMobileMenu"
              class="btn btn-ghost btn-square"
              :class="{ 'btn-active': isMobileMenuOpen }"
            >
              <svg v-if="!isMobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Mobile Menu -->
        <div v-if="isMobileMenuOpen" class="md:hidden border-t border-base-300 py-4">
          <div class="flex flex-col space-y-3">
            <a
              href="/#features"
              @click="closeMobileMenu"
              class="text-base font-medium text-base-content hover:text-primary transition-colors px-2 py-1"
            >
              Product
            </a>
            <router-link
              to="/pricing"
              @click="closeMobileMenu"
              class="text-base font-medium text-base-content hover:text-primary transition-colors px-2 py-1"
            >
              Pricing
            </router-link>
            <router-link
              to="/help"
              @click="closeMobileMenu"
              class="text-base font-medium text-base-content hover:text-primary transition-colors px-2 py-1"
            >
              Docs
            </router-link>
            
            <!-- Mobile Privacy Section -->
            <div class="px-2 py-1">
              <div class="text-base text-base-content/70 font-medium mb-3">Privacy</div>
              <div class="ml-3 space-y-3">
                <router-link
                  to="/hidden-risks-email-automation"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">Hidden automation risks</div>
                  <div class="text-xs text-base-content/60 mt-1">Gmail + Zapier inbox exposure</div>
                </router-link>
                <router-link
                  to="/why-businesses-automate-email-first"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">The business case</div>
                  <div class="text-xs text-base-content/60 mt-1">60% cost reduction</div>
                </router-link>
                <router-link
                  to="/selective-email-access-vs-full-mailbox"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">Selective vs full access</div>
                  <div class="text-xs text-base-content/60 mt-1">Security model comparison</div>
                </router-link>
                <router-link
                  to="/how-email-automation-works"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">How it works</div>
                  <div class="text-xs text-base-content/60 mt-1">Email to JSON webhook flow</div>
                </router-link>
                <router-link
                  to="/built-for-european-privacy"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">EU compliance</div>
                  <div class="text-xs text-base-content/60 mt-1">GDPR infrastructure</div>
                </router-link>
                <router-link
                  to="/privacy-policy"
                  @click="closeMobileMenu"
                  class="block text-sm text-base-content/70 hover:text-primary transition-colors py-1"
                >
                  <div class="font-medium">Privacy policy</div>
                  <div class="text-xs text-base-content/60 mt-1">Complete data protection details</div>
                </router-link>
              </div>
            </div>
            
            <!-- Mobile Actions -->
            <div class="border-t border-base-300/50 pt-3 mt-3">
              <router-link
                to="/login"
                @click="closeMobileMenu"
                class="bg-primary hover:bg-primary/90 text-primary-content px-4 py-2 rounded-md text-base font-medium text-center block mx-2"
              >
                Start free
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-base-100 via-base-200 to-primary/5 border-t border-base-300/50 mt-auto">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div class="col-span-2">
            <Logo size="sm" text-class="text-lg" />
            <p class="mt-4 text-sm text-base-content/80">
              EmailConnect: 100% EU-operated email-to-webhook service.
            </p>
            <div class="mt-4 flex items-center space-x-2">
              <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span class="text-xs text-base-content/60">EU hosted & operated, no Cloud Act, no Patriot Act.</span>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase mb-4">Product</h3>
            <ul class="space-y-3">
              <li>
                <a href="/#features" class="text-sm text-base-content/70 hover:text-primary transition-colors">Features</a>
              </li>
              <li>
                <router-link to="/pricing" class="text-sm text-base-content/70 hover:text-primary transition-colors">Pricing</router-link>
              </li>
              <li>
                <router-link to="/changelog" class="text-sm text-base-content/70 hover:text-primary transition-colors">Changelog</router-link>
              </li>
              <li>
                <a href="/docs" class="text-sm text-base-content/70 hover:text-primary transition-colors">API documentation</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase mb-4">Support</h3>
            <ul class="space-y-3">
              <li>
                <router-link to="/help" class="text-sm text-base-content/70 hover:text-primary transition-colors">Help center</router-link>
              </li>
              <li>
                <a href="mailto:<EMAIL>" class="text-sm text-base-content/70 hover:text-primary transition-colors">Contact</a>
              </li>
              <li>
                <router-link to="/terms-of-service" class="text-sm text-base-content/70 hover:text-primary transition-colors">Terms</router-link>
              </li>
              <li>
                <router-link to="/privacy-policy" class="text-sm text-base-content/70 hover:text-primary transition-colors">Privacy</router-link>
              </li>
            </ul>
          </div>
        </div>
        <div class="mt-8 border-t border-base-300/50 pt-8">
          <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p class="text-sm text-base-content/70">
              © 2025 EmailConnect.eu. All rights reserved.
            </p>
            <div class="flex items-center space-x-4">
              <span class="text-xs text-base-content/50">Made with ❤️ in the EU</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<!-- Guest layout component - for public marketing/info pages -->

<style scoped>
/* Guest layout specific styles */
</style>

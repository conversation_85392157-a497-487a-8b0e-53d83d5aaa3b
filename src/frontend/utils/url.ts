// URL utility functions

export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function getDomainFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return null
  }
}

/**
 * Smart URL truncation - prioritize meaningful parts
 * For webhook URLs, show the domain and the most meaningful part of the path
 * Example: https://webhook.site/6edc5018-0a80-44cf-b31d-7a4274eb8637 
 * becomes: https://webhook.site/...eb8637
 */
export function formatWebhookUrl(url: string, maxLength: number = 50): string {
  if (url.length <= maxLength) return url

  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const path = urlObj.pathname + urlObj.search

    // If just domain + path is still too long, truncate smartly
    const domainAndPath = `${urlObj.protocol}//${domain}${path}`
    if (domainAndPath.length <= maxLength) return domainAndPath

    // For very long domains, show a shortened domain + meaningful path end
    const protocol = urlObj.protocol
    const fullDomainPart = `${protocol}//${domain}`
    
    if (fullDomainPart.length >= maxLength - 10) {
      // Domain is extremely long, show start of domain + end of path
      const domainStart = domain.split('.')[0] // First subdomain part
      const shortDomainPart = `${protocol}//${domainStart}...`
      const availableForPath = maxLength - shortDomainPart.length - 3 // 3 for "..."
      
      if (availableForPath > 0 && path.length > 0) {
        const endPath = path.substring(Math.max(0, path.length - availableForPath))
        return `${shortDomainPart}${endPath}`
      }
      
      // Fallback to simple truncation
      return url.substring(0, maxLength - 3) + '...'
    }

    // Normal case: show full domain + end of path
    const availableForPath = maxLength - fullDomainPart.length - 3 // 3 for "..."
    
    if (availableForPath <= 0) {
      return url.substring(0, maxLength - 3) + '...'
    }

    // Take the last part of the path that fits
    const endPath = path.substring(Math.max(0, path.length - availableForPath))
    return `${fullDomainPart}/...${endPath}`
  } catch {
    // Fallback to simple truncation if URL parsing fails
    return url.length > maxLength ? url.substring(0, maxLength - 3) + '...' : url
  }
}

/**
 * Frontend Logger Utility
 * 
 * Provides consistent logging for frontend with:
 * - Development/production awareness
 * - Sentry integration for errors
 * - Structured logging
 * - Console output control
 */

import * as Sentry from '@sentry/vue';

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerOptions {
  enableInProduction?: boolean;
  sendToSentry?: boolean;
}

class FrontendLogger {
  private isDevelopment = import.meta.env.DEV;
  private enableInProduction = false;

  constructor(options: LoggerOptions = {}) {
    this.enableInProduction = options.enableInProduction || false;
  }

  private shouldLog(level: LogLevel): boolean {
    // Always log errors and warnings
    if (level === 'error' || level === 'warn') {
      return true;
    }
    
    // In development, log everything
    if (this.isDevelopment) {
      return true;
    }
    
    // In production, only log if explicitly enabled
    return this.enableInProduction;
  }

  private formatMessage(level: LogLevel, message: string, data?: any): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
      case 'debug':
        if (this.isDevelopment) {
          console.debug(prefix, message, data || '');
        }
        break;
      
      case 'info':
        console.info(prefix, message, data || '');
        break;
      
      case 'warn':
        console.warn(prefix, message, data || '');
        // Optionally send warnings to Sentry as breadcrumbs
        Sentry.addBreadcrumb({
          message,
          level: 'warning',
          data,
          timestamp: Date.now() / 1000
        });
        break;
      
      case 'error':
        console.error(prefix, message, data || '');
        // Send errors to Sentry
        if (data instanceof Error) {
          Sentry.captureException(data, {
            extra: { message }
          });
        } else {
          Sentry.captureMessage(message, 'error');
          if (data) {
            Sentry.setContext('error_data', data);
          }
        }
        break;
    }
  }

  debug(message: string, data?: any): void {
    this.formatMessage('debug', message, data);
  }

  info(message: string, data?: any): void {
    this.formatMessage('info', message, data);
  }

  warn(message: string, data?: any): void {
    this.formatMessage('warn', message, data);
  }

  error(message: string, error?: Error | any): void {
    this.formatMessage('error', message, error);
  }

  // Group logging for better organization in console
  group(label: string): void {
    if (this.isDevelopment) {
      console.group(label);
    }
  }

  groupEnd(): void {
    if (this.isDevelopment) {
      console.groupEnd();
    }
  }

  // Table logging for structured data
  table(data: any): void {
    if (this.isDevelopment && console.table) {
      console.table(data);
    }
  }

  // Performance timing
  time(label: string): void {
    if (this.isDevelopment) {
      console.time(label);
    }
  }

  timeEnd(label: string): void {
    if (this.isDevelopment) {
      console.timeEnd(label);
    }
  }
}

// Export singleton instance
export const logger = new FrontendLogger({
  enableInProduction: false // Set to true only if needed for debugging production
});

// Export class for custom instances
export { FrontendLogger };
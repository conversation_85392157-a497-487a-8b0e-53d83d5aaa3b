import DOMPurify from 'dompurify'

// Centralized HTML sanitization for frontend rendering
// Configure a balanced default policy suitable for Markdown and CMS content
export function sanitizeHtml(dirty: string): string {
  if (!dirty) return ''

  // Allow typical content elements, disallow scripts/inline handlers by default
  // DOMPurify defaults already remove scripts and event handlers
  return DOMPurify.sanitize(dirty, {
    ALLOWED_URI_REGEXP: /^(?:(?:https?|mailto|tel):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
    // Keep basic formatting, links, images; DOMPurify manages attributes safely
    ADD_ATTR: ['target', 'rel'],
    FORCE_BODY: true
  }) as string
}

// For cases where we want to force links to open in a new tab safely
export function sanitizeHtmlWithSafeLinks(dirty: string): string {
  const clean = sanitizeHtml(dirty)
  // Add rel noopener noreferrer to external links lacking it
  // Note: This is a post-process convenience; DOMPurify ensures safety
  return clean.replace(/<a\s+([^>]*href=\"[^\"]+\"[^>]*)>/gi, (m, attrs) => {
    // Ensure target and rel exist or add them
    let newAttrs = attrs
    if (!/\btarget=\"_blank\"/i.test(newAttrs)) {
      newAttrs += ' target="_blank"'
    }
    if (!/\brel=\"[^\"]*noopener[^\"]*\"/i.test(newAttrs)) {
      if (/\brel=\"[^\"]*\"/i.test(newAttrs)) {
        newAttrs = newAttrs.replace(/rel=\"([^\"]*)\"/i, (mm, rel) => `rel="${rel} noopener noreferrer"`)
      } else {
        newAttrs += ' rel="noopener noreferrer"'
      }
    }
    return `<a ${newAttrs}>`
  })
}


/**
 * Unified API helper for consistent authentication and error handling
 * Uses cookie-based authentication with credentials: 'include'
 */

export interface ApiOptions extends RequestInit {
  headers?: Record<string, string>
  toast?: {
    success?: string | false  // Custom success message or false to disable
    error?: string | false    // Custom error message or false to disable
    showErrors?: boolean      // Global toggle for error toasts (default: true)
  }
}

export interface ApiError extends Error {
  status?: number
  statusText?: string
}

/**
 * Unified API call function with consistent authentication
 * @param url - API endpoint URL
 * @param options - Fetch options (method, body, headers, etc.)
 * @returns Promise<Response>
 */
export const apiCall = async (url: string, options: ApiOptions = {}): Promise<Response> => {
  const { headers = {}, body, ...restOptions } = options

  // Only set Content-Type to JSON if there's a body
  const finalHeaders: Record<string, string> = {
    ...headers
  }
  
  if (body !== undefined) {
    finalHeaders['Content-Type'] = 'application/json'
  }

  const response = await fetch(url, {
    ...restOptions,
    body,
    credentials: 'include', // Always use cookies for web authentication
    headers: finalHeaders
  })

  return response
}

/**
 * API call with automatic JSON parsing and error handling
 * @param url - API endpoint URL
 * @param options - Fetch options
 * @returns Promise<T> - Parsed JSON response
 */
export const apiCallJson = async <T = any>(url: string, options: ApiOptions = {}): Promise<T> => {
  const { toast, ...fetchOptions } = options
  const response = await apiCall(url, fetchOptions)
  
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`
    
    try {
      const errorData = await response.json()
      errorMessage = errorData.message || errorMessage
    } catch {
      // If JSON parsing fails, use the default error message
    }
    
    // Show error toast if enabled
    if (toast?.showErrors !== false && toast?.error !== false) {
      const finalErrorMessage = typeof toast?.error === 'string' ? toast.error : errorMessage
      showToast('error', finalErrorMessage)
    }
    
    const error = new Error(errorMessage) as ApiError
    error.status = response.status
    error.statusText = response.statusText
    throw error
  }

  // Handle 204 No Content or empty body safely
  const contentType = response.headers.get('content-type') || ''
  let data: any = null
  if (response.status === 204 || contentType.indexOf('application/json') === -1) {
    data = {} as any
  } else {
    try {
      data = await response.json()
    } catch {
      data = {} as any
    }
  }
  
  // Show success toast if provided
  if (toast?.success) {
    showToast('success', toast.success)
  }

  return data
}

/**
 * Show toast notification (lazy-loaded to avoid circular dependencies)
 */
let toastFunction: ((type: string, message: string) => void) | null = null

const showToast = (type: 'success' | 'error' | 'warning', message: string) => {
  if (!toastFunction) {
    // Lazy load to avoid circular dependencies
    import('../composables/useToast').then(module => {
      const { addToast } = module.useToast()
      toastFunction = addToast
      toastFunction(type, message)
    }).catch(() => {
      // Fallback to console if toast system unavailable
      console[type === 'error' ? 'error' : 'log'](`[${type.toUpperCase()}] ${message}`)
    })
  } else {
    toastFunction(type, message)
  }
}

/**
 * Convenience methods for common HTTP verbs with toast support
 */
export const api = {
  get: <T = any>(url: string, options: Omit<ApiOptions, 'method'> = {}) =>
    apiCallJson<T>(url, { ...options, method: 'GET' }),

  post: <T = any>(url: string, data?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}) =>
    apiCallJson<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    }),

  put: <T = any>(url: string, data?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}) =>
    apiCallJson<T>(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    }),

  delete: <T = any>(url: string, options: Omit<ApiOptions, 'method'> = {}) =>
    apiCallJson<T>(url, { ...options, method: 'DELETE' }),

  patch: <T = any>(url: string, data?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}) =>
    apiCallJson<T>(url, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    }),

  // Raw fetch for special cases (file downloads, etc.)
  getRaw: (url: string, options: Omit<ApiOptions, 'method'> = {}) => 
    apiCall(url, { ...options, method: 'GET' })
}

// Usage examples:
//
// Basic usage (no toasts):
// const data = await api.get('/api/users')
//
// With automatic success toast:
// const user = await api.post('/api/users', userData, { 
//   toast: { success: 'User created successfully!' } 
// })
//
// With custom error message:
// const data = await api.delete('/api/users/123', { 
//   toast: { 
//     success: 'User deleted!',
//     error: 'Failed to delete user. Please try again.' 
//   } 
// })
//
// Disable error toasts:
// const data = await api.get('/api/auth/check', { 
//   toast: { showErrors: false } 
// })
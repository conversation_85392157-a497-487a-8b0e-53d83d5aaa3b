<template>
  <!-- Reserve space while loading to prevent layout shift -->
  <div v-if="loading || showBanner" class="relative isolate flex items-center gap-x-6 overflow-hidden bg-gray-50 px-6 py-2.5 sm:px-3.5 sm:before:flex-1 dark:bg-gray-800/50 dark:after:pointer-events-none dark:after:absolute dark:after:inset-x-0 dark:after:bottom-0 dark:after:h-px dark:after:bg-white/10">
    <div aria-hidden="true" class="absolute top-1/2 left-[max(-7rem,calc(50%-52rem))] -z-10 -translate-y-1/2 transform-gpu blur-2xl">
      <div style="clip-path: polygon(74.8% 41.9%, 97.2% 73.2%, 100% 34.9%, 92.5% 0.4%, 87.5% 0%, 75% 28.6%, 58.5% 54.6%, 50.1% 56.8%, 46.9% 44%, 48.3% 17.4%, 24.7% 53.9%, 0% 27.9%, 11.9% 74.2%, 24.9% 54.1%, 68.6% 100%, 74.8% 41.9%)" class="aspect-577/310 w-144.25 bg-linear-to-r from-[#ff80b5] to-[#9089fc] opacity-30 dark:opacity-40"></div>
    </div>
    <div aria-hidden="true" class="absolute top-1/2 left-[max(45rem,calc(50%+8rem))] -z-10 -translate-y-1/2 transform-gpu blur-2xl">
      <div style="clip-path: polygon(74.8% 41.9%, 97.2% 73.2%, 100% 34.9%, 92.5% 0.4%, 87.5% 0%, 75% 28.6%, 58.5% 54.6%, 50.1% 56.8%, 46.9% 44%, 48.3% 17.4%, 24.7% 53.9%, 0% 27.9%, 11.9% 74.2%, 24.9% 54.1%, 68.6% 100%, 74.8% 41.9%)" class="aspect-577/310 w-144.25 bg-linear-to-r from-[#ff80b5] to-[#9089fc] opacity-30 dark:opacity-40"></div>
    </div>

    <!-- Skeleton while loading -->
    <div v-if="loading" class="mx-auto w-full max-w-7xl flex items-center justify-center gap-x-4 gap-y-2">
      <div class="skeleton h-5 w-64"></div>
      <div class="skeleton h-7 w-28 rounded-full"></div>
    </div>

    <!-- Content -->
    <template v-else>
      <div class="mx-auto w-full max-w-7xl flex justify-center">
        <!-- Inner compact layout -->
        <div class="flex w-full items-center gap-x-4 sm:justify-center">
          <!-- Text -->
          <p class="flex-1 text-sm/6 text-gray-900 dark:text-gray-100 sm:flex-none sm:text-center">
            <strong class="font-semibold">
              <span v-if="announcement?.icon" aria-hidden="true" class="mr-1">{{ announcement?.icon }}</span>
              {{ announcement?.title }}
            </strong>
            <svg viewBox="0 0 2 2" aria-hidden="true" class="mx-2 inline size-0.5 fill-current">
              <circle r="1" cx="1" cy="1" />
            </svg>
            <span class="opacity-90">{{ announcement?.description }}</span>
          </p>

          <!-- CTA -->
          <a v-if="announcement?.ctaUrl && announcement?.ctaLabel"
            :href="redirectHref"
            :target="isExternal ? '_blank' : undefined"
            :rel="isExternal ? 'noopener noreferrer' : undefined"
            @click="trackClick"
            class="shrink-0 rounded-full bg-gray-900 px-3.5 py-1 text-sm font-semibold text-white shadow-xs hover:bg-gray-700 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-900 dark:bg-white/10 dark:inset-ring-white/20 dark:hover:bg-white/15 dark:focus-visible:outline-white">
            {{ announcement?.ctaLabel }} <span aria-hidden="true">&rarr;</span>
          </a>

          <!-- Dismiss -->
          <button type="button" class="-m-3 p-3 focus-visible:-outline-offset-4" @click="dismiss">
            <span class="sr-only">Dismiss</span>
            <svg viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="size-5 text-gray-900 dark:text-gray-100">
              <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
            </svg>
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { fetchAnnouncement, type Announcement } from '../../composables/usePublicContent'
import usePlausible from '../../composables/usePlausible'

const announcement = ref<Announcement | null>(null)
const dismissed = ref(false)
const loading = ref(true)
const { trackEvent } = usePlausible()

const redirectHref = computed(() => {
  if (!announcement.value?.ctaUrl) return '#'
  const u = new URL('/api/public/redirect', window.location.origin)
  u.searchParams.set('to', announcement.value.ctaUrl)
  u.searchParams.set('label', announcement.value.ctaLabel || '')
  u.searchParams.set('source', 'announcement')
  return u.toString()
})

const isExternal = computed(() => {
  const raw = announcement.value?.ctaUrl || ''
  if (!raw) return false
  let s = raw.trim()
  try { s = decodeURIComponent(s) } catch {}
  // If relative path -> internal
  if (s.startsWith('/')) return false
  if (!/^[a-zA-Z][a-zA-Z0-9+.-]*:/.test(s)) {
    s = 'https://' + s.replace(/^\/+/, '')
  }
  try {
    const url = new URL(s)
    return !url.hostname.endsWith('emailconnect.eu')
  } catch {
    return false
  }
})

const showBanner = computed(() => !!announcement.value && !dismissed.value)

function trackClick() {
  if (!announcement.value) return
  trackEvent('Announcement CTA Click', {
    props: {
      label: announcement.value.ctaLabel || '',
      url: announcement.value.ctaUrl || ''
    }
  })
}

function dismiss() {
  dismissed.value = true
}

onMounted(async () => {
  try {
    announcement.value = await fetchAnnouncement()
  } finally {
    loading.value = false
  }
})
</script>


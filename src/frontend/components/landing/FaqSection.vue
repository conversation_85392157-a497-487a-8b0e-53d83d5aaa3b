<template>
  <div class="bg-base-100">
    <div class="max-w-4xl mx-auto px-6 py-16 lg:py-24">
      <h2 class="text-3xl lg:text-4xl font-bold text-center lg:text-left mb-12 lg:mb-16">
        Frequently asked questions
      </h2>
      
      <div v-if="loading" class="text-center opacity-70">Loading...</div>
      
      <dl v-else class="divide-y divide-base-300">
        <div 
          v-for="(item, idx) in faqs" 
          :key="idx"
          class="py-6 lg:py-8 first:pt-0 last:pb-0"
        >
          <div class="lg:grid lg:grid-cols-12 lg:gap-8">
            <!-- Question -->
            <dt class="text-base lg:text-lg font-semibold lg:col-span-5">
              {{ item.question }}
            </dt>
            
            <!-- Answer -->
            <dd class="mt-3 lg:col-span-7 lg:mt-0">
              <div class="text-base-content/70">
                <!-- Display truncated or full content based on expansion state -->
                <div 
                  class="prose prose-sm max-w-none faq-content"
                  v-html="expandedItems.has(idx) ? parseMarkdown(item.answer) : parseMarkdown(getTruncatedAnswer(item.answer))"
                ></div>
                
                <!-- Expand/collapse button and help link in same line -->
                <div class="flex items-center gap-4 mt-3">
                  <button 
                    v-if="shouldTruncate(item.answer)"
                    @click="toggleExpand(idx, item.question)"
                    class="text-primary hover:text-primary-focus text-sm font-medium transition-colors"
                  >
                    {{ expandedItems.has(idx) ? 'Show less' : 'Read more' }}
                  </button>
                  
                  <a 
                    v-if="item.helpUrl" 
                    :href="item.helpUrl" 
                    @click="trackHelpClick(item.question)"
                    class="text-primary hover:text-primary-focus text-sm font-medium transition-colors"
                  >
                    Learn more →
                  </a>
                </div>
              </div>
            </dd>
          </div>
        </div>
      </dl>
      
      <!-- Optional: Add a CTA or help section at the bottom -->
      <div class="mt-12 pt-8 border-t border-base-300 text-center lg:text-left">
        <p class="text-base-content/70">
          Still have questions? 
          <a href="mailto:<EMAIL>" class="text-primary hover:text-primary-focus font-medium">
            Contact our support team
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { marked } from 'marked'
import { fetchFaqs, type FaqItem } from '../../composables/usePublicContent'
import { usePlausible } from '../../composables/usePlausible'

const faqs = ref<FaqItem[]>([])
const loading = ref(true)
const expandedItems = ref(new Set<number>())

// Configuration
const TRUNCATE_LENGTH = 200 // Increased for better context
const MIN_EXTRA_LENGTH = 50 // Minimum extra content to justify truncation

const { trackEvent } = usePlausible()

// Configure marked for safe rendering
marked.setOptions({
  breaks: true,
  gfm: true
})

const parseMarkdown = (text: string): string => {
  try {
    // Configure marked to handle code blocks with language hints
    (marked.setOptions as any)({
      breaks: true,
      gfm: true,
      highlight: function(code: string, lang: string) {
        // For JSON, add some basic syntax highlighting
        if (lang === 'json') {
          // Highlight strings (including keys)
          code = code.replace(/"([^"]+)":/g, '<span class="json-key">"$1"</span>:')
          code = code.replace(/: "([^"]+)"/g, ': <span class="json-string">"$1"</span>')
          // Highlight numbers
          code = code.replace(/: (\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
          // Highlight booleans and null
          code = code.replace(/: (true|false|null)/g, ': <span class="json-literal">$1</span>')
        }
        return code
      },
      langPrefix: 'language-'
    })
    
    let html = marked.parse(text) as string
    
    // Post-process to add better code block formatting
    // Handle code blocks with language hints (e.g., ```json)
    html = html.replace(/<pre><code class="language-(\w+)">/g, '<pre class="language-$1" data-lang="$1"><code class="hljs language-$1">')
    
    // Handle code blocks without language hints
    html = html.replace(/<pre><code>/g, '<pre data-lang="code"><code class="hljs">')
    
    // For inline code, detect patterns
    // Highlight JSON-like structures in inline code
    html = html.replace(/<code>(\{[^}]*\})<\/code>/g, '<code class="code-json">$1</code>')
    
    // Highlight URLs in inline code
    html = html.replace(/<code>(https?:\/\/[^<]*)<\/code>/g, '<code class="code-url">$1</code>')
    
    // Highlight common HTTP methods in inline code
    html = html.replace(/<code>(GET|POST|PUT|DELETE|PATCH)<\/code>/g, '<code class="code-method">$1</code>')
    
    return html
  } catch (error) {
    console.error('Failed to parse markdown:', error)
    // Fallback: return text with basic line breaks
    return text.replace(/\n/g, '<br>')
  }
}

const shouldTruncate = (answer: string): boolean => {
  return answer.length > (TRUNCATE_LENGTH + MIN_EXTRA_LENGTH)
}

const getTruncatedAnswer = (answer: string): string => {
  if (!shouldTruncate(answer)) {
    return answer
  }
  
  // Find a good break point (end of sentence or word)
  let truncateAt = TRUNCATE_LENGTH
  
  // Try to find end of sentence
  const sentenceEnd = answer.lastIndexOf('.', TRUNCATE_LENGTH)
  if (sentenceEnd > TRUNCATE_LENGTH * 0.8) {
    truncateAt = sentenceEnd + 1
  } else {
    // Find end of word
    const wordEnd = answer.lastIndexOf(' ', TRUNCATE_LENGTH)
    if (wordEnd > TRUNCATE_LENGTH * 0.8) {
      truncateAt = wordEnd
    }
  }
  
  return answer.substring(0, truncateAt).trim() + '...'
}

const toggleExpand = (idx: number, question: string) => {
  if (expandedItems.value.has(idx)) {
    expandedItems.value.delete(idx)
    
    // Track collapse event
    trackEvent('FAQ Collapsed', {
      props: {
        question: question.substring(0, 100), // Limit question length for analytics
        index: idx
      }
    })
  } else {
    expandedItems.value.add(idx)
    
    // Track expand event - this is the main indicator of interest
    trackEvent('FAQ Expanded', {
      props: {
        question: question.substring(0, 100), // Limit question length for analytics
        index: idx
      }
    })
  }
}

const trackHelpClick = (question: string) => {
  trackEvent('FAQ Help Link Clicked', {
    props: {
      question: question.substring(0, 100)
    }
  })
}

onMounted(async () => {
  faqs.value = await fetchFaqs()
  loading.value = false
  
  // Track that FAQs were loaded (optional)
  if (faqs.value.length > 0) {
    trackEvent('FAQ Section Viewed', {
      props: {
        count: faqs.value.length
      }
    })
  }
})
</script>

<style scoped>
/* Prose customization for FAQ answers */
.faq-content :deep(p) {
  font-size: 1rem;
  line-height: 1.75;
  margin-bottom: 0.75rem;
}

.faq-content :deep(p:last-child) {
  margin-bottom: 0;
}

.faq-content :deep(ul),
.faq-content :deep(ol) {
  margin: 0.75rem 0;
}

.faq-content :deep(ul) {
  list-style-type: disc;
  list-style-position: inside;
}

.faq-content :deep(ol) {
  list-style-type: decimal;
  list-style-position: inside;
}

.faq-content :deep(li) {
  font-size: 1rem;
  line-height: 1.75;
  margin-bottom: 0.25rem;
}

.faq-content :deep(strong) {
  font-weight: 600;
  color: var(--fallback-bc, oklch(var(--bc)));
}

/* Inline code styling - using DaisyUI base colors */
.faq-content :deep(:not(pre) > code) {
  background: oklch(var(--b3));
  border: 1px solid oklch(var(--bc) / 0.2);
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.85rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-weight: 500;
  color: oklch(var(--p));
  white-space: nowrap;
  display: inline-block;
  line-height: 1.4;
  box-shadow: 0 1px 3px oklch(var(--bc) / 0.1);
}

/* Special code type highlighting with DaisyUI colors */
.faq-content :deep(code.code-json) {
  background: oklch(var(--s) / 0.15);
  border-color: oklch(var(--s) / 0.3);
  color: oklch(var(--sf));
  box-shadow: 0 0 12px oklch(var(--s) / 0.2);
}

.faq-content :deep(code.code-url) {
  background: oklch(var(--a) / 0.15);
  border-color: oklch(var(--a) / 0.3);
  color: oklch(var(--af));
  word-break: break-all;
  white-space: normal;
  box-shadow: 0 0 12px oklch(var(--a) / 0.2);
}

.faq-content :deep(code.code-method) {
  background: oklch(var(--wa) / 0.15);
  border-color: oklch(var(--wa) / 0.3);
  color: oklch(var(--waf));
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  box-shadow: 0 0 12px oklch(var(--wa) / 0.2);
}

.faq-content :deep(pre) {
  background: oklch(var(--b2));
  border: 1px solid oklch(var(--bc) / 0.2);
  padding: 1.25rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1rem 0;
  position: relative;
  box-shadow: 
    0 4px 12px oklch(var(--bc) / 0.15),
    0 0 24px oklch(var(--p) / 0.1),
    inset 0 1px 0 oklch(var(--bc) / 0.05);
}

/* Add a subtle top border accent for code blocks */
.faq-content :deep(pre)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    oklch(var(--p)) 0%, 
    oklch(var(--s)) 100%);
  border-radius: 0.75rem 0.75rem 0 0;
  opacity: 0.7;
}

/* Add language label for code blocks */
.faq-content :deep(pre[data-lang])::after {
  content: attr(data-lang);
  position: absolute;
  top: 0.75rem;
  right: 1rem;
  color: oklch(var(--bc) / 0.5);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05rem;
  background: oklch(var(--b2) / 0.8);
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-family: system-ui, -apple-system, sans-serif;
}

/* Special styling for JSON code blocks */
.faq-content :deep(pre.language-json) {
  background: linear-gradient(135deg,
    oklch(var(--b2)) 0%,
    oklch(var(--s) / 0.08) 100%);
  border-color: oklch(var(--s) / 0.3);
}

.faq-content :deep(pre.language-json)::before {
  background: linear-gradient(90deg, 
    oklch(var(--s)) 0%, 
    oklch(var(--sf)) 100%);
}

/* Special styling for JavaScript/TypeScript code blocks */
.faq-content :deep(pre.language-javascript),
.faq-content :deep(pre.language-typescript),
.faq-content :deep(pre.language-js),
.faq-content :deep(pre.language-ts) {
  background: linear-gradient(135deg,
    oklch(var(--b2)) 0%,
    oklch(var(--wa) / 0.08) 100%);
  border-color: oklch(var(--wa) / 0.3);
}

/* Special styling for bash/shell code blocks */
.faq-content :deep(pre.language-bash),
.faq-content :deep(pre.language-shell),
.faq-content :deep(pre.language-sh) {
  background: linear-gradient(135deg,
    oklch(var(--b2)) 0%,
    oklch(var(--a) / 0.08) 100%);
  border-color: oklch(var(--a) / 0.3);
}

.faq-content :deep(pre code) {
  background: transparent;
  border: none;
  padding: 0;
  font-size: 0.875rem;
  line-height: 1.7;
  color: oklch(var(--bc));
  white-space: pre;
  display: block;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* JSON Syntax Highlighting */
.faq-content :deep(.json-key) {
  color: oklch(var(--p));
  font-weight: 600;
}

.faq-content :deep(.json-string) {
  color: oklch(var(--s));
}

.faq-content :deep(.json-number) {
  color: oklch(var(--wa));
}

.faq-content :deep(.json-literal) {
  color: oklch(var(--a));
  font-weight: 600;
}

/* Scrollbar styling for code blocks */
.faq-content :deep(pre)::-webkit-scrollbar {
  height: 6px;
}

.faq-content :deep(pre)::-webkit-scrollbar-track {
  background: oklch(var(--b3) / 0.3);
  border-radius: 3px;
}

.faq-content :deep(pre)::-webkit-scrollbar-thumb {
  background: oklch(var(--bc) / 0.2);
  border-radius: 3px;
}

.faq-content :deep(pre)::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--bc) / 0.3);
}

.faq-content :deep(a) {
  color: var(--fallback-p, oklch(var(--p)));
  text-decoration: underline;
  text-underline-offset: 2px;
}

.faq-content :deep(a:hover) {
  color: var(--fallback-pf, oklch(var(--pf)));
}

.faq-content :deep(blockquote) {
  border-left: 4px solid var(--fallback-b3, oklch(var(--b3)));
  padding-left: 1rem;
  margin: 0.75rem 0;
  font-style: italic;
}

/* Smooth transitions */
button, a {
  transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .faq-content :deep(p),
  .faq-content :deep(li) {
    font-size: 0.875rem;
  }
}
</style>
<template>
  <div class="w-full max-w-md mx-auto px-4">
    <div class="card bg-base-100 shadow-2xl border border-base-300/50 backdrop-blur-sm">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">
            Reset your password
          </h2>
          <p class="text-base-content/70">
            Enter your email and we'll send you a reset link
          </p>
        </div>

        <!-- Success Alert -->
        <div v-if="emailSent" class="alert alert-success rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Check your email for the password reset link!</span>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Reset Form -->
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div class="form-control">
            <label for="email" class="label">
              <span class="label-text font-medium">Email address</span>
            </label>
            <input
              id="email"
              v-model="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="input input-bordered w-full focus:input-primary transition-colors"
              placeholder="Enter your email address"
              :disabled="loading || emailSent"
            >
            <label class="label">
              <span class="label-text-alt text-base-content/60">
                We'll send a password reset link to this email
              </span>
            </label>
          </div>

          <div class="pt-2">
            <button
              type="submit"
              class="btn btn-primary w-full text-base font-medium"
              :class="{ 'loading': loading }"
              :disabled="loading || emailSent"
            >
              {{ loading ? 'Sending...' : emailSent ? 'Email sent' : 'Send reset link' }}
            </button>
          </div>
        </form>

        <!-- Back to Login -->
        <div class="text-center mt-8 pt-6 border-t border-base-300/50">
          <p class="text-base-content/70">
            Remember your password?
            <router-link to="/login" class="link link-primary hover:link-primary/80 transition-colors ml-1">
              Back to login
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { requestPasswordReset } from '@/lib/auth-client'

const email = ref('')
const loading = ref(false)
const error = ref('')
const emailSent = ref(false)

async function handleSubmit() {
  loading.value = true
  error.value = ''
  emailSent.value = false
  
  try {
    await requestPasswordReset(email.value)
    emailSent.value = true
    // Clear form after 10 seconds
    setTimeout(() => {
      email.value = ''
      emailSent.value = false
    }, 10000)
  } catch (err) {
    console.error('Password reset error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>
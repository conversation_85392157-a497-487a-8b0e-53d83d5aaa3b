<template>
  <div class="w-full max-w-md mx-auto px-4">
    <div class="card bg-base-100 shadow-2xl border border-base-300/50 backdrop-blur-sm">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">
            Set new password
          </h2>
          <p class="text-base-content/70">
            Choose a strong password for your account
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Reset Form -->
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-5">
            <div class="form-control">
              <label for="password" class="label">
                <span class="label-text font-medium">New password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter new password"
                :disabled="loading"
                @input="validatePassword"
              >
              <label class="label">
                <span class="label-text-alt" :class="passwordStrengthClass">
                  {{ passwordStrengthText }}
                </span>
              </label>
            </div>

            <div class="form-control">
              <label for="confirmPassword" class="label">
                <span class="label-text font-medium">Confirm password</span>
              </label>
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                name="confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                :class="{ 'input-error': form.confirmPassword && form.password !== form.confirmPassword }"
                placeholder="Confirm new password"
                :disabled="loading"
              >
              <label v-if="form.confirmPassword && form.password !== form.confirmPassword" class="label">
                <span class="label-text-alt text-error">Passwords do not match</span>
              </label>
            </div>
          </div>

          <!-- Password Requirements -->
          <div class="rounded-lg bg-base-200 p-4">
            <p class="text-sm font-medium mb-2">Password requirements:</p>
            <ul class="text-sm space-y-1 text-base-content/70">
              <li :class="{ 'text-success': hasMinLength }">
                <span class="inline-block w-4">{{ hasMinLength ? '✓' : '○' }}</span>
                At least 8 characters
              </li>
              <li :class="{ 'text-success': hasUppercase }">
                <span class="inline-block w-4">{{ hasUppercase ? '✓' : '○' }}</span>
                One uppercase letter
              </li>
              <li :class="{ 'text-success': hasLowercase }">
                <span class="inline-block w-4">{{ hasLowercase ? '✓' : '○' }}</span>
                One lowercase letter
              </li>
              <li :class="{ 'text-success': hasNumber }">
                <span class="inline-block w-4">{{ hasNumber ? '✓' : '○' }}</span>
                One number
              </li>
            </ul>
          </div>

          <div class="pt-2">
            <button
              type="submit"
              class="btn btn-primary w-full text-base font-medium"
              :class="{ 'loading': loading }"
              :disabled="loading || !isValidPassword || form.password !== form.confirmPassword"
            >
              {{ loading ? 'Resetting...' : 'Reset password' }}
            </button>
          </div>
        </form>

        <!-- Back to Login -->
        <div class="text-center mt-8 pt-6 border-t border-base-300/50">
          <p class="text-base-content/70">
            <router-link to="/login" class="link link-primary hover:link-primary/80 transition-colors">
              Back to login
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { resetPassword } from '@/lib/auth-client'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const error = ref('')

const form = reactive({
  password: '',
  confirmPassword: ''
})

// Password validation
const hasMinLength = computed(() => form.password.length >= 8)
const hasUppercase = computed(() => /[A-Z]/.test(form.password))
const hasLowercase = computed(() => /[a-z]/.test(form.password))
const hasNumber = computed(() => /\d/.test(form.password))
const isValidPassword = computed(() => 
  hasMinLength.value && hasUppercase.value && hasLowercase.value && hasNumber.value
)

const passwordStrengthClass = computed(() => {
  if (!form.password) return ''
  if (!hasMinLength.value) return 'text-error'
  const checks = [hasUppercase.value, hasLowercase.value, hasNumber.value].filter(Boolean).length
  if (checks === 3) return 'text-success'
  if (checks === 2) return 'text-warning'
  return 'text-error'
})

const passwordStrengthText = computed(() => {
  if (!form.password) return ''
  if (!hasMinLength.value) return 'Too short'
  const checks = [hasUppercase.value, hasLowercase.value, hasNumber.value].filter(Boolean).length
  if (checks === 3) return 'Strong password'
  if (checks === 2) return 'Good password'
  return 'Weak password'
})

function validatePassword() {
  // Trigger reactivity
}

async function handleSubmit() {
  if (!isValidPassword.value || form.password !== form.confirmPassword) {
    error.value = 'Please meet all password requirements'
    return
  }

  loading.value = true
  error.value = ''
  
  try {
    // Get token from URL query params
    const token = route.query.token as string
    
    if (!token) {
      error.value = 'Invalid or missing reset token'
      return
    }

    const { error: resetError } = await resetPassword({
      newPassword: form.password,
      token
    })
    
    if (resetError) {
      error.value = resetError.message || 'Failed to reset password'
      return
    }
    
    // Success - redirect to login with success message
    await router.push({
      path: '/login',
      query: { message: 'Password reset successful. Please login with your new password.' }
    })
  } catch (err) {
    console.error('Password reset error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // Check if we have a token in the URL
  if (!route.query.token) {
    error.value = 'Invalid password reset link. Please request a new one.'
  }
})
</script>
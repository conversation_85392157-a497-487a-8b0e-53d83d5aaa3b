<template>
  <div class="w-full max-w-md mx-auto px-4">
    <!-- Main Card -->
    <div class="card bg-base-100 shadow-2xl border border-base-300/50 backdrop-blur-sm">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">
            {{ isRegisterMode ? 'Create your account' : 'Welcome back' }}
          </h2>
          <p class="text-base-content/70">
            {{ isRegisterMode 
              ? 'Get started with your free account'
              : 'Sign in to your account to continue'
            }}
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Success Alert for Magic Link -->
        <div v-if="magicLinkSent" class="alert alert-success rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Check your email for the sign-in link!</span>
        </div>

        <!-- Success Alert for General Messages -->
        <div v-if="successMessage" class="alert alert-success rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ successMessage }}</span>
        </div>

        <!-- Two-Factor Authentication Form -->
        <div v-if="show2FA" class="space-y-6">
          <div class="text-center mb-6">
            <h3 class="text-xl font-semibold text-base-content mb-2">
              Two-factor authentication
            </h3>
            <p class="text-base-content/70">
              {{ useBackupCode 
                ? 'Enter one of your backup recovery codes' 
                : 'Enter the 6-digit code from your authenticator app' 
              }}
            </p>
            <p class="text-sm text-base-content/50 mt-1">
              Signing in as: {{ savedEmail }}
            </p>
          </div>

          <form @submit.prevent="verify2FA" class="space-y-6">
            <div class="form-control">
              <label for="totp-code" class="label">
                <span class="label-text font-medium">
                  {{ useBackupCode ? 'Backup code' : 'Verification code' }}
                </span>
              </label>
              <input
                id="totp-code"
                v-model="twoFactorCode"
                autofocus
                name="totp-code"
                type="text"
                :inputmode="useBackupCode ? 'text' : 'numeric'"
                :pattern="useBackupCode ? undefined : '[0-9]*'"
                :maxlength="useBackupCode ? undefined : '6'"
                :autocomplete="useBackupCode ? 'off' : 'one-time-code'"
                required
                class="input input-bordered w-full text-center text-xl tracking-wider font-mono focus:input-primary transition-colors"
                :placeholder="useBackupCode ? 'Enter backup code' : '000000'"
                :disabled="verifying2FA"
              >
            </div>

            <div class="text-center">
              <button
                type="button"
                @click="toggleBackupCode"
                class="btn btn-ghost btn-sm"
                :disabled="verifying2FA"
              >
                {{ useBackupCode 
                  ? 'Use authenticator code instead' 
                  : 'Use backup code instead' 
                }}
              </button>
            </div>

            <div class="flex gap-3">
              <button
                type="button"
                @click="cancel2FA"
                class="btn btn-ghost flex-1"
                :disabled="verifying2FA"
              >
                Back
              </button>
              <button
                type="submit"
                class="btn btn-primary flex-1"
                :disabled="verifying2FA || (!useBackupCode && twoFactorCode.length !== 6) || (useBackupCode && !twoFactorCode.trim())"
              >
                <span v-if="verifying2FA" class="loading loading-spinner loading-sm"></span>
                <span v-else>Verify</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Main Auth Form - Progressive Disclosure -->
        <div v-if="!show2FA">
          <form @submit.prevent="handleContinue" class="space-y-6">
            <!-- Email Field (always visible) -->
            <div class="form-control">
              <label for="email" class="label">
                <span class="label-text font-medium">Email address</span>
              </label>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="email"
                autocomplete="email"
                required
                autofocus
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your email address"
                :disabled="loading"
              >
              
              <!-- Use Password Link - always visible when not using password -->
              <div v-if="!showPasswordField" class="label">
                <span class="label-text-alt"></span>
                <button 
                  type="button" 
                  @click="showPasswordField = true" 
                  class="label-text-alt link link-primary hover:link-primary/80 transition-colors"
                >
                  Use password instead
                </button>
              </div>
            </div>

            <!-- Password Field (progressive disclosure) -->
            <div v-if="showPasswordField" class="form-control">
              <label for="password" class="label">
                <span class="label-text font-medium">Password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your password"
                :disabled="loading"
              >
            </div>

            <!-- Compact options row -->
            <div class="flex items-center justify-between text-sm py-1">
              <label class="flex items-center cursor-pointer gap-2">
                <input
                  id="remember-me"
                  v-model="form.rememberMe"
                  name="remember-me"
                  type="checkbox"
                  class="checkbox checkbox-primary checkbox-xs"
                  :disabled="loading"
                >
                <span class="text-base-content/70">Remember me</span>
              </label>

              <!-- Forgot password (only show with password field) -->
              <router-link 
                v-if="showPasswordField"
                to="/auth/forgot-password" 
                class="link link-primary hover:link-primary/80 transition-colors"
              >
                Forgot password?
              </router-link>
            </div>

            <!-- Continue Button -->
            <div class="pt-2">
              <button
                type="submit"
                class="btn btn-primary w-full text-base font-medium"
                :class="{ 'loading': loading }"
                :disabled="loading || !form.email"
              >
                <span v-if="loading">
                  {{ showPasswordField ? 'Signing in...' : 'Sending link...' }}
                </span>
                <span v-else>
                  {{ showPasswordField ? (isRegisterMode ? 'Create account' : 'Sign in') : 'Continue' }}
                </span>
              </button>
            </div>

            <!-- Helper text for magic link -->
            <div v-if="!showPasswordField" class="text-center">
              <p class="text-sm text-base-content/60">
                We'll email you a secure link to sign in instantly
              </p>
            </div>
          </form>

          <!-- Social Login Divider -->
          <div class="divider my-8">OR</div>

          <!-- GitHub OAuth Button -->
          <button
            @click="handleGitHubLogin"
            type="button"
            class="btn btn-outline w-full text-base font-medium gap-3"
            :disabled="loading"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd" />
            </svg>
            Continue with GitHub
          </button>

          <!-- Login/Register Toggle -->
          <div class="text-center mt-8 pt-6 border-t border-base-300/50">
            <p class="text-base-content/70">
              {{ isRegisterMode 
                ? 'Already have an account?' 
                : "Don't have an account?"
              }}
              <button 
                type="button"
                @click="toggleMode" 
                class="link link-primary hover:link-primary/80 transition-colors ml-1"
              >
                {{ isRegisterMode ? 'Sign in' : 'Sign up for free' }}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { signIn, signUp, signInWithGitHub, signInWithMagicLink } from '@/lib/auth-client'
import { usePlausible } from '@/composables/usePlausible'
import { useSEO } from '@/composables/useSEO'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const error = ref('')
const successMessage = ref('')

// Plausible Analytics
const { trackUserAction } = usePlausible()
const magicLinkSent = ref(false)
const show2FA = ref(false)
const twoFactorCode = ref('')
const verifying2FA = ref(false)
const savedEmail = ref('') // Store email for 2FA step
const useBackupCode = ref(false) // Toggle between TOTP and backup code
const showPasswordField = ref(false) // Progressive disclosure
const isRegisterMode = ref(false) // Toggle between login/register

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

async function handleContinue() {
  if (!form.email) return
  
  loading.value = true
  error.value = ''
  
  try {
    if (showPasswordField.value) {
      // Password-based auth (login or register)
      if (isRegisterMode.value) {
        await handleRegister()
      } else {
        await handlePasswordLogin()
      }
    } else {
      // Magic link auth
      await handleMagicLink()
    }
  } catch (err) {
    console.error('Auth error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}

async function handlePasswordLogin() {
  const result = await signIn.email({
    email: form.email,
    password: form.password,
    rememberMe: form.rememberMe,
  })
  
  // Check for 2FA redirect
  if ((result.data as any)?.twoFactorRedirect) {
    savedEmail.value = form.email
    show2FA.value = true
    loading.value = false
    return
  }
  
  if (result.error) {
    // If login fails and we're not in register mode, suggest registering
    if (result.error.message?.includes('User not found') && !isRegisterMode.value) {
      error.value = "Account not found. Would you like to create one instead?"
      // Track failed login attempt
      trackUserAction.loginFailed('User not found')
      isRegisterMode.value = true
      return
    }
    error.value = result.error.message || 'Invalid email or password'
    // Track failed login attempt with reason
    trackUserAction.loginFailed(result.error.message || 'Invalid email or password')
    return
  }
  
  // Success - track and redirect to domains
  trackUserAction.login('email')
  await redirectAfterSuccess()
}

async function handleRegister() {
  const result = await signUp.email({
    email: form.email,
    password: form.password,
    name: form.email.split('@')[0], // Use email prefix as default name
  })
  
  if (result.error) {
    // If user already exists, switch to login mode
    if (result.error.message?.includes('User already exists')) {
      error.value = 'Account already exists. Please sign in instead.'
      // Track failed registration
      trackUserAction.registerFailed('User already exists')
      isRegisterMode.value = false
      return
    }
    error.value = result.error.message || 'Failed to create account'
    // Track failed registration with reason
    trackUserAction.registerFailed(result.error.message || 'Failed to create account')
    return
  }
  
  // Success - track and redirect to domains
  trackUserAction.register('email')
  await redirectAfterSuccess()
}

async function handleMagicLink() {
  const response = await signInWithMagicLink(form.email)
  
  if (!response.ok) {
    const errorData = await response.json()
    error.value = errorData.message || 'Failed to send sign-in link'
    // Track failed magic link attempt as login failed
    trackUserAction.loginFailed(error.value)
    return
  }
  
  // Track magic link attempt as a login (email method)
  trackUserAction.login('email')
  magicLinkSent.value = true
  // Clear success message after 10 seconds
  setTimeout(() => {
    magicLinkSent.value = false
  }, 10000)
}

async function redirectAfterSuccess() {
  // Refresh auth state to include new session
  const { useAuth } = await import('@/composables/useAuth')
  const { checkAuth } = useAuth()
  await checkAuth(true) // Force refresh
  
  // Redirect to domains
  await router.push('/domains')
}

async function verify2FA() {
  if (!twoFactorCode.value) {
    error.value = useBackupCode.value ? 'Please enter your backup code' : 'Please enter a valid 6-digit code'
    return
  }
  
  if (!useBackupCode.value && twoFactorCode.value.length !== 6) {
    error.value = 'Please enter a valid 6-digit code'
    return
  }
  
  verifying2FA.value = true
  error.value = ''
  
  try {
    // Use different endpoint for backup codes vs TOTP codes
    const endpoint = useBackupCode.value 
      ? '/api/auth/two-factor/verify-backup-code'
      : '/api/auth/two-factor/verify-totp'
      
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        code: twoFactorCode.value,
        trustDevice: form.rememberMe
      })
    })
    
    if (response.ok) {
      // Track successful 2FA verification as a login success
      trackUserAction.login('email')
      await redirectAfterSuccess()
    } else {
      const errorData = await response.json()
      error.value = errorData.message || (useBackupCode.value ? 'Invalid backup code' : 'Invalid verification code')
      // Track failed 2FA verification
      trackUserAction.loginFailed(error.value)
    }
  } catch (err) {
    console.error('2FA verification error:', err)
    error.value = 'Failed to verify code. Please try again.'
    trackUserAction.loginFailed('2FA verification error')
  } finally {
    verifying2FA.value = false
  }
}

function cancel2FA() {
  show2FA.value = false
  twoFactorCode.value = ''
  savedEmail.value = ''
  useBackupCode.value = false
  error.value = ''
}

function toggleBackupCode() {
  useBackupCode.value = !useBackupCode.value
  twoFactorCode.value = ''
  error.value = ''
}

function toggleMode() {
  isRegisterMode.value = !isRegisterMode.value
  error.value = ''
  showPasswordField.value = false
  form.password = ''
}

async function handleGitHubLogin() {
  loading.value = true
  error.value = ''
  
  try {
    // Track OAuth login attempt before redirect
    trackUserAction.login('oauth')
    await signInWithGitHub()
    // GitHub OAuth will redirect automatically
  } catch (err) {
    console.error('GitHub login error:', err)
    error.value = 'Failed to connect with GitHub. Please try again.'
    // Track failed OAuth login
    trackUserAction.loginFailed('GitHub OAuth error')
    loading.value = false
  }
}

const { setPageMeta } = useSEO()

onMounted(() => {
  // Set SEO meta tags for login page
  setPageMeta({
    title: 'Login',
    description: 'Sign in to your EmailConnect.eu account to manage your email webhooks',
    ogUrl: 'https://emailconnect.eu/login'
  })
  
  // Check for success messages from query params
  if (route.query.message) {
    successMessage.value = route.query.message as string
    // Clear the query param from URL without triggering navigation
    router.replace({ path: route.path })
  }

  // Renewal success (user might be logged out coming back from payment)
  if (route.query.renewal === 'success') {
    successMessage.value = 'Sign in to confirm your renewal.'
    router.replace({ path: route.path })
  }
  
  // Check if we're on register route to set initial mode
  if (route.path.includes('register')) {
    isRegisterMode.value = true
  }
})
</script>
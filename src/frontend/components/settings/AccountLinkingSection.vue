<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Account Linking</h2>
      <p class="text-sm text-base-content/70 mb-6">
        Manage your connected authentication methods. You can link multiple methods to your account for convenience and security.
      </p>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>

      <!-- Account Linking Status -->
      <div v-else-if="linkingStatus" class="space-y-6">
        <!-- Security Score -->
        <div class="alert" :class="getSecurityScoreClass(linkingStatus.accountLinkingStatus.securityScore)">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
          <div>
            <div class="font-bold">Account Security Score: {{ linkingStatus.accountLinkingStatus.securityScore }}/100</div>
            <div class="text-xs">{{ getSecurityMessage(linkingStatus.accountLinkingStatus.securityScore) }}</div>
          </div>
        </div>

        <!-- Current User Info -->
        <div class="bg-base-200 rounded-lg p-4">
          <h3 class="font-semibold mb-2">Current User</h3>
          <div class="flex items-center gap-3">
            <div class="avatar placeholder">
              <div class="bg-neutral text-neutral-content rounded-full w-12">
                <span class="text-lg">{{ linkingStatus.email.charAt(0).toUpperCase() }}</span>
              </div>
            </div>
            <div>
              <div class="font-medium">{{ linkingStatus.email }}</div>
              <div class="text-sm text-base-content/70">User ID: {{ linkingStatus.userId }}</div>
            </div>
          </div>
        </div>

        <!-- Connected Accounts -->
        <div v-if="linkedAccounts">
          <h3 class="font-semibold mb-4">Connected Authentication Methods</h3>
          <div class="space-y-3">
            <div v-for="account in linkedAccounts.accounts" :key="account.id" 
                 class="flex items-center justify-between p-4 bg-base-200 rounded-lg">
              <div class="flex items-center gap-3">
                <div class="badge" :class="getProviderBadgeClass(account.providerId)">
                  {{ getProviderName(account.providerId) }}
                </div>
                <div>
                  <div class="font-medium">{{ account.accountId }}</div>
                  <div class="text-sm text-base-content/70">
                    Connected {{ formatDate(account.createdAt) }}
                    <span v-if="account.hasPassword" class="ml-2 text-success">• Has Password</span>
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <button
                  v-if="linkedAccounts.totalAccounts > 1"
                  @click="unlinkAccount(account)"
                  :disabled="unlinking === account.id"
                  class="btn btn-sm btn-error btn-outline"
                >
                  <span v-if="unlinking === account.id" class="loading loading-spinner loading-xs"></span>
                  {{ unlinking === account.id ? 'Removing...' : 'Remove' }}
                </button>
                <span v-else class="badge badge-sm badge-neutral">Primary</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Add Authentication Methods -->
        <div class="space-y-4">
          <h3 class="font-semibold">Add Authentication Methods</h3>
          
          <!-- GitHub OAuth -->
          <div v-if="linkingStatus.accountLinkingStatus.canLinkGithub" 
               class="flex items-center justify-between p-4 border border-base-300 rounded-lg">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-gray-800 text-white rounded flex items-center justify-center">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </div>
              <div>
                <div class="font-medium">GitHub</div>
                <div class="text-sm text-base-content/70">Connect with your GitHub account</div>
              </div>
            </div>
            <a href="/api/auth/signin/github" class="btn btn-sm btn-outline">
              Connect GitHub
            </a>
          </div>

          <!-- Password Setup -->
          <div v-if="linkingStatus.accountLinkingStatus.canSetPassword" 
               class="flex items-center justify-between p-4 border border-base-300 rounded-lg">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-blue-600 text-white rounded flex items-center justify-center">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l6.879-6.879A6 6 0 0121 9z"></path>
                </svg>
              </div>
              <div>
                <div class="font-medium">Email & Password</div>
                <div class="text-sm text-base-content/70">Set up password authentication</div>
              </div>
            </div>
            <router-link to="/settings/profile" class="btn btn-sm btn-outline">
              Set Password
            </router-link>
          </div>
        </div>

        <!-- Recommendations -->
        <div v-if="linkingStatus.accountLinkingStatus.recommendations.length > 0" class="space-y-3">
          <h3 class="font-semibold">Recommendations</h3>
          <div class="space-y-2">
            <div v-for="rec in linkingStatus.accountLinkingStatus.recommendations" :key="rec"
                 class="flex items-start gap-2 p-3 bg-info/10 rounded-lg">
              <svg class="w-5 h-5 text-info mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span class="text-sm">{{ rec }}</span>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex gap-3 pt-4">
          <button @click="refreshAccountInfo" :disabled="refreshing" class="btn btn-outline">
            <span v-if="refreshing" class="loading loading-spinner loading-xs"></span>
            {{ refreshing ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button @click="testAccountLinking" class="btn btn-primary">
            Test Account Linking
          </button>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-error">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Account {
  id: string
  providerId: string
  accountId: string
  createdAt: string
  hasPassword: boolean
}

interface LinkedAccounts {
  success: boolean
  user: {
    id: string
    email: string
    name: string | null
    image: string | null
  }
  accounts: Account[]
  totalAccounts: number
  hasCredentialAccount: boolean
  hasGithubAccount: boolean
}

interface LinkingStatus {
  success: boolean
  userId: string
  email: string
  accountLinkingStatus: {
    canLinkGithub: boolean
    canSetPassword: boolean
    hasMultipleProviders: boolean
    securityScore: number
    recommendations: string[]
  }
}

// State
const loading = ref(true)
const refreshing = ref(false)
const error = ref<string | null>(null)
const linkingStatus = ref<LinkingStatus | null>(null)
const linkedAccounts = ref<LinkedAccounts | null>(null)
const unlinking = ref<string | null>(null)

// Methods
const loadAccountInfo = async () => {
  try {
    const [statusResponse, accountsResponse] = await Promise.all([
      fetch('/api/account-linking/linking-test', { credentials: 'include' }),
      fetch('/api/account-linking/linked-accounts', { credentials: 'include' })
    ])

    if (statusResponse.ok && accountsResponse.ok) {
      linkingStatus.value = await statusResponse.json()
      linkedAccounts.value = await accountsResponse.json()
      error.value = null
    } else {
      error.value = 'Failed to load account information'
    }
  } catch (err) {
    console.error('Error loading account info:', err)
    error.value = 'Failed to load account information'
  }
}

const refreshAccountInfo = async () => {
  refreshing.value = true
  await loadAccountInfo()
  refreshing.value = false
}

const unlinkAccount = async (account: Account) => {
  const confirmed = confirm(`Are you sure you want to remove ${getProviderName(account.providerId)} authentication? You will no longer be able to sign in using this method.`)
  if (!confirmed) return

  unlinking.value = account.id
  try {
    const response = await fetch(`/api/account-linking/unlink/${account.id}`, {
      method: 'DELETE',
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      alert(data.message)
      await loadAccountInfo() // Refresh the list
    } else {
      const errorData = await response.json()
      alert(errorData.message || 'Failed to unlink account')
    }
  } catch (err) {
    console.error('Error unlinking account:', err)
    alert('Failed to unlink account')
  } finally {
    unlinking.value = null
  }
}

const testAccountLinking = () => {
  if (!linkingStatus.value) return
  
  const status = linkingStatus.value.accountLinkingStatus
  const testResults = [
    `Security Score: ${status.securityScore}/100`,
    `Multiple Providers: ${status.hasMultipleProviders ? 'Yes' : 'No'}`,
    `Can Link GitHub: ${status.canLinkGithub ? 'Yes' : 'No'}`,
    `Can Set Password: ${status.canSetPassword ? 'Yes' : 'No'}`,
    `Total Accounts: ${linkedAccounts.value?.totalAccounts || 0}`
  ]
  
  alert('Account Linking Test Results:\n\n' + testResults.join('\n'))
}

const getProviderName = (providerId: string) => {
  switch (providerId) {
    case 'credential': return 'Email & Password'
    case 'github': return 'GitHub'
    default: return providerId.charAt(0).toUpperCase() + providerId.slice(1)
  }
}

const getProviderBadgeClass = (providerId: string) => {
  switch (providerId) {
    case 'credential': return 'badge-primary'
    case 'github': return 'badge-neutral'
    default: return 'badge-outline'
  }
}

const getSecurityScoreClass = (score: number) => {
  if (score >= 80) return 'alert-success'
  if (score >= 60) return 'alert-warning'
  return 'alert-error'
}

const getSecurityMessage = (score: number) => {
  if (score >= 80) return 'Excellent account security with multiple authentication methods'
  if (score >= 60) return 'Good security, consider adding more authentication methods'
  return 'Account security could be improved with additional authentication methods'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  await loadAccountInfo()
  loading.value = false
})
</script>
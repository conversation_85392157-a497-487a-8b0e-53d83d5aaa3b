<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Profile information</h2>
      <div class="bg-base-200/40 rounded-lg p-6">
        <div class="space-y-4">
          <div>
            <label class="label">
              <span class="label-text">Display name</span>
            </label>
            <input
              type="text"
              v-model="displayName"
              placeholder="Enter your display name"
              class="input input-bordered w-full"
            >
          </div>
          <div>
            <label class="label flex items-center cursor-pointer">
              <span class="label-text">Use Gravatar for profile picture</span>
              <input
                type="checkbox"
                v-model="useGravatar"
                class="checkbox checkbox-primary ml-auto"
              >
            </label>
            <label class="label">
              <span class="label-text-alt text-xs">When enabled, your Gravatar image will be loaded based on your email address</span>
            </label>
          </div>
          <div>
            <label class="label">
              <span class="label-text">Email address</span>
            </label>
            <div class="join w-full">
              <input
                type="email"
                :value="currentUser?.email || ''"
                disabled
                class="input join-item w-full cursor-not-allowed"
                placeholder="Email address"
              />
              <button
                type="button"
                @click="toggleEmailForm"
                class="btn join-item cursor-pointer"
              >
                Change
              </button>
            </div>
            <label class="label">
              <span class="label-text-alt text-xs">Click "Change" to update your email address.</span>
            </label>
          </div>
          <div v-if="showEmailForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
            <h4 class="font-medium mb-3">Change email address</h4>
            <div class="space-y-3">
              <div>
                <label class="label">
                  <span class="label-text">New email address</span>
                </label>
                <input
                  type="email"
                  v-model="newEmail"
                  placeholder="Enter new email address"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">Current password</span>
                </label>
                <input
                  type="password"
                  v-model="emailChangePassword"
                  placeholder="Enter your current password"
                  class="input input-bordered w-full"
                >
              </div>
              <div class="flex gap-2">
                <button
                  type="button"
                  @click="changeEmail"
                  :disabled="changingEmail"
                  class="btn btn-primary"
                >
                  {{ changingEmail ? 'Changing...' : 'Update' }}
                </button>
                <button
                  type="button"
                  @click="cancelEmailChange"
                  class="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <label class="label">
              <span class="label-text">Password</span>
            </label>
            <div class="join w-full">
              <input
                type="password"
                :value="hasPassword ? '••••••••••••' : 'No password set'"
                disabled
                class="input join-item w-full cursor-not-allowed"
                :placeholder="hasPassword ? 'Password' : 'Set a password to enable email/password login'"
              />
              <button
                type="button"
                @click="togglePasswordForm"
                class="btn join-item cursor-pointer"
              >
                {{ hasPassword ? 'Change' : 'Set' }}
              </button>
            </div>
            <label class="label">
              <span class="label-text-alt text-xs">
                {{ hasPassword ? 'Change your current password' : 'Set a password to enable login with email and password' }}
              </span>
            </label>
          </div>
          <div v-if="showPasswordForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
            <h4 class="font-medium mb-3">{{ hasPassword ? 'Change password' : 'Set password' }}</h4>
            <div class="space-y-3">
              <div v-if="hasPassword">
                <label class="label">
                  <span class="label-text">Current password</span>
                </label>
                <input
                  type="password"
                  v-model="currentPassword"
                  placeholder="Enter your current password"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">New password</span>
                </label>
                <input
                  type="password"
                  v-model="newPassword"
                  placeholder="Enter new password (min 6 characters)"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">Confirm new password</span>
                </label>
                <input
                  type="password"
                  v-model="confirmPassword"
                  placeholder="Confirm new password"
                  class="input input-bordered w-full"
                >
              </div>
              <div class="flex gap-2">
                <button
                  type="button"
                  @click="changePassword"
                  :disabled="changingPassword"
                  class="btn btn-primary"
                >
                  {{ changingPassword ? 'Changing...' : 'Update' }}
                </button>
                <button
                  type="button"
                  @click="cancelPasswordChange"
                  class="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
          
          <!-- Two-Factor Authentication Section -->
          <div class="mt-6">
            <label class="label">
              <span class="label-text">Two-factor authentication (2FA)</span>
            </label>
            <div class="join w-full">
              <input
                type="text"
                :value="twoFactorEnabled ? 'Enabled' : 'Disabled'"
                disabled
                class="input join-item w-full cursor-not-allowed"
                :class="{ 'text-success': twoFactorEnabled, 'text-base-content/50': !twoFactorEnabled }"
              />
              <button
                type="button"
                @click="toggle2FA"
                class="btn join-item cursor-pointer btn-outline"
                :class="{ 'btn-error': twoFactorEnabled, 'btn-success': !twoFactorEnabled }"
                :disabled="toggling2FA || !hasPassword"
              >
                {{ toggling2FA ? 'Loading...' : (twoFactorEnabled ? 'Disable' : 'Enable') }}
              </button>
            </div>
            <label class="label">
              <span class="label-text-alt text-xs">
                {{ twoFactorEnabled 
                  ? 'Two-factor authentication adds an extra layer of security to your account' 
                  : 'Enable 2FA for enhanced account security using an authenticator app'
                }}
              </span>
            </label>
          </div>

          <!-- 2FA Setup Modal Content (shown when enabling) -->
          <div v-if="show2FASetup" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
            <h4 class="font-medium mb-3">Set up two-factor authentication</h4>
            
            <div v-if="qrCode" class="space-y-4">
              <div class="text-center">
                <p class="text-sm mb-4">Scan this QR code with your authenticator app</p>
                <div v-html="qrCode" class="flex justify-center"></div>
              </div>
              
              <div class="text-center">
                <p class="text-sm mb-2">Or enter this secret manually:</p>
                <code class="bg-base-200 px-2 py-1 rounded text-sm">{{ secretKey }}</code>
              </div>
              
              <div>
                <label class="label">
                  <span class="label-text">Enter verification code from your authenticator app</span>
                </label>
                <input
                  type="text"
                  v-model="verificationCode"
                  placeholder="000000"
                  maxlength="6"
                  class="input input-bordered w-full text-center text-lg tracking-widest"
                >
              </div>
              
              <div class="flex gap-2">
                <button
                  type="button"
                  @click="verify2FASetup"
                  :disabled="verifying2FA || verificationCode.length !== 6"
                  class="btn btn-primary"
                >
                  {{ verifying2FA ? 'Verifying...' : 'Verify & enable' }}
                </button>
                <button
                  type="button"
                  @click="cancel2FASetup"
                  class="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
            
            <div v-else class="text-center">
              <span class="loading loading-spinner loading-md"></span>
              <p class="text-sm mt-2">Generating QR code...</p>
            </div>
          </div>

          <!-- 2FA Backup Codes (shown when 2FA is enabled) -->
          <div v-if="twoFactorEnabled && !show2FASetup" class="mt-4">
            <div class="flex justify-between items-center">
              <span class="text-sm font-medium">Backup recovery codes</span>
              <button
                type="button"
                @click="show2FABackupCodes = !show2FABackupCodes"
                class="btn btn-sm btn-outline"
              >
                {{ show2FABackupCodes ? 'Hide' : 'Show' }} codes
              </button>
            </div>
            
            <div v-if="show2FABackupCodes" class="mt-2 p-3 bg-base-200 rounded">
              <p class="text-xs text-base-content/70 mb-2">
                Save these codes in a secure location. Each code can only be used once.
              </p>
              <div v-if="backupCodes.length > 0" class="grid grid-cols-2 gap-1 text-sm font-mono">
                <div v-for="code in backupCodes" :key="code" class="bg-base-100 px-2 py-1 rounded">
                  {{ code }}
                </div>
              </div>
              <div v-else class="text-center">
                <button
                  type="button"
                  @click="generateBackupCodes"
                  :disabled="generatingBackupCodes"
                  class="btn btn-sm btn-primary"
                >
                  {{ generatingBackupCodes ? 'Generating...' : 'Generate backup codes' }}
                </button>
              </div>
            </div>
          </div>
          
          <div class="flex justify-end pt-4">
            <button
              type="button"
              @click="saveChanges"
              :disabled="saving"
              class="btn btn-primary w-full md:w-2/5"
            >
              {{ saving ? 'Saving...' : 'Save profile changes' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Password Prompt Modal -->
  <div v-if="showPasswordPrompt" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-base-100 p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
      <h3 class="text-lg font-bold mb-4">
        {{ passwordPromptType === 'enable' ? 'Enable two-factor authentication' : 
           passwordPromptType === 'disable' ? 'Disable two-factor authentication' : 
           'Generate backup codes' }}
      </h3>
      <p class="text-sm text-base-content/70 mb-4">
        Please enter your password to continue.
      </p>
      <div class="space-y-4">
        <input
          type="password"
          v-model="twoFactorPassword"
          placeholder="Enter your password"
          class="input input-bordered w-full"
          @keyup.enter="confirmPasswordAction"
        >
        <div class="flex gap-2 justify-end">
          <button
            type="button"
            @click="showPasswordPrompt = false; twoFactorPassword = ''"
            class="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="confirmPasswordAction"
            class="btn btn-primary"
            :disabled="!twoFactorPassword"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 2FA Success Modal -->
  <div v-if="show2FASuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-base-100 p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="text-center mb-4">
        <div class="w-16 h-16 bg-success text-success-content rounded-full mx-auto flex items-center justify-center mb-4">
          <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-success">2FA Enabled Successfully!</h3>
      </div>
      
      <div class="space-y-4">
        <p class="text-center text-sm">
          Two-factor authentication has been successfully enabled for your account.
        </p>
        
        <div v-if="backupCodes.length > 0" class="bg-warning/10 p-4 rounded-lg">
          <h4 class="font-bold text-warning mb-2">⚠️ Save Your Backup Codes</h4>
          <p class="text-xs mb-3">These codes can be used to access your account if you lose your authenticator device. Save them in a secure location!</p>
          <div class="grid grid-cols-2 gap-1 text-xs font-mono bg-base-200 p-3 rounded max-h-32 overflow-y-auto">
            <div v-for="code in backupCodes" :key="code" class="bg-base-100 px-2 py-1 rounded text-center">
              {{ code }}
            </div>
          </div>
        </div>
        
        <div class="text-center">
          <button
            @click="show2FASuccessModal = false"
            class="btn btn-primary"
          >
            Yes, I've saved these codes
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Organization/Invoice Information Section -->
  <div class="card bg-base-100 mt-6">
    <div class="card-body">
      <h2 class="card-title mb-6">Organization information</h2>
      <p class="text-sm text-base-content/70 mb-4">
        This information will appear on your invoices. Fill this out for proper business invoicing.
      </p>
      <div class="bg-base-200/40 rounded-lg p-6">
        <div class="space-y-4">
          <div>
            <label class="label">
              <span class="label-text">Organization name</span>
            </label>
            <input
              type="text"
              v-model="organizationName"
              placeholder="e.g., Acme Corporation Ltd."
              class="input input-bordered w-full"
            >
          </div>
          <div>
            <label class="label">
              <span class="label-text">Registration/VAT number</span>
            </label>
            <input
              type="text"
              v-model="registrationNumber"
              placeholder="e.g., VAT123456789 or REG-2024-001"
              class="input input-bordered w-full"
            >
          </div>
          <div class="divider">Address</div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="md:col-span-2">
              <label class="label">
                <span class="label-text">Street address</span>
              </label>
              <input
                type="text"
                v-model="addressStreet"
                class="input input-bordered w-full"
              >
            </div>
            <div>
              <label class="label">
                <span class="label-text">Apartment/Suite (optional)</span>
              </label>
              <input
                type="text"
                v-model="addressApartment"
                class="input input-bordered w-full"
              >
            </div>
            <div>
              <label class="label">
                <span class="label-text">Postal code</span>
              </label>
              <input
                type="text"
                v-model="addressZipCode"
                class="input input-bordered w-full"
              >
            </div>
            <div>
              <label class="label">
                <span class="label-text">City</span>
              </label>
              <input
                type="text"
                v-model="addressCity"
                class="input input-bordered w-full"
              >
            </div>
            <div>
              <label class="label">
                <span class="label-text">Country</span>
              </label>
              <input
                type="text"
                v-model="addressCountry"
                class="input input-bordered w-full"
              >
            </div>
          </div>
          <div class="flex justify-end pt-4">
            <button
              type="button"
              @click="saveOrganizationChanges"
              :disabled="savingOrganization"
              class="btn btn-primary w-full md:w-2/5"
            >
              {{ savingOrganization ? 'Saving...' : 'Save organization details' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Delete Account Section -->
  <div class="card bg-base-100 mt-6">
    <div class="card-body">
      <h2 class="card-title mb-2">Danger zone</h2>
      <p class="text-sm text-base-content/70 mb-4">Permanently delete your account and all associated data. This action cannot be undone.</p>
      <div class="flex justify-end">
        <button type="button" class="btn btn-error" @click="openDeleteDialog">Delete my account</button>
      </div>
    </div>
  </div>

  <!-- Self-delete Confirmation Modal -->
  <div v-if="showDeleteDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-base-100 p-6 rounded-lg shadow-xl max-w-lg w-full mx-4">
      <h3 class="text-lg font-bold mb-3">Confirm account deletion</h3>
      <div class="space-y-3 text-sm">
        <p>Here's what happens when you delete your account:</p>
        <ul class="list-disc list-inside space-y-1 text-base-content/80">
          <li>We delete your account and personal data immediately</li>
          <li>Your data will expire in existing backups within 7 days</li>
          <li>Anonymized financial transactions will remain for tax compliance</li>
        </ul>
        <div>
          <label class="label"><span class="label-text">Type DELETE to confirm</span></label>
          <input type="text" v-model="deleteConfirm" placeholder="DELETE" class="input input-bordered w-full" />
        </div>
        <div>
          <label class="label"><span class="label-text">Feedback (optional)</span></label>
          <textarea v-model="deleteReason" class="textarea textarea-bordered w-full" rows="3" placeholder="What could we improve?"></textarea>
        </div>
        <div v-if="hasActiveSubscription" class="alert alert-warning">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" /></svg>
          <span>You currently have an active subscription. Please cancel it in Billing first. Deleting the account before the end of the billing period won’t refund the remainder.</span>
        </div>
      </div>
      <div class="mt-4 flex justify-end gap-2">
        <button class="btn btn-ghost" @click="closeDeleteDialog">Cancel</button>
        <button class="btn btn-error" :disabled="deleteConfirm !== 'DELETE' || deleting || hasActiveSubscription" @click="confirmSelfDelete">
          <span v-if="deleting" class="loading loading-spinner loading-xs"></span>
          <span v-else>Delete account</span>
        </button>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuth } from '../../composables/useAuth'
import { useMetrics } from '../../composables/useMetrics'
import { api } from '../../utils/api'
import { useRouter } from 'vue-router'
import { useToast } from '../../composables/useToast'

// State
const displayName = ref('')
const useGravatar = ref(false)
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const newEmail = ref('')
const emailChangePassword = ref('')
const saving = ref(false)
const changingPassword = ref(false)
const changingEmail = ref(false)
const showPasswordForm = ref(false)
const showEmailForm = ref(false)

// Organization fields
const organizationName = ref('')
const registrationNumber = ref('')
const addressStreet = ref('')
const addressApartment = ref('')
const addressZipCode = ref('')
const addressCity = ref('')
const addressCountry = ref('')
const savingOrganization = ref(false)

// 2FA fields
const twoFactorEnabled = ref(false)
const toggling2FA = ref(false)
const show2FASetup = ref(false)
const qrCode = ref('')
const secretKey = ref('')
const verificationCode = ref('')
const verifying2FA = ref(false)
const show2FABackupCodes = ref(false)
const backupCodes = ref<string[]>([])
const generatingBackupCodes = ref(false)
const twoFactorPassword = ref('')
const showPasswordPrompt = ref(false)
const passwordPromptType = ref<'enable' | 'disable' | 'backup'>('enable')
const show2FASuccessModal = ref(false)

// User - use auth composable
const { user, refreshUser, clearAuth } = useAuth()
const currentUser = computed(() => user.value)
const hasPassword = ref(true) // Will be updated from API

// Router + toast
const router = useRouter()
const { success: toastSuccess, error: toastError } = useToast()

// Self-delete state
const showDeleteDialog = ref(false)
const deleteConfirm = ref('')
const deleteReason = ref('')
const deleting = ref(false)
const hasActiveSubscription = ref(false)

// Metrics - for organization data
const { metricsData, loadMetrics, refreshMetrics } = useMetrics()

// Load user profile - data already available from auth composable
const initializeProfile = async () => {
  if (user.value) {
    displayName.value = user.value.name || ''
    newEmail.value = user.value.email || ''
  }

  // Load profile data to get hasPassword status
  try {
    const profileData = await api.get('/api/profile')
    if (profileData.success && profileData.user) {
      hasPassword.value = profileData.user.hasPassword || false
    }
  } catch (error) {
    console.error('Failed to load profile data:', error)
  }

  // Load 2FA status
  await load2FAStatus()

  // Load metrics data to get organization info and user settings
  await loadMetrics()

  // Determine active subscription to gate deletion flow
  try {
    const status = await api.get('/api/auth-status', { toast: { showErrors: false } })
    hasActiveSubscription.value = !!status?.user?.hasActiveSubscription
  } catch {}
  
  // Initialize Gravatar setting from metrics data
  const userSettings = metricsData.value?.userSettings
  if (userSettings) {
    useGravatar.value = userSettings.useGravatar || false
  }
  
  // Initialize organization fields from metrics data
  const organization = metricsData.value?.user?.organization
  if (organization) {
    organizationName.value = organization.name || ''
    registrationNumber.value = organization.registrationNumber || ''
    addressStreet.value = organization.addressStreet || ''
    addressApartment.value = organization.addressApartment || ''
    addressZipCode.value = organization.addressZipCode || ''
    addressCity.value = organization.addressCity || ''
    addressCountry.value = organization.addressCountry || ''
  } else {
    organizationName.value = ''
    registrationNumber.value = ''
    addressStreet.value = ''
    addressApartment.value = ''
    addressZipCode.value = ''
    addressCity.value = ''
    addressCountry.value = ''
  }
}

// Save display name and Gravatar setting
const saveChanges = async () => {
  saving.value = true
  try {
    // Update profile name
    const profileData = await api.put('/api/profile', { name: displayName.value || null })
    
    // Update Gravatar setting
    await api.put('/api/settings', { useGravatar: useGravatar.value })
    
    if (profileData.success && profileData.user) {
      // Refresh auth state with updated user data
      await refreshUser()
      // Refresh metrics to get updated settings
      await refreshMetrics()
      alert('Profile updated successfully!')
    }
  } catch (error: any) {
    console.error('Failed to save changes:', error)
    alert(error.message || 'Failed to save changes. Please try again.')
  } finally {
    saving.value = false
  }
}

// Change password
const changePassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    alert('New passwords do not match')
    return
  }
  if (newPassword.value.length < 6) {
    alert('New password must be at least 6 characters long')
    return
  }
  changingPassword.value = true
  try {
    const requestBody: any = {
      newPassword: newPassword.value
    }
    
    // Only include currentPassword if user has an existing password
    if (hasPassword.value) {
      requestBody.currentPassword = currentPassword.value
    }
    
    await api.put('/api/profile/password', requestBody)
    
    currentPassword.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
    showPasswordForm.value = false
    
    // Update hasPassword status after successful password set
    if (!hasPassword.value) {
      hasPassword.value = true
    }
    
    alert(hasPassword.value ? 'Password changed successfully!' : 'Password set successfully!')
  } catch (error: any) {
    console.error('Failed to change password:', error)
    alert(error.message || 'Failed to change password. Please try again.')
  } finally {
    changingPassword.value = false
  }
}

// Change email
const changeEmail = async () => {
  if (!newEmail.value || !emailChangePassword.value) {
    alert('Please fill in all fields')
    return
  }
  changingEmail.value = true
  try {
    const data = await api.put('/api/profile/email', {
      newEmail: newEmail.value,
      password: emailChangePassword.value
    })
    if (data.success && data.user) {
      // Refresh auth state with updated user data
      await refreshUser()
      emailChangePassword.value = ''
      showEmailForm.value = false
      alert('Email address changed successfully!')
    }
  } catch (error: any) {
    console.error('Failed to change email:', error)
    alert(error.message || 'Failed to change email. Please try again.')
  } finally {
    changingEmail.value = false
  }
}

function toggleEmailForm() {
  showEmailForm.value = !showEmailForm.value
}
function cancelEmailChange() {
  showEmailForm.value = false
  emailChangePassword.value = ''
}
function togglePasswordForm() {
  showPasswordForm.value = !showPasswordForm.value
}
function cancelPasswordChange() {
  showPasswordForm.value = false
  currentPassword.value = ''
  newPassword.value = ''
  confirmPassword.value = ''
}

// Save organization details
const saveOrganizationChanges = async () => {
  savingOrganization.value = true
  try {
    const organizationData = {
      name: organizationName.value || null,
      registrationNumber: registrationNumber.value || null,
      addressStreet: addressStreet.value || null,
      addressApartment: addressApartment.value || null,
      addressZipCode: addressZipCode.value || null,
      addressCity: addressCity.value || null,
      addressCountry: addressCountry.value || null
    }
    
    const data = await api.put('/api/profile/organization', organizationData)
    if (data.success && data.user) {
      // Refresh both auth state and metrics data to get updated organization info
      await refreshUser()
      await refreshMetrics()
      alert('Organization details updated successfully!')
    }
  } catch (error: any) {
    console.error('Failed to save organization details:', error)
    alert(error.message || 'Failed to save organization details. Please try again.')
  } finally {
    savingOrganization.value = false
  }
}

// 2FA Methods
const load2FAStatus = async () => {
  try {
    // Get 2FA status from the current user data (loaded from auth composable)
    if (user.value && 'twoFactorEnabled' in user.value) {
      twoFactorEnabled.value = user.value.twoFactorEnabled || false
    } else {
      // Fallback to API call if not available in user data
      const response = await fetch('/api/auth-status', {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        if (data.user && 'twoFactorEnabled' in data.user) {
          twoFactorEnabled.value = data.user.twoFactorEnabled || false
        }
      }
    }
  } catch (error) {
    console.error('Failed to load 2FA status:', error)
  }
}

const toggle2FA = async () => {
  if (twoFactorEnabled.value) {
    // Disable 2FA - need password
    passwordPromptType.value = 'disable'
    showPasswordPrompt.value = true
  } else {
    // Enable 2FA - need password
    passwordPromptType.value = 'enable'
    showPasswordPrompt.value = true
  }
}

const confirmPasswordAction = async () => {
  if (!twoFactorPassword.value) {
    alert('Please enter your password')
    return
  }

  const password = twoFactorPassword.value
  twoFactorPassword.value = ''
  showPasswordPrompt.value = false

  if (passwordPromptType.value === 'disable') {
    await disable2FA(password)
  } else if (passwordPromptType.value === 'enable') {
    await enable2FA(password)
  } else if (passwordPromptType.value === 'backup') {
    await generateBackupCodesWithPassword(password)
  }
}

const enable2FA = async (password: string) => {
  toggling2FA.value = true
  try {
    const response = await fetch('/api/auth/two-factor/enable', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ password })
    })
    
    if (response.ok) {
      const data = await response.json()
      if (data.totpURI) {
        // Generate QR code using qrcode library
        try {
          const QRCode = (await import('qrcode')).default
          const qrCodeDataUrl = await QRCode.toDataURL(data.totpURI, {
            width: 256,
            margin: 2
          })
          qrCode.value = `<div class="text-center"><img src="${qrCodeDataUrl}" alt="2FA QR Code" class="mx-auto border rounded"/></div>`
        } catch (error) {
          // Fallback to text display
          qrCode.value = `<div class="text-center"><div class="bg-base-200 p-4 rounded text-sm font-mono break-all">${data.totpURI}</div></div>`
        }
        
        // Extract secret from TOTP URI
        const secretMatch = data.totpURI.match(/secret=([A-Z2-7]+)/i)
        secretKey.value = secretMatch ? secretMatch[1] : (data.secret || '')
        show2FASetup.value = true
        
        // Store backup codes if provided
        if (data.backupCodes) {
          backupCodes.value = data.backupCodes
        }
      }
    } else {
      const error = await response.json()
      alert(error.message || 'Failed to enable 2FA. Please check your password.')
    }
  } catch (error) {
    console.error('Failed to enable 2FA:', error)
    alert('Failed to enable 2FA. Please try again.')
  } finally {
    toggling2FA.value = false
  }
}

const disable2FA = async (password: string) => {
  const confirmed = confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')
  if (!confirmed) return

  toggling2FA.value = true
  try {
    const response = await fetch('/api/auth/two-factor/disable', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ password })
    })
    
    if (response.ok) {
      twoFactorEnabled.value = false
      backupCodes.value = []
      show2FABackupCodes.value = false
      await refreshUser() // Refresh user data
      alert('Two-factor authentication has been disabled.')
    } else {
      const error = await response.json()
      alert(error.message || 'Failed to disable 2FA. Please check your password.')
    }
  } catch (error) {
    console.error('Failed to disable 2FA:', error)
    alert('Failed to disable 2FA. Please try again.')
  } finally {
    toggling2FA.value = false
  }
}

const verify2FASetup = async () => {
  if (verificationCode.value.length !== 6) {
    alert('Please enter a valid 6-digit verification code')
    return
  }

  verifying2FA.value = true
  try {
    const response = await fetch('/api/auth/two-factor/verify-totp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        code: verificationCode.value,
        trustDevice: true
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      twoFactorEnabled.value = true
      show2FASetup.value = false
      verificationCode.value = ''
      await refreshUser() // Refresh user data to get updated twoFactorEnabled status
      
      // Show backup codes if available
      if (data.backupCodes && data.backupCodes.length > 0) {
        backupCodes.value = data.backupCodes
      }
      show2FASuccessModal.value = true
    } else {
      const error = await response.json()
      alert(error.message || 'Invalid verification code. Please try again.')
    }
  } catch (error) {
    console.error('Failed to verify 2FA setup:', error)
    alert('Failed to verify 2FA setup. Please try again.')
  } finally {
    verifying2FA.value = false
  }
}

const cancel2FASetup = () => {
  show2FASetup.value = false
  qrCode.value = ''
  secretKey.value = ''
  verificationCode.value = ''
}

const generateBackupCodes = async () => {
  passwordPromptType.value = 'backup'
  showPasswordPrompt.value = true
}

const generateBackupCodesWithPassword = async (password: string) => {
  generatingBackupCodes.value = true
  try {
    const response = await fetch('/api/auth/two-factor/generate-backup-codes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ password })
    })
    
    if (response.ok) {
      const data = await response.json()
      // Handle backup codes - they should be an array from BetterAuth API response
      backupCodes.value = Array.isArray(data.backupCodes) ? data.backupCodes : (data.codes || [])
      alert('New backup codes have been generated. Please save them securely as the old codes are no longer valid.')
    } else {
      const error = await response.json()
      alert(error.message || 'Failed to generate backup codes. Please check your password.')
    }
  } catch (error) {
    console.error('Failed to generate backup codes:', error)
    alert('Failed to generate backup codes. Please try again.')
  } finally {
    generatingBackupCodes.value = false
  }
}

onMounted(() => {
  initializeProfile()
})

function openDeleteDialog() {
  showDeleteDialog.value = true
  deleteConfirm.value = ''
}
function closeDeleteDialog() {
  showDeleteDialog.value = false
  deleteConfirm.value = ''
}

const confirmSelfDelete = async () => {
  if (deleteConfirm.value !== 'DELETE') return
  deleting.value = true
  try {
    await api.post('/api/account/delete', {
      confirmToken: 'DELETE',
      reason: deleteReason.value || undefined
    })
    toastSuccess('Your account has been deleted.')
    // Clear local auth state to avoid guards treating you as still logged in
    clearAuth()
    // Hard redirect to ensure cookies/state are reset
    window.location.href = '/login'
  } catch (e: any) {
    toastError(e?.message || 'Failed to delete account')
  } finally {
    deleting.value = false
  }
}
</script>

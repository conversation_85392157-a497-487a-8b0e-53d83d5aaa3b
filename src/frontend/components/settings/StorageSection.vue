<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title">
        <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline">Pro</span>
        Storage configuration
      </h2>
      <p class="mt-1 mb-6 text-base-content/70">
        Configure how your attachments are processed and stored.
      </p>
      
      <!-- Free Plan Limitations - Only show for free users -->
      <div v-if="!isProUser" class="bg-info/10 border border-info/20 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
        <div class="space-y-2 text-sm text-base-content/70">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Maximum attachment size: <strong>128KB</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Text-based files only: <strong>TXT, PDF, CSV, MD, ICS, XML</strong></span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Attachments included as <strong>base64</strong> in webhook payload</span>
          </div>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccessMessage" class="bg-success/10 border border-success/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          <div class="text-sm text-success">
            <strong>Settings saved successfully!</strong>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showErrorMessage" class="bg-error/10 border border-error/20 rounded-lg p-3 mb-4">
        <div class="flex items-start gap-2">
          <svg class="w-4 h-4 text-error mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div class="text-sm text-error">
            <strong>Error:</strong> {{ errorMessage }}
          </div>
        </div>
      </div>

      <!-- Attachment Processing Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
          Attachment storage & processing
        </h3>
        <p v-if="!isProUser" class="text-sm text-base-content/70 mb-6">
          Upgrade to Pro to unlock advanced attachment processing and storage options
        </p>
        <div v-else class="mb-6">
          <p class="text-sm text-base-content/70 mb-2">Configure how attachments are processed and stored</p>
        </div>

        <div class="space-y-6" :class="{ 'opacity-60': !isProUser || loading }">
          <!-- Max File Size Slider -->
          <div>
            <label class="label">
              <span class="label-text">Maximum attachment size</span>
            </label>
            <div class="flex items-center space-x-4">
              <input
                type="range"
                min="0.1"
                max="10"
                step="0.1"
                v-model="settings.maxInlineSize"
                :disabled="!isProUser"
                class="range range-primary flex-1"
              >
              <span class="text-sm font-medium min-w-[4rem]">{{ settings.maxInlineSize }} MB</span>
            </div>
            <label class="label">
              <span class="label-text-alt text-xs">
                Maximum size for any single attachment
              </span>
            </label>
          </div>

          <!-- File Type Categories -->
          <div>
            <label class="label">
              <span class="label-text">Supported file types</span>
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Text Files -->
              <div class="card card-compact rounded-lg bg-base-100 border border-base-300">
                <div class="card-body">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                      <div class="mt-1">
                        <svg class="w-5 h-5 text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold">Text files</h4>
                        <div class="flex flex-wrap gap-1 mt-2">
                          <span class="badge badge-sm badge-neutral">TXT</span>
                          <span class="badge badge-sm badge-neutral">CSV</span>
                          <span class="badge badge-sm badge-neutral">MD</span>
                          <span class="badge badge-sm badge-neutral">XML</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        v-model="settings.fileTypeSettings!.text"
                        class="toggle toggle-sm toggle-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Documents -->
              <div class="card card-compact rounded-lg bg-base-100 border border-base-300">
                <div class="card-body">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                      <div class="mt-1">
                        <svg class="w-5 h-5 text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold">Documents</h4>
                        <div class="flex flex-wrap gap-1 mt-2">
                          <span class="badge badge-sm badge-neutral">PDF</span>
                          <span class="badge badge-sm badge-neutral">DOC</span>
                          <span class="badge badge-sm badge-neutral">DOCX</span>
                          <span class="badge badge-sm badge-neutral">ICS</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        v-model="settings.fileTypeSettings!.documents"
                        class="toggle toggle-sm toggle-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Images -->
              <div class="card card-compact rounded-lg bg-base-100 border border-base-300" 
                   :class="{ 'opacity-50': !isProUser }">
                <div class="card-body">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                      <div class="mt-1">
                        <svg class="w-5 h-5 text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold">Images</h4>
                        <div class="flex flex-wrap gap-1 mt-2">
                          <span class="badge badge-sm badge-neutral">JPG</span>
                          <span class="badge badge-sm badge-neutral">PNG</span>
                          <span class="badge badge-sm badge-neutral">GIF</span>
                          <span class="badge badge-sm badge-neutral">WEBP</span>
                          <span class="badge badge-sm badge-neutral">SVG</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        v-model="settings.fileTypeSettings!.images"
                        :disabled="!isProUser"
                        class="toggle toggle-sm toggle-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Archives -->
              <div class="card card-compact rounded-lg bg-base-100 border border-base-300" 
                   :class="{ 'opacity-50': !isProUser }">
                <div class="card-body">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                      <div class="mt-1">
                        <svg class="w-5 h-5 text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold">Archives</h4>
                        <div class="flex flex-wrap gap-1 mt-2">
                          <span class="badge badge-sm badge-neutral">ZIP</span>
                          <span class="badge badge-sm badge-neutral">RAR</span>
                          <span class="badge badge-sm badge-neutral">7Z</span>
                          <span class="badge badge-sm badge-neutral">TAR</span>
                          <span class="badge badge-sm badge-neutral">GZIP</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        v-model="settings.fileTypeSettings!.archives"
                        :disabled="!isProUser"
                        class="toggle toggle-sm toggle-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- Media -->
              <div class="card card-compact rounded-lg bg-base-100 border border-base-300" 
                   :class="{ 'opacity-50': !isProUser }">
                <div class="card-body">
                  <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                      <div class="mt-1">
                        <svg class="w-5 h-5 text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h4 class="text-sm font-semibold">Audio & video</h4>
                        <div class="flex flex-wrap gap-1 mt-2">
                          <span class="badge badge-sm badge-neutral">MP3</span>
                          <span class="badge badge-sm badge-neutral">MP4</span>
                          <span class="badge badge-sm badge-neutral">WAV</span>
                          <span class="badge badge-sm badge-neutral">AVI</span>
                          <span class="badge badge-sm badge-neutral">MOV</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        v-model="settings.fileTypeSettings!.media"
                        :disabled="!isProUser"
                        class="toggle toggle-sm toggle-primary"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Storage Provider -->
          <div>
            <label class="label">
              <span class="label-text">Storage provider</span>
            </label>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <label class="label cursor-pointer justify-start border border-base-300 rounded-lg p-4"
                     :class="{ 'border-primary bg-primary/5': settings.storageProvider === 'default' }">
                <input
                  type="radio"
                  value="default"
                  v-model="settings.storageProvider"
                  :disabled="!isProUser"
                  class="radio radio-primary mr-3"
                />
                <div class="flex flex-col">
                  <span class="label-text font-medium whitespace-normal break-words">EmailConnect storage</span>
                  <span class="label-text-alt text-xs">{{ isProUser ? 'Managed by us, no setup required' : 'Free plan option' }}</span>
                </div>
              </label>
              
              <label class="label cursor-pointer justify-start border border-base-300 rounded-lg p-4"
                     :class="{ 
                       'border-primary bg-primary/5': settings.storageProvider === 's3-compatible',
                       'opacity-50 cursor-not-allowed': !isProUser
                     }">
                <input
                  type="radio"
                  value="s3-compatible"
                  v-model="settings.storageProvider"
                  :disabled="!isProUser"
                  class="radio radio-primary mr-3"
                />
                <div class="flex flex-col">
                  <span class="label-text font-medium whitespace-normal break-words [overflow-wrap:anywhere]">Custom S3 storage</span>
                  <span class="label-text-alt text-xs">{{ isProUser ? 'Use your own S3-compatible bucket' : 'Requires Pro plan' }}</span>
                </div>
              </label>
            </div>
            <label class="label">
              <span v-if="!isProUser" class="label-text-alt text-xs">
                <router-link to="/settings#billing" class="link">Upgrade to Pro</router-link> to use custom S3 storage
              </span>
            </label>
          </div>

          <!-- S3 Configuration (only show when S3-compatible is selected) -->
          <div v-if="isProUser && settings.storageProvider === 's3-compatible'" class="space-y-4 bg-base-200/40 p-4 border border-base-300 rounded-lg">
            <h4 class="text-sm font-semibold">Custom storage configuration</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="label">
                  <span class="label-text">Region</span>
                </label>
                <input
                  v-model="s3Config.region"
                  type="text"
                  placeholder="ams-1"
                  class="input input-bordered w-full"
                />
              </div>

              <div>
                <label class="label">
                  <span class="label-text">Bucket name</span>
                </label>
                <input
                  v-model="s3Config.bucket"
                  type="text"
                  placeholder="my-email-attachments"
                  class="input input-bordered w-full"
                />
              </div>

              <div>
                <label class="label">
                  <span class="label-text">Access key ID</span>
                </label>
                <input
                  v-model="s3Config.accessKey"
                  type="text"
                  placeholder="AKIAIOSFODNN7EXAMPLE"
                  class="input input-bordered w-full"
                />
              </div>

              <div>
                <label class="label">
                  <span class="label-text">Secret access key</span>
                </label>
                <input
                  v-model="s3Config.secretKey"
                  type="password"
                  placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                  class="input input-bordered w-full"
                />
              </div>
            </div>

            <div>
              <label class="label">
                <span class="label-text">Custom endpoint</span>
              </label>
              <input
                v-model="s3Config.endpoint"
                type="url"
                placeholder="https://s3.example.com"
                class="input input-bordered w-full"
              />
            </div>
            
            <!-- Test Connection Button -->
            <div class="bg-base-200/70 py-2 px-4 rounded-lg flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
              <div>
                <span v-if="s3TestResult?.message" class="text-sm" :class="s3TestResult.success ? 'text-success' : 'text-error'">{{ s3TestResult.message }}</span>
                <span v-else class="text-sm">Test your S3 connection</span>
              </div>
              <div>
                <button
                  type="button"
                  @click="testS3Connection"
                  :disabled="!s3Config.region || !s3Config.bucket || !s3Config.accessKey || !s3Config.secretKey || testingS3"
                  class="btn btn-outline btn-sm w-full sm:w-auto"
                >
                  <span v-if="testingS3" class="loading loading-spinner loading-xs"></span>
                  {{ testingS3 ? 'Testing...' : 'Test connection' }}
                </button>
              </div>
            </div>
            
            <!-- Retention Options -->
            <div class="divider">Retention settings</div>
            
            <div class="space-y-3">
              <label class="label cursor-pointer justify-start">
                <input
                  type="checkbox"
                  v-model="s3Config.retainAttachmentsOnEmailDeletion"
                  class="checkbox checkbox-primary mr-3 flex-shrink-0"
                />
                <div class="flex flex-col min-w-0">
                  <span class="label-text break-words">Keep attachments when emails expire</span>
                  <span class="label-text-alt text-xs break-words">Attachments won't be deleted from your S3 bucket when emails are cleaned up by retention policy</span>
                </div>
              </label>
            </div>
            
            <!-- Fallback Options -->
            <div class="divider">Fallback settings</div>
            
            <div class="space-y-3">
              <label class="label cursor-pointer justify-start">
                <input
                  type="checkbox"
                  v-model="settings.allowFallbackStorage"
                  class="checkbox checkbox-primary mr-3 flex-shrink-0"
                />
                <div class="flex flex-col min-w-0">
                  <span class="label-text break-words">Allow fallback to EmailConnect storage</span>
                  <span class="label-text-alt text-xs break-words">Use our storage if your S3 becomes unavailable</span>
                </div>
              </label>
              
              <label class="label cursor-pointer justify-start">
                <input
                  type="checkbox"
                  v-model="settings.fallbackNotification"
                  :disabled="true || !settings.allowFallbackStorage"
                  class="checkbox checkbox-primary mr-3 flex-shrink-0"
                />
                <div class="flex flex-col min-w-0">
                  <span class="label-text break-words">Notify on fallback [roadmap]</span>
                  <span class="label-text-alt text-xs break-words">Get notified when we use fallback storage</span>
                </div>
              </label>
            </div>
          </div>

          <!-- Save Button -->
          <div class="flex justify-end pt-4">
            <button
              type="button"
              @click="saveSettings"
              :disabled="!isProUser || saving || loading || testingS3"
              class="btn btn-primary w-full md:w-2/5"
            >
              <span v-if="saving || testingS3" class="loading loading-spinner loading-sm"></span>
              {{ testingS3 ? 'Testing connection...' : (saving ? 'Saving...' : 'Save storage settings') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useAuth } from '../../composables/useAuth'
import { useMetrics } from '../../composables/useMetrics'
import { useUserSettings } from '../../composables/useUserSettings'
import { api } from '../../utils/api'

// Composables  
const { isPro } = useAuth()
const { metricsData, loadMetrics } = useMetrics()
const {
  settings,
  loading,
  saving,
  loadSettings,
  saveSettings: saveUserSettings
} = useUserSettings()

// Local state for form
const showSuccessMessage = ref(false)
const showErrorMessage = ref(false)
const errorMessage = ref('')

// Check if user has Pro plan or higher (uses subscription status)
const isProUser = computed(() => isPro.value)

// Local reactive s3Config for form binding
const s3Config = ref({
  region: '',
  bucket: '',
  accessKey: '',
  secretKey: '',
  endpoint: '',
  retainAttachmentsOnEmailDeletion: false
})

// S3 test state
const testingS3 = ref(false)
const s3TestResult = ref<{ success: boolean; message: string } | null>(null)

// Watch for settings changes to update local s3Config
watch(() => settings.value.s3Config, (newS3Config) => {
  if (newS3Config) {
    s3Config.value = {
      region: newS3Config.region || '',
      bucket: newS3Config.bucket || '',
      accessKey: newS3Config.accessKey || '',
      secretKey: newS3Config.secretKey || '',
      endpoint: newS3Config.endpoint || '',
      retainAttachmentsOnEmailDeletion: (newS3Config as any).retainAttachmentsOnEmailDeletion || false
    }
  }
}, { immediate: true, deep: true })

// Methods
const testS3Connection = async () => {
  testingS3.value = true
  s3TestResult.value = null
  
  try {
    const result = await api.post('/api/settings/test-s3', {
      region: s3Config.value.region,
      bucket: s3Config.value.bucket,
      accessKey: s3Config.value.accessKey,
      secretKey: s3Config.value.secretKey,
      endpoint: s3Config.value.endpoint || undefined
    })
    
    s3TestResult.value = result
    
    // Clear result after 5 seconds
    setTimeout(() => {
      s3TestResult.value = null
    }, 10000)
  } catch (error: any) {
    s3TestResult.value = {
      success: false,
      message: 'Failed to test connection: ' + error.message
    }
  } finally {
    testingS3.value = false
  }
}

const saveSettings = async () => {
  try {
    showSuccessMessage.value = false
    showErrorMessage.value = false
    
    // If using custom S3, automatically test connection before saving
    if (settings.value.storageProvider === 's3-compatible') {
      // Check if we have all required fields
      if (!s3Config.value.region || !s3Config.value.bucket || !s3Config.value.accessKey || !s3Config.value.secretKey) {
        errorMessage.value = 'Please fill in all required S3 configuration fields'
        showErrorMessage.value = true
        setTimeout(() => {
          showErrorMessage.value = false
        }, 5000)
        return
      }
      
      // Always test connection before saving to ensure it works
      try {
        await testS3Connection()
        
        // Check if test was successful
        if (!s3TestResult.value?.success) {
          const confirmSave = confirm('S3 connection test failed. Save configuration anyway?')
          if (!confirmSave) return
        }
      } catch (error) {
        console.error('S3 test error during save:', error)
        const confirmSave = confirm('Failed to test S3 connection. Save configuration anyway?')
        if (!confirmSave) return
      }
    }

    // Prepare s3Config - only include if storageProvider is s3-compatible
    const s3ConfigToSave = settings.value.storageProvider === 's3-compatible'
      ? s3Config.value
      : null

    await saveUserSettings({
      maxInlineSize: settings.value.maxInlineSize,
      fileTypeSettings: settings.value.fileTypeSettings!,
      storageProvider: settings.value.storageProvider,
      s3Config: s3ConfigToSave,
      allowFallbackStorage: settings.value.allowFallbackStorage,
      fallbackNotification: settings.value.fallbackNotification
    })

    // Show success message
    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)
  } catch (err: any) {
    console.error('Failed to save storage settings:', err)
    errorMessage.value = err.message || 'Failed to save settings. Please try again.'
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 5000)
  }
}



onMounted(async () => {
  // Load user metrics to determine plan type and user settings
  await Promise.all([
    loadMetrics(),
    loadSettings()
  ])
})
</script>

<style scoped>
/* Storage section specific styles */
.stat {
  padding: 1rem;
}
</style>

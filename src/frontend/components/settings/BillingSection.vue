<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title">Billing</h2>
      <p class="mt-1 mb-6 text-base-content/70">
        Manage your subscription, payment methods, and additional purchases.
      </p>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-md"></span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-error rounded-lg mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ error }}</span>
        <button @click="loadBillingInfo" class="btn btn-sm btn-outline">
          Try again
        </button>
      </div>

      <!-- Billing Content -->
      <div v-else-if="billingInfo" class="space-y-6">

        <!-- 1. Subscription Management -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Subscription</h3>

          <!-- Free Plan - Upgrade Focus -->
          <div v-if="billingInfo.currentPlan.type === 'free'" class="space-y-4">
            <!-- Current Status -->
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">Free Plan</div>
                <p class="text-sm text-base-content/60 mt-1">Limited to {{ billingInfo.limits.emails }} emails/month</p>
              </div>
              <div class="badge badge-lg badge-ghost">Free</div>
            </div>

            <!-- Upgrade CTA -->
            <div class="bg-primary/10 rounded-lg p-4 border border-primary/20">
              <h4 class="font-semibold text-primary mb-2">Unlock Pro features</h4>
              <ul class="text-sm space-y-1 text-base-content/80 mb-4">
                <li class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ proPlan.monthlyEmailLimit?.toLocaleString() || '1,000' }} emails/month
                </li>
                <li class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ proPlan.domains || 5 }} domains + {{ proPlan.aliases || 50 }} aliases and {{ proPlan.webhooks || 50 }} webhooks
                </li>
                <li class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  SPAM filtering, custom storage & more
                </li>
              </ul>
              <button @click="showUpgradeModal = true" class="btn btn-primary w-full">
                <span class="block sm:inline">Upgrade to Pro</span>
                <span class="block sm:inline sm:ml-1">€{{ proPlan.price?.monthly?.toFixed(2) || '9.95' }}/month</span>
              </button>
            </div>
          </div>

          <!-- Pro/Enterprise Plan - Active Subscription -->
          <div v-else class="space-y-4">
            <!-- Plan Header -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                  <h4 class="text-2xl font-bold">{{ billingInfo.currentPlan.name }} Plan</h4>
                  <div
                    :class="[
                      'badge badge-lg',
                      billingInfo.currentPlan.status === 'active' ? 'badge-success' :
                      billingInfo.currentPlan.status === 'cancelled' ? 'badge-error' :
                      'badge-warning'
                    ]"
                  >
                    {{ billingInfo.currentPlan.status || 'active' }}
                  </div>
                </div>
                <p class="text-sm text-base-content/60 mt-1">
                  {{ billingInfo.limits.emails }} emails/month
                  <span v-if="billingInfo.limits.domains === null"> • Unlimited domains</span>
                </p>
              </div>
              <div class="text-left sm:text-right">
                <div class="text-2xl font-bold">
                  €{{ billingInfo.currentPlan.amount?.value || '0' }}
                </div>
                <div class="text-sm text-base-content/60">
                  {{ billingInfo.currentPlan.interval }}
                </div>
              </div>
            </div>

            <!-- Billing Details (only show if we have data) -->
            <div v-if="billingInfo.currentPlan.nextPaymentDate || billingInfo.paymentMethods?.length > 0" 
                 class="bg-base-100 rounded-lg p-3 space-y-2">
              <div v-if="shouldShowRenewButton" class="flex justify-between items-center p-3 mb-2 bg-warning/10 border border-warning/20 rounded-lg">
                <div class="text-sm">
                  <div class="font-medium">Renewal coming up</div>
                  <div class="text-base-content/70">Your plan expires on {{ formatDate(billingInfo.currentPlan.nextPaymentDate!) }}.</div>
                </div>
                <button class="btn btn-warning btn-sm" @click="renewNow">Renew now</button>
              </div>
              <div v-if="billingInfo.currentPlan.nextPaymentDate" class="flex justify-between text-sm">
                <span class="text-base-content/60">Next billing date</span>
                <span class="font-medium">{{ formatDate(billingInfo.currentPlan.nextPaymentDate) }}</span>
              </div>
              <div v-if="billingInfo.paymentMethods?.length > 0" class="flex justify-between text-sm">
                <span class="text-base-content/60">Payment method</span>
                <span class="font-medium">{{ billingInfo.paymentMethods[0].description || billingInfo.paymentMethods[0].type }}</span>
              </div>
            </div>

            <!-- Grandfathering Badge -->
            <GrandfatheringBadge :grandfathering="billingInfo.grandfathering || null" />

            <!-- Actions -->
            <div class="flex justify-end pt-2">
              <button
                v-if="billingInfo.currentPlan.status === 'active'"
                @click="showCancelModal = true"
                class="btn btn-outline btn-sm text-error"
              >
                Cancel subscription
              </button>
            </div>
          </div>
        </div>

        <!-- 2. Usage & Limits (Combined) -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Usage & limits</h3>

          <!-- Email Usage (Primary/Featured) -->
          <div class="mb-8">
            <h4 class="text-base font-medium mb-4">Email usage</h4>

            <!-- Monthly Allowance -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Monthly allowance</span>
                <span class="text-sm text-base-content/70">
                  {{ billingInfo.emailBreakdown?.monthlyUsed || billingInfo.usage.emails }} / {{ billingInfo.emailBreakdown?.monthlyAllowance || billingInfo.limits.emails }}
                </span>
              </div>
              <div class="w-full bg-base-300 rounded-full h-3">
                <div
                  class="h-3 rounded-full transition-all duration-300"
                  :class="getMonthlyUsageColor()"
                  :style="{ width: getMonthlyUsagePercentage() + '%' }"
                ></div>
              </div>
            </div>

            <!-- Purchased Credits (if any) -->
            <div v-if="billingInfo.creditInfo && billingInfo.creditInfo.balance > 0" class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium">Purchased credits</span>
                <span class="text-sm text-base-content/70">
                  {{ billingInfo.creditInfo.balance }} emails
                </span>
              </div>
              <div v-if="billingInfo.creditInfo.expiringCredits > 0" class="text-xs text-warning mb-1">
                {{ billingInfo.creditInfo.expiringCredits }} credits expire in 30 days
              </div>
              <div v-if="billingInfo.creditInfo.nextExpirationDate" class="text-xs text-base-content/50">
                Next expiration: {{ formatDate(billingInfo.creditInfo.nextExpirationDate) }}
              </div>
            </div>

            <!-- Total Available -->
            <div class="bg-base-100 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <span class="font-medium">Total available</span>
                <span class="font-bold text-xl text-primary">
                  {{ billingInfo.emailBreakdown?.totalAvailable || billingInfo.limits.emails }} emails
                </span>
              </div>
            </div>
          </div>

          <!-- Other Resources (Secondary) -->
          <div class="border-t border-base-300 pt-6">
            <h4 class="text-base font-medium mb-4">Other resources</h4>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.domains }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.domains }} domains
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('domains') + '%' }"
                  ></div>
                </div>
              </div>

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.webhooks }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.webhooks }} webhooks
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('webhooks') + '%' }"
                  ></div>
                </div>
              </div>

              <div class="text-center">
                <div class="text-xl font-bold">{{ billingInfo.usage.aliases }}</div>
                <div class="text-sm text-base-content/70">
                  of {{ billingInfo.limits.aliases }} aliases
                </div>
                <div class="w-full bg-base-300 rounded-full h-2 mt-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    :style="{ width: getUsagePercentage('aliases') + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 3. Additional Purchases -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Additional purchases</h3>

          <!-- Email Credits -->
          <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4">
            <h4 class="text-base font-medium">Purchase email credits</h4>
            <div class="text-sm text-base-content/70">
              €{{ billingInfo.currentPlan.type === 'pro' ? proCreditPricing.pricePerHundred?.toFixed(2) || '0.80' : freeCreditPricing.pricePerHundred?.toFixed(2) || '1.00' }} per 100 emails
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <!-- Credit packages -->
            <div
              v-for="pkg in creditPackages"
              :key="pkg.credits"
              class="card bg-base-100 shadow cursor-pointer hover:shadow-lg transition-shadow"
              :class="{ 'ring-2 ring-primary': selectedCreditPackage === pkg.credits }"
              @click="selectedCreditPackage = pkg.credits"
            >
              <div class="card-body p-4 text-center">
                <h4 class="font-bold text-lg">{{ pkg.credits }} emails</h4>
                <div class="text-2xl font-bold text-primary">€{{ pkg.price.toFixed(2) }}</div>
                <div class="text-sm text-base-content/70">{{ pkg.pricePerEmail }} per email</div>
              </div>
            </div>
          </div>

          <button
            @click="purchaseCredits"
            :disabled="!selectedCreditPackage || isPurchasingCredits"
            class="btn btn-primary w-full"
          >
            {{ isPurchasingCredits ? 'Processing...' : `Purchase ${selectedCreditPackage} credits` }}
          </button>
        </div>

        <!-- 4. Billing & Payment Management -->
        <div class="bg-base-200/40 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-6">Billing & payment management</h3>

          <!-- Payment Methods -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-base font-medium">Payment methods</h4>
              <button
                class="btn btn-outline btn-sm"
                @click="addPaymentMethod"
              >
                <span class="hidden sm:inline">Add payment method</span>
                <span class="sm:hidden">Add</span>
              </button>
            </div>

            <div v-if="billingInfo.paymentMethods.length > 0" class="space-y-3">
              <div
                v-for="method in billingInfo.paymentMethods"
                :key="method.id"
                class="flex items-center justify-between p-4 bg-base-100 rounded-lg"
              >
                <div class="flex items-center space-x-3">
                  <div 
                    class="w-10 h-10 rounded flex items-center justify-center"
                    :class="getPaymentMethodIconClass(method.type)"
                  >
                    <!-- Visa -->
                    <svg v-if="method.type?.toLowerCase().includes('visa')" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" />
                    </svg>
                    <!-- Mastercard -->
                    <svg v-else-if="method.type?.toLowerCase().includes('mastercard')" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                      <circle cx="9" cy="12" r="3" opacity="0.8"/>
                      <circle cx="15" cy="12" r="3" opacity="0.8"/>
                    </svg>
                    <!-- Amex -->
                    <svg v-else-if="method.type?.toLowerCase().includes('amex') || method.type?.toLowerCase().includes('american')" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                      <rect x="4" y="8" width="16" height="8" rx="1" fill="currentColor"/>
                    </svg>
                    <!-- PayPal -->
                    <svg v-else-if="method.type?.toLowerCase().includes('paypal')" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944 3.72a.77.77 0 0 1 .76-.656h4.81c2.605 0 4.495.566 5.617 1.68 1.16 1.152 1.558 2.83 1.181 4.99-.74 4.241-3.924 6.374-9.46 6.337H6.308l-.935 5.267z"/>
                    </svg>
                    <!-- SEPA -->
                    <svg v-else-if="method.type?.toLowerCase().includes('sepa') || method.type?.toLowerCase().includes('directdebit')" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2L2 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-10-5z"/>
                    </svg>
                    <!-- Default card icon -->
                    <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="font-medium">{{ method.description }}</div>
                    <div class="text-xs text-base-content/50 uppercase">{{ getPaymentMethodTypeLabel(method.type) }}</div>
                  </div>
                  <div v-if="method.isDefault" class="badge badge-primary badge-sm ml-2">Default</div>
                </div>
                <div class="flex items-center gap-2">
                  <button
                    v-if="!method.isDefault"
                    class="btn btn-outline btn-sm"
                    @click="setDefaultPaymentMethod(method.id)"
                  >Make default</button>
                  <button 
                    class="btn btn-ghost btn-sm text-error" 
                    :class="{ 'cursor-not-allowed opacity-50': method.isDefault && hasActiveSubscription }"
                    :disabled="method.isDefault && hasActiveSubscription"
                    @click="removePaymentMethod(method.id)"
                  >Remove</button>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8 text-base-content/70">
              <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <p>No payment methods added</p>
              <p class="text-sm">Add a payment method to upgrade your plan</p>
            </div>
          </div>

          <!-- Billing History -->
          <div class="border-t border-base-300 pt-6">
            <h4 class="text-base font-medium mb-4">Recent payments</h4>

            <!-- Recent Payments -->
            <div class="mb-6">
              <div v-if="billingInfo.recentPayments.length > 0" class="space-y-3">
                <div
                  v-for="payment in billingInfo.recentPayments"
                  :key="payment.id"
                  class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-base-100 rounded-lg border border-base-300 hover:border-primary/30 transition-colors gap-3"
                >
                  <div class="flex-1 min-w-0">
                    <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                      <span :class="['badge badge-sm flex-shrink-0', payment.status === 'paid' ? 'badge-success' : payment.status === 'failed' ? 'badge-error' : 'badge-ghost']">
                        {{ payment.status.toUpperCase() }}
                      </span>
                      <p class="font-medium text-base-content break-words">{{ payment.description }}</p>
                    </div>
                    <div class="text-sm text-base-content/70 mt-1">
                      {{ formatDate(payment.paidAt || payment.createdAt) }}
                    </div>
                  </div>
                  <div class="flex flex-row items-center gap-3">
                    <div class="text-left sm:text-right">
                      <div class="font-medium">€{{ Number(payment.amount.value).toFixed(2) }}</div>
                      <div class="text-xs text-base-content/70" v-if="payment.method">
                        {{ payment.method.toUpperCase() }}
                      </div>
                    </div>
                    <!-- Invoice Download Button -->
                    <div v-if="payment.invoiceId && payment.status === 'paid'" class="flex-shrink-0">
                      <a 
                        :href="`/api/invoices/${payment.invoiceId}/download`" 
                        target="_blank"
                        class="btn btn-outline btn-sm btn-square hover:bg-primary/10"
                        title="Download Invoice"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="flex justify-center mt-4" v-if="canLoadMorePayments">
                  <button class="btn btn-outline btn-sm" @click="loadMorePayments" :disabled="loadingMorePayments">
                    <span v-if="loadingMorePayments" class="loading loading-spinner loading-xs mr-1"></span>
                    Load more
                  </button>
                </div>
              </div>

              <div v-else class="text-center py-6 text-base-content/70">
                <svg class="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p class="text-sm">No payment history</p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Plan Upgrade Modal -->
    <div v-if="showUpgradeModal" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-bold">Upgrade to Pro</h3>
          <!-- Interval Toggle in top-right -->
          <div class="flex items-center gap-2 bg-base-200 rounded-lg p-1">
            <button
              @click="selectedInterval = 'monthly'"
              :class="[
                'px-3 py-1.5 rounded-md text-sm font-medium transition-all',
                selectedInterval === 'monthly' ? 'bg-base-100 shadow-sm' : 'text-base-content/70'
              ]"
            >
              Monthly
            </button>
            <button
              @click="selectedInterval = 'yearly'"
              :class="[
                'px-3 py-1.5 rounded-md text-sm font-medium transition-all',
                selectedInterval === 'yearly' ? 'bg-base-100 shadow-sm' : 'text-base-content/70'
              ]"
            >
              Yearly
              <span v-if="selectedInterval === 'yearly'" class="ml-1 text-xs text-success">-17%</span>
            </button>
          </div>
        </div>

        <!-- Pro Plan Card -->
        <div class="card bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 mb-6">
          <div class="card-body">
            <div class="flex items-start justify-between">
              <div>
                <h4 class="text-lg font-bold text-primary mb-1">Pro Plan</h4>
                <div class="text-3xl font-bold">
                  €{{ selectedInterval === 'yearly' ? proPlan.price?.yearly?.toFixed(2) || '99.50' : proPlan.price?.monthly?.toFixed(2) || '9.95' }}
                  <span class="text-base font-normal text-base-content/60">
                    /{{ selectedInterval === 'yearly' ? 'year' : 'month' }}
                  </span>
                </div>
              </div>
              <div v-if="selectedInterval === 'yearly'" class="text-right">
                <div class="text-sm text-base-content/60 line-through">€119.40/year</div>
                <div class="text-sm font-medium text-success">Save €19.90</div>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">{{ proPlan.monthlyEmailLimit?.toLocaleString() || '1,000' }} emails/month</span>
              </div>
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">{{ proPlan.domains || 5 }} domains + {{ proPlan.aliases || 50 }} aliases</span>
              </div>
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">Custom webhook headers</span>
              </div>
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">S3 attachment storage</span>
              </div>
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">SPAM filtering</span>
              </div>
              <div class="flex items-start gap-2">
                <svg class="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-sm">20% discount on credits</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Method Selection -->
        <div class="space-y-3">
          <h4 class="font-semibold">Choose payment method</h4>
          
          <!-- Default payment options -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <!-- Credit Card -->
            <label class="relative cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="creditcard"
                v-model="selectedPaymentMethod"
                class="sr-only"
              >
              <div 
                class="border-2 rounded-lg p-4 transition-all hover:border-base-300"
                :class="{
                  'border-primary bg-primary/5': selectedPaymentMethod === 'creditcard',
                  'border-base-300': selectedPaymentMethod !== 'creditcard'
                }"
              >
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-lg bg-base-200 flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="font-medium">Credit Card</div>
                    <div class="text-xs text-base-content/60">Visa, Mastercard, Amex</div>
                  </div>
                  <div 
                    class="w-5 h-5 rounded-full border-2 relative transition-all"
                    :class="{
                      'border-primary bg-primary': selectedPaymentMethod === 'creditcard',
                      'border-base-300': selectedPaymentMethod !== 'creditcard'
                    }"
                  >
                    <div 
                      class="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-opacity"
                      :class="{
                        'opacity-100': selectedPaymentMethod === 'creditcard',
                        'opacity-0': selectedPaymentMethod !== 'creditcard'
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </label>

            <!-- Direct Debit -->
            <label class="relative cursor-pointer">
              <input
                type="radio"
                name="paymentMethod"
                value="directdebit"
                v-model="selectedPaymentMethod"
                class="sr-only"
              >
              <div 
                class="border-2 rounded-lg p-4 transition-all hover:border-base-300"
                :class="{
                  'border-primary bg-primary/5': selectedPaymentMethod === 'directdebit',
                  'border-base-300': selectedPaymentMethod !== 'directdebit'
                }"
              >
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-lg bg-base-200 flex items-center justify-center">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="font-medium">Direct Debit</div>
                    <div class="text-xs text-base-content/60">SEPA bank transfer</div>
                  </div>
                  <div 
                    class="w-5 h-5 rounded-full border-2 relative transition-all"
                    :class="{
                      'border-primary bg-primary': selectedPaymentMethod === 'directdebit',
                      'border-base-300': selectedPaymentMethod !== 'directdebit'
                    }"
                  >
                    <div 
                      class="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-opacity"
                      :class="{
                        'opacity-100': selectedPaymentMethod === 'directdebit',
                        'opacity-0': selectedPaymentMethod !== 'directdebit'
                      }"
                    ></div>
                  </div>
                </div>
              </div>
            </label>
          </div>

          <!-- Alternative payment option -->
          <div class="relative">
            <div class="divider text-xs text-base-content/60">OR</div>
            <button
              @click="showOneOffOption = !showOneOffOption"
              class="w-full text-left text-sm text-primary hover:text-primary-focus transition-colors flex items-center gap-2"
            >
              <svg 
                :class="['w-4 h-4 transition-transform', showOneOffOption ? 'rotate-90' : '']"
                fill="none" stroke="currentColor" viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
              Pay with bank transfer or iDEAL instead
              <div class="tooltip tooltip-right" data-tip="We'll send you a payment link each month via email. Great for those who prefer manual payments.">
                <svg class="w-4 h-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </button>
            
            <!-- One-off payment details (collapsible) -->
            <div v-if="showOneOffOption" class="mt-3 p-4 bg-base-200/30 rounded-lg border border-base-300">
              <label class="cursor-pointer">
                <div class="flex items-start gap-3">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="oneoff"
                    v-model="selectedPaymentMethod"
                    class="radio radio-primary mt-0.5"
                  >
                  <div class="flex-1">
                    <div class="font-medium">Manual payment (iDEAL, Bank Transfer, etc.)</div>
                    <div class="text-sm text-base-content/60 mt-1">
                      Pay month-by-month with your preferred payment method
                    </div>
                    <div class="mt-3 space-y-2 text-xs text-base-content/60">
                      <div class="flex items-start gap-2">
                        <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span>We'll email you a payment link 3 days before renewal</span>
                      </div>
                      <div class="flex items-start gap-2">
                        <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>Supports iDEAL, PayPal, bank transfer, and more</span>
                      </div>
                      <div class="flex items-start gap-2">
                        <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>No automatic charges - you're in control</span>
                      </div>
                    </div>
                    <a href="/help/manual-payments" class="text-primary hover:text-primary-focus text-xs mt-2 inline-block">Learn more →</a>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <div class="modal-action">
          <button @click="closeUpgradeModal" class="btn btn-ghost">Cancel</button>
          <button
            @click="proceedToPayment"
            :disabled="!selectedPaymentMethod || isProcessingPayment"
            class="btn btn-primary"
          >
            {{ isProcessingPayment ? 'Processing...' : getPaymentButtonText() }}
          </button>
        </div>
      </div>
    </div>


    <!-- Cancel Subscription Modal -->
    <div v-if="showCancelModal" class="modal modal-open">
      <div class="modal-box max-w-md">
        <h3 class="text-lg font-bold mb-4 text-error">Are you sure to cancel?</h3>

        <div class="space-y-4 mb-6">
          <ul class="space-y-2 text-sm">
            <p class="text-sm text-base-content/70">
              Canceling your subscription will:
            </p>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Downgrade your account to the Free plan at the end of your current billing period</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Reduce your email limit to {{ freePlan.monthlyEmailLimit }} emails per month</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Remove access to features like custom headers and attachment storage</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-error">•</span>
              <span>Limit you to 1 domain, {{ freePlan.aliases }} aliases, and {{ freePlan.webhooks }} webhooks</span>
            </li>
          </ul>

          <div class="bg-base-300/10 border border-base-300/20 rounded-lg p-3">
            <p class="text-sm text-base-content/70">
              <strong>Note:</strong> you'll continue to have Pro access until {{ billingInfo?.currentPlan.nextPaymentDate ? formatDate(billingInfo.currentPlan.nextPaymentDate) : 'the end of your billing period' }}.
            </p>
          </div>
        </div>

        <div class="modal-action">
          <button @click="showCancelModal = false" class="btn btn-ghost">
            <span class="hidden sm:inline">Keep subscription</span>
            <span class="sm:hidden">No, keep</span>
          </button>
          <button
            @click="cancelSubscription"
            :disabled="isCancelling"
            class="btn btn-error"
          >
            <span v-if="isCancelling">Cancelling...</span>
            <span v-if="!isCancelling" class="hidden sm:inline">Yes, cancel subscription</span>
            <span v-if="!isCancelling" class="sm:hidden">Yes, cancel</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- Post-return confirmation modal -->
  <input type="checkbox" class="modal-toggle" v-model="showReturnModal" />
  <div class="modal">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-2">{{ returnModalTitle }}</h3>
      <div class="flex items-center gap-3" v-if="returnModalStatus === 'processing'">
        <span class="loading loading-spinner loading-md"></span>
        <p>{{ returnModalMessage }}</p>
      </div>
      <div v-else-if="returnModalStatus === 'success'" class="text-success">
        <p>{{ returnModalMessage }}</p>
      </div>
      <div v-else class="text-warning">
        <p>{{ returnModalMessage }}</p>
      </div>
      <div class="mt-4 text-sm text-base-content/70" v-if="returnModalStatus === 'processing'">
        Max {{ returnTimerSeconds }}s remaining...
      </div>
      <div class="modal-action">
        <button class="btn" @click="showReturnModal = false">Close</button>
      </div>
    </div>
  </div>
  <!-- End post-return modal -->


</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import type { BillingInfo } from '../../../shared/types/payment'
import { usePlanConfig } from '../../composables/usePlanConfig'
import GrandfatheringBadge from './GrandfatheringBadge.vue'
import { api } from '../../utils/api'
import { logger } from '../../utils/logger';

// State
const billingInfo = ref<BillingInfo | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

// Payments pagination
const paymentsLimit = ref(5)
const canLoadMorePayments = ref(true)
const loadingMorePayments = ref(false)

const loadMorePayments = async () => {
  try {
    loadingMorePayments.value = true
    paymentsLimit.value += 5
    const data = await api.get(`/api/billing/info?paymentsLimit=${paymentsLimit.value}`)
    if (data.success && billingInfo.value) {
      const newList = data.data.recentPayments || []
      canLoadMorePayments.value = newList.length >= paymentsLimit.value
      billingInfo.value.recentPayments = newList
    }
  } finally {
    loadingMorePayments.value = false
  }
}

// Plan configuration
const { freePlan, proPlan, freeCreditPricing, proCreditPricing, loadPlanConfig } = usePlanConfig()

// Show Renew now button inside T-3 day window for manual renewals
const RENEW_WINDOW_DAYS = 3
const shouldShowRenewButton = computed(() => {
  const plan = billingInfo.value?.currentPlan
  if (!plan || plan.status !== 'active' || !plan.nextPaymentDate) return false
  if (plan.canManualRenew === false) return false
  const next = new Date(plan.nextPaymentDate)
  const now = new Date()
  const diffDays = Math.ceil((next.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
  return diffDays <= RENEW_WINDOW_DAYS && diffDays >= 0
})

const renewNow = () => {
  window.location.href = '/api/payments/renew'
}
const showUpgradeModal = ref(false)
const showCancelModal = ref(false)
const isCancelling = ref(false)

// Plan selection state
const selectedPlan = ref<'pro' | null>('pro')
const selectedPaymentMethod = ref<'creditcard' | 'directdebit' | 'oneoff'>('creditcard')
const showOneOffOption = ref(false)
// Post-return confirmation modal state
const showReturnModal = ref(false)
const returnModalTitle = ref('Processing your payment')
const returnModalMessage = ref('Please wait while we confirm your payment...')
const returnModalStatus = ref<'processing' | 'success' | 'pending'>('processing')
const returnTimerSeconds = ref(60)
let returnPollTimer: number | null = null
let countdownTimer: number | null = null

const startReturnPolling = async (kind: 'credits' | 'mandate' | 'subscription') => {
  logger.debug('Starting return polling for:', kind)
  showReturnModal.value = true
  
  if (kind === 'credits') {
    returnModalTitle.value = 'Processing your credits purchase'
  } else if (kind === 'subscription') {
    returnModalTitle.value = 'Activating your subscription'
  } else {
    returnModalTitle.value = 'Setting up your subscription'
  }
  
  returnModalMessage.value = 'Please wait while we confirm the result...'
  returnModalStatus.value = 'processing'
  returnTimerSeconds.value = 60

  // Store the initial payment count to detect new payments
  const initialPaymentCount = billingInfo.value?.recentPayments?.length || 0

  // Countdown
  if (countdownTimer) clearInterval(countdownTimer)
  countdownTimer = window.setInterval(() => {
    if (returnTimerSeconds.value > 0) returnTimerSeconds.value -= 1
  }, 1000) as any

  // Helper function to check success conditions
  const checkSuccess = async () => {
    await loadBillingInfo()

    if (kind === 'credits') {
      // Check if there are new paid payments or if the most recent payment is paid
      const recentPayments = billingInfo.value?.recentPayments || []
      const hasNewPaidPayment = recentPayments.length > initialPaymentCount || 
        (recentPayments.length > 0 && recentPayments[0].status === 'paid')
      
      if (hasNewPaidPayment) {
        returnModalStatus.value = 'success'
        returnModalMessage.value = 'Payment confirmed. Your balance has been updated.'
        clearInterval(returnPollTimer!)
        clearInterval(countdownTimer!)
        logger.debug('Credits payment confirmed')
        return true
      }
    } else if (kind === 'mandate') {
      // Success criteria: plan becomes pro
      const isPro = billingInfo.value?.currentPlan?.type === 'pro'
      if (isPro) {
        returnModalStatus.value = 'success'
        returnModalMessage.value = 'Subscription activated. Welcome to Pro!'
        clearInterval(returnPollTimer!)
        clearInterval(countdownTimer!)
        logger.debug('Subscription activated')
        return true
      }
    } else if (kind === 'subscription') {
      // Success criteria: plan is pro (subscription was already created successfully)
      const isPro = billingInfo.value?.currentPlan?.type === 'pro'
      if (isPro) {
        returnModalStatus.value = 'success'
        returnModalMessage.value = 'Subscription activated successfully!'
        clearInterval(returnPollTimer!)
        clearInterval(countdownTimer!)
        logger.debug('Subscription confirmed')
        return true
      }
    }
    return false
  }

  // Check immediately first
  if (await checkSuccess()) {
    return
  }

  // Poll billing info every 3s up to 60s
  const start = Date.now()
  if (returnPollTimer) clearInterval(returnPollTimer)
  returnPollTimer = window.setInterval(async () => {
    if (await checkSuccess()) {
      return
    }

    if (Date.now() - start > 60000) {
      // Plan B timeout reached
      clearInterval(returnPollTimer!)
      clearInterval(countdownTimer!)
      returnModalStatus.value = 'pending'
      returnModalMessage.value = 'We are still waiting for confirmation from the payment provider. You can close this and we\'ll keep monitoring in the background.'
      logger.debug('Polling timeout reached')
    }
  }, 3000) as any
}

const selectedInterval = ref<'monthly' | 'yearly'>('monthly')
const isProcessingPayment = ref(false)

// Credit purchase state
const selectedCreditPackage = ref<number>(100)
const isPurchasingCredits = ref(false)

// Computed
const creditPackages = computed(() => {
  if (!billingInfo.value) return []

  const isPro = billingInfo.value.currentPlan.type === 'pro'
  const pricePerHundred = isPro ? 0.80 : 1.00

  return [
    {
      credits: 100,
      price: pricePerHundred,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    },
    {
      credits: 500,
      price: pricePerHundred * 5,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    },
    {
      credits: 1000,
      price: pricePerHundred * 10,
      pricePerEmail: '€' + (pricePerHundred / 100).toFixed(3)
    }
  ]

})

// Methods
const loadPaymentMethods = async () => {
  if (!billingInfo.value) return
  try {
    const resp = await api.get('/api/subscriptions/payment-methods')
    if (resp.success && billingInfo.value) {
      billingInfo.value.paymentMethods = resp.paymentMethods || []
    }
  } catch (e) {
    console.warn('Failed to load payment methods', e)
  }
}

const loadBillingInfo = async () => {
  try {
    isLoading.value = true
    error.value = null

    const data = await api.get('/api/billing/info')

    if (data.success) {
      billingInfo.value = data.data
      // Update pagination 'Load more' availability based on current list length
      const list = billingInfo.value?.recentPayments || []
      canLoadMorePayments.value = list.length >= paymentsLimit.value
    } else {
      throw new Error(data.message || 'Invalid response format')
    }
  } catch (err: any) {
    if (err.name === 'AbortError') {
      error.value = 'Request timed out. Please check your connection and try again.'
      console.error('Billing info loading timed out')
    } else {
      error.value = err.message
      console.error('Failed to load billing info:', err)
    }
  } finally {
    isLoading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getPaymentMethodIconClass = (type: string) => {
  const lowerType = type?.toLowerCase() || ''
  if (lowerType.includes('visa')) return 'bg-blue-100 text-blue-600'
  if (lowerType.includes('mastercard')) return 'bg-red-100 text-red-600'
  if (lowerType.includes('amex') || lowerType.includes('american')) return 'bg-blue-100 text-blue-700'
  if (lowerType.includes('paypal')) return 'bg-blue-100 text-blue-600'
  if (lowerType.includes('sepa') || lowerType.includes('directdebit')) return 'bg-green-100 text-green-600'
  return 'bg-primary/20 text-primary'
}

const getPaymentMethodTypeLabel = (type: string) => {
  const lowerType = type?.toLowerCase() || ''
  if (lowerType.includes('visa')) return 'Visa'
  if (lowerType.includes('mastercard')) return 'Mastercard'
  if (lowerType.includes('amex') || lowerType.includes('american')) return 'Amex'
  if (lowerType.includes('paypal')) return 'PayPal'
  if (lowerType.includes('sepa') || lowerType.includes('directdebit')) return 'SEPA'
  return type || 'Card'
}

const getUsagePercentage = (type: keyof BillingInfo['usage']) => {
  if (!billingInfo.value) return 0
  const usage = billingInfo.value.usage[type]
  const limit = billingInfo.value.limits[type]
  return Math.min((usage / limit) * 100, 100)
}



const getMonthlyUsagePercentage = () => {
  if (!billingInfo.value) return 0

  // Use emailBreakdown if available, otherwise fall back to basic usage/limits
  if (billingInfo.value.emailBreakdown) {
    const { monthlyUsed, monthlyAllowance } = billingInfo.value.emailBreakdown
    return Math.min((monthlyUsed / monthlyAllowance) * 100, 100)
  }

  // Fallback to basic usage data
  const usage = billingInfo.value.usage.emails
  const limit = billingInfo.value.limits.emails
  return Math.min((usage / limit) * 100, 100)
}

const getMonthlyUsageColor = () => {
  const percentage = getMonthlyUsagePercentage()
  if (percentage >= 90) return 'text-error bg-error'
  if (percentage >= 75) return 'text-warning bg-warning'
  return 'text-success bg-success'
}

const getPaymentButtonText = () => {
  if (selectedPaymentMethod.value === 'oneoff') {
    return 'Continue to payment'
  }
  return 'Start subscription'
}

const closeUpgradeModal = () => {
  showUpgradeModal.value = false
  showOneOffOption.value = false
  selectedPaymentMethod.value = 'creditcard'
}

const proceedToPayment = async () => {
  if (!selectedPlan.value || !selectedPaymentMethod.value) return

  try {
    isProcessingPayment.value = true

    const plan = selectedPlan.value
    const interval = selectedInterval.value
    const currency = 'EUR'
    const amountNum = plan === 'pro'
      ? (interval === 'yearly' ? (proPlan.value.price?.yearly ?? 99.50) : (proPlan.value.price?.monthly ?? 9.95))
      : 0 // extend if more plans later

    const origin = window.location.origin
    const successUrl = `${origin}/settings/billing?subscription=success&plan=${plan}&interval=${interval}`
    const cancelUrl = `${origin}/settings/billing?subscription=cancelled`

    if (selectedPaymentMethod.value === 'oneoff') {
      // One-off payment - will create virtual subscription after payment
      logger.debug(`Creating one-off payment: ${plan} ${interval}`, { amountNum })
      
      const data = await api.post('/api/payments/create', {
        planType: plan,
        interval: 'one-time', // Mark as one-time for special handling
        amount: amountNum,
        currency,
        description: `${plan === 'pro' ? 'Pro' : plan} plan - ${interval} (Manual payment)`,
        successUrl,
        cancelUrl,
        metadata: {
          planType: plan,
          interval,
          paymentType: 'oneoff',
          virtualSubscription: true
        }
      })
      
      if (data.success && data.checkoutUrl) {
        window.location.href = data.checkoutUrl
      } else {
        throw new Error(data.message || 'Failed to create one-off payment')
      }
    } else {
      // Recurring subscription (existing flow)
      logger.debug(`Creating subscription: ${plan} ${interval}`, { amountNum })

      const data = await api.post('/api/subscriptions/create-with-first-payment', {
        planType: plan,
        interval,
        amount: { value: amountNum.toFixed(2), currency },
        description: `${plan === 'pro' ? 'Pro' : plan} plan - ${interval}`,
        successUrl,
        cancelUrl,
        method: selectedPaymentMethod.value
      })

      logger.debug('Subscription creation API response:', data)

      if (data.success) {
        if (data.checkoutUrl) {
          // Redirect to checkout if payment is required
          window.location.href = data.checkoutUrl
        } else {
          // Subscription was created immediately (existing mandate)
          logger.debug('Subscription created immediately with existing mandate')
          
          // Show user-friendly message about using existing payment method
          const existingMethod = billingInfo.value?.paymentMethods?.find(m => m.isDefault) 
            || billingInfo.value?.paymentMethods?.[0]
          const methodText = existingMethod ? 
            `your ${getPaymentMethodTypeLabel(existingMethod.type).toLowerCase()} ending in ${existingMethod.description.split(' ').pop()}` :
            'your saved payment method'
            
          alert(`✅ Subscription activated successfully!\n\nWe've charged ${methodText} and your Pro plan is now active.`)
          
          await startReturnPolling('subscription')
        }
      } else {
        throw new Error(data.message || 'Invalid response from server')
      }
    }
  } catch (err: any) {
    console.error('Subscription creation failed:', err)

    let userMessage = 'Failed to start subscription. Please try again.'
    if (err.name === 'AbortError') {
      userMessage = 'Request timed out. Please check your connection and try again.'
    } else if (err.message?.includes('HTTP 403')) {
      userMessage = 'You do not have permission to create subscriptions.'
    } else if (err.message) {
      userMessage = err.message
    }

    alert(userMessage)
  } finally {
    isProcessingPayment.value = false
  }
}

const purchaseCredits = async () => {
  if (!selectedCreditPackage.value) return


  try {
    isPurchasingCredits.value = true

    logger.debug('Purchasing credits:', selectedCreditPackage.value)
    const origin = window.location.origin

    const data = await api.post('/api/billing/credits', {
      creditAmount: selectedCreditPackage.value,
      successUrl: `${origin}/settings/billing?credits=success`,
      cancelUrl: `${origin}/settings/billing?credits=cancelled`
    })
    logger.debug('Credit purchase API response data:', data)

    if (data.success && data.payment?.checkoutUrl) {
      window.location.href = data.payment.checkoutUrl
    } else {
      throw new Error(data.message || 'Invalid credit purchase response - no checkout URL provided')
    }
  } catch (err: any) {
    console.error('Credit purchase failed:', err)

    let userMessage = 'Failed to create credit purchase. Please try again.'
    if (err.name === 'AbortError') {
      userMessage = 'Credit purchase timed out. Please check your connection and try again.'
    } else if (err.message?.includes('HTTP 402')) {
      userMessage = 'Payment required. Please add a payment method first.'
    } else if (err.message?.includes('HTTP 403')) {
      userMessage = 'You do not have permission to purchase credits.'
    } else if (err.message) {
      userMessage = err.message
    }

    alert(userMessage)
  } finally {
    isPurchasingCredits.value = false
  }
}

const syncPaymentMethods = async () => {
  const resp = await api.post('/api/subscriptions/sync-payment-methods', {})
  if (!resp.success) throw new Error('Failed to sync payment methods')
  // Reload billing info to show methods
  await loadBillingInfo()
}

// Computed property to check if user has an active subscription
const hasActiveSubscription = computed(() => {
  return billingInfo.value?.currentPlan?.status === 'active'
})

const removePaymentMethod = async (paymentMethodId: string) => {
  try {
    await api.delete(`/api/payment-methods/${paymentMethodId}`)
    await syncPaymentMethods()
  } catch (e: any) {
    alert(e.message || 'Failed to remove payment method')
  }
}

const setDefaultPaymentMethod = async (paymentMethodId: string) => {
  try {
    const resp = await api.put(`/api/payment-methods/${paymentMethodId}/default`, {})
    if (!resp.success) throw new Error('Failed to set default payment method')
    await syncPaymentMethods()
  } catch (e: any) {
    alert(e.message || 'Failed to set default payment method')
  }
}

onUnmounted(() => {
  if (returnPollTimer) clearInterval(returnPollTimer)
  if (countdownTimer) clearInterval(countdownTimer)
})

const addPaymentMethod = async () => {
  try {
    const origin = window.location.origin
    const successUrl = `${origin}/settings/billing?mandate=success`
    const cancelUrl = `${origin}/settings/billing?mandate=cancelled`


    const data = await api.post('/api/subscriptions/mandate-payment', {
      amount: { value: '0.01', currency: 'EUR' },
      description: 'Add payment method',
      successUrl,
      cancelUrl,
      method: 'creditcard' // Default to credit card for adding payment method
    })
    if (data.success && data.checkoutUrl) {
      window.location.href = data.checkoutUrl
    } else {
      throw new Error(data.message || 'Failed to start add payment method')
    }
  } catch (e: any) {
    alert(e.message || 'Failed to start add payment method')
  }
}

onMounted(async () => {
  await Promise.all([
    loadBillingInfo(),
    loadPlanConfig(),
    loadPaymentMethods(),
  ])

  // After return from Mollie, finalize subscription onboarding if applicable
  const params = new URLSearchParams(window.location.search)
  const subscriptionStatus = params.get('subscription')
  const mandateStatus = params.get('mandate')
  const creditsStatus = params.get('credits')
  const renewalStatus = params.get('renewal')

  // Handle new subscription flow (subscription created directly)
  if (subscriptionStatus === 'success') {
    try {
      // Just refresh billing info - subscription is already created
      await loadBillingInfo()
      startReturnPolling('subscription')
    } catch (e: any) {
      console.error('Failed to refresh billing info after subscription', e)
    }
  }
  // Handle legacy mandate flow (for existing payment methods)
  else if (mandateStatus === 'success') {
    try {
      await syncPaymentMethods()
      // Only create subscription when we have intended plan + interval
      const plan = params.get('plan') as 'pro' | null
      const interval = params.get('interval') as 'monthly' | 'yearly' | null
      if (plan && (interval === 'monthly' || interval === 'yearly')) {
        const res = await api.post('/api/subscriptions', {
          planType: plan,
          interval
        })
        if (!res.success) throw new Error('Failed to create subscription')
        await loadBillingInfo()
      } else {
        await loadBillingInfo()
      }
      startReturnPolling('mandate')
    } catch (e: any) {
      console.error('Finalize subscription failed', e)
      alert(e.message || 'Failed to finalize subscription')
    }
  }
  // Handle credit purchases
  else if (creditsStatus === 'success') {
    startReturnPolling('credits')
  }
  // Handle renewal payments
  else if (renewalStatus === 'success') {
    try {
      await loadBillingInfo()
      alert('✅ Subscription renewed successfully!\n\nYour subscription has been renewed and is now active.')
    } catch (e: any) {
      console.error('Failed to refresh billing info after renewal', e)
      alert('Renewal payment successful, but failed to refresh billing info. Please refresh the page.')
    }
  }
  else if (renewalStatus === 'cancelled') {
    alert('Renewal payment was cancelled. Your subscription will expire unless renewed.')
  }

})

const cancelSubscription = async () => {
  try {
    isCancelling.value = true
    logger.debug('Cancelling subscription...')

    // Fetch subscriptions and pick the latest active/pending
    const list = await api.get('/api/subscriptions')
    if (!list.success || !Array.isArray(list.subscriptions) || list.subscriptions.length === 0) {
      throw new Error('No active subscription found to cancel.')
    }

    const active = list.subscriptions.find((s: any) => s.status === 'active') || list.subscriptions[0]
    if (!active?.id) throw new Error('No active subscription found to cancel.')

    const res = await api.delete(`/api/subscriptions/${active.id}`)
    if (res.success) {
      await loadBillingInfo()
      showCancelModal.value = false
      alert('Subscription cancelled. You will keep access until the end of your current billing period.')
    } else {
      throw new Error(res.message || 'Failed to cancel subscription')
    }
  } catch (err: any) {
    console.error('Subscription cancellation failed:', err)
    let userMessage = 'Failed to cancel subscription. Please try again or contact support.'
    if (err.name === 'AbortError') {
      userMessage = 'Cancellation request timed out. Please check your connection and try again.'
    } else if (err.message?.includes('404')) {
      userMessage = 'No active subscription found to cancel.'
    } else if (err.message?.includes('403')) {
      userMessage = 'You do not have permission to cancel this subscription.'
    } else if (err.message) {
      userMessage = err.message
    }
    alert(userMessage)
  } finally {
    isCancelling.value = false
  }
}


</script>
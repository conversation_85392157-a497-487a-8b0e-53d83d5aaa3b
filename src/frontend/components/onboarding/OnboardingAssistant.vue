<template>
  <Transition
    enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="translate-y-full sm:translate-y-0 sm:translate-x-full opacity-0"
    enter-to-class="translate-y-0 translate-x-0 opacity-100"
    leave-active-class="transition-all duration-300 ease-in"
    leave-from-class="translate-y-0 translate-x-0 opacity-100"
    leave-to-class="translate-y-full sm:translate-y-0 sm:translate-x-full opacity-0"
  >
    <div 
      v-if="shouldShow"
      class="fixed bottom-0 right-0 sm:bottom-6 sm:right-6 z-40 w-full sm:w-96 max-w-full"
    >
      <!-- Assistant Card -->
      <div 
        class="bg-base-100 border-t sm:border border-base-300 sm:rounded-lg shadow-2xl"
        :class="{ 'cursor-pointer': isMinimized }"
        @click="isMinimized ? expand() : null"
      >
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b border-base-300">
          <div class="flex items-center space-x-3">
            <div class="p-2 rounded-lg bg-primary/10">
              <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-base-content">
                {{ isMinimized ? 'Onboarding assistant' : currentStepTitle }}
              </h3>
              <p v-if="!isMinimized" class="text-xs text-base-content/60">
                Step {{ currentStepIndex + 1 }} of {{ totalSteps }}
              </p>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <!-- Progress indicator when minimized -->
            <div v-if="isMinimized" class="text-sm font-medium text-primary">
              {{ completedSteps }}/{{ totalSteps }}
            </div>
            
            <!-- Minimize/Close buttons -->
            <button
              @click.stop="isMinimized ? expand() : minimize()"
              class="btn btn-ghost btn-sm btn-square"
              :title="isMinimized ? 'Expand' : 'Minimize'"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path v-if="isMinimized" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M5 15l7-7 7 7" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <button
              @click.stop="dismiss"
              class="btn btn-ghost btn-sm btn-square"
              title="Dismiss onboarding"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Content (only when expanded) -->
        <div v-if="!isMinimized" class="p-4">
          <!-- Progress bar -->
          <div class="mb-4">
            <div class="w-full bg-base-300 rounded-full h-2">
              <div 
                class="bg-primary h-2 rounded-full transition-all duration-300"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
          </div>

          <!-- Step content -->
          <div class="space-y-4">
            <!-- Step 1: Send email -->
            <div v-if="currentStep === 'send-email'" class="space-y-3">
              <p class="text-sm text-base-content">
                Welcome! We've created a test endpoint for you to experience the full email-to-webhook flow. Send any email to:
              </p>
              
              <div class="bg-base-200 rounded-lg p-3">
                <div class="flex items-center gap-2">
                  <code class="text-sm font-mono text-primary flex-1">{{ testEmail }}</code>
                  <button 
                    @click="copyTestEmail"
                    class="btn btn-ghost btn-xs"
                    :class="{ 'btn-success': copied }"
                  >
                    <svg v-if="!copied" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex flex-col sm:flex-row gap-2">
                <button
                  @click="markEmailSent"
                  class="btn btn-outline btn-sm flex-1"
                >
                  I've sent the email
                </button>
              </div>
            </div>

            <!-- Step 2: Check logs -->
            <div v-if="currentStep === 'check-logs'" class="space-y-3">
              <p class="text-sm text-base-content">
                Great! Your email should arrive any moment. Let's check the logs to see it.
              </p>
              
              <div v-if="waitingForEmail" class="flex items-center space-x-2 text-sm text-base-content/60">
                <span class="loading loading-spinner loading-sm"></span>
                <span>Waiting for email to arrive...</span>
              </div>

              <button
                @click="navigateToLogs"
                class="btn btn-primary btn-sm w-full"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                View logs
              </button>
            </div>

            <!-- Step 3: View email details -->
            <div v-if="currentStep === 'view-details'" class="space-y-3">
              <p class="text-sm text-base-content">
                Perfect! Your email was delivered to your test webhook endpoint. Click on it in the logs to see both the email data and the webhook request.
              </p>
              
              <div class="text-sm text-base-content/60">
                This shows exactly how EmailConnect processes emails and delivers them to your endpoints.
              </div>

              <button
                @click="completeOnboarding"
                class="btn btn-success btn-sm w-full"
              >
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                Got it!
              </button>
            </div>

            <!-- Completion -->
            <div v-if="currentStep === 'complete'" class="space-y-3 text-center">
              <div class="text-4xl mb-2">🎉</div>
              <p class="text-sm text-base-content font-medium">
                Awesome! You've seen how EmailConnect works.
              </p>
              <p class="text-sm text-base-content/60">
                Ready to set up your own domain?
              </p>

              <div class="flex gap-2">
                <button
                  @click="createDomain"
                  class="btn btn-primary btn-sm flex-1"
                >
                  Create domain
                </button>
                <button
                  @click="dismiss"
                  class="btn btn-ghost btn-sm flex-1"
                >
                  Explore first
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOnboarding } from '@composables/useOnboarding'
import { useMetrics } from '@composables/useMetrics'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useToast } from '@composables/useToast'

const route = useRoute()
const router = useRouter()
const { success } = useToast()

// Composables
const { 
  shouldShowOnboarding, 
  completeStep, 
  dismissOnboarding,
  isStepCompleted 
} = useOnboarding()
const { metricsData } = useMetrics()
const { refreshState, triggerRefresh } = useDataRefresh()

// State
const isMinimized = ref(false)
const waitingForEmail = ref(false)
const copied = ref(false)

// Steps definition
const steps = [
  { id: 'send-email', title: 'Send a test email' },
  { id: 'check-logs', title: 'Check your logs' },
  { id: 'view-details', title: 'View email details' },
  { id: 'complete', title: 'All done!' }
]

// Computed
const testEmail = computed(() => {
  const userId = metricsData.value?.user?.id
  if (!userId) return 'Loading...'
  return `${userId.slice(-8)}+<EMAIL>`
})

const currentRoute = computed(() => route.path)

const currentStepIndex = computed(() => {
  if (isStepCompleted('view-details')) return 3 // complete
  if (isStepCompleted('check-logs')) return 2 // view-details
  if (isStepCompleted('send-email')) return 1 // check-logs
  return 0 // send-email
})

const currentStep = computed(() => steps[currentStepIndex.value].id)
const currentStepTitle = computed(() => steps[currentStepIndex.value].title)
const totalSteps = computed(() => steps.length - 1) // Don't count 'complete' in total
const completedSteps = computed(() => Math.min(currentStepIndex.value, totalSteps.value))
const progressPercentage = computed(() => (completedSteps.value / totalSteps.value) * 100)

const shouldShow = computed(() => {
  return shouldShowOnboarding.value && testEmail.value !== 'Loading...'
})

// Methods
const minimize = () => {
  isMinimized.value = true
}

const expand = () => {
  isMinimized.value = false
}

const dismiss = () => {
  dismissOnboarding()
}

const copyTestEmail = async () => {
  try {
    await navigator.clipboard.writeText(testEmail.value)
    copied.value = true
    success('Email address copied!')
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

const navigateToAliases = () => {
  router.push('/aliases')
}

const navigateToLogs = () => {
  // Set the filter to show test webhooks
  sessionStorage.setItem('logsView_selectedDomain', 'test-webhooks')
  router.push('/logs')
  completeStep('check-logs')
}

const markEmailSent = () => {
  completeStep('send-email')
  waitingForEmail.value = true
}

const completeOnboarding = () => {
  completeStep('view-details')
}

const createDomain = () => {
  dismiss()
  // Open create domain modal
  if ((window as any).openModal) {
    (window as any).openModal('create-domain', {
      onSuccess: () => {
        router.push('/domains')
      }
    })
  }
}

// Watch for email arrival (via aliases refresh or direct websocket events)
watch(() => refreshState.aliases, () => {
  if (waitingForEmail.value && currentStep.value === 'check-logs') {
    // Email arrived!
    waitingForEmail.value = false
    clearInteractionTimer() // Clear any auto-minimize timer
    // Auto-navigate to logs after a short delay
    setTimeout(() => {
      if (currentStep.value === 'check-logs') {
        navigateToLogs()
      }
    }, 1000)
  }
})

// Also listen to metrics updates which are triggered by email_processed events
watch(() => metricsData.value?.counts?.emails, (newCount, oldCount) => {
  if (waitingForEmail.value && currentStep.value === 'check-logs' && newCount && oldCount && newCount > oldCount) {
    // Email count increased, likely our test email arrived
    waitingForEmail.value = false
    clearInteractionTimer() // Clear any auto-minimize timer
    // Trigger refresh and navigate
    triggerRefresh('aliases')
    setTimeout(() => {
      if (currentStep.value === 'check-logs') {
        navigateToLogs()
      }
    }, 1000)
  }
})

// Auto-expand when step changes
watch(currentStepIndex, (newIndex, oldIndex) => {
  if (isMinimized.value && newIndex > oldIndex) {
    expand()
    // Clear any pending interaction timer when manually expanding
    clearInteractionTimer()
  }
})

// Auto-minimize after user interacts with the first step
let interactionTimeout: number | null = null
const startInteractionTimer = () => {
  if (interactionTimeout) clearTimeout(interactionTimeout)
  interactionTimeout = window.setTimeout(() => {
    // Only minimize if still on step 2 and not already minimized
    if (currentStepIndex.value === 1 && !isMinimized.value) {
      minimize()
    }
  }, 10000) // Increased to 10 seconds for better UX
}

// Clear timer when moving to next step or when email arrives
const clearInteractionTimer = () => {
  if (interactionTimeout) {
    clearTimeout(interactionTimeout)
    interactionTimeout = null
  }
}

watch(() => isStepCompleted('send-email'), (completed) => {
  if (completed) {
    startInteractionTimer()
  }
})

// Clear timer when moving past step 2
watch(currentStepIndex, (newIndex) => {
  if (newIndex > 1) {
    clearInteractionTimer()
  }
})

onUnmounted(() => {
  if (interactionTimeout) clearTimeout(interactionTimeout)
})
</script>

<style scoped>
/* Ensure the assistant is above other elements but below modals */
</style>
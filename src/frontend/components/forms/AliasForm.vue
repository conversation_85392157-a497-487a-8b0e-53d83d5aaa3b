<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useForm } from '../../composables/useForm'
import { useAliasApi, useDomainApi } from '../../composables/useApi'
import { useDataRefresh } from '../../composables/useDataRefresh'
import { useMetrics } from '../../composables/useMetrics'
import { usePermissions } from '../../composables/usePermissions'
import { useUserSettings } from '../../composables/useUserSettings'
import { api } from '../../utils/api'
import WebhookSelector from './WebhookSelector.vue'
import type { CreateAliasRequest, Domain, Webhook } from '../../types'

interface Props {
  initialData?: Partial<CreateAliasRequest & {
    id?: string
    allowAttachments?: boolean
    includeEnvelope?: boolean
    isCatchAll?: boolean
    attachmentHandling?: 'inline' | 'storage'
    s3Folder?: string
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [alias: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, setFieldError, handleSubmit, resetForm } = useForm<CreateAliasRequest & {
  allowAttachments?: boolean
  includeEnvelope?: boolean
  attachmentHandling?: 'inline' | 'storage'
  s3Folder?: string
}>({
  email: props.initialData.email || '',
  domainId: props.initialData.domainId || '',
  webhookId: props.initialData.webhookId || '',
  active: props.initialData.active ?? true,
  allowAttachments: props.initialData.allowAttachments ?? false,
  includeEnvelope: props.initialData.includeEnvelope ?? true,
  attachmentHandling: props.initialData.attachmentHandling || 'inline',
  s3Folder: props.initialData.s3Folder || ''
}, [
  { name: 'email', label: 'Email alias', type: 'email', required: true },
  { name: 'domainId', label: 'Domain', type: 'select', required: false }, // Made optional for system aliases
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true },
  { name: 'allowAttachments', label: 'Allow attachments', type: 'checkbox', required: false },
  { name: 'includeEnvelope', label: 'Include envelope data', type: 'checkbox', required: false },
  { name: 'attachmentHandling', label: 'Attachment handling', type: 'select', required: false },
  { name: 's3Folder', label: 'S3 Folder', type: 'text', required: false }
])

// API setup
const { createAlias, updateAlias } = useAliasApi()
const { getDomains } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics, metricsData } = useMetrics()
const { hasPermission, loadPermissions } = usePermissions()
const { settings: userSettings, loadSettings } = useUserSettings()

// State
const domains = ref<Domain[]>([])
const loadingDomains = ref(false)
const aliasPrefix = ref('')
const aliasType = ref<'custom' | 'system'>('custom') // Toggle between custom domain and system alias
const systemTag = ref('') // For system alias tags (<EMAIL>)

// Check if user is using custom S3 configuration
const isUsingCustomS3 = computed(() => {
  return !!(userSettings.value.s3Config?.bucket && 
            userSettings.value.s3Config?.accessKey && 
            userSettings.value.s3Config?.secretKey)
})

// S3 bucket name from user settings
const s3BucketName = computed(() => {
  if (isUsingCustomS3.value && userSettings.value.s3Config?.bucket) {
    return userSettings.value.s3Config.bucket
  }
  // Show generic system indicator when using default storage
  return 'eceu-files'
})

// Reset form when props change (modal reopened)
watch(() => props.initialData, () => {
  resetForm()
  // Re-initialize state
  aliasPrefix.value = ''
  systemTag.value = ''
  aliasType.value = 'custom'
}, { deep: true })

// Computed
const selectedDomain = computed(() =>
  customDomains.value.find(d => d.id === values.domainId)
)

const fullEmail = computed(() => {
  if (aliasType.value === 'system') {
    // System alias format: <EMAIL>
    const userId = metricsData.value?.user?.id
    if (userId && systemTag.value) {
      return `${userId.slice(-8)}+${systemTag.value}@user.emailconnect.eu`
    }
    return userId ? `${userId.slice(-8)}+...@user.emailconnect.eu` : ''
  } else {
    // Custom domain format: <EMAIL>
    if (aliasPrefix.value && selectedDomain.value) {
      return `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`
    }
    return ''
  }
})

// Get user ID suffix for system aliases
const userIdSuffix = computed(() => {
  const userId = metricsData.value?.user?.id
  return userId ? userId.slice(-8) : ''
})

// Filter out system domain from custom domains list
const customDomains = computed(() => 
  domains.value.filter(domain => 
    domain.domain !== 'user.emailconnect.eu' && 
    (domain.domainName || domain.domain) !== 'user.emailconnect.eu'
  )
)

// Smart default: show system alias if user has no custom domains
const hasCustomDomains = computed(() => customDomains.value.length > 0)

// Set smart default for alias type based on available domains
const setSmartDefault = () => {
  if (!props.isEditMode && !hasCustomDomains.value) {
    aliasType.value = 'system'
  }
}

// Set alias type based on existing alias domain in edit mode
const setAliasTypeFromData = () => {
  if (props.isEditMode && props.initialData?.email) {
    // Check if the alias uses the system domain
    if (props.initialData.email.includes('@user.emailconnect.eu')) {
      aliasType.value = 'system'
    } else {
      aliasType.value = 'custom'
    }
  }
}

// Check if user can use attachment storage (Pro feature)
const canUseAttachments = computed(() => {
  return hasPermission('custom_headers') // Pro users have custom_headers permission
})

// Check if S3 storage is available (Pro users on custom domains only)
const canUseS3Storage = computed(() => {
  const hasS3Storage = hasPermission('s3_storage')
  const isSystemDomain = aliasType.value === 'system'
  return hasS3Storage && !isSystemDomain
})

// Keep isProUser for backward compatibility with template
const isProUser = computed(() => {
  return canUseAttachments.value
})

// Auto-reset attachment handling to inline when switching to system domain
watch(aliasType, (newType) => {
  if (newType === 'system' && values.attachmentHandling === 'storage') {
    setFieldValue('attachmentHandling', 'inline')
  }
})

// Methods
const loadDomains = async () => {
  loadingDomains.value = true
  try {
    const response = await getDomains() as any
    domains.value = response.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    loadingDomains.value = false
  }
}

const onWebhookCreated = (webhook: Webhook) => {
  // The WebhookSelector component will automatically select the new webhook
}

// Initialize alias prefix based on initial data
const initializeAliasPrefix = () => {
  if (props.initialData.email) {
    if (props.initialData.isCatchAll) {
      aliasPrefix.value = 'catch-all'
    } else if (props.initialData.email.includes('@')) {
      aliasPrefix.value = props.initialData.email.split('@')[0]
    } else {
      aliasPrefix.value = props.initialData.email
    }
  }
}

const updateEmail = () => {
  if (props.isEditMode && props.initialData.isCatchAll) {
    // Don't update email for catch-all aliases in edit mode
    return
  }

  if (aliasType.value === 'system') {
    // System alias: use fullEmail computed property
    if (systemTag.value && userIdSuffix.value) {
      setFieldValue('email', fullEmail.value)
    }
  } else {
    // Custom domain alias
    if (aliasPrefix.value && selectedDomain.value) {
      setFieldValue('email', `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`)
    }
  }
}

// Watch for system tag changes to update email
const updateSystemEmail = () => {
  if (aliasType.value === 'system') {
    updateEmail()
  }
}

// Autofocus methods for tab switching
const focusCustomInput = async () => {
  await nextTick()
  const customInput = document.querySelector('.alias-custom-input') as HTMLInputElement
  if (customInput) {
    customInput.focus()
  }
}

const focusSystemInput = async () => {
  await nextTick()
  const systemInput = document.querySelector('.alias-system-input') as HTMLInputElement
  if (systemInput) {
    systemInput.focus()
  }
}

// Enhanced tab switching with autofocus
const switchToCustom = async () => {
  aliasType.value = 'custom'
  await focusCustomInput()
}

const switchToSystem = async () => {
  aliasType.value = 'system'
  await focusSystemInput()
}

const onSubmit = async () => {
  // Validation for custom domain aliases
  if (aliasType.value === 'custom') {
    if (!props.isEditMode && selectedDomain.value && selectedDomain.value.verificationStatus !== 'VERIFIED') {
      setFieldError('_form', 'Cannot create aliases for unverified domains.')
      return
    }
    if (!values.domainId) {
      setFieldError('_form', 'Please select a domain for your custom alias.')
      return
    }
  }
  
  // Validation for system aliases
  if (aliasType.value === 'system') {
    // In edit mode, the tag is immutable and already set; do not require re-entry
    if (!props.isEditMode) {
      if (!systemTag.value || systemTag.value.trim().length === 0) {
        setFieldError('_form', 'Please enter a tag for your system alias.')
        return
      }
      if (!/^[a-zA-Z0-9\-_]+$/.test(systemTag.value)) {
        setFieldError('_form', 'Tag can only contain letters, numbers, hyphens, and underscores.')
        return
      }
    }
    // For system aliases, backend will handle the domainId
    // We don't need to explicitly set domainId for system aliases
  }

  // Validate S3 access if using custom S3 bucket for storage
  if (values.allowAttachments && 
      values.attachmentHandling === 'storage' && 
      isUsingCustomS3.value && 
      userSettings.value.s3Config) {
    try {
      // Test S3 access before saving
      const testResult = await api.post('/api/settings/test-s3', {
        bucket: userSettings.value.s3Config.bucket,
        region: userSettings.value.s3Config.region,
        accessKey: userSettings.value.s3Config.accessKey,
        secretKey: userSettings.value.s3Config.secretKey,
        endpoint: userSettings.value.s3Config.endpoint
      })
      
      if (!testResult.success) {
        setFieldError('_form', 'Unable to access your S3 bucket. Please check your S3 settings.')
        return
      }
    } catch (error: any) {
      setFieldError('_form', `S3 validation failed: ${error.message || 'Please check your S3 settings'}`)
      return
    }
  }

  await handleSubmit(async (formData) => {
    let result: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing alias
      const updateData: any = {
        webhookId: formData.webhookId,
        allowAttachments: formData.allowAttachments,
        includeEnvelope: formData.includeEnvelope,
        attachmentHandling: formData.attachmentHandling,
        s3Folder: formData.s3Folder
      }
      // Only include email for custom domain aliases; system alias tag is immutable
      if (aliasType.value !== 'system') {
        updateData.email = formData.email
      }
      result = await updateAlias(props.initialData.id, updateData)
      emit('success', result.alias)
      refreshWithSuccess('Alias updated successfully!', 'aliases')
    } else {
      // Create mode - create new alias
      result = await createAlias(formData) as any

      // Check if webhook needs verification
      if (result.webhookNeedsVerification) {
        // Show success message with webhook verification guidance
        const message = result.aliasSetInactive
          ? 'Alias created but set to inactive. Please verify the webhook to activate and start receiving emails.'
          : 'Alias created successfully! Please verify the webhook to ensure reliable email delivery.'

        refreshWithSuccess(message, 'aliases')

        // Emit success with webhook verification info
        emit('success', {
          ...result.alias,
          webhookNeedsVerification: true,
          webhook: result.webhook
        })
      } else {
        // Standard success flow
        emit('success', result.alias)
        refreshWithSuccess('Alias created successfully!', 'aliases')
      }
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after alias operation:', error)
      }
    }, 500)
  })
}

onMounted(async () => {
  loadDomains()
  initializeAliasPrefix()
  // Set alias type based on existing data in edit mode
  setAliasTypeFromData()
  // Load permissions for Pro feature checking and user settings for S3 bucket name
  await Promise.all([
    loadPermissions(),
    loadSettings(),
    refreshMetrics() // Ensure we have user data for system aliases
  ])
  // Set smart default after loading data (only for create mode)
  setTimeout(async () => {
    if (!props.isEditMode) {
      setSmartDefault()
    }
    // Auto-focus the appropriate input after setting smart default
    if (aliasType.value === 'system') {
      await focusSystemInput()
    } else {
      await focusCustomInput()
    }
  }, 100)
  // WebhookSelector component handles loading webhooks
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Alias Type Tabs with Connected Content -->
    <div class="form-control" v-if="!isEditMode">
      <!-- Tab Headers -->
      <div class="flex border-b border-base-300">
        <button
          type="button"
          @click="switchToCustom"
          class="px-4 py-2 text-sm font-medium border-b-2 transition-colors"
          :class="aliasType === 'custom' 
            ? 'border-primary text-primary bg-base-100' 
            : 'border-transparent text-base-content/70 hover:text-base-content hover:border-base-300'"
        >
          Your domain
        </button>
        <button
          type="button"
          @click="switchToSystem"
          class="px-4 py-2 text-sm font-medium border-b-2 transition-colors"
          :class="aliasType === 'system' 
            ? 'border-primary text-primary bg-base-100' 
            : 'border-transparent text-base-content/70 hover:text-base-content hover:border-base-300'"
        >
          System alias
        </button>
        
        <!-- Spacer and tooltip -->
        <div class="flex-1 border-b-2 border-transparent"></div>
        <div class="tooltip tooltip-left px-2 py-2" data-tip="Choose between creating an alias under your domain or using the system domain with tags">
          <svg class="w-4 h-4 text-gray-400 cursor-help" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      </div>

      <!-- Tab Content Area -->
      <div class="border border-t-0 border-base-300 rounded-b-lg bg-base-100 p-4">
        <!-- Custom Domain Input -->
        <div v-if="aliasType === 'custom'" class="relative flex items-center min-w-0 bg-base-50 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
        <input
          v-model="aliasPrefix"
          @input="updateEmail"
          type="text"
          autocapitalize="none"
          spellcheck="false"
          :placeholder="props.initialData.isCatchAll ? 'catch-all' : 'support'"
          class="alias-custom-input w-1/3 sm:w-1/2 min-w-0 px-3 py-2 text-sm text-base-content bg-transparent border-0 placeholder:text-base-content/40 focus:outline-none focus:ring-0"
          :disabled="isEditMode"
          :readonly="props.isEditMode && props.initialData.isCatchAll"
          required
        />
        <div class="px-2 text-sm text-base-content/70 select-none">@</div>
        <select
          :value="values.domainId"
          @change="setFieldValue('domainId', ($event.target as HTMLSelectElement).value); updateEmail()"
          class="flex-1 min-w-0 py-2 pl-3 pr-8 text-sm text-base-content bg-transparent border-0 appearance-none cursor-pointer focus:outline-none focus:ring-0"
          :disabled="loadingDomains || isEditMode"
          required
        >
          <option value="">
            {{ loadingDomains ? 'Loading...' : 'Select domain...' }}
          </option>
          <option
            v-for="domain in customDomains"
            :key="domain.id"
            :value="domain.id"
          >
            {{ domain.domainName || domain.domain }}
          </option>
        </select>
        <svg class="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 pointer-events-none right-2 top-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

        <!-- System Domain Input (plus alias) -->
        <div v-if="aliasType === 'system'" class="relative flex items-center min-w-0 bg-base-50 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
        <div class="flex items-center px-2 sm:px-3 py-2 text-sm text-base-content/70">
          {{ userIdSuffix }}
        </div>
        <div class="px-1 text-sm text-base-content/70 select-none">+</div>
        <input
          v-model="systemTag"
          @input="updateSystemEmail"
          type="text"
          autocapitalize="none"
          spellcheck="false"
          placeholder="support"
          class="alias-system-input flex-1 min-w-0 px-2 sm:px-3 py-2 text-sm text-base-content bg-transparent border-0 placeholder:text-base-content/40 focus:outline-none focus:ring-0"
          pattern="^[a-zA-Z0-9\-_]+$"
          required
        />
          <div class="hidden sm:block px-2 text-sm text-base-content/70 select-none">@user.emailconnect.eu</div>
          <div class="sm:hidden px-2 text-sm text-base-content/70 select-none">@...</div>
        </div>

        <div v-if="errors.email" class="label">
          <span class="label-text-alt text-error">{{ errors.email }}</span>
        </div>
        
        <div class="label">
          <span class="label-text-alt text-xs">
            {{ aliasType === 'system' 
              ? 'A tagged alias using the system domain - no domain setup required' 
              : 'A unique alias to forward emails to your webhook'
            }}
          </span>
        </div>
      </div>
    </div>

    <!-- Email Alias (for edit mode - read-only display) -->
    <div class="form-control" v-if="isEditMode">
      <label class="label">
        <span class="label-text">Email alias</span>
      </label>
      
      <!-- Read-only email display for all aliases in edit mode -->
      <div class="bg-base-100 border border-base-300 rounded-lg p-3">
        <div class="flex items-center text-sm text-base-content">
          <span class="font-medium">{{ props.initialData.email }}</span>
        </div>
      </div>
      
      <div class="label">
        <span class="label-text-alt text-xs">
          Alias cannot be changed. Delete and recreate if needed.
        </span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-alias"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Configuration Options -->
    <div class="bg-base-200/40 rounded-lg p-4 border border-base-300 space-y-4">
      <div class="flex items-center gap-2 mb-2">
        <h3 class="font-medium">Advanced configuration</h3>
      </div>

      <!-- Allow Attachments -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.allowAttachments"
            @change="setFieldValue('allowAttachments', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Allow attachments</span>
            <span class="label-text-alt text-xs break-words">Process email attachments inline or store in bucket</span>
          </div>
        </label>
      </div>

      <!-- Attachment Handling Options (only show when attachments enabled) -->
      <div v-if="values.allowAttachments" class="ml-6 pl-4 space-y-3">
        <!-- Attachment Handling Choice - Card Layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <!-- Inline (Base64) Option -->
          <div
            @click="setFieldValue('attachmentHandling', 'inline')"
            class="card bg-base-100 border-2 cursor-pointer transition-all hover:shadow-md"
            :class="values.attachmentHandling === 'inline' ? 'border-primary ring-2 ring-primary/20' : 'border-base-300 hover:border-primary/50'"
          >
            <div class="card-body p-4">
              <h4 class="card-title text-sm">Include inline (Base64)</h4>
              <div class="text-xs text-base-content/70 space-y-1">
                <p>Encoded in webhook payload</p>
                <p>• Immediate access</p>
                <p v-if="canUseAttachments">• Limited to 750KB per attachment</p>
                <p v-else>• Limited to 128KB per attachment</p>
              </div>
            </div>
          </div>

          <!-- S3 Storage Option (Pro only, custom domains only) -->
          <div
            @click="canUseS3Storage ? setFieldValue('attachmentHandling', 'storage') : null"
            class="card bg-base-100 border-2 transition-all"
            :class="[
              values.attachmentHandling === 'storage' && canUseS3Storage ? 'border-primary ring-2 ring-primary/20' : 'border-base-300',
              canUseS3Storage ? 'cursor-pointer hover:shadow-md hover:border-primary/50' : 'cursor-not-allowed opacity-60'
            ]"
          >
            <div class="card-body p-4">
              <div class="flex items-center gap-2">
                <span class="badge badge-primary badge-outline badge-xs">Pro</span>
                <h4 class="card-title text-sm">Store in S3 bucket</h4>
              </div>
              <div class="text-xs text-base-content/70 space-y-1">
                <template v-if="canUseS3Storage">
                  <p>File URLs in payload</p>
                  <p>• EC storage or <router-link to="/settings#storage" @click="emit('cancel')" class="link link-primary">bring your own</router-link></p>
                  <p>• Up to 10MB per attachment</p>
                </template>
                <template v-else-if="!canUseAttachments">
                  <p>Upgrade to Pro to enable S3 storage</p>
                </template>
                <template v-else>
                  <p>S3 storage is only available for custom domains</p>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- S3 Storage Configuration (only show when storage option selected and S3 available) -->
      <div v-if="values.allowAttachments && values.attachmentHandling === 'storage' && canUseS3Storage" class="ml-6 pl-4 space-y-3">
        <!-- S3 Bucket and Folder Configuration -->
        <div class="form-control">
          <label class="label">
            <span class="label-text text-sm">
              Storage location 
              <span class="">
                {{ isUsingCustomS3 ? 'in your S3 bucket' : 'in EmailConnect storage' }}
              </span>
            </span>
          </label>
          <div class="flex items-center gap-0 bg-base-100 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
            <!-- S3 Bucket (readonly) -->
            <div class="flex items-center px-3 py-2 bg-base-200/50 border-r border-base-300 rounded-l-lg">
              <svg class="w-4 h-4 mr-2" :class="isUsingCustomS3 ? 'text-success' : 'text-primary'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h10a2 2 0 012 2v0a2 2 0 002 2v0" />
              </svg>
              <span class="text-sm text-base-content/70 font-mono">{{ s3BucketName }}/</span>
            </div>
            <!-- Folder Path Input -->
            <input
              :value="values.s3Folder"
              @input="setFieldValue('s3Folder', ($event.target as HTMLInputElement).value)"
              type="text"
              placeholder="alias-folder-name"
              class="flex-1 px-3 py-2 text-sm bg-transparent border-0 focus:outline-none focus:ring-0"
              pattern="^[a-zA-Z0-9\-_/]*$"
            />
          </div>
          <div v-if="isUsingCustomS3" class="text-xs text-base-content/60 mt-1">
            <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Using your custom S3 bucket. <router-link to="/settings#storage" @click="emit('cancel')" class="link link-primary">Manage settings</router-link>
          </div>
        </div>
      </div>

      <!-- Include Envelope -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.includeEnvelope"
            @change="setFieldValue('includeEnvelope', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Include envelope data</span>
            <span class="label-text-alt text-xs">Include email headers and metadata in webhook payload</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Form-level error display -->
    <div v-if="errors._form" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errors._form }}</span>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="() => {
          resetForm()
          emit('cancel')
        }"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update alias' : 'Create alias')
        }}
      </button>
    </div>
  </form>
</template>
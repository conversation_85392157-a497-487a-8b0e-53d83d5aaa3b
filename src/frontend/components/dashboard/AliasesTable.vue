<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Alias } from '@types'
import { formatWebhookUrl } from '../../utils/url'
import { useMetrics } from '@composables/useMetrics'
import { useOnboarding } from '@composables/useOnboarding'
import { useRouter } from 'vue-router'
import { useToast } from '@composables/useToast'

interface Props {
  aliases: Alias[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  toggleAlias: [aliasId: string, active: boolean]
  editAlias: [alias: Ali<PERSON>]
  deleteAlias: [aliasId: string, aliasEmail: string]
  viewLogs: [domainId: string, aliasId: string]
}>()

// Get user ID for test alias
const { metricsData } = useMetrics()
const { isNewUser } = useOnboarding()
const router = useRouter()
const { success: showSuccessToast } = useToast()

// Helper function to check if an alias is a +test alias
const isTestAlias = (alias: Alias): boolean => {
  if (!alias.email) return false
  const localPart = alias.email.split('@')[0]
  return localPart.endsWith('+test') && alias.email.includes('@user.emailconnect.eu')
}

// Just use the original aliases - test aliases now come from the API
const displayAliases = computed(() => {
  return props.aliases
})

// Helper function to check if an alias can be deleted
const canDeleteAlias = (alias: Alias): boolean => {
  // Test aliases can now be deleted
  if (isTestAlias(alias)) {
    return true
  }
  
  // Cannot delete catch-all aliases at all (they are required for domain functionality)
  if (alias.email === '*' || alias.email.startsWith('*@')) {
    return false
  }

  // Count aliases for the same domain
  const domainAliases = props.aliases.filter(a => a.domainId === alias.domainId)

  // Cannot delete if it's the only alias for the domain
  if (domainAliases.length === 1) {
    return false
  }

  return true
}

// Copy email address to clipboard
const copyEmailToClipboard = async (email: string) => {
  try {
    await navigator.clipboard.writeText(email)
    showSuccessToast(`Copied ${email} to clipboard`)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = email
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    showSuccessToast(`Copied ${email} to clipboard`)
  }
}

const columns: TableColumn<Alias>[] = [
  {
    key: 'email',
    label: 'Alias',
    sortable: true,
    render: (value, row) => {
      // Check if this is a +test alias
      const isTestAliasRow = isTestAlias(row)

      // Get the full email address for display and copying
      const fullEmailAddress = value
      
      // Extract just the alias part (before @) for display or show "catch-all" for wildcards
      let displayAlias: string
      if (value === '*' || value.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (value.includes('@')) {
        displayAlias = value.split('@')[0]
      } else {
        displayAlias = value
      }

      // Test alias special rendering
      if (isTestAliasRow) {
        return `
          <div class="flex items-center space-x-3">
            <div class="badge badge-primary badge-sm">TEST</div>
            <button type="button" 
                    class="flex items-center space-x-2 hover:bg-base-200 px-2 py-1 rounded transition-colors cursor-pointer"
                    data-action="copyEmail" 
                    data-email="${fullEmailAddress}"
                    title="Click to copy full email address: ${fullEmailAddress}">
              <div class="text-sm font-medium text-base-content">${displayAlias}</div>
              <svg class="w-3 h-3 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          </div>
        `
      }

      // Disable toggle if webhook is unverified
      const isWebhookVerified = row.webhook?.verified ?? false
      const isDisabled = !isWebhookVerified
      const toggleClass = isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
      const title = isDisabled ? 'Verify webhook to enable this alias' : ''

      // Check if this is a catch-all alias (no point copying those)
      const isCatchAll = value === '*' || value.startsWith('*@')

      return `
        <div class="flex items-center space-x-3">
          <input type="checkbox"
                 class="toggle toggle-primary alias-status-toggle ${toggleClass}"
                 data-alias-id="${row.id}"
                 ${row.active ? 'checked' : ''}
                 ${isDisabled ? 'disabled' : ''}
                 title="${title}">
          ${isCatchAll ? `
            <div class="text-sm font-medium text-base-content px-2 py-1">${displayAlias}</div>
          ` : `
            <button type="button" 
                    class="flex items-center space-x-2 hover:bg-base-200 px-2 py-1 rounded transition-colors cursor-pointer"
                    data-action="copyEmail" 
                    data-email="${fullEmailAddress}"
                    title="Click to copy full email address: ${fullEmailAddress}">
              <div class="text-sm font-medium text-base-content">${displayAlias}</div>
              <svg class="w-3 h-3 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          `}
        </div>
      `
    }
  },
  {
    key: 'domain',
    label: 'Domain',
    sortable: true,
    render: (value) => {
      return `<div class="text-sm text-base-content">${value?.domain || value?.name || value}</div>`
    }
  },
  {
    key: 'webhook',
    label: 'Webhook',
    sortable: true,
    render: (value, row) => {
      if (!value?.url) return '<span class="text-base-content/60">No webhook</span>'

      const verificationBadge = value.verified
        ? '<span class="badge badge-success bg-primary/10 text-base-content badge-sm ml-2"><div class="status status-success mr-1"></div>Verified</span>'
        : '<span class="badge badge-warning badge-sm ml-2 cursor-pointer underline hover:badge-warning/80" data-action="verifyWebhook" data-webhook-id="' + value.id + '" data-webhook-url="' + value.url + '"><div class="status status-info mr-1 animate-bounce"></div>Verify now</span>'

      const displayUrl = formatWebhookUrl(value.url, 40)

      return `
        <div class="flex items-center gap-2 tooltip" data-tip="${value.url}">
          <span class="text-sm text-base-content">${displayUrl}</span>
          ${verificationBadge}
        </div>
      `
    }
  },
  {
    key: 'actions',
    label: '',
    width: '200px',
    render: (_value, row) => {
      // Check if this is a +test alias
      const isTestAliasRow = isTestAlias(row)
      
      // Test alias has view logs and delete actions (no edit)
      if (isTestAliasRow) {
        // Extract display name for test alias
        let displayAlias: string
        if (row.email.includes('@')) {
          displayAlias = row.email.split('@')[0]
        } else {
          displayAlias = row.email
        }
        
        return `
          <div class="flex items-center justify-end space-x-1">
            <button type="button"
                    class="btn btn-outline btn-primary btn-xs"
                    data-action="viewTestLogs">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Logs
            </button>
            <button type="button"
                    class="btn btn-outline btn-error btn-xs"
                    data-action="deleteAlias"
                    data-alias-id="${row.id}"
                    data-alias-name="${displayAlias}">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </button>
          </div>
        `
      }
      
      // Use the same logic for display name in delete confirmation
      let displayAlias: string
      if (row.email === '*' || row.email.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (row.email.includes('@')) {
        displayAlias = row.email.split('@')[0]
      } else {
        displayAlias = row.email
      }

      const isDeletable = canDeleteAlias(row)
      const buttonClass = isDeletable
        ? 'btn btn-outline btn-error btn-xs'
        : 'btn btn-disabled btn-xs'

      const deleteAttrs = isDeletable
        ? `data-action="deleteAlias" data-alias-id="${row.id}" data-alias-name="${displayAlias}"`
        : ''

      // Determine tooltip message based on why deletion is disabled
      let tooltip = ''
      if (!isDeletable) {
        if (row.email === '*' || row.email.startsWith('*@')) {
          tooltip = 'title="Cannot delete catch-all aliases - they are required for domain functionality"'
        } else {
          tooltip = 'title="Cannot delete the last alias for a domain"'
        }
      }

      return `
        <div class="flex items-center justify-end space-x-1">
          <button type="button"
                  class="btn btn-outline btn-secondary btn-xs"
                  data-action="editAlias"
                  data-alias-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
          <button type="button"
                  class="${buttonClass}"
                  ${deleteAttrs}
                  ${tooltip}
                  ${isDeletable ? '' : 'disabled'}>
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (alias: Alias, _index: number) => {
  // Handle row click if needed
}

const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const button = target.closest('button[data-action]') as HTMLButtonElement

  if (!button) return

  const action = button.dataset.action
  const domainId = button.dataset.domainId
  const aliasId = button.dataset.aliasId
  const aliasName = button.dataset.aliasName
  const email = button.dataset.email

  if (action === 'copyEmail' && email) {
    copyEmailToClipboard(email)
  } else if (action === 'editAlias' && aliasId) {
    const alias = props.aliases.find(a => a.id === aliasId)
    if (alias) {
      emit('editAlias', alias)
    }
  } else if (action === 'viewLogs' && domainId && aliasId) {
    emit('viewLogs', domainId, aliasId)
  } else if (action === 'deleteAlias' && aliasId && aliasName) {
    emit('deleteAlias', aliasId, aliasName)
  } else if (action === 'verifyWebhook') {
    const webhookId = button.dataset.webhookId
    const webhookUrl = button.dataset.webhookUrl
    if (webhookId && webhookUrl) {
      ;(window as any).openModal('webhook-verification', {
        webhookId: webhookId,
        webhookUrl: webhookUrl
      })
    }
  } else if (action === 'viewTestLogs') {
    // Navigate to logs with test webhooks filter
    sessionStorage.setItem('logsView_selectedDomain', 'test-webhooks')
    router.push('/logs')
  }
}

const handleToggleClick = (event: Event) => {
  const target = event.target as HTMLInputElement

  if (target.classList.contains('alias-status-toggle')) {
    const aliasId = target.dataset.aliasId
    const isActive = target.checked

    if (aliasId) {
      emit('toggleAlias', aliasId, isActive)
    }
  }
}
</script>

<template>
  <div class="bg-base-100 border border-base-300 rounded-lg shadow-sm" @click="handleActionClick" @change="handleToggleClick">
    <DataTable
      :columns="columns"
      :data="displayAliases"
      :loading="loading"
      empty-message="No aliases yet. Create your first alias to start receiving emails!"
      @row-click="handleRowClick"
    />
  </div>
</template>

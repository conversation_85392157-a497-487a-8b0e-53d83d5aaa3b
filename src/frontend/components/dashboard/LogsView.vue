<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import DataTable from '../ui/DataTable.vue'
import type { TableColumn } from '../ui/DataTable.vue'
import type { Domain, Alias } from '../../types'
import DeliveryResultModal from '../modals/DeliveryResultModal.vue'
import { useToast } from '../../composables/useToast'
import { useDataRefresh } from '../../composables/useDataRefresh'
import { useWebSocket } from '../../composables/useWebSocket'
import { api } from '../../utils/api'
import { logger } from '../../utils/logger';

// State
const domains = ref<Domain[]>([])
const aliases = ref<Alias[]>([])
const logs = ref<any[]>([])
const selectedDomain = ref('')
const selectedAlias = ref('')
const isLoading = ref(true)
const isLoadingLogs = ref(false)

// Filter out system domain from dropdown
const displayDomains = computed(() =>
  domains.value.filter(domain =>
    domain.domain !== 'user.emailconnect.eu' &&
    (domain.domainName || domain.domain) !== 'user.emailconnect.eu'
  )
)

// Modal state
const showDeliveryResultModal = ref(false)
const selectedLogForResult = ref<any>(null)

// Toast for notifications
const { addToast } = useToast()

// Retry state
const retryingLogs = ref(new Set<string>())

// Data refresh integration
const { refreshState } = useDataRefresh()

// Computed
const filteredAliases = computed(() => {
  if (!selectedDomain.value) return []
  return aliases.value.filter(alias => alias.domainId === selectedDomain.value)
})

// Load initial data
const loadDomains = async () => {
  try {
    const data = await api.get('/api/domains')
    domains.value = data.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
  }
}

const loadAliases = async () => {
  try {
    const data = await api.get('/api/aliases')
    aliases.value = data.aliases || []
  } catch (error) {
    console.error('Failed to load aliases:', error)
  }
}

const loadLogs = async () => {
  if (!selectedDomain.value) {
    logs.value = []
    return
  }

  try {
    isLoadingLogs.value = true
    let url = '/api/logs'
    const params = new URLSearchParams()

    // Handle special test-webhooks filter
    if (selectedDomain.value === 'test-webhooks') {
      params.append('testWebhooksOnly', 'true')
    } else {
      params.append('domainId', selectedDomain.value)

      if (selectedAlias.value) {
        params.append('aliasId', selectedAlias.value)
      }
    }

    const data = await api.get(`${url}?${params}`)
    logs.value = data.logs || []
  } catch (error) {
    console.error('Failed to load logs:', error)
    logs.value = []
  } finally {
    isLoadingLogs.value = false
  }
}

// Watch for changes in selections
watch(selectedDomain, () => {
  selectedAlias.value = '' // Reset alias when domain changes
  loadLogs()
})

watch(selectedAlias, () => {
  loadLogs()
})

// Watch for refresh triggers from websocket (via aliases refresh)
watch(() => refreshState.aliases, () => {
  // When aliases are refreshed (triggered by websocket), also refresh logs
  loadLogs()
})

onMounted(async () => {
  isLoading.value = true
  await Promise.all([loadDomains(), loadAliases()])

  // Check for pre-selected domain/alias from navigation
  const preSelectedDomain = sessionStorage.getItem('logsView_selectedDomain')
  const preSelectedAlias = sessionStorage.getItem('logsView_selectedAlias')

  if (preSelectedDomain) {
    selectedDomain.value = preSelectedDomain
    sessionStorage.removeItem('logsView_selectedDomain')

    if (preSelectedAlias) {
      selectedAlias.value = preSelectedAlias
      sessionStorage.removeItem('logsView_selectedAlias')
    }

    // Load logs for the pre-selected domain/alias
    await loadLogs()
  }

  isLoading.value = false
})

// Quick access method for external calls
const viewLogsFor = (domainId: string, aliasId?: string) => {

// WebSocket integration to auto-refresh logs on email processed
const { isConnected } = useWebSocket()

// Refresh logs when an email is processed (toast is handled in useWebSocket)
// We listen via data refresh triggers: ensure metrics refresh also triggers logs reload
watch(isConnected, (connected) => {
  if (connected) {
    // Nothing to do here; useWebSocket already wires email_processed to metrics refresh.
    // We can still set a small timer to refresh logs shortly after metrics update for UX.
    const handle = setInterval(() => {
      if (selectedDomain.value) {
        loadLogs()
      }
    }, 5000)
    onUnmounted(() => clearInterval(handle))
  }
})

  selectedDomain.value = domainId
  if (aliasId) {
    selectedAlias.value = aliasId
  }
}

// Get alias display name for a log entry
const getAliasDisplayName = (log: any) => {
  if (!log.toAddresses || log.toAddresses.length === 0) return 'Unknown'

  const toAddress = log.toAddresses[0] // Use first address
  const selectedDomainData = domains.value.find(d => d.id === selectedDomain.value)

  if (!selectedDomainData) return toAddress

  // Extract the local part (before @)
  const localPart = toAddress.split('@')[0]

  // Check if it's a catch-all (wildcard)
  if (localPart === '*' || toAddress.startsWith('*@')) {
    return 'catch-all'
  }

  return localPart
}

// Table columns configuration
const columns: TableColumn<any>[] = [
  {
    key: 'createdAt',
    label: 'Timestamp',
    sortable: true,
    render: (value: any) => {
      return new Date(value).toLocaleString()
    }
  },
  {
    key: 'deliveryStatus',
    label: 'Status',
    sortable: true,
    render: (value: any, row: any) => {
      const statusConfig = {
        'DELIVERED': {
          statusClass: 'status status-success',
          text: row.isTestWebhook ? 'Processed' : 'Delivered',
          textClass: 'text-success'
        },
        'FAILED': {
          statusClass: 'status status-error animate-ping',
          text: 'Failed',
          textClass: 'text-error'
        },
        'REJECTED': {
          statusClass: 'status status-error',
          text: 'Rejected',
          textClass: 'text-error'
        },
        'PENDING': {
          statusClass: 'status status-info animate-pulse',
          text: 'Pending',
          textClass: 'text-info'
        },
        'RETRYING': {
          statusClass: 'status status-warning animate-bounce',
          text: 'Retrying',
          textClass: 'text-warning'
        },
        'EXPIRED': {
          statusClass: 'status status-neutral',
          text: 'Expired',
          textClass: 'text-neutral'
        }
      }

      const config = statusConfig[value] || statusConfig['PENDING']

      return `
        <div class="flex items-center gap-2">
          <div class="${config.statusClass}"></div>
          <span class="${config.textClass}">${config.text}</span>
        </div>
      `
    }
  },
  {
    key: 'alias',
    label: 'Alias',
    sortable: true,
    render: (_value: any, row: any) => {
      if (row.isTestWebhook) {
        return `<span class="badge badge-primary badge-sm">Test webhook</span>`
      }
      return getAliasDisplayName(row)
    }
  },
  {
    key: 'fromAddress',
    label: 'From',
    sortable: true,
    render: (value: any) => {
      return `<div class="truncate max-w-[200px]" title="${value}">${value}</div>`
    }
  },
  {
    key: 'subject',
    label: 'Subject',
    sortable: true,
    render: (value: any) => {
      const displaySubject = value || '(no subject)'
      return `<div class="truncate max-w-[300px]" title="${displaySubject}">${displaySubject}</div>`
    }
  },
  {
    key: 'actions',
    label: '',
    width: '140px',
    render: (_value: any, row: any) => {
      const canRetry = row.deliveryStatus === 'FAILED' || row.deliveryStatus === 'EXPIRED' || row.deliveryStatus === 'REJECTED'
      const isRetrying = retryingLogs.value.has(row.messageId)

      return `
        <div class="flex gap-1">
          <button type="button"
                  class="btn btn-outline btn-primary btn-xs"
                  data-action="viewResult"
                  data-log-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Result
          </button>
          ${canRetry ? `
            <button type="button"
                    class="btn btn-outline btn-warning btn-xs ${isRetrying ? 'loading' : ''}"
                    data-action="retry"
                    data-message-id="${row.messageId}"
                    ${isRetrying ? 'disabled' : ''}>
              ${isRetrying ? '' : `
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Retry
              `}
            </button>
          ` : ''}
        </div>
      `
    }
  }
]

const handleRowClick = (_log: any, _index: number) => {
  // Optional: Handle row click for future log details modal
}

// Manual retry function
const retryWebhook = async (messageId: string) => {
  try {
    retryingLogs.value.add(messageId)

    try {
      await api.post(`/api/logs/${messageId}/retry`)
      addToast('success', 'Webhook retry queued successfully')

      // Refresh logs to show updated status
      await loadLogs()
    } catch (error: any) {
      // Handle rate limiting
      if (error.status === 429) {
        addToast('warning', error.message || 'Rate limited: Please wait before retrying')
      } else {
        addToast('error', error.message || 'Failed to retry webhook')
      }
    }
  } catch (error) {
    console.error('Retry error:', error)
    addToast('error', 'Failed to retry webhook. Please try again.')
  } finally {
    retryingLogs.value.delete(messageId)
  }
}

// Handle action button clicks (View Result and Retry)
const handleActionClick = async (event: Event) => {
  const target = event.target as HTMLElement
  const action = target.getAttribute('data-action')

  if (action === 'viewResult') {
    const logId = target.getAttribute('data-log-id')
    const log = logs.value.find(l => l.id === logId)

    if (log) {
      // Prepare unified result data for the modal
      const resultData = {
        logId: log.id,
        deliveryStatus: log.deliveryStatus,
        deliveryAttempts: log.deliveryAttempts || 0,
        errorMessage: log.errorMessage,
        deliveredAt: log.deliveredAt,
        lastAttemptAt: log.lastAttemptAt,
        messageId: log.messageId,
        fromAddress: log.fromAddress,
        toAddresses: log.toAddresses || [],
        subject: log.subject,
        alias: getAliasDisplayName(log),
        domain: selectedDomain.value === 'test-webhooks'
          ? 'user.emailconnect.eu'
          : domains.value.find(d => d.id === selectedDomain.value)?.domain,
        webhookUrl: log.alias?.webhook?.url || log.webhook?.url,
        webhookName: log.alias?.webhook?.name || log.webhook?.name,
        webhookPayload: log.webhookPayload,
        timestamp: log.createdAt,
        httpStatus: log.httpStatus || null,
        webhookTestRequestData: null
      }

      // If this was delivered to a WebhookTest URL, fetch the request data
      if (resultData.webhookUrl && isWebhookTestUrl(resultData.webhookUrl) && log.deliveryStatus === 'DELIVERED') {
        try {
          const requestData = await fetchWebhookTestRequestData(resultData.webhookUrl, log.createdAt, log.deliveredAt)
          logger.debug('Successfully fetched WebhookTest request data:', requestData)
          resultData.webhookTestRequestData = requestData
        } catch (error) {
          console.warn('Failed to fetch WebhookTest request data:', error)
          // Continue without request data - not critical
        }
      }

      selectedLogForResult.value = resultData
      showDeliveryResultModal.value = true
    }
  } else if (action === 'retry') {
    const messageId = target.getAttribute('data-message-id')
    if (messageId && !retryingLogs.value.has(messageId)) {
      retryWebhook(messageId)
    }
  }
}


// Helper function to check if URL is a WebhookTest endpoint
const isWebhookTestUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url)
    // Check for webhooktest.eu domains
    if (urlObj.hostname === 'webhooktest.eu' || urlObj.hostname === 'www.webhooktest.eu' || urlObj.hostname.includes('webhooktest')) {
      return true
    }
    // Check for ngrok domains (used for WebhookTest tunnels)
    if (urlObj.hostname.includes('ngrok-free.app') || urlObj.hostname.includes('ngrok.app') || urlObj.hostname.includes('ngrok.io')) {
      return true
    }
    // Check for localhost WebhookTest development
    if (urlObj.hostname === 'localhost' && urlObj.port === '3002') {
      return true
    }
    return false
  } catch {
    return false
  }
}

// Helper function to fetch WebhookTest request data
const fetchWebhookTestRequestData = async (webhookUrl: string, emailTimestamp: string, deliveredAt?: string) => {
  try {
    // Calculate time window around the delivery time
    const emailTime = new Date(emailTimestamp)
    const deliveryTime = deliveredAt ? new Date(deliveredAt) : emailTime

    // Search within a 5-minute window around the delivery time
    const timeWindowMs = 5 * 60 * 1000 // 5 minutes
    const startTime = new Date(deliveryTime.getTime() - timeWindowMs)
    const endTime = new Date(deliveryTime.getTime() + timeWindowMs)
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    const data = await api.post('/api/webhooktest/get-request-data', {
      webhookUrl,
      timestampStart: startTime.toISOString(),
      timestampEnd: endTime.toISOString()
    }, {
      toast: { showErrors: false }  // Disable error toast for this call
    })

    clearTimeout(timeoutId)
    logger.debug('WebhookTest API data:', data)

    // Find the most relevant request (closest to delivery time)
    if (data.requests && data.requests.length > 0) {
      const sortedRequests = data.requests
        .map((req: any) => ({
          ...req,
          timeDiff: Math.abs(new Date(req.createdAt).getTime() - deliveryTime.getTime())
        }))
        .sort((a: any, b: any) => a.timeDiff - b.timeDiff)

      logger.debug(`Found WebhookTest requests: ${sortedRequests.length}, returning closest match`, sortedRequests[0])
      return sortedRequests[0] // Return the closest match
    }

    logger.debug('No WebhookTest requests found in response')
    return null
  } catch (error) {
    console.error('Failed to fetch WebhookTest request data:', error)
    throw error
  }
}

// Expose methods for parent components
defineExpose({
  viewLogsFor,
  refresh: () => Promise.all([loadDomains(), loadAliases(), loadLogs()])
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <div v-else class="space-y-6">
      <!-- Selectors -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Domain selector -->
        <div class="form-control w-full">
          <label class="label">
            <span class="label-text font-semibold text-base-content">Select a log</span>
          </label>
          <select v-model="selectedDomain" class="select select-bordered w-full focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
            <option value="">Choose a log...</option>
            <option value="test-webhooks" class="font-medium">
              Aliases at user.emailconnect.eu
            </option>
            <optgroup label="Domains">
              <option v-for="domain in displayDomains" :key="domain.id" :value="domain.id">
                {{ domain.domainName || domain.domain }}
              </option>
            </optgroup>
          </select>
        </div>

        <!-- Alias selector -->
        <div v-if="false && selectedDomain !== 'test-webhooks'" class="form-control w-full">
          <label class="label">
            <span class="label-text font-semibold text-base-content">Select alias</span>
          </label>
          <select
            v-model="selectedAlias"
            class="select select-bordered w-full focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            :disabled="!selectedDomain || filteredAliases.length === 0"
            :class="{ 'select-disabled': !selectedDomain || filteredAliases.length === 0 }"
          >
            <option value="">Show all</option>
            <option v-for="alias in filteredAliases" :key="alias.id" :value="alias.id">
              {{ alias.email.includes('*@') ? 'catch-all' : alias.email.split('@')[0] }}
            </option>
          </select>
        </div>
      </div>

      <!-- Logs viewer -->
      <div class="card bg-base-100 border border-base-300 rounded-lg">
        <div class="card-header px-6 py-4 border-b border-base-200 flex justify-between items-center" v-if="selectedDomain && !isLoadingLogs">
          <h3 class="text-lg font-semibold">
            {{ selectedDomain === 'test-webhooks' ? 'Test webhook calls' : 'Email logs' }}
          </h3>
          <button
            type="button"
            @click="loadLogs"
            :disabled="isLoadingLogs"
            class="btn btn-outline btn-sm"
            :class="{ 'loading': isLoadingLogs }"
          >
            <svg v-if="!isLoadingLogs" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ isLoadingLogs ? 'Refreshing...' : 'Refresh' }}
          </button>
        </div>
        <div class="card-body p-0">
          <!-- Loading logs -->
          <div v-if="isLoadingLogs" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>

          <!-- No domain selected -->
          <div v-else-if="!selectedDomain" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Select a domain or test webhook calls to view logs</p>
          </div>

          <!-- No logs found -->
          <div v-else-if="logs.length === 0" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-4.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            <p>{{ selectedDomain === 'test-webhooks' ? 'No test webhook calls found' : 'No email logs found for the selected criteria' }}</p>
          </div>

          <!-- Logs table -->
          <div v-else class="bg-base-100" @click="handleActionClick">
            <DataTable
              :columns="columns"
              :data="logs"
              :loading="isLoadingLogs"
              empty-message="No logs found for the selected criteria."
              default-sort-column="createdAt"
              default-sort-direction="desc"
              @row-click="handleRowClick"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Delivery Result Modal -->
    <div v-if="showDeliveryResultModal" class="modal modal-open">
      <div class="modal-box max-w-4xl">
        <h3 class="font-bold text-lg mb-4">Delivery result</h3>
        <DeliveryResultModal
          v-if="selectedLogForResult"
          :result-data="selectedLogForResult"
          @close="showDeliveryResultModal = false; selectedLogForResult = null"
        />
      </div>
      <div class="modal-backdrop" @click="showDeliveryResultModal = false; selectedLogForResult = null"></div>
    </div>

  </div>
</template>

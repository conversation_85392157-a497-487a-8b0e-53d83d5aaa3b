<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

interface Tab {
  id: string
  name: string
  icon: string
  count?: number
  route: string
}

interface Props {
  counts: {
    domains: number
    aliases: number
    webhooks: number
  }
  hasVerifiedDomains?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hasVerifiedDomains: true
})

const emit = defineEmits<{
  createAction: [type: string]
}>()

// Router
const route = useRoute()
const router = useRouter()

// No longer need dropdown state - handled by DaisyUI

// Computed active tab from route
const activeTab = computed(() => {
  const routeName = route.name as string
  return routeName || 'domains'
})

// Computed
const tabs = computed<Tab[]>(() => [
  {
    id: 'domains',
    name: 'Domains',
    icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9',
    count: props.counts.domains,
    route: '/domains'
  },
  {
    id: 'aliases',
    name: '<PERSON><PERSON>',
    icon: 'M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207',
    count: props.counts.aliases,
    route: '/aliases'
  },
  {
    id: 'webhooks',
    name: 'Webhooks',
    icon: 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
    count: props.counts.webhooks,
    route: '/webhooks'
  },
  {
    id: 'logs',
    name: 'Logs',
    icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
    route: '/logs'
  }
])

// Removed activeTabData - not used

const createButtonText = computed(() => {
  switch (activeTab.value) {
    case 'domains': return 'Create domain'
    case 'aliases': return 'Create alias'
    case 'webhooks': return 'Create webhook'
    case 'logs': return 'View logs'
    default: return 'Create'
  }
})

const createActions = computed(() => [
  { id: 'create-domain', name: 'Create domain', icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9', disabled: false },
  {
    id: 'create-alias',
    name: 'Create alias',
    icon: 'M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207',
    disabled: !props.hasVerifiedDomains,
    tooltip: !props.hasVerifiedDomains ? 'Verify at least one domain to create aliases' : undefined
  },
  { id: 'create-webhook', name: 'Create webhook', icon: 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1', disabled: false }
])

// Check if main action should be disabled
const isMainActionDisabled = computed(() => {
  return activeTab.value === 'aliases' && !props.hasVerifiedDomains
})

// Methods
const handleMainAction = () => {
  // Don't proceed if action is disabled
  if (isMainActionDisabled.value) {
    return
  }

  const actionMap: Record<string, string> = {
    'domains': 'create-domain',
    'aliases': 'create-alias',
    'webhooks': 'create-webhook',
    'logs': 'view-logs'
  }

  const action = actionMap[activeTab.value]
  if (action) {
    emit('createAction', action)
  }
}

const handleDropdownAction = (actionId: string) => {
  // Check if action is disabled
  const action = createActions.value.find(a => a.id === actionId)
  if (action?.disabled) {
    return // Don't emit if disabled
  }

  // DaisyUI dropdown will close automatically
  emit('createAction', actionId)
}

const navigateToTab = (tab: Tab) => {
  // Use Vue Router for SPA navigation
  router.push(tab.route)
}

// DaisyUI dropdown handler takes care of closing dropdowns
</script>

<template>
  <div class="bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-3 sm:py-4">
        <!-- Tab Navigation - Mobile Optimized -->
        <div class="flex-1 overflow-x-auto">
          <div class="flex space-x-1 min-w-max">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="navigateToTab(tab)"
              :class="[
                'flex items-center px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors rounded-lg whitespace-nowrap',
                tab.id === activeTab
                  ? 'bg-primary/10 text-base-content border border-primary'
                  : 'hover:bg-base-200 text-base-content/70 hover:border-primary/20 border border-transparent'
              ]"
            >
              <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="tab.icon" />
              </svg>
              <!-- Show text on sm+ screens, hide on mobile -->
              <span class="hidden sm:inline ml-2">{{ tab.name }}</span>
              <!-- Show count badge if available -->
              <span
                v-if="tab.count !== undefined"
                :class="[
                  'inline-flex items-center justify-center min-w-[1.25rem] h-5 rounded-full text-xs font-medium',
                  'sm:ml-2 ml-1 px-1.5',
                  tab.id === activeTab
                    ? 'bg-primary text-primary-content'
                    : 'bg-base-300 text-base-content'
                ]"
              >
                {{ tab.count }}
              </span>
            </button>
          </div>
        </div>

        <!-- Create Button Section - Mobile Optimized -->
        <div v-if="activeTab !== 'logs'" class="flex ml-2 sm:ml-4">
          <!-- Main Create Button (Dynamic) -->
          <button
            type="button"
            @click="handleMainAction"
            :disabled="isMainActionDisabled"
            :title="isMainActionDisabled ? 'Verify at least one domain to create aliases' : ''"
            class="inline-flex items-center px-3 sm:px-4 py-1 border border-transparent text-sm font-medium rounded-l-md transition-colors"
            :class="isMainActionDisabled
              ? 'text-base-content/40 bg-base-300 cursor-not-allowed'
              : 'text-primary-content bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary'"
          >
            
            <span class="hidden sm:inline ml-2">{{ createButtonText }}</span>
            <span class="sm:hidden ml-1">+</span>
          </button>

          <!-- DaisyUI Dropdown -->
          <div class="dropdown dropdown-end">
            <!-- Dropdown Toggle Button -->
            <div
              tabindex="0"
              role="button"
              class="inline-flex items-center px-2 py-2 border-l border-primary/20 text-sm font-medium rounded-r-md text-primary-content bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              <svg
                class="h-4 w-4 sm:h-5 sm:w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>

            <!-- Dropdown menu -->
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-44 sm:w-48 p-2 shadow-lg border border-base-300">
              <li v-for="action in createActions" :key="action.id">
                <button
                  type="button"
                  @click="handleDropdownAction(action.id)"
                  :disabled="action.disabled"
                  :title="action.tooltip"
                  class="flex items-center gap-2 w-full text-left text-sm rounded-lg p-2 transition-colors"
                  :class="action.disabled
                    ? 'cursor-not-allowed opacity-50 text-base-content/40'
                    : 'hover:bg-base-200 text-base-content'"
                >
                  <svg class="w-4 h-4 flex-shrink-0"
                       :class="action.disabled ? 'text-base-content/30' : 'text-base-content/60'"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="action.icon" />
                  </svg>
                  <span class="truncate">{{ action.name }}</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- Subtle separator line -->
    <div class="border-b border-base-300"></div>
  </div>
</template>

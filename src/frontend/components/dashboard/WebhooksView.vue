<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useWebhookApi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import { useMetrics } from '@composables/useMetrics'
import WebhooksTable from './WebhooksTable.vue'
import { api } from '../../utils/api'

// Composables
const { deleteWebhook, testWebhook } = useWebhookApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()
const { refreshMetrics } = useMetrics()

// State for webhooks data
const webhooks = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData, triggerRefresh } = useDataRefresh()

// Load webhooks data
const loadWebhooks = async () => {
  try {
    isLoading.value = true
    const data = await api.get('/api/webhooks')
    webhooks.value = data.webhooks || []

    // Update global data store
    updateData('webhooks', data.webhooks || [])
  } catch (error) {
    console.error('Failed to load webhooks:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.webhooks, () => {
  loadWebhooks()
})

// Handle webhook editing
const handleEditWebhook = (webhook: any) => {
  const modalData = {
    id: webhook.id,
    name: webhook.name,
    url: webhook.url,
    description: webhook.description,
    customHeaders: webhook.customHeaders,
    onSuccess: () => {
      loadWebhooks() // Refresh the webhooks list
      triggerRefresh('webhooks')
    }
  }

  // Open the edit modal
  ;(window as any).openModal('edit-webhook', modalData)
}

// Handle webhook verification
const handleVerifyWebhook = (webhookId: string) => {
  // Find the webhook to get its details
  const webhook = webhooks.value.find((w: any) => w.id === webhookId)
  if (webhook) {
    ;(window as any).openModal('webhook-verification', {
      webhookId: webhook.id,
      webhookUrl: webhook.url,
      webhookName: webhook.name
    })
  }
}

// Handle webhook testing
const handleTestWebhook = async (webhookId: string) => {
  try {
    const webhook = webhooks.value.find((w: any) => w.id === webhookId)
    if (!webhook) {
      error('Webhook not found')
      return
    }

    if (!webhook.verified) {
      error('Please verify the webhook before testing')
      return
    }

    const result = await testWebhook(webhookId)
    success(`Test webhook sent successfully to ${webhook.name}! Check your logs to see the delivery status.`)
    
    // Optional: You could trigger a logs refresh here if you have access to it
    // triggerRefresh('logs')
    
  } catch (err) {
    console.error('Failed to test webhook:', err)
    error(err instanceof Error ? err.message : 'Failed to send test webhook')
  }
}

// Handle webhook deletion
const handleDeleteWebhook = async (webhookId: string, webhookName: string) => {
  try {
    const confirmed = await confirmDelete(webhookName, 'webhook')
    if (!confirmed) return

    await deleteWebhook(webhookId)
    success(`Webhook "${webhookName}" deleted successfully`)

    // Refresh the webhooks list and trigger metrics update
    await loadWebhooks()
    triggerRefresh('webhooks')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after webhook deletion:', error)
      }
    }, 500)
  } catch (err) {
    console.error('Failed to delete webhook:', err)
    error(err instanceof Error ? err.message : 'Failed to delete webhook')
  }
}

onMounted(() => {
  loadWebhooks()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadWebhooks
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Webhooks table -->
    <WebhooksTable
      v-else
      :webhooks="webhooks"
      @refresh="loadWebhooks"
      @edit-webhook="handleEditWebhook"
      @verify-webhook="handleVerifyWebhook"
      @test-webhook="handleTestWebhook"
      @delete-webhook="handleDeleteWebhook"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import TabNavigation from './TabNavigation.vue'
import { useMetrics } from '@composables/useMetrics'
import { useDataRefresh } from '@composables/useDataRefresh'

// Use metrics system for reactive counts
const { counts: metricsCount, refreshMetrics } = useMetrics()
const { refreshState, globalData } = useDataRefresh()

// Use computed counts directly from metrics instead of local state
const counts = computed(() => {
  return metricsCount.value
})

// Check if there are verified domains for alias creation
const hasVerifiedDomains = computed(() => {
  const domains = globalData.domains || []
  return domains.some((domain: any) => domain.verificationStatus === 'VERIFIED')
})

// Watch for data refresh triggers and update counts
watch([() => refreshState.domains, () => refreshState.aliases, () => refreshState.webhooks], async (newValues, oldValues) => {
  try {
    await refreshMetrics()
  } catch (error) {
    console.error('Failed to refresh metrics:', error)
    // Retry once after a delay
    setTimeout(async () => {
      try {
        console.log('Retrying metrics refresh...')
        await refreshMetrics()
        console.log('Metrics refresh retry successful:', metricsCount.value)
      } catch (retryError) {
        console.error('Metrics refresh retry failed:', retryError)
      }
    }, 1000)
  }
}, { immediate: false, deep: true })

// Methods
const handleCreateAction = (actionType: string) => {
  // Check if trying to create alias without verified domains
  if (actionType === 'create-alias' && !hasVerifiedDomains.value) {
    // Show a helpful message instead of opening the modal
    if ((window as any).showToast) {
      (window as any).showToast('Please verify at least one domain before creating aliases', 'warning')
    } else {
      alert('Please verify at least one domain before creating aliases')
    }
    return
  }

  // Use the existing global modal system
  if ((window as any).openModal) {
    (window as any).openModal(actionType)
  } else {
    console.warn('Modal system not available')
  }
}

onMounted(async () => {
  // Load initial metrics to get accurate counts
  try {
    await refreshMetrics()
  } catch (error) {
    console.error('Failed to load initial metrics:', error)
  }

  // Ensure domains are loaded for verification check
  if (globalData.domains.length === 0) {
    try {
      const response = await fetch('/api/domains')
      const data = await response.json()
      // Update global data store directly
      globalData.domains = data.domains || []
    } catch (error) {
      console.error('Failed to load domains for verification check:', error)
    }
  }
})
</script>

<template>
  <TabNavigation
    :counts="counts"
    :has-verified-domains="hasVerifiedDomains"
    @create-action="handleCreateAction"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useDomain<PERSON>pi } from '@composables/useApi'
import { useConfirm } from '@composables/useConfirm'
import { useToast } from '@composables/useToast'
import { useMetrics } from '@composables/useMetrics'
import { useAuth } from '@composables/useAuth'
import DomainsTable from './DomainsTable.vue'
import { api } from '../../utils/api'

// Router
const router = useRouter()
const route = useRoute()

// Composables
const { deleteDomain } = useDomainApi()
const { confirmDelete } = useConfirm()
const { success, error } = useToast()
const { refreshMetrics } = useMetrics()
const { user } = useAuth()

// State for domains data
const domains = ref([])
const isLoading = ref(true)

// Filter out system domain from display
const displayDomains = computed(() => 
  domains.value.filter(domain => 
    domain.domain !== 'user.emailconnect.eu' && 
    (domain.domainName || domain.domain) !== 'user.emailconnect.eu'
  )
)

// Data refresh system
const { refreshState, updateData, triggerRefresh } = useDataRefresh()

// User ID is now available from auth composable
const userId = computed(() => user.value?.id)

// Load domains data
const loadDomains = async () => {
  try {
    isLoading.value = true
    const data = await api.get('/api/domains')
    domains.value = data.domains || []

    // Update global data store
    updateData('domains', data.domains || [])
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.domains, () => {
  loadDomains()
})

// Handle view logs navigation
const handleViewLogs = (domainId: string) => {
  // Navigate to logs tab and store domain selection for LogsView to pick up
  sessionStorage.setItem('logsView_selectedDomain', domainId)
  router.push('/logs')
}

// Handle domain editing
const handleEditDomain = (domain: any) => {
  // Extract configuration from the domain object
  const config = domain.configuration || {}

  const modalData = {
    id: domain.id,
    domain: domain.domain || domain.domainName, // Use actual domain name
    webhookId: domain.webhook?.id || domain.webhookId, // Extract webhook ID from webhook object or direct property
    allowAttachments: config.allowAttachments || false,
    includeEnvelope: config.includeEnvelope !== false, // Default to true if not explicitly set
    configuration: domain.configuration, // Pass the full configuration object
    spamFiltering: config.spamFiltering?.enabled || false, // Extract spam filtering enabled state
    onSuccess: () => {
      loadDomains() // Refresh the domains list
      triggerRefresh('domains')
    }
  }

  // Open the edit modal
  ;(window as any).openModal('edit-domain', modalData)
}

// Handle domain toggle
const handleToggleDomain = async (domainId: string, active: boolean) => {
  try {
    const result = await api.put(`/api/domains/${domainId}/status`, { active })
    success(`Domain ${active ? 'activated' : 'deactivated'} successfully`)

    // Refresh the domains list and trigger metrics update
    await loadDomains()
    triggerRefresh('domains')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after domain toggle:', error)
      }
    }, 500)
  } catch (err) {
    console.error('Failed to toggle domain:', err)
    error(err instanceof Error ? err.message : 'Failed to update domain status')

    // Refresh to revert the toggle state in UI
    await loadDomains()
  }
}

// Handle domain deletion
const handleDeleteDomain = async (domainId: string, domainName: string) => {
  try {
    const confirmed = await confirmDelete(domainName, 'domain')
    if (!confirmed) return

    await deleteDomain(domainId)
    success(`Domain "${domainName}" deleted successfully`)

    // Refresh the domains list and trigger metrics update
    await loadDomains()
    triggerRefresh('domains')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after domain deletion:', error)
      }
    }, 500)
  } catch (err) {
    console.error('Failed to delete domain:', err)
    error(err instanceof Error ? err.message : 'Failed to delete domain')
  }
}

// Handle create domain
const handleCreateDomain = () => {
  if ((window as any).openModal) {
    (window as any).openModal('create-domain')
  }
}

onMounted(async () => {
  // User info is already available from auth composable via router guards
  await loadDomains()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadDomains
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Empty state -->
    <div v-else-if="displayDomains.length === 0" class="space-y-6">
      <div class="text-center py-8">
        <div class="max-w-md mx-auto">
          <h3 class="text-lg font-semibold text-base-content mb-2">
            No domains configured yet
          </h3>
          <p class="text-base-content/70 mb-4">
            Setup your own domain to receive emails at custom aliases.
          </p>
          <button
            @click="handleCreateDomain"
            class="btn btn-primary"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Create your first domain
          </button>
        </div>
      </div>
    </div>

    <!-- Domains table -->
    <DomainsTable
      v-else
      :domains="displayDomains"
      @refresh="loadDomains"
      @toggle-domain="handleToggleDomain"
      @edit-domain="handleEditDomain"
      @view-logs="handleViewLogs"
      @delete-domain="handleDeleteDomain"
    />
  </div>
</template>

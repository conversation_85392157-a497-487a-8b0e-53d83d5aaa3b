<template>
  <div v-if="isDevelopment" class="p-4 bg-warning/20 rounded-lg border border-warning">
    <h3 class="text-lg font-semibold mb-2">🐛 Error tracking testing</h3>
    <p class="text-sm mb-4">These buttons will trigger errors to test error tracking integration. Only visible in development.</p>
    
    <div class="flex flex-wrap gap-2">
      <button 
        @click="throwSyncError" 
        class="btn btn-error btn-sm"
      >
        Throw sync error
      </button>
      
      <button 
        @click="throwAsyncError" 
        class="btn btn-error btn-sm"
      >
        Throw async error
      </button>
      
      <button 
        @click="triggerBackendError" 
        class="btn btn-error btn-sm"
      >
        Trigger backend error
      </button>
      
      <button 
        @click="sendTestMessage" 
        class="btn btn-info btn-sm"
      >
        Send test message
      </button>
    </div>
    
    <div v-if="result" class="mt-4 p-2 bg-base-200 rounded text-sm">
      <pre>{{ result }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { captureException, captureMessage } from '../lib/sentry';

const result = ref('');

// Only show in development
const isDevelopment = computed(() => import.meta.env.DEV);

function throwSyncError() {
  result.value = 'Throwing sync error...';
  throw new Error('Test sync error thrown on purpose to verify Bugsink frontend integration');
}

async function throwAsyncError() {
  result.value = 'Throwing async error...';
  await new Promise((resolve) => setTimeout(resolve, 100));
  throw new Error('Test async error thrown on purpose to verify Bugsink frontend integration');
}

async function triggerBackendError() {
  try {
    result.value = 'Triggering backend error...';
    const response = await fetch('/api/test/error');
    const data = await response.json();
    result.value = `Backend response: ${JSON.stringify(data, null, 2)}`;
  } catch (error) {
    result.value = `Request failed: ${error}`;
    captureException(error as Error, { 
      component: 'TestError',
      action: 'triggerBackendError' 
    });
  }
}

async function sendTestMessage() {
  result.value = 'Sending test message...';
  captureMessage('Test message from Vue frontend', 'info', {
    component: 'TestError',
    timestamp: new Date().toISOString(),
  });
  
  try {
    const response = await fetch('/api/test/message');
    const data = await response.json();
    result.value = `Message sent: ${JSON.stringify(data, null, 2)}`;
  } catch (error) {
    result.value = `Request failed: ${error}`;
  }
}
</script>
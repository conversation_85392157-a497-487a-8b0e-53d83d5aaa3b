<template>
  <div class="min-h-screen bg-base-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Page Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl lg:text-5xl font-bold text-base-content mb-4">
          Simple, transparent pricing
        </h1>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto mb-8">
          Start free and scale as you grow. No hidden fees, no setup costs, no vendor lock-in.
        </p>
        <div class="flex justify-center space-x-4">
          <div class="badge badge-primary">🇪🇺 EU hosted</div>
          <div class="badge badge-outline">GDPR compliant</div>
          <div class="badge badge-outline">No long-term contracts</div>
        </div>
      </div>
      
      <!-- Pricing Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16 max-w-4xl mx-auto">
        <!-- Starter Plan -->
        <div class="card bg-base-100 shadow-lg h-full border border-base-300">
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h2 class="card-title text-2xl justify-center mb-2">Starter</h2>
              <p class="text-base-content/70 mb-6">Perfect for small projects and testing</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€0</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ freePlan.monthlyEmailLimit || 50 }} emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ freePlan.domains || 1 }} domain + {{ freePlan.aliases || 3 }} aliases and {{ freePlan.webhooks || 3 }} webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Text &amp; docs inline (&lt;128KB)</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Pay-as-you-go: €1.00/100 emails</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Community support</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/login" class="btn btn-outline btn-primary btn-block">Get started free</router-link>
            </div>
          </div>
        </div>
        
        <!-- Professional Plan -->
        <div class="card bg-base-100 shadow-xl border-2 border-primary relative h-full">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div class="badge badge-primary badge-lg">Recommended</div>
          </div>
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h2 class="card-title text-2xl justify-center mb-2">Pro</h2>
              <p class="text-base-content/70 mb-6">For growing businesses and applications</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€{{ proPlan.price?.monthly?.toFixed(2) || '9.95' }}</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ proPlan.monthlyEmailLimit?.toLocaleString() || '1,000' }} emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ proPlan.domains || 5 }} domains + {{ proPlan.aliases || 10 }} aliases and {{ proPlan.webhooks || 10 }} webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Custom headers in webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Docs inline (&lt;768KB) + multi-file (custom) S3 storage</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Discounted credits: €0.80/100 emails</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Priority email support</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/login" class="btn btn-primary btn-block">Start with Pro</router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Feature Comparison -->
      <div class="mb-16">
        <h2 class="text-3xl font-bold text-center mb-8">Feature comparison</h2>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Feature</th>
                <th class="text-center">Starter</th>
                <th class="text-center">Pro</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>Monthly emails</strong></td>
                <td class="text-center">50</td>
                <td class="text-center">1,000</td>
              </tr>
              <tr>
                <td><strong>Domains</strong></td>
                <td class="text-center">1</td>
                <td class="text-center">5</td>
              </tr>
              <tr>
                <td><strong>Aliases per domain</strong></td>
                <td class="text-center">3</td>
                <td class="text-center">10</td>
              </tr>
              <tr>
                <td><strong>Webhooks per alias</strong></td>
                <td class="text-center">3</td>
                <td class="text-center">10</td>
              </tr>
              <tr>
                <td><strong>Attachment handling</strong></td>
                <td class="text-center">Text &amp; docs inline (&lt;128KB)</td>
                <td class="text-center">Text/docs inline (&lt;768KB) + S3 storage (all file types, 10MB max)</td>
              </tr>
              <tr>
                <td><strong>Custom webhook headers</strong></td>
                <td class="text-center">❌</td>
                <td class="text-center">✅</td>
              </tr>
              <tr>
                <td><strong>Data retention</strong></td>
                <td class="text-center">2hrs</td>
                <td class="text-center">Configurable</td>
              </tr>
              <tr>
                <td><strong>EU hosting</strong></td>
                <td class="text-center">✅</td>
                <td class="text-center">✅</td>
              </tr>
              <tr>
                <td><strong>GDPR compliance</strong></td>
                <td class="text-center">✅</td>
                <td class="text-center">✅</td>
              </tr>
              <tr>
                <td><strong>Support</strong></td>
                <td class="text-center">Best effort</td>
                <td class="text-center">Dedicated</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="mb-16">
        <h2 class="text-3xl font-bold text-center mb-8">Pricing FAQ</h2>
        <div class="max-w-3xl mx-auto space-y-4">
          <div class="collapse collapse-arrow bg-base-200">
            <input type="radio" name="pricing-faq" /> 
            <div class="collapse-title text-lg font-medium">
              How does the free plan work?
            </div>
            <div class="collapse-content"> 
              <p>The free plan includes 50 emails per month at no cost. When you exceed this limit, you can purchase additional credits at €1.00 per 100 emails. No monthly fees, pay only for what you use.</p>
            </div>
          </div>
          <div class="collapse collapse-arrow bg-base-200">
            <input type="radio" name="pricing-faq" /> 
            <div class="collapse-title text-lg font-medium">
              Can I change plans anytime?
            </div>
            <div class="collapse-content"> 
              <p>Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing adjustments.</p>
            </div>
          </div>
          <div class="collapse collapse-arrow bg-base-200">
            <input type="radio" name="pricing-faq" /> 
            <div class="collapse-title text-lg font-medium">
              What happens if I exceed my monthly email limit?
            </div>
            <div class="collapse-content"> 
              <p>You can purchase additional email credits as needed. Starter plans pay €1.00 per 100 emails, Pro plans get discounted rates at €0.80 per 100 emails. Your service continues uninterrupted.</p>
            </div>
          </div>
          <div class="collapse collapse-arrow bg-base-200">
            <input type="radio" name="pricing-faq" /> 
            <div class="collapse-title text-lg font-medium">
              Do you offer annual discounts?
            </div>
            <div class="collapse-content"> 
              <p>Yes! Annual plans are available with significant discounts. Contact us or upgrade from your dashboard to see annual pricing options for your plan.</p>
            </div>
          </div>
          <div class="collapse collapse-arrow bg-base-200">
            <input type="radio" name="pricing-faq" /> 
            <div class="collapse-title text-lg font-medium">
              Is there a setup fee or contract?
            </div>
            <div class="collapse-content"> 
              <p>No setup fees, no contracts, no hidden costs. Start free and cancel anytime. You only pay for the plan and additional email credits you actually use.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom CTA -->
      <div class="text-center">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 text-primary-content">
          <h3 class="text-2xl font-bold mb-4">Ready to get started?</h3>
          <p class="mb-6 opacity-90">
            Join developers who trust EmailConnect for secure, EU-hosted email automation.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link to="/login" class="btn btn-accent btn-lg">
              Get started free
            </router-link>
            <a href="/docs" class="btn btn-outline btn-lg text-primary-content border-primary-content hover:bg-primary-content hover:text-primary">
              View API docs
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { usePlanConfig } from '../../composables/usePlanConfig'

// Load plan configuration
const { freePlan, proPlan, loadPlanConfig } = usePlanConfig()

onMounted(async () => {
  await loadPlanConfig()
})
</script>
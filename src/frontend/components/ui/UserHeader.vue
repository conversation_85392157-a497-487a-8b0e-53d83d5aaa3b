<template>
  <header class="bg-base-100 border-b border-base-300">
    <div class="flex items-center justify-between h-16 px-6 mx-auto max-w-7xl lg:px-8">
      <div class="flex items-center space-x-4">
        <router-link to="/domains" class="flex items-center space-x-2">
          <Logo :show-text="false" :clickable="false" />
        </router-link>
      </div>

      <div class="flex-none flex items-center gap-1 md:gap-4">
        <!-- Notifications -->
        <NotificationBell />

        <!-- User Dropdown -->
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
            <div class="w-10 rounded-full" :class="avatarBorderClass">
              <img
                v-if="gravatarUrl"
                :src="gravatarUrl"
                :alt="user?.name || user?.email || 'User'"
                class="w-10 h-10 rounded-full"
                width="40"
                height="40"
                @error="onGravatarError"
                @load="onGravatarLoad"
              />
              <div v-else class="flex items-center justify-center w-10 h-10 bg-primary text-primary-content rounded-full">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
            <!-- Impersonation Notice -->
            <li v-if="isImpersonating" class="mb-2">
              <div class="alert alert-warning py-2 px-3">
                <div class="flex-col items-start">
                  <span class="text-xs font-bold">Impersonating</span>
                  <span class="text-xs"> as {{ user?.email }}</span>
                  <button @click="stopImpersonation" class="btn btn-xs btn-error mt-2">
                    Stop impersonation
                  </button>
                </div>
              </div>
            </li>
            
            <li class="menu-title">
              <div class="inline-flex items-center gap-2">
                <span>{{ user?.name || user?.email || 'User' }}</span>
                <span
                  v-if="user?.planType"
                  class="badge badge-sm"
                  :class="planBadgeClass"
                >
                  {{ planDisplayName }}
                </span>
              </div>
            </li>
            <li>
              <router-link to="/settings" class="flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span class="text-base">Settings</span>
              </router-link>
            </li>
            <li>
              <router-link to="/help" class="flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span class="text-base">Help center</span>
              </router-link>
            </li>
            <li>
              <a
                href="mailto:<EMAIL>?subject=Support"
                class="flex items-center gap-2"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-1.106A9.723 9.723 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8c0 1.063-.229 2.077-.602 3.001m0 0H21"
                  />
                </svg>
                <span class="text-base">Contact</span>
              </a>
            </li>
            <li>
              <button @click="toggleTheme" class="flex items-center w-full gap-2 text-left">
                <svg v-if="!isDarkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
                <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <span class="text-base">{{ isDarkMode ? 'Light mode' : 'Dark mode' }}</span>
              </button>
            </li>
            <li>
              <button @click="logout" class="flex items-center w-full gap-2 text-left">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span class="text-base">Logout</span>
              </button>
            </li>
            
            <!-- Admin Actions (separated with divider) -->
            <template v-if="canImpersonate && !isImpersonating">
              <div class="border border-t-1 border-base-200 my-1"></div>
              <li>
                <router-link to="/admin" class="flex items-center gap-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  <span class="text-base">Admin</span>
                </router-link>
              </li>
            </template>
          </ul>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useAuth } from '../../composables/useAuth'
import { useMetrics } from '../../composables/useMetrics'
import { useNotifications } from '../../composables/useNotifications'
import { useUserSettings } from '../../composables/useUserSettings'
import Logo from '../ui/Logo.vue'
import NotificationBell from '../ui/NotificationBell.vue'
import md5 from 'blueimp-md5'
import { api } from '../../utils/api'

// Types
interface User {
  id: string
  email: string
  name?: string
  planType?: string
}

// Use global auth composable
const { user: authUser, logout: authLogout, checkAuth } = useAuth()

// Use shared metrics composable instead of duplicate API call
const { metricsData, loadMetrics } = useMetrics()

// Initialize notifications for the app
const { initializeNotifications } = useNotifications()

// Use user settings composable for Gravatar setting
const { settings: userSettings, loadSettings } = useUserSettings()

// Computed user from auth composable
const user = computed(() => authUser.value)
const useGravatar = computed(() => userSettings.value.useGravatar)
const showGravatar = ref(false)
// Get impersonation status from auth user data
const isImpersonating = computed(() => user.value?.isImpersonating || false)
const impersonatorEmail = computed(() => user.value?.impersonatorEmail || null)
const canImpersonate = ref(false)

// Computed properties for plan badge
const planDisplayName = computed(() => {
  const planType = user.value?.planType || 'free'
  switch (planType.toLowerCase()) {
    case 'pro':
      return 'Pro'
    case 'enterprise':
      return 'Enterprise'
    case 'free':
    default:
      return 'Free'
  }
})

const planBadgeClass = computed(() => {
  const planType = user.value?.planType || 'free'
  switch (planType.toLowerCase()) {
    case 'pro':
      return 'badge-primary badge-outline'
    case 'enterprise':
      return 'badge-secondary badge-outline'
    case 'free':
    default:
      return 'badge-neutral'
  }
})

// Avatar border styling based on plan
const avatarBorderClass = computed(() => {
  const planType = user.value?.planType || 'free'
  switch (planType.toLowerCase()) {
    case 'pro':
      return 'ring-2 ring-primary ring-offset-2 ring-offset-base-100'
    case 'enterprise':
      return 'ring-2 ring-secondary ring-offset-2 ring-offset-base-100'
    case 'free':
    default:
      return ''
  }
})

// Generate Gravatar URL
const gravatarUrl = computed(() => {
  if (!useGravatar.value || !user.value?.email) return null

  const emailHash = md5(user.value.email.trim().toLowerCase())
  return `https://www.gravatar.com/avatar/${emailHash}?s=40&d=404`
})

// Handle Gravatar loading error
const onGravatarError = () => {
  showGravatar.value = false
}

// Handle Gravatar loading success
const onGravatarLoad = () => {
  if (useGravatar.value) {
    showGravatar.value = true
  }
}

// Watch for changes to useGravatar setting
watch(useGravatar, (newValue) => {
  if (newValue && user.value?.email) {
    // Reset showGravatar when user enables it, to retry loading
    showGravatar.value = true
  } else {
    showGravatar.value = false
  }
})

// Check impersonation status
// Check if user can impersonate (admin check)
const checkAdminStatus = async () => {
  try {
    // Check admin status via backend API
    const response = await fetch('/api/auth-status', {
      credentials: 'include'
    })
    
    if (response.ok) {
      const data = await response.json()
      canImpersonate.value = data?.user?.role === 'admin'
    } else {
      canImpersonate.value = false
    }
  } catch (error) {
    console.error('Failed to check admin status:', error)
    canImpersonate.value = false
  }
}

// Stop impersonation
const stopImpersonation = async () => {
  try {
    // Use BetterAuth admin stop impersonation endpoint
    const response = await fetch('/api/auth/admin/stop-impersonating', {
      method: 'POST',
      credentials: 'include'
    })
    
    if (response.ok) {
      // Reload page to restore original session
      window.location.href = '/domains'
    } else {
      console.error('Failed to stop impersonation:', await response.text())
    }
  } catch (error) {
    console.error('Error stopping impersonation:', error)
  }
}

// Load user data - auth composable handles user state via router guards
// Just need to check admin status and load metrics
const initializeHeader = async () => {
  try {
    // Ensure auth state is initialized so planType is up-to-date
    await checkAuth?.()

    // Load metrics for dashboard counts/stats
    await loadMetrics()
    
    // Load user settings for Gravatar preference
    await loadSettings()

    // Check if user can impersonate
    await checkAdminStatus()
  } catch (error) {
    console.error('Failed to initialize header:', error)
  }
}

// Logout function - use auth composable
const logout = authLogout

// Theme toggle functionality
const isDarkMode = ref(false)

const toggleTheme = () => {
  const newTheme = isDarkMode.value ? 'emerald' : 'dim'
  document.documentElement.setAttribute('data-theme', newTheme)
  isDarkMode.value = !isDarkMode.value

  // Store preference in localStorage
  localStorage.setItem('theme', newTheme)
}

const initializeTheme = () => {
  // Check for saved theme preference or default to 'emerald'
  const savedTheme = localStorage.getItem('theme')
  const currentTheme = savedTheme || document.documentElement.getAttribute('data-theme') || 'emerald'

  isDarkMode.value = currentTheme === 'dim'
  document.documentElement.setAttribute('data-theme', currentTheme)
}

onMounted(() => {
  initializeHeader()
  // Initialize notifications when header loads (user is authenticated)
  initializeNotifications()
  // Initialize theme
  initializeTheme()
})
</script>

<style scoped>
/* User header specific styles */
</style>

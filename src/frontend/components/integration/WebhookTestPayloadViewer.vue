<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useWebhookTest } from '@composables/useWebhookTest'

  interface Props {
    verificationCode: string
    webhookEndpoint: string
  }

  const props = defineProps<Props>()
  const { getPayloads } = useWebhookTest()

  const payloads = ref<any[]>([])
  const loading = ref(false)
  const polling = ref<NodeJS.Timeout>()

  const loadPayloads = async () => {
    try {
      loading.value = true
      const result = await getPayloads(props.verificationCode)
      payloads.value = result
    } catch (error) {
      console.error('Failed to load payloads:', error)
    } finally {
      loading.value = false
    }
  }

  const copyPayload = (payload: any) => {
    navigator.clipboard.writeText(JSON.stringify(payload.data, null, 2))
  }

  onMounted(() => {
    loadPayloads()
    // Poll for new payloads every 3 seconds
    polling.value = setInterval(loadPayloads, 3000)
  })

  onUnmounted(() => {
    if (polling.value) {
      clearInterval(polling.value)
    }
  })
  </script>

  <template>
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">Received Payloads</h3>
        <div class="badge badge-primary">{{ payloads.length }} received</div>
      </div>

      <div class="text-sm text-base-content/70">
        Webhook endpoint: <code class="bg-base-200 px-2 py-1rounded">{{ webhookEndpoint }}</code>
      </div>

      <div v-if="payloads.length === 0 && !loading" class="text-center py-8 text-base-content/50">
        No payloads received yet. Send a test email to see the structure.
      </div>

      <div v-for="(payload, index) in payloads" :key="index" class="card bg-base-200 shadow">
        <div class="card-body">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium">Payload {{ payloads.length - index }}</h4>
            <div class="text-xs text-base-content/60">{{ payload.timestamp }}</div>
          </div>
          <pre class="text-xs bg-base-300 p-3 rounded overflow-auto max-h-64">{{ JSON.stringify(payload.data, null, 2) }}</pre>
          <div class="card-actions justify-end">
            <button class="btn btn-sm btn-outline" @click="copyPayload(payload)">
              Copy JSON
            </button>
          </div>
        </div>
      </div>
    </div>
  </template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuth } from '@composables/useAuth'
import { useMetrics } from '@composables/useMetrics'
import { useToast } from '@composables/useToast'
import { api } from '../../utils/api'

interface Props {
  webhookData: {
    webhookId: string
    webhookUrl: string
    webhookName: string
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// Composables
const { isPro } = useAuth()
const { metricsData } = useMetrics()
const { success, error } = useToast()

// State
const isLoading = ref(false)
const testResult = ref<any>(null)

// Form data
const formData = ref({
  subject: 'Test email from EmailConnect',
  content: 'This is a test email sent from EmailConnect to verify your webhook is working correctly.\n\nIf you receive this, your webhook integration is functioning properly!'
})

// Attachment samples for quick testing
const attachmentSamples = [
  {
    key: 'none',
    label: 'No attachments',
    attachments: []
  },
  {
    key: 'small-text',
    label: 'Small text (inline)',
    attachments: [
      {
        filename: 'hello.txt',
        contentType: 'text/plain',
        content: 'SGVsbG8gRW1haWxDb25uZWN0IQ==', // "Hello EmailConnect!"
        size: 19
      }
    ]
  },
  {
    key: 'small-zip',
    label: 'Small ZIP (test file)',
    attachments: [
      {
        filename: 'test.zip',
        contentType: 'application/zip',
        downloadUrl: 'https://static.emailconnect.eu/files/test.zip',
        size: 2048 // Will be determined by the actual file
      }
    ]
  },
  {
    key: 'image',
    label: 'Image (PNG ~10KB)',
    attachments: [
      {
        filename: 'pixel.png',
        contentType: 'image/png',
        content: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGNgYAAAAAMAASsJTYQAAAAASUVORK5CYII=',
        size: 67
      }
    ]
  }
]

const selectedSampleKey = ref('none')
const selectedSample = computed(() => attachmentSamples.find(a => a.key === selectedSampleKey.value) || attachmentSamples[0])

// Plan-based restrictions - use auth composable
// const isPro is already available from useAuth()



// Methods
const handleTest = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    testResult.value = null

    const customPayload = isPro.value ? {
      subject: formData.value.subject,
      content: {
        text: formData.value.content,
        html: formData.value.content // Use same content for both text and HTML
      }
    } : undefined

    // Compose payload; include sample attachments when selected (Pro users only)
    const payloadBody = customPayload ? { customPayload } : {}
    if (isPro.value && selectedSample.value.attachments?.length) {
      ;(payloadBody as any).attachments = selectedSample.value.attachments
    }

    // Make API call directly
    const result = await api.post(`/api/webhooks/${props.webhookData.webhookId}/test`, payloadBody)

    testResult.value = result
    success(`Test webhook sent successfully to ${props.webhookData.webhookName}!`)

  } catch (err) {
    console.error('Failed to test webhook:', err)
    error(err instanceof Error ? err.message : 'Failed to send test webhook')
  } finally {
    isLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    subject: 'Test email from EmailConnect',
    content: 'This is a test email sent from EmailConnect to verify your webhook is working correctly.\n\nIf you receive this, your webhook integration is functioning properly!'
  }
  testResult.value = null
}

onMounted(() => {
  resetForm()
})
</script>

<template>
  <form @submit.prevent="handleTest" class="space-y-6">
    <!-- Webhook URL (Read-only) -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook URL</span>
      </label>
      <input
        :value="webhookData.webhookUrl"
        type="text"
        class="w-full input input-bordered bg-base-200"
        readonly
      />
    </div>

    <!-- Sample attachments -->
    <div class="form-control">
      <label class="label">
        <span class="badge badge-primary badge-outline badge-sm">Pro</span>
        <span class="label-text">Attachments (sample)</span>
      </label>
      <select v-model="selectedSampleKey" class="select select-bordered w-full" :disabled="!isPro || isLoading">
        <option v-for="opt in attachmentSamples" :key="opt.key" :value="opt.key">{{ opt.label }}</option>
      </select>
      <div v-if="!isPro" class="label">
        <span class="label-text-alt text-base-content/50 text-xs">
          Attachment samples available with Pro plan.
        </span>
      </div>
      <div v-else-if="selectedSample.attachments.length" class="label">
        <span class="label-text-alt text-xs">
          {{ selectedSample.attachments.length }} attachment(s) will be included in the test payload
        </span>
      </div>
    </div>

    <!-- Subject Line -->
    <div class="form-control">
      <label class="label">
        <span class="badge badge-primary badge-outline badge-sm">Pro</span>
        <span class="label-text">Subject line</span>
      </label>
      <input
        v-model="formData.subject"
        type="text"
        placeholder="Test email from EmailConnect"
        class="w-full input input-bordered"
        :disabled="!isPro || isLoading"
      />
      <div v-if="!isPro" class="label">
        <span class="label-text-alt text-base-content/50 text-xs">
          Custom subject available with Pro plan.
          <router-link to="/settings#billing" @click="emit('close')" class="link link-primary">Upgrade now</router-link>
        </span>
      </div>
    </div>

    <!-- Email Content -->
    <div class="form-control">
      <label class="label">
        <span class="badge badge-primary badge-outline badge-sm">Pro</span>
        <span class="label-text">Email content</span>
      </label>
      <textarea
        v-model="formData.content"
        rows="4"
        placeholder="This is a test email sent from EmailConnect to verify your webhook is working correctly.

If you receive this, your webhook integration is functioning properly!"
        class="w-full textarea textarea-bordered"
        :disabled="!isPro || isLoading"
      />
      <div v-if="!isPro" class="label">
        <span class="label-text-alt text-base-content/50 text-xs">
          Custom content available with Pro plan.
          <router-link to="/settings#billing" @click="emit('close')" class="link link-primary">Upgrade now</router-link>
        </span>
      </div>
      <div v-else class="label">
        <span class="label-text-alt text-xs">This content will be used for both text and HTML versions of the email</span>
      </div>
    </div>

    <!-- Test Result -->
    <div v-if="testResult" class="px-6 py-4 bg-gradient-to-r from-primary/10 via-secondary/10 to-accent/10 rounded-lg flex items-start gap-3">
      <!-- Icon -->
      <div class="pt-1">
        <svg v-if="testResult.success" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>

      <!-- Content -->
      <div>
        <h4 class="font-bold">{{ testResult.success ? 'Test successful!' : 'Test failed' }}</h4>
        <p class="text-sm">{{ testResult.message }}</p>
        <p v-if="testResult.jobId" class="text-xs opacity-70 mt-1">Job ID: {{ testResult.jobId }}</p>
      </div>
    </div>


    <!-- Modal Actions -->
    <div class="modal-action">
      <button
        type="button"
        v-if="isPro"
        @click="resetForm"
        class="btn btn-ghost"
        :disabled="isLoading"
      >
        Reset
      </button>
      <button
        type="button"
        @click="emit('close')"
        class="btn btn-ghost"
        :disabled="isLoading"
      >
        Close
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="isLoading"
        :class="{ loading: isLoading }"
      >
        {{ isLoading ? 'Sending...' : 'Send test' }}
      </button>
    </div>
  </form>
</template>

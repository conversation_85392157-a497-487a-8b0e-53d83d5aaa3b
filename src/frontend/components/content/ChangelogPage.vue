<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <div class="prose prose-lg max-w-none text-base-content">
      <h1 class="text-4xl font-bold text-base-content mb-8">Changelog</h1>
      
      <p class="text-base-content/70 mb-8">
        Stay up to date with the latest features, improvements, and bug fixes.
      </p>

      <!-- Loading state -->
      <div v-if="loading" class="flex justify-center py-8">
        <div class="loading loading-spinner loading-lg"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="alert alert-error mb-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Failed to load changelog entries. Please try again later.</span>
      </div>

      <!-- Changelog timeline -->
      <ul v-else-if="entries.length > 0" class="timeline timeline-vertical">
        <li v-for="(entry, index) in entries" :key="entry.slug">
          <hr v-if="index > 0" />

          <!-- On desktop: alternate between start and end for different sides -->
          <!-- On mobile: always use timeline-end for consistent full-width display -->
          <div v-if="index % 2 === 0" class="timeline-start timeline-box md:block hidden">
            <!-- Entry header -->
            <div class="flex items-center gap-2 mb-3">
              <span
                :class="getTypeClass(entry.type)"
                class="badge badge-sm font-medium"
              >
                {{ getTypeLabel(entry.type) }}
              </span>
              <time class="text-xs text-base-content/60">
                {{ formatDate(entry.date) }}
              </time>
            </div>

            <!-- Entry title -->
            <h3 class="text-lg font-semibold text-base-content mb-2">
              {{ entry.title }}
            </h3>

            <!-- Entry excerpt (2-3 lines max) -->
            <p class="text-sm text-base-content/80 leading-relaxed">
              {{ entry.excerpt }}
            </p>
          </div>

          <div class="timeline-middle">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              :class="getIconClass(entry.type)"
              class="h-5 w-5"
            >
              <path
                v-if="entry.type === 'added'"
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-11.25a.75.75 0 00-1.5 0v2.5h-2.5a.75.75 0 000 1.5h2.5v2.5a.75.75 0 001.5 0v-2.5h2.5a.75.75 0 000-1.5h-2.5v-2.5z"
                clip-rule="evenodd"
              />
              <path
                v-else-if="entry.type === 'fixed'"
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                clip-rule="evenodd"
              />
              <path
                v-else
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm-3.25-7.25a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <!-- On desktop: show on end for odd indices, on mobile: always show -->
          <div :class="{'md:block': index % 2 === 1, 'block md:hidden': index % 2 === 0}" class="timeline-end timeline-box">
            <!-- Entry header -->
            <div class="flex items-center gap-2 mb-3">
              <span
                :class="getTypeClass(entry.type)"
                class="badge badge-sm font-medium"
              >
                {{ getTypeLabel(entry.type) }}
              </span>
              <time class="text-xs text-base-content/60">
                {{ formatDate(entry.date) }}
              </time>
            </div>

            <!-- Entry title -->
            <h3 class="text-lg font-semibold text-base-content mb-2">
              {{ entry.title }}
            </h3>

            <!-- Entry excerpt (2-3 lines max) -->
            <p class="text-sm text-base-content/80 leading-relaxed">
              {{ entry.excerpt }}
            </p>
          </div>

          <hr v-if="index < entries.length - 1" />
        </li>
      </ul>

      <!-- Empty state -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">📝</div>
        <h3 class="text-xl font-semibold text-base-content mb-2">No changelog entries yet</h3>
        <p class="text-base-content/60">Check back later for updates and new features.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface ChangelogEntry {
  slug: string
  date: string
  type: 'added' | 'changed' | 'fixed'
  title: string
  excerpt: string
  content: string
}

const entries = ref<ChangelogEntry[]>([])
const loading = ref(true)
const error = ref(false)

const fetchChangelog = async () => {
  try {
    loading.value = true
    error.value = false
    
    const response = await fetch('/api/public/changelog')
    if (!response.ok) {
      throw new Error('Failed to fetch changelog')
    }
    
    const data = await response.json()
    entries.value = data.entries || []
  } catch (err) {
    console.error('Error fetching changelog:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

const getTypeClass = (type: string) => {
  switch (type) {
    case 'added':
      return 'badge-success'
    case 'changed':
      return 'badge-warning'
    case 'fixed':
      return 'badge-error'
    default:
      return 'badge-neutral'
  }
}

const getIconClass = (type: string) => {
  switch (type) {
    case 'added':
      return 'text-success'
    case 'changed':
      return 'text-warning'
    case 'fixed':
      return 'text-error'
    default:
      return 'text-neutral'
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'added':
      return 'Added'
    case 'changed':
      return 'Changed'
    case 'fixed':
      return 'Fixed'
    default:
      return 'Update'
  }
}

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}



onMounted(() => {
  fetchChangelog()
})
</script>

<style scoped>
/* Additional styles if needed */
</style>

<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="loading loading-spinner loading-lg"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="alert alert-error mb-8">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errorMessage }}</span>
    </div>

    <!-- Article content -->
    <div v-else-if="article" class="prose prose-lg max-w-none text-base-content">
      <!-- Breadcrumb navigation -->
      <nav class="flex items-center space-x-2 text-sm text-base-content/60 mb-8">
        <router-link to="/help" class="hover:text-primary">Help</router-link>
        <span>/</span>
        <span>{{ formatCategoryName(article.category) }}</span>
        <span>/</span>
        <span class="text-base-content">{{ article.title }}</span>
      </nav>

      <!-- Article header -->
      <div class="mb-8">
        <div class="flex items-center gap-2 mb-4">
          <span class="badge badge-primary badge-sm">{{ formatCategoryName(article.category) }}</span>
        </div>
        <h1 class="text-4xl font-bold text-base-content mb-4">{{ article.title }}</h1>
        <p class="text-lg text-base-content/70">{{ article.excerpt }}</p>
      </div>

      <!-- Article content with enhanced styling -->
      <div 
        class="prose prose-lg max-w-none text-base-content help-content"
        v-html="parseMarkdown(article.content)"
      ></div>

      <!-- Article footer -->
      <div class="mt-12 pt-8 border-t border-base-300">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="text-sm text-base-content/60">
            Was this article (not) helpful? 
            <a href="mailto:<EMAIL>" class="text-primary hover:underline ml-1">
              Let us know
            </a>
          </div>
          
          <router-link 
            to="/help"
            class="btn btn-outline btn-sm"
          >
            ← Back to help
          </router-link>
        </div>
      </div>
    </div>

    <!-- Not found state -->
    <div v-else class="text-center py-12">
      <div class="text-6xl mb-4">📄</div>
      <h1 class="text-2xl font-bold text-base-content mb-2">Article not found</h1>
      <p class="text-base-content/60 mb-6">The help article you're looking for doesn't exist or has been moved.</p>
      <router-link to="/help" class="btn btn-primary">
        Browse help articles
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { marked } from 'marked'
import { sanitizeHtml } from '../../utils/sanitizeHtml'
import { useSEO } from '@/composables/useSEO'

interface HelpArticle {
  slug: string
  title: string
  excerpt: string
  category: string
  order: number
  content: string
}

const route = useRoute()
const article = ref<HelpArticle | null>(null)
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')

// Configure marked for proper rendering
marked.setOptions({
  breaks: true,
  gfm: true
})

const parseMarkdown = (text: string): string => {
  try {
    // Configure marked to handle code blocks with language hints
    (marked.setOptions as any)({
      breaks: true,
      gfm: true,
      highlight: function(code: string, lang: string) {
        // For JSON, add some basic syntax highlighting
        if (lang === 'json') {
          // Highlight strings (including keys)
          code = code.replace(/"([^"]+)":/g, '<span class="json-key">"$1"</span>:')
          code = code.replace(/: "([^"]+)"/g, ': <span class="json-string">"$1"</span>')
          // Highlight numbers
          code = code.replace(/: (\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
          // Highlight booleans and null
          code = code.replace(/: (true|false|null)/g, ': <span class="json-literal">$1</span>')
        }
        // For bash/shell commands
        if (lang === 'bash' || lang === 'shell' || lang === 'sh') {
          // Highlight comments
          code = code.replace(/(#.*$)/gm, '<span class="bash-comment">$1</span>')
          // Highlight strings
          code = code.replace(/"([^"]+)"/g, '<span class="bash-string">"$1"</span>')
          code = code.replace(/'([^']+)'/g, '<span class="bash-string">\'$1\'</span>')
        }
        return code
      },
      langPrefix: 'language-'
    })
    
let html = marked.parse(text) as string

    // Sanitize HTML using DOMPurify wrapper
    html = sanitizeHtml(html)
    
    // Post-process to add better code block formatting
    // Handle code blocks with language hints (e.g., ```json)
    html = html.replace(/<pre><code class="language-(\w+)">/g, '<pre class="language-$1" data-lang="$1"><code class="hljs language-$1">')
    
    // Handle code blocks without language hints
    html = html.replace(/<pre><code>/g, '<pre data-lang="code"><code class="hljs">')
    
    // For inline code, detect patterns
    // Highlight JSON-like structures in inline code
    html = html.replace(/<code>(\{[^}]*\})<\/code>/g, '<code class="code-json">$1</code>')
    
    // Highlight URLs in inline code
    html = html.replace(/<code>(https?:\/\/[^<]*)<\/code>/g, '<code class="code-url">$1</code>')
    
    // Highlight common HTTP methods in inline code
    html = html.replace(/<code>(GET|POST|PUT|DELETE|PATCH)<\/code>/g, '<code class="code-method">$1</code>')
    
    // Highlight file paths in inline code
    html = html.replace(/<code>(\/[^<]+\.[^<]+)<\/code>/g, '<code class="code-path">$1</code>')
    
    return html
  } catch (error) {
    console.error('Failed to parse markdown:', error)
    // Fallback: return text with basic line breaks
    return text.replace(/\n/g, '<br>')
  }
}

const { setPageMeta } = useSEO()

const fetchArticle = async (slug: string) => {
  try {
    loading.value = true
    error.value = false
    article.value = null
    
    const response = await fetch(`/api/public/help/${slug}`)
    
    if (response.status === 404) {
      // Article not found - this is handled by the template
      return
    }
    
    if (!response.ok) {
      throw new Error('Failed to fetch article')
    }
    
    const data = await response.json()
    article.value = data.article
    
    // Set SEO meta tags after article loads
    if (article.value) {
      setPageMeta({
        title: article.value.title,
        description: article.value.excerpt,
        keywords: `${article.value.category}, email automation, webhook, help, tutorial`,
        ogUrl: `https://emailconnect.eu/help/${slug}`
      })
    }
  } catch (err) {
    console.error('Error fetching article:', err)
    error.value = true
    errorMessage.value = 'Failed to load the article. Please try again later.'
  } finally {
    loading.value = false
  }
}

const formatCategoryName = (category: string) => {
  const formatted = category.replace(/-/g, ' ').toLowerCase()
  return formatted.charAt(0).toUpperCase() + formatted.slice(1)
}

// Watch for route changes
watch(() => route.params.slug, (newSlug) => {
  if (newSlug && typeof newSlug === 'string') {
    fetchArticle(newSlug)
  }
}, { immediate: true })

onMounted(() => {
  const slug = route.params.slug
  if (slug && typeof slug === 'string') {
    fetchArticle(slug)
  }
})
</script>

<style scoped>
/* Base prose styling for help content */
.help-content :deep(h1) {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.help-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.help-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.help-content :deep(p) {
  font-size: 1rem;
  line-height: 1.75;
  margin-bottom: 1rem;
}

.help-content :deep(p:last-child) {
  margin-bottom: 0;
}

.help-content :deep(ul),
.help-content :deep(ol) {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.help-content :deep(ul) {
  list-style-type: disc;
}

.help-content :deep(ol) {
  list-style-type: decimal;
}

.help-content :deep(li) {
  font-size: 1rem;
  line-height: 1.75;
  margin-bottom: 0.5rem;
}

.help-content :deep(strong) {
  font-weight: 600;
  color: var(--fallback-bc, oklch(var(--bc)));
}

/* Inline code styling - using DaisyUI base colors */
.help-content :deep(:not(pre) > code) {
  background: oklch(var(--b3));
  border: 1px solid oklch(var(--bc) / 0.2);
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.85rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-weight: 500;
  color: oklch(var(--p));
  white-space: nowrap;
  display: inline-block;
  line-height: 1.4;
  box-shadow: 0 1px 3px oklch(var(--bc) / 0.1);
}

/* Special code type highlighting with DaisyUI colors */
.help-content :deep(code.code-json) {
  background: oklch(var(--s) / 0.15);
  border-color: oklch(var(--s) / 0.3);
  color: oklch(var(--sf));
  box-shadow: 0 0 12px oklch(var(--s) / 0.2);
}

.help-content :deep(code.code-url) {
  background: oklch(var(--a) / 0.15);
  border-color: oklch(var(--a) / 0.3);
  color: oklch(var(--af));
  word-break: break-all;
  white-space: normal;
  box-shadow: 0 0 12px oklch(var(--a) / 0.2);
}

.help-content :deep(code.code-method) {
  background: oklch(var(--wa) / 0.15);
  border-color: oklch(var(--wa) / 0.3);
  color: oklch(var(--waf));
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  box-shadow: 0 0 12px oklch(var(--wa) / 0.2);
}

.help-content :deep(code.code-path) {
  background: oklch(var(--in) / 0.15);
  border-color: oklch(var(--in) / 0.3);
  color: oklch(var(--inf));
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  box-shadow: 0 0 12px oklch(var(--in) / 0.2);
}

/* Code block styling */
.help-content :deep(pre) {
  background: oklch(var(--b3));
  border: 1px solid oklch(var(--bc) / 0.2);
  padding: 1.25rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  position: relative;
  box-shadow: 
    0 4px 12px oklch(var(--bc) / 0.1),
    0 0 24px oklch(var(--p) / 0.05),
    inset 0 1px 0 oklch(var(--bc) / 0.05);
}

/* Add a subtle top border accent for code blocks */
.help-content :deep(pre)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    oklch(var(--p)) 0%, 
    oklch(var(--s)) 100%);
  border-radius: 0.75rem 0.75rem 0 0;
  opacity: 0.7;
}

/* Add language label for code blocks */
.help-content :deep(pre[data-lang])::after {
  content: attr(data-lang);
  position: absolute;
  top: 0.75rem;
  right: 1rem;
  color: oklch(var(--bc) / 0.5);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05rem;
  background: oklch(var(--b2) / 0.8);
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-family: system-ui, -apple-system, sans-serif;
}

/* Special styling for JSON code blocks */
.help-content :deep(pre.language-json) {
  background: linear-gradient(135deg,
    oklch(var(--b3)) 0%,
    oklch(var(--s) / 0.05) 100%);
  border-color: oklch(var(--s) / 0.2);
}

.help-content :deep(pre.language-json)::before {
  background: linear-gradient(90deg, 
    oklch(var(--s)) 0%, 
    oklch(var(--sf)) 100%);
}

/* Special styling for JavaScript/TypeScript code blocks */
.help-content :deep(pre.language-javascript),
.help-content :deep(pre.language-typescript),
.help-content :deep(pre.language-js),
.help-content :deep(pre.language-ts) {
  background: linear-gradient(135deg,
    oklch(var(--b3)) 0%,
    oklch(var(--wa) / 0.05) 100%);
  border-color: oklch(var(--wa) / 0.2);
}

/* Special styling for bash/shell code blocks */
.help-content :deep(pre.language-bash),
.help-content :deep(pre.language-shell),
.help-content :deep(pre.language-sh) {
  background: linear-gradient(135deg,
    oklch(var(--b3)) 0%,
    oklch(var(--a) / 0.05) 100%);
  border-color: oklch(var(--a) / 0.2);
}

.help-content :deep(pre code) {
  background: transparent;
  border: none;
  padding: 0;
  font-size: 0.875rem;
  line-height: 1.7;
  color: oklch(var(--bc));
  white-space: pre;
  display: block;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* JSON Syntax Highlighting */
.help-content :deep(.json-key) {
  color: oklch(var(--p));
  font-weight: 600;
}

.help-content :deep(.json-string) {
  color: oklch(var(--s));
}

.help-content :deep(.json-number) {
  color: oklch(var(--wa));
}

.help-content :deep(.json-literal) {
  color: oklch(var(--a));
  font-weight: 600;
}

/* Bash Syntax Highlighting */
.help-content :deep(.bash-comment) {
  color: oklch(var(--bc) / 0.5);
  font-style: italic;
}

.help-content :deep(.bash-string) {
  color: oklch(var(--s));
}

/* Scrollbar styling for code blocks */
.help-content :deep(pre)::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.help-content :deep(pre)::-webkit-scrollbar-track {
  background: oklch(var(--b3) / 0.3);
  border-radius: 4px;
}

.help-content :deep(pre)::-webkit-scrollbar-thumb {
  background: oklch(var(--bc) / 0.2);
  border-radius: 4px;
}

.help-content :deep(pre)::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--bc) / 0.3);
}

/* Links */
.help-content :deep(a) {
  color: var(--fallback-p, oklch(var(--p)));
  text-decoration: underline;
  text-underline-offset: 2px;
}

.help-content :deep(a:hover) {
  color: var(--fallback-pf, oklch(var(--pf)));
}

/* Blockquotes */
.help-content :deep(blockquote) {
  border-left: 4px solid var(--fallback-p, oklch(var(--p) / 0.3));
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: oklch(var(--bc) / 0.8);
}

/* Tables */
.help-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
}

.help-content :deep(th) {
  background: oklch(var(--b2));
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  border: 1px solid oklch(var(--bc) / 0.1);
}

.help-content :deep(td) {
  padding: 0.75rem;
  border: 1px solid oklch(var(--bc) / 0.1);
}

.help-content :deep(tr:hover) {
  background: oklch(var(--b2) / 0.3);
}

/* Horizontal rules */
.help-content :deep(hr) {
  border: none;
  border-top: 1px solid oklch(var(--bc) / 0.1);
  margin: 2rem 0;
}
</style>
<template>
  <div class="container mx-auto px-4 py-8 max-w-6xl">
    <div class="flex items-center justify-between mb-8">
      <h1 class="text-3xl font-bold">Admin dashboard</h1>
    </div>
    
    <!-- Status Banner -->
    <div v-if="!canAdmin" class="alert alert-error mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>You don't have admin permissions for this section.</span>
    </div>

    <!-- Admin Tabs -->
    <div v-else class="w-full">
      <div role="tablist" class="tabs tabs-lifted mb-6">
        <!-- Users Tab -->
        <input type="radio" name="admin_tabs" role="tab" class="tab" aria-label="Users" checked />
        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold flex items-center gap-2">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.239" />
              </svg>
              User management
            </h2>
            <div class="flex gap-2">
              <button @click="loadAllUsers" class="btn btn-outline btn-sm" :disabled="loadingUsers">
                <span v-if="loadingUsers" class="loading loading-spinner loading-xs"></span>
                <span v-else>Refresh</span>
              </button>
              <button @click="showUnifiedUserManager = true" class="btn btn-primary btn-sm">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search by email
              </button>
            </div>
          </div>

          <!-- Users Table -->
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>User</th>
                  <th>Plan</th>
                  <th>Trial status</th>
                  <th>Subscription</th>
                  <th>Joined</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="loadingUsers">
                  <td colspan="6" class="text-center py-8">
                    <span class="loading loading-spinner loading-md"></span>
                    Loading users...
                  </td>
                </tr>
                <tr v-else-if="allUsers.length === 0">
                  <td colspan="6" class="text-center py-8 text-base-content/60">
                    No users found
                  </td>
                </tr>
                <tr v-else v-for="user in allUsers" :key="user.id">
                  <td>
                    <div>
                      <div class="font-semibold">{{ user.name || 'No name' }}</div>
                      <div class="text-sm text-base-content/70">{{ user.email }}</div>
                    </div>
                  </td>
                  <td>
                    <span class="badge" :class="{
                      'badge-neutral': user.planType === 'free',
                      'badge-primary': user.planType === 'pro'
                    }">
                      {{ user.planType }}
                    </span>
                  </td>
                  <td>
                    <span v-if="user.trialStartedAt" class="badge badge-info badge-sm">
                      {{ isTrialActive(user) ? 'Active' : 'Used' }}
                    </span>
                    <span v-else class="text-sm text-base-content/50">Available</span>
                  </td>
                  <td>
                    <span v-if="user.subscription" class="badge badge-success badge-sm">
                      {{ user.subscription.status }}
                    </span>
                    <span v-else class="text-sm text-base-content/50">None</span>
                  </td>
                  <td class="text-sm">
                    {{ formatDate(user.createdAt) }}
                  </td>
                  <td class="justify-end">
                    <button @click="openUserManagementModal(user)" class="btn btn-primary btn-outline btn-sm">
                      Manage
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Finances Tab -->
        <input type="radio" name="admin_tabs" role="tab" class="tab" aria-label="Finances" />
        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <h2 class="card-title flex items-center gap-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Invoice management
                </h2>
                <p class="text-sm text-base-content/70">Generate and manage customer invoices</p>
                <div class="mt-4">
                  <button @click="showGenerateInvoice = true" class="btn btn-primary btn-sm">Generate invoice</button>
                </div>
              </div>
            </div>
            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <h2 class="card-title">Payments status</h2>
                <p class="text-sm text-base-content/70">Recent payments rollup</p>
                <div class="text-sm text-base-content/60">Coming soon</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Audits Tab -->
        <input type="radio" name="admin_tabs" role="tab" class="tab" aria-label="Audits" />
        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
          <AdminAuditsTab />
        </div>

        <!-- Workers Tab -->
        <input type="radio" name="admin_tabs" role="tab" class="tab" aria-label="Workers" />
        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
          <div class="mb-6 flex items-center justify-between">
            <h2 class="text-2xl font-bold">Background workers</h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <h3 class="card-title">Subscription lifecycle</h3>
                <p class="text-sm text-base-content/70">Processes end-of-period downgrades for users with cancelled subscriptions.</p>
                <div class="mt-4 flex gap-2">
                  <button class="btn btn-primary btn-sm" @click="runSubscriptionLifecycle" :disabled="runningLifecycle">
                    <span v-if="runningLifecycle" class="loading loading-spinner loading-xs"></span>
                    Run now
                  </button>
                  <span v-if="lastLifecycleRun" class="text-xs opacity-70">Last run: {{ lastLifecycleRun }}</span>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <h3 class="card-title">Renewal notifications</h3>
                <p class="text-sm text-base-content/70">Sends subscription renewal email reminders to users with virtual subscriptions expiring in 1-3 days.</p>
                <div class="mt-4 flex gap-2">
                  <button class="btn btn-primary btn-sm" @click="runRenewalNotifications" :disabled="runningRenewalNotifications">
                    <span v-if="runningRenewalNotifications" class="loading loading-spinner loading-xs"></span>
                    Run now
                  </button>
                  <span v-if="lastRenewalNotificationRun" class="text-xs opacity-70">Last run: {{ lastRenewalNotificationRun }}</span>
                </div>
              </div>
            </div>

            <div class="card bg-base-100 shadow">
              <div class="card-body">
                <h3 class="card-title">Heartbeat</h3>
                <p class="text-sm text-base-content/70">Sends a heartbeat to the monitoring service.</p>
                <div class="mt-4">
                  <button class="btn btn-outline btn-sm" @click="runHeartbeat" :disabled="runningHeartbeat">
                    <span v-if="runningHeartbeat" class="loading loading-spinner loading-xs"></span>
                    Trigger heartbeat
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Tab -->
        <input type="radio" name="admin_tabs" role="tab" class="tab" aria-label="Security" />
        <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
          <AdminSecurityTab />
        </div>
      </div>
    </div>

    <!-- Recent Payments Section -->
    <div v-if="recentPayments.length > 0" class="mt-8">
      <h2 class="text-xl font-semibold mb-4">Recent payments</h2>
      <div class="overflow-x-auto">
        <table class="table table-zebra">
          <thead>
            <tr>
              <th>User</th>
              <th>Amount</th>
              <th>Description</th>
              <th>Status</th>
              <th>Date</th>
              <th>Invoice</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="payment in recentPayments" :key="payment.id">
              <td>{{ payment.user?.email }}</td>
              <td class="font-semibold">{{ payment.currency }} {{ payment.amount }}</td>
              <td>{{ payment.description }}</td>
              <td>
                <span class="badge" :class="getStatusBadge(payment.status)">
                  {{ payment.status }}
                </span>
              </td>
              <td>{{ formatDate(payment.createdAt) }}</td>
              <td>
                <span v-if="payment.invoice" class="text-success">✓ Generated</span>
                <span v-else class="text-warning">Missing</span>
              </td>
              <td>
                <button 
                  v-if="!payment.invoice && payment.status === 'PAID'"
                  @click="generateInvoiceForPayment(payment.id)"
                  :disabled="generatingInvoices.has(payment.id)"
                  class="btn btn-xs btn-primary"
                >
                  <span v-if="generatingInvoices.has(payment.id)" class="loading loading-spinner loading-xs"></span>
                  <span v-else>Generate invoice</span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Generate Invoice Modal -->
    <div v-if="showGenerateInvoice" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Generate invoice</h3>
        
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">Payment ID</span>
          </label>
          <input 
            v-model="manualPaymentId" 
            type="text" 
            placeholder="Enter payment ID (e.g., cme1gl...)" 
            class="w-full input input-bordered"
          />
          <label class="label">
            <span class="label-text-alt text-sm">Generate invoice for a specific payment</span>
          </label>
        </div>

        <div class="modal-action">
          <button @click="showGenerateInvoice = false" class="btn">Cancel</button>
          <button 
            @click="generateManualInvoice" 
            :disabled="!manualPaymentId || generatingManual"
            class="btn btn-primary"
          >
            <span v-if="generatingManual" class="loading loading-spinner loading-xs"></span>
            Generate invoice
          </button>
        </div>
      </div>
    </div>

    <!-- User Lookup Modal -->
    <div v-if="showUserLookup" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg mb-4">Manage user subscription</h3>
        
        <!-- User Search -->
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">User email</span>
          </label>
          <div class="flex gap-2">
            <input 
              v-model="userEmail" 
              type="email" 
              placeholder="Enter user email address" 
              class="flex-1 input input-bordered"
              @keyup.enter="lookupUser"
            />
            <button 
              @click="lookupUser" 
              :disabled="!userEmail || lookingUpUser"
              class="btn btn-primary"
            >
              <span v-if="lookingUpUser" class="loading loading-spinner loading-xs"></span>
              <span v-else>Search</span>
            </button>
          </div>
        </div>

        <!-- User Details -->
        <div v-if="selectedUser" class="card bg-base-200 mb-4">
          <div class="card-body">
            <h4 class="card-title text-base">{{ selectedUser.email }}</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-semibold">Name:</span> {{ selectedUser.name || 'Not set' }}
              </div>
              <div>
                <span class="font-semibold">Current plan:</span> 
                <span class="badge" :class="selectedUser.planType === 'pro' ? 'badge-primary' : 'badge-neutral'">
                  {{ selectedUser.planType }}
                </span>
              </div>
              <div>
                <span class="font-semibold">Subscription status:</span>
                <span v-if="selectedUser.subscription" class="badge badge-success">
                  {{ selectedUser.subscription.status }}
                </span>
                <span v-else class="badge badge-neutral">No subscription</span>
              </div>
              <div v-if="selectedUser.subscription">
                <span class="font-semibold">Next payment:</span>
                {{ formatDate(selectedUser.subscription.nextPaymentDate) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Subscription Actions -->
        <div v-if="selectedUser" class="space-y-4">
          <!-- Upgrade Form -->
          <div v-if="selectedUser.planType === 'free'" class="space-y-3">
            <div class="form-control">
              <label class="label w-full sm:w-1/3">
                <span class="label-text">Renewal date</span>
              </label>
              <input 
                v-model="renewalDate" 
                type="date" 
                :min="getTomorrowDate()"
                class="w-full sm:w-2/3 input input-bordered"
                :disabled="processing"
              />
              <label class="label">
                <span class="label-text-alt text-xs">User will get renewal notifications 3 days before this date</span>
              </label>
            </div>
            
            <div class="flex justify-center">
              <button 
                @click="manageSubscription('upgrade')"
                :disabled="processing || !renewalDate"
                class="btn btn-success"
              >
                <span v-if="processing" class="loading loading-spinner loading-xs"></span>
                <span v-else>Upgrade to Pro</span>
              </button>
            </div>
          </div>

          <!-- Downgrade Action -->
          <div v-if="selectedUser.planType === 'pro'" class="flex justify-center">
            <button 
              @click="manageSubscription('downgrade')"
              :disabled="processing"
              class="btn btn-warning"
            >
              <span v-if="processing" class="loading loading-spinner loading-xs"></span>
<span v-else>Downgrade to Free</span>
            </button>
          </div>

          <!-- Warning for downgrades -->
          <div v-if="selectedUser.planType === 'pro'" class="alert alert-warning">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-sm">Downgrading will preserve user data within free plan limits (2 domains, basic features).</span>
          </div>
        </div>

        <div class="modal-action">
          <button @click="closeUserLookup" class="btn">Close</button>
        </div>
      </div>
    </div>

    <!-- Results -->
    <div v-if="lastResult" class="mt-6">
      <div class="alert" :class="lastResult.success ? 'alert-success' : 'alert-error'">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path v-if="lastResult.success" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ lastResult.message }}</span>
      </div>
    </div>
  </div>

  <!-- Trial Manager Modal -->
  <div v-if="showTrialManager" class="modal modal-open">
    <div class="modal-box max-w-2xl">
      <h3 class="font-bold text-lg mb-4">Trial management</h3>
      
      <!-- User Lookup Section -->
      <div class="space-y-4">
        <div>
          <label class="label">
            <span class="label-text">User email address</span>
          </label>
          <div class="join w-full">
            <input
              type="email"
              placeholder="Enter user email"
              class="input input-bordered join-item flex-1"
              v-model="trialUserEmail"
              @keyup.enter="lookupTrialUser"
            />
            <button 
              class="btn join-item" 
              @click="lookupTrialUser"
              :disabled="lookingUpTrialUser || !trialUserEmail"
            >
              <span v-if="lookingUpTrialUser" class="loading loading-spinner loading-sm"></span>
              <span v-else>Lookup</span>
            </button>
          </div>
        </div>

        <!-- User Info -->
        <div v-if="trialSelectedUser" class="card bg-base-200">
          <div class="card-body p-4">
            <h4 class="font-semibold">{{ trialSelectedUser.name || 'No name' }}</h4>
            <p class="text-sm text-base-content/70">{{ trialSelectedUser.email }}</p>
            <p class="text-sm">
              <span class="font-medium">Current plan:</span>
              <span class="badge ml-2" :class="{
                'badge-neutral': trialSelectedUser.planType === 'free',
                'badge-primary': trialSelectedUser.planType === 'pro'
              }">
                {{ trialSelectedUser.planType }}
              </span>
            </p>

            <!-- Trial Status -->
            <div v-if="trialStatus" class="mt-4 space-y-2">
              <div class="divider text-sm">Trial Status</div>
              
              <div v-if="trialStatus.hasUsedTrial">
                <div class="alert" :class="trialStatus.isActive ? 'alert-info' : 'alert-warning'">
                  <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <div class="font-bold">
                      {{ trialStatus.isActive ? 'Trial Active' : 'Trial Used' }}
                    </div>
                    <div class="text-sm">
                      <div v-if="trialStatus.isActive">
                        {{ trialStatus.trial.daysRemaining }} days remaining
                      </div>
                      <div v-if="trialStatus.trial.activatedBy">
                        Activated by: {{ trialStatus.trial.activatedBy }}
                      </div>
                      <div>Started: {{ new Date(trialStatus.trial.started).toLocaleDateString() }}</div>
                      <div>Ends: {{ new Date(trialStatus.trial.ends).toLocaleDateString() }}</div>
                    </div>
                  </div>
                </div>

                <!-- Trial Actions -->
                <div class="flex gap-2 mt-4">
                  <button
                    v-if="trialStatus.isActive"
                    @click="endTrial"
                    :disabled="trialProcessing"
                    class="btn btn-error btn-sm"
                  >
                    <span v-if="trialProcessing" class="loading loading-spinner loading-xs"></span>
                    <span v-else>End Trial</span>
                  </button>
                  <button
                    @click="resetTrial"
                    :disabled="trialProcessing"
                    class="btn btn-warning btn-sm"
                  >
                    <span v-if="trialProcessing" class="loading loading-spinner loading-xs"></span>
                    <span v-else>Reset Trial</span>
                  </button>
                </div>
              </div>

              <div v-else class="alert alert-success">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <div class="font-bold">Trial Available</div>
                  <div class="text-sm">This user can start a 7-day Pro trial</div>
                </div>
              </div>

              <!-- Start Trial Button -->
              <div v-if="!trialStatus.hasUsedTrial" class="mt-4 space-y-3">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Trial duration (days)</span>
                  </label>
                  <input
                    type="number"
                    v-model="trialDays"
                    min="1"
                    max="365"
                    class="input input-bordered w-24"
                    :disabled="trialProcessing"
                  />
                </div>
                <button
                  @click="startTrial"
                  :disabled="trialProcessing"
                  class="btn btn-primary"
                >
                  <span v-if="trialProcessing" class="loading loading-spinner loading-sm"></span>
                  <span v-else>Start {{ trialDays }}-Day Trial</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-action">
        <button class="btn" @click="showTrialManager = false">Close</button>
      </div>
    </div>
  </div>

  <!-- Unified User Management Modal -->
  <div v-if="showUnifiedUserManager" class="modal modal-open">
    <div class="modal-box max-w-4xl">
      <h3 class="font-bold text-lg mb-4">User Management</h3>
      
      <!-- User Lookup Section -->
      <div class="space-y-4">
        <div>
          <label class="label">
            <span class="label-text">User email address</span>
          </label>
          <div class="join w-full">
            <input
              type="email"
              placeholder="Enter user email"
              class="input input-bordered join-item flex-1"
              v-model="unifiedUserEmail"
              @keyup.enter="lookupUnifiedUser"
            />
            <button 
              class="btn join-item" 
              @click="lookupUnifiedUser"
              :disabled="unifiedLookingUpUser || !unifiedUserEmail"
            >
              <span v-if="unifiedLookingUpUser" class="loading loading-spinner loading-sm"></span>
              <span v-else>Search</span>
            </button>
          </div>
        </div>

        <!-- User Info Card -->
        <div v-if="unifiedSelectedUser" class="card bg-base-200">
          <div class="card-body p-4">
            <h4 class="font-semibold">{{ unifiedSelectedUser.name || 'No name' }}</h4>
            <p class="text-sm text-base-content/70">{{ unifiedSelectedUser.email }}</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mt-3">
              <div>
                <span class="font-medium">Current plan:</span>
                <span class="badge ml-2" :class="{
                  'badge-neutral': unifiedSelectedUser.planType === 'free',
                  'badge-primary': unifiedSelectedUser.planType === 'pro'
                }">
                  {{ unifiedSelectedUser.planType }}
                </span>
              </div>
              <div v-if="unifiedSelectedUser.subscription">
                <span class="font-medium">Subscription status:</span>
                <span class="badge badge-success ml-2">
                  {{ unifiedSelectedUser.subscription.status }}
                </span>
              </div>
              <div v-if="unifiedSelectedUser.subscription">
                <span class="font-medium">Next payment:</span>
                <span class="ml-2">{{ formatDate(unifiedSelectedUser.subscription.nextPaymentDate) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Tabs -->
        <div v-if="unifiedSelectedUser" class="w-full">
          <div role="tablist" class="tabs tabs-lifted">
            <!-- Subscription Tab -->
            <input type="radio" name="unified_tabs" role="tab" class="tab" aria-label="Subscription" checked />
            <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
              <h4 class="font-semibold mb-4">Subscription Management</h4>
              
              <!-- Upgrade Form (for Free users) -->
              <div v-if="unifiedSelectedUser.planType === 'free'" class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Renewal date</span>
                  </label>
                  <input 
                    v-model="unifiedRenewalDate" 
                    type="date" 
                    :min="getTomorrowDate()"
                    class="input input-bordered w-full max-w-xs"
                    :disabled="unifiedProcessing"
                  />
                  <div class="label">
                    <span class="label-text-alt text-xs">User will get renewal notifications 3 days before this date</span>
                  </div>
                </div>
                
                <button 
                  @click="manageUnifiedSubscription('upgrade')"
                  :disabled="unifiedProcessing || !unifiedRenewalDate"
                  class="btn btn-success"
                >
                  <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
                  <span v-else>Upgrade to Pro</span>
                </button>
              </div>

              <!-- Downgrade Action (for Pro users) -->
              <div v-if="unifiedSelectedUser.planType === 'pro'" class="space-y-4">
                <button 
                  @click="manageUnifiedSubscription('downgrade')"
                  :disabled="unifiedProcessing"
                  class="btn btn-warning"
                >
                  <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
                  <span v-else>Downgrade to Free</span>
                </button>

                <div class="alert alert-warning">
                  <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <span class="text-sm">Downgrading will preserve user data within free plan limits (2 domains, basic features).</span>
                </div>
              </div>
            </div>

            <!-- Trial Tab -->
            <input type="radio" name="unified_tabs" role="tab" class="tab" aria-label="Trial" />
            <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
              <h4 class="font-semibold mb-4">Trial Management</h4>

              <!-- Trial Status -->
              <div v-if="unifiedTrialStatus" class="space-y-4">
                <div v-if="unifiedTrialStatus.hasUsedTrial">
                  <div class="alert" :class="unifiedTrialStatus.isActive ? 'alert-info' : 'alert-warning'">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <div class="font-bold">
                        {{ unifiedTrialStatus.isActive ? 'Trial Active' : 'Trial Used' }}
                      </div>
                      <div class="text-sm">
                        <div v-if="unifiedTrialStatus.isActive">
                          {{ unifiedTrialStatus.trial.daysRemaining }} days remaining
                        </div>
                        <div v-if="unifiedTrialStatus.trial.activatedBy">
                          Activated by: {{ unifiedTrialStatus.trial.activatedBy }}
                        </div>
                        <div>Started: {{ new Date(unifiedTrialStatus.trial.started).toLocaleDateString() }}</div>
                        <div>Ends: {{ new Date(unifiedTrialStatus.trial.ends).toLocaleDateString() }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- Trial Actions -->
                  <div class="flex gap-2">
                    <button
                      v-if="unifiedTrialStatus.isActive"
                      @click="endUnifiedTrial"
                      :disabled="unifiedProcessing"
                      class="btn btn-error btn-sm"
                    >
                      <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
                      <span v-else>End Trial</span>
                    </button>
                    <button
                      @click="resetUnifiedTrial"
                      :disabled="unifiedProcessing"
                      class="btn btn-warning btn-sm"
                    >
                      <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
                      <span v-else>Reset Trial</span>
                    </button>
                  </div>
                </div>

                <div v-else class="space-y-4">
                  <div class="alert alert-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <div class="font-bold">Trial Available</div>
                      <div class="text-sm">This user can start a Pro trial</div>
                    </div>
                  </div>

                  <!-- Start Trial Form -->
                  <div class="space-y-3">
                    <div class="form-control">
                      <label class="label">
                        <span class="label-text">Trial duration (days)</span>
                      </label>
                      <input
                        type="number"
                        v-model="unifiedTrialDays"
                        min="1"
                        max="365"
                        class="input input-bordered w-24"
                        :disabled="unifiedProcessing"
                      />
                    </div>
                    <button
                      @click="startUnifiedTrial"
                      :disabled="unifiedProcessing"
                      class="btn btn-primary"
                    >
                      <span v-if="unifiedProcessing" class="loading loading-spinner loading-sm"></span>
                      <span v-else>Start {{ unifiedTrialDays }}-Day Trial</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions Tab -->
            <input type="radio" name="unified_tabs" role="tab" class="tab" aria-label="Actions" />
            <div role="tabpanel" class="tab-content bg-base-100 border-base-300 rounded-box p-6">
              <h4 class="font-semibold mb-4">Admin actions</h4>
              
              <div class="space-y-4">
                <button 
                  @click="impersonateUnifiedUser"
                  :disabled="unifiedProcessing"
                  class="btn btn-secondary"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
                  <span v-else>Impersonate user</span>
                </button>

                <div class="text-sm text-base-content/60">
                  <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Impersonation allows you to see the application as this user would see it.
                </div>

                <div class="divider"></div>

                <button 
                  @click="openDeleteUserDialog"
                  :disabled="unifiedProcessing || !unifiedSelectedUser"
                  class="btn btn-error"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M10 3h4a1 1 0 011 1v2H9V4a1 1 0 011-1z" />
                  </svg>
                  <span>Delete user account</span>
                </button>

                <div class="text-sm text-base-content/60">
                  <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  This will remove the user from production immediately.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-action">
        <button class="btn" @click="closeUnifiedUserManager">Close</button>
      </div>
    </div>
  </div>

  <!-- Admin Delete User Modal -->
  <div v-if="showDeleteUserDialog" class="modal modal-open">
    <div class="modal-box max-w-lg">
      <h3 class="font-bold text-lg mb-2">Delete user account</h3>
      <p class="text-sm text-base-content/70 mb-3">
        This will remove this user’s data from production immediately. Data may remain in daily backups for up to 7 days and will be purged automatically. If the user previously used paid features, financial transaction data will be retained for 7 years per Dutch tax regulations.
      </p>
      <div class="form-control mb-3">
        <label class="label"><span class="label-text">Type DELETE to confirm</span></label>
        <input v-model="adminDeleteConfirm" type="text" class="input input-bordered w-full" placeholder="DELETE" />
      </div>
      <div class="form-control mb-4">
        <label class="label"><span class="label-text">Reason (optional)</span></label>
        <textarea v-model="adminDeleteReason" class="textarea textarea-bordered" rows="3" placeholder="Add notes to store in audit log (optional)"></textarea>
      </div>
      <div class="modal-action">
        <button class="btn" @click="closeDeleteUserDialog">Cancel</button>
        <button class="btn btn-error" :disabled="adminDeleteConfirm !== 'DELETE' || unifiedProcessing" @click="confirmAdminDeleteUser">
          <span v-if="unifiedProcessing" class="loading loading-spinner loading-xs"></span>
          <span v-else>Delete</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AdminAuditsTab from './AdminAuditsTab.vue'
import AdminSecurityTab from './AdminSecurityTab.vue'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '../../utils/api'

interface Payment {
  id: string
  amount: number
  currency: string
  description: string
  status: string
  createdAt: string
  user?: { email: string }
  invoice?: { id: string }
}

interface User {
  id: string
  email: string
  name: string | null
  planType: string
  subscription?: {
    id: string
    status: string
    nextPaymentDate: string
    mollieId: string | null
  }
}

const canAdmin = ref(false)
const recentPayments = ref<Payment[]>([])
const loadingPayments = ref(false)
const showGenerateInvoice = ref(false)
const manualPaymentId = ref('')
const generatingManual = ref(false)
const generatingInvoices = ref(new Set<string>())
const lastResult = ref<{ success: boolean; message: string } | null>(null)

// User subscription management
const showUserLookup = ref(false)
const userEmail = ref('')
const selectedUser = ref<User | null>(null)
const lookingUpUser = ref(false)
const processing = ref(false)
const renewalDate = ref('')

// Trial management
const showTrialManager = ref(false)
const trialUserEmail = ref('')
const trialSelectedUser = ref<User | null>(null)
const lookingUpTrialUser = ref(false)
const trialProcessing = ref(false)
const trialStatus = ref<any>(null)
const trialDays = ref(7)

// Unified user management modal
const showUnifiedUserManager = ref(false)
const unifiedUserEmail = ref('')
const unifiedSelectedUser = ref<User | null>(null)
const unifiedLookingUpUser = ref(false)
const unifiedProcessing = ref(false)
const unifiedRenewalDate = ref('')
const unifiedTrialStatus = ref<any>(null)
const unifiedTrialDays = ref(7)

// Admin delete user dialog state
const showDeleteUserDialog = ref(false)
const adminDeleteConfirm = ref('')
const adminDeleteReason = ref('')

// Users table
const allUsers = ref<(User & { createdAt: string, trialStartedAt?: string, trialEndsAt?: string })[]>([])
const loadingUsers = ref(false)

// Audits & Security state
const audits = ref<any[]>([])
const auditsLoading = ref(false)
const auditSummary = ref<any>({ totalLogs: 0, dateRange: null, actionCounts: {}, recentActivity: [] })

const securityLoading = ref(false)
const securityEvents = ref<any[]>([])
const securityCounts = ref<{ rateLimit: number; unauthorized: number; csp: number }>({ rateLimit: 0, unauthorized: 0, csp: 0 })

// Router instance for SPA navigation
const router = useRouter()

const filters = ref<{ start: string; end: string; category: string; success: string; userEmail: string; actions: string; resourceType: string; ipContains: string; apiKeySuffix: string }>({
  start: '', end: '', category: '', success: '', userEmail: '', actions: '', resourceType: '', ipContains: '', apiKeySuffix: ''
})

const buildAuditQuery = () => {
  const params = new URLSearchParams()
  if (filters.value.start) params.set('startDate', new Date(filters.value.start).toISOString())
  if (filters.value.end) params.set('endDate', new Date(filters.value.end).toISOString())
  if (filters.value.category) params.set('categories', filters.value.category)
  if (filters.value.success) params.set('success', filters.value.success)
  if (filters.value.actions) params.set('actions', filters.value.actions)
  if (filters.value.resourceType) params.set('resourceType', filters.value.resourceType)
  if (filters.value.ipContains) params.set('ipContains', filters.value.ipContains)
  if (filters.value.apiKeySuffix) params.set('apiKeySuffix', filters.value.apiKeySuffix)
  if (filters.value.userEmail) params.set('userEmail', filters.value.userEmail)
  params.set('limit', '50')
  return params.toString()
}

const applyAuditFilters = () => loadAudits()
const resetAuditFilters = () => {
  filters.value = { start: '', end: '', category: '', success: '', userEmail: '', actions: '', resourceType: '', ipContains: '', apiKeySuffix: '' }
  loadAudits()
}

const loadAudits = async () => {
  auditsLoading.value = true
  try {
    const q = buildAuditQuery()
    // If admin, use admin endpoint to fetch across all users, else use user-scoped endpoint
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const url = isAdmin ? `/api/admin/audit-logs?${q}` : `/api/audit-logs?${q}`
    const data = await api.get(url)
    audits.value = data.logs || []
    const summary = await api.get('/api/audit-logs/summary')
    auditSummary.value = summary
  } catch (e) {
    console.error('Failed to load audits', e)
  } finally {
    auditsLoading.value = false
  }
}

const exportAudits = async () => {
  // legacy 30-day export without filters
  try {
    const end = new Date()
    const start = new Date()
    start.setDate(end.getDate() - 30)
    const res = await api.getRaw(`/api/audit-logs/export?startDate=${start.toISOString()}&endDate=${end.toISOString()}&format=json`)
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${start.toISOString().slice(0,10)}-to-${end.toISOString().slice(0,10)}.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (e) {
    console.error('Failed to export audit logs', e)
  }
}

const exportAuditsFiltered = async () => {
  try {
    const end = filters.value.end ? new Date(filters.value.end) : new Date()
    const start = filters.value.start ? new Date(filters.value.start) : new Date(new Date().getTime() - 30*24*60*60*1000)
    const params = new URLSearchParams(buildAuditQuery())
    params.set('startDate', start.toISOString())
    params.set('endDate', end.toISOString())
    params.set('format', 'json')
    const res = await api.getRaw(`/api/audit-logs/export?${params.toString()}`)
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${start.toISOString().slice(0,10)}-to-${end.toISOString().slice(0,10)}.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (e) {
    console.error('Failed to export filtered audit logs', e)
  }
}

const loadSecurity = async () => {
  securityLoading.value = true
  try {
    // reuse audit logs, filter client-side for security.* and CSP
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const data = await api.get(isAdmin ? '/api/admin/audit-logs?limit=100' : '/api/audit-logs?limit=100')
    const logs = (data.logs || []) as any[]
    const sec = logs.filter(l => String(l.action || '').startsWith('security.') || l.resourceType === 'csp')
    securityEvents.value = sec.slice(0, 50)

    // counts
    securityCounts.value = {
      rateLimit: sec.filter(l => l.action === 'security.rate_limit_exceeded').length,
      unauthorized: sec.filter(l => l.action === 'security.unauthorized_access').length,
      csp: sec.filter(l => l.resourceType === 'csp').length
    }
  } catch (e) {
    console.error('Failed to load security events', e)
  } finally {
    securityLoading.value = false
  }
}

const summarizeSecurityMetadata = (meta: any) => {
  if (!meta) return ''
  const vd = meta['violated-directive']
  const bu = meta['blocked-uri']
  if (vd || bu) return `CSP: ${vd || ''} ${bu ? '→ ' + bu : ''}`.trim()
  if (meta.endpoint) return `${meta.method || ''} ${meta.endpoint}`.trim()
  return ''
}

// Display helpers for audits table
const formatAuditTimestamp = (log: any) => {
  const ts = log.createdAt || log.timestamp || log.metadata?.timestamp
  try {
    return ts ? new Date(ts).toLocaleString() : '-'
  } catch {
    return '-'
  }
}
const displayAuditUser = (log: any) => log?.metadata?.userEmail || log?.metadata?.userId || '-'
const displayAuditAction = (log: any) => log?.action || log?.metadata?.action || '-' 
const displayAuditResource = (log: any) => log?.resourceType || log?.metadata?.resourceType || '-'
const displayAuditIp = (log: any) => log?.ipAddress || log?.metadata?.ip || '-'

const openDeleteUserDialog = () => {
  adminDeleteConfirm.value = ''
  adminDeleteReason.value = ''
  showDeleteUserDialog.value = true
}
const closeDeleteUserDialog = () => {
  showDeleteUserDialog.value = false
}

const confirmAdminDeleteUser = async () => {
  if (!unifiedSelectedUser.value || adminDeleteConfirm.value !== 'DELETE') return
  unifiedProcessing.value = true
  const targetEmail = unifiedSelectedUser.value.email
  const targetId = unifiedSelectedUser.value.id
  try {
    await api.post(`/api/admin/users/${targetId}/delete`, {
      reason: adminDeleteReason.value || undefined,
      notify: true
    }, {
      toast: { showErrors: false }
    })
    lastResult.value = { success: true, message: `Deleted ${targetEmail}` }
    showDeleteUserDialog.value = false
  } catch (e: any) {
    // Suppress noisy toast already disabled; check if user is actually gone after refresh
    lastResult.value = { success: false, message: e?.message || 'Failed to delete user' }
  } finally {
    // Always refresh lists to reflect current state
    try { await loadAllUsers() } catch {}
    unifiedProcessing.value = false
  }
}

const checkAdminStatus = async () => {
  try {
    const data = await api.get('/api/auth-status')
    canAdmin.value = data?.user?.role === 'admin'
  } catch (error) {
    console.error('Failed to check admin status:', error)
  }
}


const loadRecentPayments = async () => {
  loadingPayments.value = true
  try {
    // This would need a new API endpoint - for now just show the structure
    await new Promise(resolve => setTimeout(resolve, 1000))
    recentPayments.value = []
    lastResult.value = { success: false, message: 'Recent payments API not implemented yet - would show recent payments with invoice status' }
  } catch (error) {
    console.error('Failed to load recent payments:', error)
    lastResult.value = { success: false, message: 'Failed to load recent payments' }
  } finally {
    loadingPayments.value = false
  }
}

const generateManualInvoice = async () => {
  if (!manualPaymentId.value) return
  
  generatingManual.value = true
  try {
    const data = await api.post(`/api/invoices/generate/${manualPaymentId.value}`)
    
    if (data.success) {
      lastResult.value = { success: true, message: `Invoice generated successfully: ${data.invoiceId}` }
      showGenerateInvoice.value = false
      manualPaymentId.value = ''
    } else {
      lastResult.value = { success: false, message: data.message || 'Failed to generate invoice' }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to generate invoice' }
  } finally {
    generatingManual.value = false
  }
}

const generateInvoiceForPayment = async (paymentId: string) => {
  generatingInvoices.value.add(paymentId)
  try {
    const data = await api.post(`/api/invoices/generate/${paymentId}`)
    
    if (data.success) {
      lastResult.value = { success: true, message: `Invoice generated for payment ${paymentId}` }
      // Reload payments to update invoice status
      await loadRecentPayments()
    } else {
      lastResult.value = { success: false, message: data.message || 'Failed to generate invoice' }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to generate invoice' }
  } finally {
    generatingInvoices.value.delete(paymentId)
  }
}

const getStatusBadge = (status: string) => {
  switch (status.toUpperCase()) {
    case 'PAID': return 'badge-success'
    case 'PENDING': return 'badge-warning'
    case 'FAILED': return 'badge-error'
    default: return 'badge-neutral'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Trial management functions
const lookupTrialUser = async () => {
  if (!trialUserEmail.value) return
  
  lookingUpTrialUser.value = true
  try {
    const userData = await api.get(`/api/admin/users/lookup?email=${encodeURIComponent(trialUserEmail.value)}`)
    trialSelectedUser.value = userData.user
    
    if (!trialSelectedUser.value) {
      lastResult.value = { success: false, message: 'User not found' }
    } else {
      // Get trial status
      const trialData = await api.get(`/api/admin/trials/status/${trialSelectedUser.value.id}`)
      trialStatus.value = trialData
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to lookup user' }
  } finally {
    lookingUpTrialUser.value = false
  }
}

const startTrial = async () => {
  if (!trialSelectedUser.value) return
  
  trialProcessing.value = true
  try {
    await api.post('/api/admin/trials/start', {
      userId: trialSelectedUser.value.id,
      days: trialDays.value
    })
    
    lastResult.value = { success: true, message: `Trial started successfully for ${trialDays.value} days!` }
    
    // Refresh trial status
    await lookupTrialUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to start trial' }
  } finally {
    trialProcessing.value = false
  }
}

const endTrial = async () => {
  if (!trialSelectedUser.value) return
  
  trialProcessing.value = true
  try {
    await api.post('/api/admin/trials/end', {
      userId: trialSelectedUser.value.id
    })
    
    lastResult.value = { success: true, message: 'Trial ended successfully!' }
    
    // Refresh trial status
    await lookupTrialUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to end trial' }
  } finally {
    trialProcessing.value = false
  }
}

const resetTrial = async () => {
  if (!trialSelectedUser.value) return
  
  trialProcessing.value = true
  try {
    await api.post('/api/admin/trials/reset', {
      userId: trialSelectedUser.value.id
    })
    
    lastResult.value = { success: true, message: 'Trial eligibility reset successfully!' }
    
    // Refresh trial status
    await lookupTrialUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to reset trial' }
  } finally {
    trialProcessing.value = false
  }
}

// User subscription management methods
const lookupUser = async () => {
  if (!userEmail.value) return
  
  lookingUpUser.value = true
  try {
    const data = await api.get(`/api/admin/users/lookup?email=${encodeURIComponent(userEmail.value)}`)
    selectedUser.value = data.user
    
    if (!selectedUser.value) {
      lastResult.value = { success: false, message: 'User not found' }
    } else if (selectedUser.value.planType === 'free' && !renewalDate.value) {
      // Set default renewal date for free users
      renewalDate.value = getDefaultRenewalDate()
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to lookup user' }
    selectedUser.value = null
  } finally {
    lookingUpUser.value = false
  }
}

const manageSubscription = async (action: 'upgrade' | 'downgrade') => {
  if (!selectedUser.value) return
  if (action === 'upgrade' && !renewalDate.value) return
  
  processing.value = true
  try {
    const endpoint = action === 'upgrade' 
      ? `/api/admin/users/${selectedUser.value.id}/upgrade`
      : `/api/admin/users/${selectedUser.value.id}/downgrade`
    
    // Include renewal date for upgrades
    const payload = action === 'upgrade' ? { renewalDate: renewalDate.value } : {}
    
    const data = await api.post(endpoint, payload)
    
    if (data.success) {
      const renewalInfo = action === 'upgrade' && renewalDate.value 
        ? ` (renewal due: ${new Date(renewalDate.value).toLocaleDateString()})`
        : ''
      
      lastResult.value = { 
        success: true, 
        message: `Successfully ${action}d ${selectedUser.value.email} ${action === 'upgrade' ? 'to Pro' : 'to Free'}${renewalInfo}`
      }
      
      // Refresh user data
      await lookupUser()
    } else {
      lastResult.value = { success: false, message: data.message || `Failed to ${action} user` }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || `Failed to ${action} user` }
  } finally {
    processing.value = false
  }
}

const closeUserLookup = () => {
  showUserLookup.value = false
  userEmail.value = ''
  selectedUser.value = null
  processing.value = false
  renewalDate.value = ''
}

const getTomorrowDate = () => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
}

const getDefaultRenewalDate = () => {
  const oneMonthFromNow = new Date()
  oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1)
  return oneMonthFromNow.toISOString().split('T')[0]
}

// Unified user management functions
const lookupUnifiedUser = async () => {
  if (!unifiedUserEmail.value) return
  
  unifiedLookingUpUser.value = true
  try {
    const userData = await api.get(`/api/admin/users/lookup?email=${encodeURIComponent(unifiedUserEmail.value)}`)
    unifiedSelectedUser.value = userData.user
    
    if (!unifiedSelectedUser.value) {
      lastResult.value = { success: false, message: 'User not found' }
    } else {
      // Get trial status for unified modal
      const trialData = await api.get(`/api/admin/trials/status/${unifiedSelectedUser.value.id}`)
      unifiedTrialStatus.value = trialData
      
      // Set default renewal date for free users
      if (unifiedSelectedUser.value.planType === 'free' && !unifiedRenewalDate.value) {
        unifiedRenewalDate.value = getDefaultRenewalDate()
      }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to lookup user' }
    unifiedSelectedUser.value = null
  } finally {
    unifiedLookingUpUser.value = false
  }
}

const manageUnifiedSubscription = async (action: 'upgrade' | 'downgrade') => {
  if (!unifiedSelectedUser.value) return
  if (action === 'upgrade' && !unifiedRenewalDate.value) return
  
  unifiedProcessing.value = true
  try {
    const endpoint = action === 'upgrade' 
      ? `/api/admin/users/${unifiedSelectedUser.value.id}/upgrade`
      : `/api/admin/users/${unifiedSelectedUser.value.id}/downgrade`
    
    // Include renewal date for upgrades
    const payload = action === 'upgrade' ? { renewalDate: unifiedRenewalDate.value } : {}
    
    const data = await api.post(endpoint, payload)
    
    if (data.success) {
      const renewalInfo = action === 'upgrade' && unifiedRenewalDate.value 
        ? ` (renewal due: ${new Date(unifiedRenewalDate.value).toLocaleDateString()})`
        : ''
      
      lastResult.value = { 
        success: true, 
        message: `Successfully ${action}d ${unifiedSelectedUser.value.email} ${action === 'upgrade' ? 'to Pro' : 'to Free'}${renewalInfo}`
      }
      
      // Refresh user data
      await lookupUnifiedUser()
    } else {
      lastResult.value = { success: false, message: data.message || `Failed to ${action} user` }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || `Failed to ${action} user` }
  } finally {
    unifiedProcessing.value = false
  }
}

const startUnifiedTrial = async () => {
  if (!unifiedSelectedUser.value) return
  
  unifiedProcessing.value = true
  try {
    await api.post('/api/admin/trials/start', {
      userId: unifiedSelectedUser.value.id,
      days: unifiedTrialDays.value
    })
    
    lastResult.value = { success: true, message: `Trial started successfully for ${unifiedTrialDays.value} days!` }
    
    // Refresh trial status
    await lookupUnifiedUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to start trial' }
  } finally {
    unifiedProcessing.value = false
  }
}

const endUnifiedTrial = async () => {
  if (!unifiedSelectedUser.value) return
  
  unifiedProcessing.value = true
  try {
    await api.post('/api/admin/trials/end', {
      userId: unifiedSelectedUser.value.id
    })
    
    lastResult.value = { success: true, message: 'Trial ended successfully!' }
    
    // Refresh trial status
    await lookupUnifiedUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to end trial' }
  } finally {
    unifiedProcessing.value = false
  }
}

const resetUnifiedTrial = async () => {
  if (!unifiedSelectedUser.value) return
  
  unifiedProcessing.value = true
  try {
    await api.post('/api/admin/trials/reset', {
      userId: unifiedSelectedUser.value.id
    })
    
    lastResult.value = { success: true, message: 'Trial eligibility reset successfully!' }
    
    // Refresh trial status
    await lookupUnifiedUser()
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to reset trial' }
  } finally {
    unifiedProcessing.value = false
  }
}

const impersonateUnifiedUser = async () => {
  if (!unifiedSelectedUser.value) return
  
  unifiedProcessing.value = true
  try {
    // Use BetterAuth admin impersonation endpoint
    const response = await fetch('/api/auth/admin/impersonate-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        userId: unifiedSelectedUser.value.id
      })
    })
    
    if (response.ok) {
      // BetterAuth might return empty response on success
      let data = null
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json()
        } catch (e) {
          // Empty or invalid JSON response is OK for successful impersonation
        }
      }
      
      lastResult.value = { success: true, message: `Successfully started impersonating ${unifiedSelectedUser.value.email}` }
      
      // Navigate to domains page using Vue Router (SPA navigation)
      setTimeout(() => {
        router.push('/domains')
      }, 1000)
    } else {
      let errorMessage = 'Failed to start impersonation'
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        try {
          const error = await response.json()
          errorMessage = error.message || errorMessage
        } catch (e) {
          // Fallback to status text if JSON parsing fails
          errorMessage = response.statusText || errorMessage
        }
      } else {
        errorMessage = response.statusText || errorMessage
      }
      
      lastResult.value = { success: false, message: errorMessage }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to start impersonation' }
  } finally {
    unifiedProcessing.value = false
  }
}

const closeUnifiedUserManager = () => {
  showUnifiedUserManager.value = false
  unifiedUserEmail.value = ''
  unifiedSelectedUser.value = null
  unifiedTrialStatus.value = null
  unifiedProcessing.value = false
  unifiedRenewalDate.value = ''
  unifiedTrialDays.value = 7
}

// Users table functions
const loadAllUsers = async () => {
  loadingUsers.value = true
  try {
    const data = await api.get('/api/admin/users/all')
    allUsers.value = data.users || []
  } catch (error: any) {
    console.error('Failed to load users:', error)
    lastResult.value = { success: false, message: error.message || 'Failed to load users' }
  } finally {
    loadingUsers.value = false
  }
}

const isTrialActive = (user: any) => {
  if (!user.trialStartedAt || !user.trialEndsAt) return false
  const now = new Date()
  const trialEnds = new Date(user.trialEndsAt)
  return user.planType === 'pro' && trialEnds > now
}

const openUserManagementModal = (user: any) => {
  // Pre-fill the unified modal with user data
  unifiedUserEmail.value = user.email
  unifiedSelectedUser.value = user
  unifiedTrialStatus.value = null // Will be loaded when modal opens
  unifiedTrialDays.value = 7
  if (user.planType === 'free') {
    unifiedRenewalDate.value = getDefaultRenewalDate()
  }
  showUnifiedUserManager.value = true
  
  // Load trial status for the unified modal
  setTimeout(async () => {
    try {
      const trialData = await api.get(`/api/admin/trials/status/${user.id}`)
      unifiedTrialStatus.value = trialData
    } catch (error) {
      console.error('Failed to load trial status:', error)
    }
  }, 100)
}

const impersonateUser = async (user: any) => {
  try {
    // Use BetterAuth admin impersonation endpoint
    const response = await fetch('/api/auth/admin/impersonate-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        userId: user.id
      })
    })
    
    if (response.ok) {
      // BetterAuth might return empty response on success
      let data = null
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json()
        } catch (e) {
          // Empty or invalid JSON response is OK for successful impersonation
        }
      }
      
      lastResult.value = { success: true, message: `Successfully started impersonating ${user.email}` }
      
      // Navigate to domains page using Vue Router (SPA navigation)
      setTimeout(() => {
        router.push('/domains')
      }, 1000)
    } else {
      let errorMessage = 'Failed to start impersonation'
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        try {
          const error = await response.json()
          errorMessage = error.message || errorMessage
        } catch (e) {
          // Fallback to status text if JSON parsing fails
          errorMessage = response.statusText || errorMessage
        }
      } else {
        errorMessage = response.statusText || errorMessage
      }
      
      lastResult.value = { success: false, message: errorMessage }
    }
  } catch (error: any) {
    lastResult.value = { success: false, message: error.message || 'Failed to start impersonation' }
  }
}

onMounted(() => {
  // initial loads
  loadAudits()
  loadSecurity()
  checkAdminStatus()
  loadAllUsers()
})
const schedulerLoading = ref(false);
const runningLifecycle = ref(false);
const runningHeartbeat = ref(false);
const runningRenewalNotifications = ref(false);
const lastLifecycleRun = ref<string | null>(null);
const lastRenewalNotificationRun = ref<string | null>(null);


async function runSubscriptionLifecycle() {
  try {
    runningLifecycle.value = true;
    const res = await api.post('/api/admin/subscriptions/run-lifecycle');
    if (res?.success) {
      lastLifecycleRun.value = new Date().toLocaleString();
    }
  } catch (e) {
    // noop: toast surfaces via global handler
  } finally {
    runningLifecycle.value = false;
  }
}

async function runRenewalNotifications() {
  try {
    runningRenewalNotifications.value = true;
    const res = await api.post('/api/admin/subscriptions/run-renewal-notifications');
    if (res?.success) {
      lastRenewalNotificationRun.value = new Date().toLocaleString();
    }
  } catch (e) {
    // noop: toast surfaces via global handler
  } finally {
    runningRenewalNotifications.value = false;
  }
}

async function runHeartbeat() {
  try {
    runningHeartbeat.value = true;
    await api.post('/health/heartbeat');
  } catch (e) {
    // noop
  } finally {
    runningHeartbeat.value = false;
  }
}

async function refreshSchedulerStatus() {
  try {
    schedulerLoading.value = true;
    await api.get('/health/scheduler');
  } catch (e) {
    // noop
  } finally {
    schedulerLoading.value = false;
  }
}
</script>

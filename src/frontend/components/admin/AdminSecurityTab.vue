<template>
  <div>
    <div class="mb-6">
      <h2 class="text-2xl font-bold">Security overview</h2>
      <p class="text-sm text-base-content/70">Key security signals aggregated from audit logs</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="stats shadow">
        <div class="stat">
          <div class="stat-title">Rate limit violations</div>
          <div class="stat-value text-warning">{{ securityCounts.rateLimit }}</div>
        </div>
      </div>
      <div class="stats shadow">
        <div class="stat">
          <div class="stat-title">Unauthorized access</div>
          <div class="stat-value text-error">{{ securityCounts.unauthorized }}</div>
        </div>
      </div>
      <div class="stats shadow">
        <div class="stat">
          <div class="stat-title">CSP violations (recent)</div>
          <div class="stat-value text-accent">{{ securityCounts.csp }}</div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-lg font-semibold mb-2">Recent security events</h3>
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>Action</th>
              <th>Details</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="securityLoading">
              <td colspan="3" class="text-center py-8">
                <span class="loading loading-spinner loading-md"></span>
                Loading security events...
              </td>
            </tr>
            <tr v-else-if="securityEvents.length === 0">
              <td colspan="3" class="text-center py-8 text-base-content/60">No recent security events</td>
            </tr>
            <tr v-else v-for="e in securityEvents" :key="e.id">
              <td class="text-sm">{{ new Date(e.createdAt || e.timestamp).toLocaleString() }}</td>
              <td class="text-sm font-medium">{{ e.action }}</td>
              <td class="text-sm truncate max-w-[40ch]">
                <code class="text-xs opacity-80">{{ summarizeSecurityMetadata(e.metadata) }}</code>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { api } from '../../utils/api'

const securityLoading = ref(false)
const securityEvents = ref<any[]>([])
const securityCounts = ref<{ rateLimit: number; unauthorized: number; csp: number }>({ rateLimit: 0, unauthorized: 0, csp: 0 })

const loadSecurity = async () => {
  securityLoading.value = true
  try {
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const data = await api.get(isAdmin ? '/api/admin/audit-logs?limit=100' : '/api/audit-logs?limit=100')
    const logs = (data.logs || []) as any[]
    const sec = logs.filter(l => String(l.action || '').startsWith('security.') || l.resourceType === 'csp')
    securityEvents.value = sec.slice(0, 50)

    securityCounts.value = {
      rateLimit: sec.filter(l => l.action === 'security.rate_limit_exceeded').length,
      unauthorized: sec.filter(l => l.action === 'security.unauthorized_access').length,
      csp: sec.filter(l => l.resourceType === 'csp').length
    }
  } catch (e) {
    console.error('Failed to load security events', e)
  } finally {
    securityLoading.value = false
  }
}

const summarizeSecurityMetadata = (meta: any) => {
  if (!meta) return ''
  const vd = meta['violated-directive']
  const bu = meta['blocked-uri']
  if (vd || bu) return `CSP: ${vd || ''} ${bu ? '→ ' + bu : ''}`.trim()
  if (meta.endpoint) return `${meta.method || ''} ${meta.endpoint}`.trim()
  return ''
}

onMounted(loadSecurity)
</script>


<template>
  <div>
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-2xl font-bold">Audit logs</h2>
      <div class="flex gap-2">
        <button class="btn btn-outline btn-sm" @click="loadAudits" :disabled="auditsLoading">
          <span v-if="auditsLoading" class="loading loading-spinner loading-xs"></span>
          Refresh
        </button>
        <button class="btn btn-sm btn-outline" @click="exportAudits">Export (JSON)</button>
        <button class="btn btn-sm btn-outline" @click="exportAuditsFilteredCsv">Export (CSV)</button>
      </div>
    </div>

    <!-- Filter bar -->
    <div class="mb-4 grid grid-cols-1 md:grid-cols-5 gap-3">
      <div>
        <label class="label"><span class="label-text text-xs">Date from</span></label>
        <input type="date" v-model="filters.start" class="input input-bordered input-sm w-full"/>
      </div>
      <div>
        <label class="label"><span class="label-text text-xs">Date to</span></label>
        <input type="date" v-model="filters.end" class="input input-bordered input-sm w-full"/>
      </div>
      <div>
        <label class="label"><span class="label-text text-xs">Category</span></label>
        <select v-model="filters.category" class="select select-bordered select-sm w-full">
          <option value="">All</option>
          <option value="auth">auth</option>
          <option value="data">data</option>
          <option value="billing">billing</option>
          <option value="security">security</option>
          <option value="settings">settings</option>
          <option value="webhook">webhook</option>
        </select>
      </div>
      <div>
        <label class="label"><span class="label-text text-xs">Success</span></label>
        <select v-model="filters.success" class="select select-bordered select-sm w-full">
          <option value="">All</option>
          <option value="true">Success</option>
          <option value="false">Failures</option>
        </select>
      </div>
      <div>
        <label class="label"><span class="label-text text-xs">User email</span></label>
        <input type="email" v-model="filters.userEmail" placeholder="optional" class="input input-bordered input-sm w-full"/>
      </div>
      <div class="md:col-span-5 flex gap-2 mt-1">
        <input type="text" v-model="filters.actions" placeholder="actions (csv, supports *)" class="input input-bordered input-sm flex-1"/>
        <input type="text" v-model="filters.resourceType" placeholder="resourceType" class="input input-bordered input-sm w-44"/>
        <input type="text" v-model="filters.ipContains" placeholder="ip contains" class="input input-bordered input-sm w-44"/>
        <input type="text" v-model="filters.apiKeySuffix" placeholder="api key suffix" class="input input-bordered input-sm w-44"/>
        <button class="btn btn-sm btn-outline" @click="applyAuditFilters">Apply</button>
        <button class="btn btn-sm btn-outline" @click="resetAuditFilters">Reset</button>
        <!-- <button class="btn btn-sm btn-outline" @click="exportAuditsFiltered">Export</button> -->
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
          <tr>
            <th>Timestamp</th>
            <th>User</th>
            <th>Success</th>
            <th>Action</th>
            <th>Details</th>
          </tr>
        </thead>
<tbody>
          <tr v-if="auditsLoading">
            <td colspan="7" class="text-center py-8">
              <span class="loading loading-spinner loading-md"></span>
              Loading audit logs...
            </td>
          </tr>
          <tr v-else-if="audits.length === 0">
            <td colspan="7" class="text-center py-8 text-base-content/60">No audit logs</td>
          </tr>
          <tr v-else v-for="log in audits" :key="log.id">
            <td class="text-sm">{{ formatAuditTimestamp(log) }}</td>
            <td class="text-sm">{{ displayAuditUser(log) }}</td>
            <td class="text-sm">
              <span :class="(log.metadata?.success ?? true) ? 'text-success' : 'text-error'">{{ (log.metadata?.success ?? true) ? 'yes' : 'no' }}</span>
            </td>
            <td class="text-sm font-medium">{{ displayAuditAction(log) }}</td>
            <td class="text-sm">
              <button class="btn btn-primary btn-outline btn-xs" @click="openDetails(log)">Details</button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination controls -->
      <div class="flex flex-col md:flex-row items-center justify-between gap-3 mt-3">
        <div class="text-sm text-base-content/70">
          Showing {{ startItem }}–{{ endItem }} of {{ total }}
        </div>
        <div class="flex items-center gap-2">
          <select class="select select-bordered select-sm" v-model.number="pageSize" @change="handlePageSizeChange">
            <option :value="25">25</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
          </select>
          <div class="join">
            <button class="btn btn-sm join-item" :disabled="page <= 1 || auditsLoading" @click="prevPage">Prev</button>
            <button class="btn btn-sm join-item" :disabled="page >= totalPages || auditsLoading" @click="nextPage">Next</button>
          </div>
          <div class="text-sm text-base-content/70">{{ page }}/{{ totalPages }}</div>
        </div>
      </div>
</div>

    <!-- Details Modal -->
    <div v-if="showDetails" class="modal modal-open">
      <div class="modal-box max-w-3xl">
        <h3 class="font-bold text-lg mb-2">Audit log details</h3>
        <div class="space-y-2 text-sm">
          <div><span class="font-semibold">Timestamp:</span> {{ formatAuditTimestamp(selectedLog) }}</div>
          <div><span class="font-semibold">User:</span> {{ displayAuditUser(selectedLog) }}</div>
          <div><span class="font-semibold">Action:</span> {{ displayAuditAction(selectedLog) }}</div>
          <div v-if="selectedLog?.resourceType"><span class="font-semibold">Resource:</span> {{ selectedLog.resourceType }} {{ selectedLog.resourceId ? `(${selectedLog.resourceId})` : '' }}</div>
          <div v-if="selectedLog?.ipAddress"><span class="font-semibold">IP:</span> {{ selectedLog.ipAddress }}</div>
          <div v-if="selectedLog?.metadata?.domain"><span class="font-semibold">Domain:</span> {{ selectedLog.metadata.domain }}</div>
          <div>
            <span class="font-semibold">Metadata:</span>
            <pre class="bg-base-200 p-2 rounded overflow-x-auto text-xs max-h-80">{{ JSON.stringify(selectedLog?.metadata || {}, null, 2) }}</pre>
          </div>
        </div>
        <div class="modal-action">
          <button class="btn" @click="closeDetails">Close</button>
        </div>
      </div>
      <div class="modal-backdrop" @click="closeDetails"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { api } from '../../utils/api'

const audits = ref<any[]>([])
const auditsLoading = ref(false)

// Pagination state
const page = ref(1)
const pageSize = ref(25)
const total = ref(0)
const totalPages = computed(() => Math.max(1, Math.ceil(total.value / pageSize.value)))
const startItem = computed(() => (total.value === 0 ? 0 : (page.value - 1) * pageSize.value + 1))
const endItem = computed(() => Math.min(total.value, page.value * pageSize.value))

const filters = ref<{ start: string; end: string; category: string; success: string; userEmail: string; actions: string; resourceType: string; ipContains: string; apiKeySuffix: string }>({
  start: '', end: '', category: '', success: '', userEmail: '', actions: '', resourceType: '', ipContains: '', apiKeySuffix: ''
})

const buildAuditQuery = () => {
  const params = new URLSearchParams()
  if (filters.value.start) params.set('startDate', new Date(filters.value.start).toISOString())
  if (filters.value.end) params.set('endDate', new Date(filters.value.end).toISOString())
  if (filters.value.category) params.set('categories', filters.value.category)
  if (filters.value.success) params.set('success', filters.value.success)
  if (filters.value.actions) params.set('actions', filters.value.actions)
  if (filters.value.resourceType) params.set('resourceType', filters.value.resourceType)
  if (filters.value.ipContains) params.set('ipContains', filters.value.ipContains)
  if (filters.value.apiKeySuffix) params.set('apiKeySuffix', filters.value.apiKeySuffix)
  if (filters.value.userEmail) params.set('userEmail', filters.value.userEmail)
  params.set('limit', String(pageSize.value))
  params.set('offset', String((page.value - 1) * pageSize.value))
  return params.toString()
}

const applyAuditFilters = () => { page.value = 1; loadAudits() }
const resetAuditFilters = () => {
  filters.value = { start: '', end: '', category: '', success: '', userEmail: '', actions: '', resourceType: '', ipContains: '', apiKeySuffix: '' }
  page.value = 1
  loadAudits()
}

const loadAudits = async () => {
  auditsLoading.value = true
  try {
    const q = buildAuditQuery()
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const url = isAdmin ? `/api/admin/audit-logs?${q}` : `/api/audit-logs?${q}`
    const data = await api.get(url)

    // Trust server normalization (admin endpoint includes userEmail)
    audits.value = data.logs || []
    total.value = data.total || (data.logs?.length ?? 0)
  } catch (e) {
    console.error('Failed to load audits', e)
    audits.value = []
    total.value = 0
  } finally {
    auditsLoading.value = false
  }
}

const exportAudits = async () => {
  try {
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const end = new Date()
    const start = new Date()
    start.setDate(end.getDate() - 30)
    const base = isAdmin ? '/api/admin/audit-logs/export' : '/api/audit-logs/export'
    const res = await api.getRaw(`${base}?startDate=${start.toISOString()}&endDate=${end.toISOString()}&format=json`)
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${start.toISOString().slice(0,10)}-to-${end.toISOString().slice(0,10)}.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (e) {
    console.error('Failed to export audit logs', e)
  }
}

const exportAuditsFiltered = async () => {
  try {
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const end = filters.value.end ? new Date(filters.value.end) : new Date()
    const start = filters.value.start ? new Date(filters.value.start) : new Date(new Date().getTime() - 30*24*60*60*1000)
    const params = new URLSearchParams(buildAuditQuery())
    params.set('startDate', start.toISOString())
    params.set('endDate', end.toISOString())
    params.set('format', 'json')
    params.delete('limit')
    params.delete('offset')
    const base = isAdmin ? '/api/admin/audit-logs/export' : '/api/audit-logs/export'
    const res = await api.getRaw(`${base}?${params.toString()}`)
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${start.toISOString().slice(0,10)}-to-${end.toISOString().slice(0,10)}.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (e) {
    console.error('Failed to export filtered audit logs', e)
  }
}

const exportAuditsFilteredCsv = async () => {
  try {
    const authStatus = await api.get('/api/auth-status').catch(() => ({ user: null }))
    const isAdmin = authStatus?.user?.role === 'admin'
    const end = filters.value.end ? new Date(filters.value.end) : new Date()
    const start = filters.value.start ? new Date(filters.value.start) : new Date(new Date().getTime() - 30*24*60*60*1000)
    const params = new URLSearchParams(buildAuditQuery())
    params.set('startDate', start.toISOString())
    params.set('endDate', end.toISOString())
    params.set('format', 'csv')
    params.delete('limit')
    params.delete('offset')
    const base = isAdmin ? '/api/admin/audit-logs/export' : '/api/audit-logs/export'
    const res = await api.getRaw(`${base}?${params.toString()}`)
    const blob = await res.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `audit-logs-${start.toISOString().slice(0,10)}-to-${end.toISOString().slice(0,10)}.csv`
    a.click()
    URL.revokeObjectURL(url)
  } catch (e) {
    console.error('Failed to export filtered audit logs (CSV)', e)
  }
}

const summarizeSecurityMetadata = (meta: any) => {
  if (!meta) return ''
  const vd = meta['violated-directive']
  const bu = meta['blocked-uri']
  if (vd || bu) return `CSP: ${vd || ''} ${bu ? '→ ' + bu : ''}`.trim()
  if (meta.endpoint) return `${meta.method || ''} ${meta.endpoint}`.trim()
  return ''
}

const formatAuditTimestamp = (log: any) => {
  const ts = log.createdAt || log.timestamp || log.metadata?.timestamp
  try {
    return ts ? new Date(ts).toLocaleString() : '-'
  } catch {
    return '-'
  }
}
const displayAuditUser = (log: any) => log?.userEmail || log?.metadata?.userEmail || log?.metadata?.userId || '-'
const displayAuditAction = (log: any) => log?.action || log?.metadata?.action || '-'
const displayAuditResource = (log: any) => log?.resourceType || log?.metadata?.resourceType || '-'
const displayAuditIp = (log: any) => log?.ipAddress || log?.metadata?.ip || '-'

const handlePageSizeChange = () => { page.value = 1; loadAudits() }
const prevPage = () => { if (page.value > 1) { page.value -= 1; loadAudits() } }
const nextPage = () => { if (page.value < totalPages.value) { page.value += 1; loadAudits() } }

// Details modal state
const showDetails = ref(false)
const selectedLog = ref<any | null>(null)

const openDetails = (log: any) => {
  selectedLog.value = log
  showDetails.value = true
}
const closeDetails = () => {
  showDetails.value = false
  selectedLog.value = null
}

onMounted(() => {
  loadAudits()
})
</script>


export interface Announcement {
  title: string
  description?: string
  ctaLabel?: string
  ctaUrl?: string
  icon?: string
}

export interface FaqItem {
  question: string
  answer: string
  helpUrl?: string
  order?: number
}

export async function fetchAnnouncement(): Promise<Announcement | null> {
  try {
    const res = await fetch('/api/public/announcements')
    if (!res.ok) return null
    const data = await res.json()
    return data.announcement ?? null
  } catch (e) {
    return null
  }
}

export async function fetchFaqs(): Promise<FaqItem[]> {
  try {
    const res = await fetch('/api/public/faqs')
    if (!res.ok) return []
    const data = await res.json()
    return Array.isArray(data.faqs) ? data.faqs : []
  } catch (e) {
    return []
  }
}


/**
 * Global authentication composable
 * Provides centralized auth state management and methods
 */

import { ref, readonly, computed } from 'vue'
import { api } from '../utils/api'
import { usePlausible } from '@/composables/usePlausible'
import { logger } from '../utils/logger';

// Global auth state (singleton pattern)
const user = ref<any>(null)
const isInitialized = ref(false)
const isLoading = ref(false)

// Auth state interface
export interface AuthUser {
  id: string
  email: string
  name?: string
  planType?: string
  hasActiveSubscription?: boolean
  isImpersonating?: boolean
  impersonatorEmail?: string
}

export const useAuth = () => {
  // Plausible Analytics
  const { trackUserAction } = usePlausible()
  
  // Computed properties
  const isAuthenticated = computed(() => !!user.value)
  const isPro = computed(() => {
    // Check for active subscription first, then fall back to planType
    if (user.value?.hasActiveSubscription !== undefined) {
      return user.value.hasActiveSubscription
    }
    // Fallback to planType check for backwards compatibility
    const planType = user.value?.planType || 'free'
    return planType === 'pro' || planType === 'enterprise'
  })

  /**
   * Check authentication status and load user data
   * @param force - Force refresh even if already initialized
   * @returns Promise<AuthUser | null>
   */
  const checkAuth = async (force = false): Promise<AuthUser | null> => {
    // Avoid redundant API calls unless forced
    if (isInitialized.value && !force && !isLoading.value) {
      return user.value
    }

    isLoading.value = true
    try {
      const authData = await api.get('/api/auth-status', {
        toast: { showErrors: false } // Don't show toast for auth checks
      })
      
      if (authData.authenticated && authData.user) {
        user.value = authData.user
        isInitialized.value = true
        return authData.user
      } else {
        user.value = null
        isInitialized.value = true
        return null
      }
    } catch (error) {
      user.value = null
      isInitialized.value = true
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Login user (updates auth state)
   * @param credentials - Login credentials
   * @returns Promise<AuthUser>
   */
  const login = async (credentials: { email: string; password: string }): Promise<AuthUser> => {
    const authData = await api.post('/login', credentials, {
      toast: { 
        success: 'Welcome back!',
        error: 'Login failed. Please check your credentials.' 
      }
    })
    
    if (authData.success && authData.user) {
      user.value = authData.user
      isInitialized.value = true
      return authData.user
    }
    
    throw new Error('Login failed')
  }


  /**
   * Logout user (clears auth state)
   */
  const logout = async (): Promise<void> => {
    try {
      // Track logout event
      trackUserAction.logout()

      // Use BetterAuth client logout first (should work now with correct baseURL)
      const { signOut } = await import('../lib/auth-client')
      await signOut()
      
      // Also call server logout to ensure httpOnly cookies are cleared
      await api.post('/api/logout', {}, {
        toast: { showErrors: false }
      })
      
    } catch (error) {
      logger.error('Logout error:', error)
    } finally {
      // Clear frontend state
      user.value = null
      isInitialized.value = false
      
      // Clear storage
      sessionStorage.clear()
      localStorage.clear()
      
      // Redirect to login
      window.location.href = '/login'
    }
  }

  /**
   * Refresh user data from server
   */
  const refreshUser = async (): Promise<AuthUser | null> => {
    return checkAuth(true)
  }

  /**
   * Clear auth state (for logout/session expiry)
   */
  const clearAuth = (): void => {
    user.value = null
    isInitialized.value = false
  }

  return {
    // Readonly state
    user: readonly(user),
    isAuthenticated,
    isPro,
    isInitialized: readonly(isInitialized),
    isLoading: readonly(isLoading),
    
    // Methods
    checkAuth,
    login,
    logout,
    refreshUser,
    clearAuth
  }
}
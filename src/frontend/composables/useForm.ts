import { ref, reactive, computed, readonly, type Ref } from 'vue'
import type { FormState, FormField } from '@types'
import { logger } from '../utils/logger';

export function useForm<T extends Record<string, any>>(
  initialValues: T,
  fields: FormField[] = []
) {
  const values = reactive<T>({ ...initialValues })
  const errors = reactive<Record<string, string>>({})
  const touched = reactive<Record<string, boolean>>({})
  const isSubmitting = ref(false)

  const isValid = computed(() => {
    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) return false

    // Check required fields only
    const requiredFields = fields.filter(field => field.required)
    return requiredFields.every(field => {
      const value = (values as any)[field.name]
      return value !== '' && value !== null && value !== undefined
    })
  })

  const validateField = (name: string, value: any): string | null => {
    const field = fields.find(f => f.name === name)
    if (!field) return null

    // Required validation
    if (field.required && (!value || value === '')) {
      return `${field.label} is required`
    }

    // Type-specific validation
    if (value && field.type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(value)) {
        return 'Please enter a valid email address'
      }
    }

    if (value && field.type === 'url') {
      try {
        new URL(value)
      } catch {
        return 'Please enter a valid URL'
      }
    }

    // Custom validation
    if (field.validation) {
      const { pattern, minLength, maxLength, custom } = field.validation

      if (pattern && value) {
        const regex = new RegExp(pattern)
        if (!regex.test(value)) {
          return `${field.label} format is invalid`
        }
      }

      if (minLength && value && value.length < minLength) {
        return `${field.label} must be at least ${minLength} characters`
      }

      if (maxLength && value && value.length > maxLength) {
        return `${field.label} must be no more than ${maxLength} characters`
      }

      if (custom && value) {
        const customError = custom(value)
        if (customError) return customError
      }
    }

    return null
  }

  const setFieldValue = (name: string, value: any) => {
    (values as any)[name] = value
    touched[name] = true

    // Validate field
    const error = validateField(name, value)
    if (error) {
      errors[name] = error
    } else {
      delete errors[name]
    }
  }

  const setFieldError = (name: string, error: string) => {
    errors[name] = error
  }

  const clearFieldError = (name: string) => {
    delete errors[name]
  }

  const validateForm = (): boolean => {
    // Clear existing errors
    Object.keys(errors).forEach(key => delete errors[key])

    // Validate all fields
    fields.forEach(field => {
      const error = validateField(field.name, (values as any)[field.name])
      if (error) {
        errors[field.name] = error
      }
    })

    return Object.keys(errors).length === 0
  }

  const resetForm = () => {
    Object.keys(values).forEach(key => {
      (values as any)[key] = (initialValues as any)[key]
    })
    Object.keys(errors).forEach(key => delete errors[key])
    Object.keys(touched).forEach(key => delete touched[key])
    isSubmitting.value = false
  }

  const handleSubmit = async (submitFn: (values: T) => Promise<void>) => {
    if (!validateForm()) return

    isSubmitting.value = true
    try {
      await submitFn({ ...values } as T)
    } catch (error: any) {
      // Handle submission errors
      logger.error('Form submission error:', error)

      // Handle API error responses
      if (error.response?.data?.message) {
        // Check if it's a domain-specific error
        if (error.response.data.message.includes('domain') || error.response.data.message.includes('Domain')) {
          setFieldError('domain', error.response.data.message)
        } else {
          setFieldError('_form', error.response.data.message)
        }
      } else if (error.message) {
        setFieldError('_form', error.message)
      } else {
        setFieldError('_form', 'An unexpected error occurred')
      }
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    values: readonly(values),
    errors: readonly(errors),
    touched: readonly(touched),
    isSubmitting: readonly(isSubmitting),
    isValid,
    setFieldValue,
    setFieldError,
    clearFieldError,
    validateForm,
    resetForm,
    handleSubmit
  }
}
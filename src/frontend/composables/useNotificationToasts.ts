import { watch } from 'vue'
import { useNotifications } from './useNotifications'
import { useToast } from './useToast'
import { useAuth } from './useAuth'

/**
 * Auto-show toast notifications for urgent notifications
 * This composable bridges the notification system with toast notifications
 */
export function useNotificationToasts() {
  const { isAuthenticated } = useAuth()
  const { urgentNotifications } = useNotifications()
  const { warning, error, info } = useToast()

  // Track which notifications we've already shown as toasts
  const shownNotificationIds = new Set<string>()

  // Watch for new urgent notifications and show them as toasts
  // Only when user is authenticated
  watch(urgentNotifications, (newUrgentNotifications) => {
    // Don't show notifications if user is not authenticated
    if (!isAuthenticated.value) {
      return
    }
    newUrgentNotifications.forEach(notification => {
      // Skip if we've already shown this notification as a toast
      if (shownNotificationIds.has(notification.id)) {
        return
      }

      // Mark as shown
      shownNotificationIds.add(notification.id)

      // Show appropriate toast based on notification type
      switch (notification.type) {
        case 'PLAN_LIMIT_REACHED':
          error(
            `${notification.title}: ${notification.message}`,
            { duration: 8000 } // Longer duration for important messages
          )
          break

        case 'EMAIL_QUOTA_EXHAUSTED':
          error(
            `${notification.title}: ${notification.message}`,
            { duration: 8000 }
          )
          break

        case 'PLAN_LIMIT_WARNING':
          warning(
            `${notification.title}: ${notification.message}`,
            { duration: 6000 }
          )
          break

        case 'PAYMENT_FAILED':
          error(
            `${notification.title}: ${notification.message}`,
            { duration: 10000 } // Very long for payment issues
          )
          break

        case 'SECURITY_ALERT':
          error(
            `${notification.title}: ${notification.message}`,
            { duration: 0 } // Persistent until manually closed
          )
          break

        default:
          // For other urgent notifications, show as info
          info(
            `${notification.title}: ${notification.message}`,
            { duration: 5000 }
          )
          break
      }
    })
  }, { immediate: true })

  return {
    // This composable is mostly side-effect based
    // but we can expose this for manual cleanup if needed
    clearShownNotifications: () => shownNotificationIds.clear()
  }
}

/**
 * Show a plan limit error toast
 * This can be called directly from API error handlers
 */
export function showPlanLimitToast(errorMessage: string, details?: {
  resourceType?: string
  currentUsage?: number
  limit?: number
  upgradeRequired?: boolean
}) {
  const { error } = useToast()
  
  let message = errorMessage
  
  if (details && details.currentUsage !== undefined && details.limit !== undefined) {
    message += ` (${details.currentUsage}/${details.limit} used)`
  }
  
  error(message, { 
    duration: 8000,
    closable: true 
  })
}

/**
 * Show an email quota exhausted toast
 */
export function showEmailQuotaToast(currentUsage: number, limit: number) {
  const { error } = useToast()
  
  error(
    `Email quota exhausted (${currentUsage}/${limit} used). Upgrade your plan or purchase credits to continue.`,
    { 
      duration: 10000,
      closable: true 
    }
  )
}

import { ref, onMounted, onUnmounted } from 'vue'
import { io, Socket } from 'socket.io-client'
import { useToast } from './useToast'
import { useDataRefresh } from './useDataRefresh'
import { useMetrics } from './useMetrics'
import { useNotifications } from './useNotifications'
import { logger } from '../utils/logger';

interface EmailProcessedData {
  messageId: string
  fromAddress: string
  subject: string
  isTestWebhook: boolean
  deliveryStatus: 'DELIVERED' | 'FAILED'
  webhookPayload?: any
  timestamp: string
}

interface MetricsUpdatedData {
  domains: number
  aliases: number
  webhooks: number
  emails: number
}

interface DomainVerificationData {
  domainId: string
  domain: string
  verified: boolean
  verificationStatus: 'PENDING' | 'VERIFIED' | 'FAILED'
  verificationFailureCount: number
  nextVerificationCheck?: string
  error?: string
  timestamp: string
}

// Global WebSocket instance
let globalSocket: Socket | null = null
let connectionCount = 0

export function useWebSocket() {
  const { success: showSuccess, error: showError } = useToast()
  const { triggerRefresh } = useDataRefresh()
  const { refreshMetrics } = useMetrics()
  const { fetchUnreadCount } = useNotifications()
  
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionError = ref<string | null>(null)

  const connect = () => {
    if (globalSocket?.connected) {
      isConnected.value = true
      return
    }

    if (isConnecting.value) return

    isConnecting.value = true
    connectionError.value = null

    try {
      globalSocket = io({
        withCredentials: true, // Send httpOnly cookies automatically
        transports: ['websocket', 'polling'],
        timeout: 10000,
        retries: 3
      })

      globalSocket.on('connect', () => {
        logger.debug('WebSocket connected')
        isConnected.value = true
        isConnecting.value = false
        connectionError.value = null
      })

      globalSocket.on('disconnect', (reason) => {
        logger.debug('WebSocket disconnected:', reason)
        isConnected.value = false
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          setTimeout(() => connect(), 2000)
        }
      })

      globalSocket.on('connect_error', (error) => {
        logger.error('WebSocket connection error:', error)
        isConnected.value = false
        isConnecting.value = false
        connectionError.value = error.message
      })

      // Handle email processed events
      globalSocket.on('email_processed', (data: EmailProcessedData) => {       
        if (data.isTestWebhook) {
          showSuccess(`Test email processed! From: ${data.fromAddress}`)
          
          // Always trigger aliases refresh for test webhooks (helps with onboarding)
          triggerRefresh('aliases')
          
          // Note: Logs are handled separately and don't need refresh triggers
        } else {
          showSuccess(`Email processed and delivered to webhook`)
        }

        // Always refresh metrics for email count
        setTimeout(() => refreshMetrics(), 500)
      })

      // Handle metrics updated events
      globalSocket.on('metrics_updated', (data: MetricsUpdatedData) => {
        // Update metrics in the metrics composable
        refreshMetrics()
      })

      // Handle domain verification events
      globalSocket.on('domain_verification_updated', (data: DomainVerificationData) => {

        // Update the specific domain in the data refresh system
        const { updateItem } = useDataRefresh()
        updateItem('domains', data.domainId, {
          verified: data.verified,
          verificationStatus: data.verificationStatus,
          verificationFailureCount: data.verificationFailureCount,
          nextVerificationCheck: data.nextVerificationCheck,
          lastVerificationAttempt: data.timestamp
        })

        // Show appropriate toast notification
        if (data.verified) {
          showSuccess(`Domain ${data.domain} verified successfully! 🎉`)
        } else if (data.verificationStatus === 'FAILED') {
          showError(`Domain ${data.domain} verification failed after multiple attempts`)
        } else {
          // For PENDING status, show a more subtle notification
          logger.debug(`Domain ${data.domain} verification attempt completed, will retry automatically`)
        }

        // Refresh metrics to update counts
        setTimeout(() => refreshMetrics(), 500)
      })

      // Handle notification events
      globalSocket.on('notification_created', (data: any) => {
        logger.debug('New notification received:', data)

        // Show toast notification
        showSuccess(data.title)

        // Refresh notification count
        fetchUnreadCount()
      })

      // Handle pong responses
      globalSocket.on('pong', () => {
        logger.debug('WebSocket pong received')
      })

    } catch (error: any) {
      logger.error('Failed to initialize WebSocket:', error)
      isConnecting.value = false
      connectionError.value = error.message
    }
  }

  const disconnect = () => {
    if (globalSocket) {
      globalSocket.disconnect()
      globalSocket = null
    }
    isConnected.value = false
    isConnecting.value = false
  }

  const ping = () => {
    if (globalSocket?.connected) {
      globalSocket.emit('ping')
    }
  }

  // Auto-connect on mount, disconnect on unmount
  onMounted(() => {
    connectionCount++
    if (connectionCount === 1) {
      connect()
    } else {
      // Already connected, just update state
      isConnected.value = globalSocket?.connected || false
    }
  })

  onUnmounted(() => {
    connectionCount--
    if (connectionCount === 0) {
      disconnect()
    }
  })

  return {
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    ping
  }
}

// Global WebSocket functions for manual control
export const globalWebSocket = {
  connect: () => {
    const { connect } = useWebSocket()
    connect()
  },
  
  disconnect: () => {
    const { disconnect } = useWebSocket()
    disconnect()
  },
  
  getStatus: () => ({
    connected: globalSocket?.connected || false,
    id: globalSocket?.id
  })
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).webSocket = globalWebSocket
}

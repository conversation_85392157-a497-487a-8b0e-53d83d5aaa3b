import { logger } from '../utils/logger';

// Plausible Analytics tracking utility
// Docs: https://plausible.io/docs/custom-event-goals

export interface PlausibleEventOptions {
  callback?: () => void;
  props?: Record<string, string | number>;
  interactive?: boolean;
}

export function usePlausible() {
  // Track custom events
  const trackEvent = (eventName: string, options?: PlausibleEventOptions) => {
    if (typeof window !== 'undefined' && window.plausible) {
      try {
        window.plausible(eventName, options);
        
        // Development logging
        if (import.meta.env.DEV) {
          logger.debug(`[Plausible] ${eventName}`, options);
        }
      } catch (error) {
        logger.warn('Plausible tracking error:', error);
      }
    } else if (import.meta.env.DEV) {
      logger.debug(`[Plausible] ${eventName} (not loaded)`, options);
    }
  };

  // Common event tracking functions
  const trackUserAction = {
    // Authentication events
    login: (method: 'email' | 'oauth' = 'email') => 
      trackEvent('Login', { props: { method } }),
    
    loginFailed: (reason: string) => 
      trackEvent('Login Failed', { props: { reason } }),
    
    register: (method: 'email' | 'oauth' = 'email') => 
      trackEvent('Register', { props: { method } }),
    
    registerFailed: (reason: string) => 
      trackEvent('Register Failed', { props: { reason } }),
    
    logout: () => 
      trackEvent('Logout'),

    // Domain management events
    domainCreate: (domain: string) => 
      trackEvent('Domain Created', { props: { domain } }),
    
    domainVerify: (domain: string, success: boolean) => 
      trackEvent('Domain Verify', { props: { domain, result: success ? 'success' : 'failed' } }),
    
    domainDelete: (domain: string) => 
      trackEvent('Domain Deleted', { props: { domain } }),

    // Webhook events
    webhookCreate: (domain: string) => 
      trackEvent('Webhook Created', { props: { domain } }),
    
    webhookTest: (domain: string, success: boolean) => 
      trackEvent('Webhook Test', { props: { domain, result: success ? 'success' : 'failed' } }),
    
    webhookDelete: (domain: string) => 
      trackEvent('Webhook Deleted', { props: { domain } }),

    // Alias events
    aliasCreate: (domain: string) => 
      trackEvent('Alias Created', { props: { domain } }),
    
    aliasDelete: (domain: string) => 
      trackEvent('Alias Deleted', { props: { domain } }),

    // Plan and billing events
    planUpgrade: (from: string, to: string) => 
      trackEvent('Plan Upgrade', { props: { from, to } }),
    
    planDowngrade: (from: string, to: string) => 
      trackEvent('Plan Downgrade', { props: { from, to } }),
    
    paymentSuccess: (amount: number, plan: string) => 
      trackEvent('Payment Success', { props: { amount, plan } }),
    
    paymentFailed: (reason: string, plan: string) => 
      trackEvent('Payment Failed', { props: { reason, plan } }),

    // Settings events
    settingsUpdate: (section: string) => 
      trackEvent('Settings Updated', { props: { section } }),
    
    spamFilterToggle: (enabled: boolean, domain: string) => 
      trackEvent('Spam Filter Toggle', { props: { enabled: enabled.toString(), domain } }),

    // Engagement events
    helpArticleView: (article: string) => 
      trackEvent('Help Article View', { props: { article } }),
    
    onboardingComplete: (step: string) => 
      trackEvent('Onboarding Complete', { props: { step } }),
    
    featureDiscovery: (feature: string) => 
      trackEvent('Feature Discovery', { props: { feature } }),

    // Error tracking (complement to Bugfender)
    criticalError: (component: string, error: string) => 
      trackEvent('Critical Error', { props: { component, error } }),
  };

  return {
    trackEvent,
    trackUserAction,
  };
}

// Export for use in components
export default usePlausible;
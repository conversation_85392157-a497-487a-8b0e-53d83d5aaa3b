import { ref, readonly, type Ref } from 'vue'
import type { ApiResponse, ApiError } from '@types'
import { usePlausible } from '@/composables/usePlausible'
import { logger } from '../utils/logger';

export function useApi() {
  const loading: Ref<boolean> = ref(false)
  const error: Ref<string | null> = ref(null)

  async function request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    loading.value = true
    error.value = null

    try {
      // Only set Content-Type for requests with a body
      const headers: Record<string, string> = {
        ...options.headers as Record<string, string>
      }

      if (options.body) {
        headers['Content-Type'] = 'application/json'
      }

      const response = await fetch(url, {
        headers,
        credentials: 'include', // Include cookies for authentication
        ...options
      })

      if (!response.ok) {
        const errorData: ApiError = await response.json()
        const errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`
        
        // Track API errors with Plausible
        logger.error('API error:', {
          url,
          method: options.method || 'GET',
          status: response.status,
          error: errorMessage
        })
        
        throw new Error(errorMessage)
      }

      const data = await response.json()
      return data as T
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      error.value = errorMessage
      
      // Log network or parsing errors
      logger.error(`API request failed: ${url}`, err)
      
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  const get = <T>(url: string, options?: RequestInit) =>
    request<T>(url, { method: 'GET', ...options })

  const post = <T>(url: string, data: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    })

  const put = <T>(url: string, data: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options
    })

  const patch = <T>(url: string, data?: any, options?: RequestInit) =>
    request<T>(url, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    })

  const del = <T>(url: string, options?: RequestInit) =>
    request<T>(url, { method: 'DELETE', ...options })

  return {
    loading: readonly(loading),
    error: readonly(error),
    request,
    get,
    post,
    put,
    patch,
    del
  }
}

// Specific API composables
export function useDomainApi() {
  const { loading, error, get, post, put, del } = useApi()
  const { trackUserAction } = usePlausible()

  const getDomains = () => get('/api/domains')
  
  const createDomain = async (data: any) => {
    try {
      const result = await post('/api/domains', data)
      trackUserAction.domainCreate(data.domain)
      return result
    } catch (err) {
      logger.error('Domain creation failed:', { 
        domain: data.domain, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      })
      throw err
    }
  }
  
  const updateDomain = (id: string, data: any) => put(`/api/domains/${id}`, data)
  
  const deleteDomain = async (domainId: string, domain?: string) => {
    try {
      const result = await del(`/api/domains/${domainId}`)
      trackUserAction.domainDelete(domain || domainId)
      return result
    } catch (err) {
      throw err
    }
  }
  
  const verifyDomain = async (domainId: string, domain?: string) => {
    try {
      const result = await post(`/api/domains/${domainId}/verify`, {})
      trackUserAction.domainVerify(domain || domainId, true)
      return result
    } catch (err) {
      logger.error('Domain verification failed:', { 
        domainId, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      })
      trackUserAction.domainVerify(domain || domainId, false)
      throw err
    }
  }
  
  const updateSpamFilter = (domainId: string, settings: { enabled: boolean; thresholds: { green: number; red: number } }) =>
    put(`/api/domains/${domainId}/spam-filter`, settings)

  return {
    loading,
    error,
    getDomains,
    createDomain,
    updateDomain,
    deleteDomain,
    verifyDomain,
    updateSpamFilter
  }
}

export function useWebhookApi() {
  const { loading, error, get, post, put, del } = useApi()
  const { trackUserAction } = usePlausible()

  const getWebhooks = () => get('/api/webhooks')
  
  const createWebhook = async (data: any) => {
    const result = await post('/api/webhooks', data)
    trackUserAction.webhookCreate(data.domain || data.url)
    return result
  }
  
  const updateWebhook = (id: string, data: any) => put(`/api/webhooks/${id}`, data)
  
  const deleteWebhook = async (id: string, domain?: string) => {
    const result = await del(`/api/webhooks/${id}`)
    trackUserAction.webhookDelete(domain || id)
    return result
  }
  const verifyWebhook = (id: string) => post(`/api/webhooks/${id}/verify`, {})
  const completeWebhookVerification = (id: string, token: string) =>
    post(`/api/webhooks/${id}/verify/complete`, { verificationToken: token })
  const testWebhook = async (id: string, domain?: string) => {
    try {
      const result = await post(`/api/webhooks/${id}/test`, {})
      trackUserAction.webhookTest(domain || id, true)
      return result
    } catch (err) {
      logger.error('Webhook test failed:', { 
        webhookId: id, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      })
      trackUserAction.webhookTest(domain || id, false)
      throw err
    }
  }
  
  const testWebhookCustom = async (id: string, customPayload?: { subject: string; content: { text: string; html: string } }, domain?: string) => {
    try {
      const result = await post(`/api/webhooks/${id}/test`, customPayload ? { customPayload } : {})
      trackUserAction.webhookTest(domain || id, true)
      return result
    } catch (err) {
      logger.error('Custom webhook test failed:', { 
        webhookId: id, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      })
      trackUserAction.webhookTest(domain || id, false)
      throw err
    }
  }

  return {
    loading,
    error,
    getWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    verifyWebhook,
    completeWebhookVerification,
    testWebhook,
    testWebhookCustom
  }
}

export function useAliasApi() {
  const { loading, error, get, post, put, del } = useApi()
  const { trackUserAction } = usePlausible()

  const getAliases = () => get('/api/aliases')
  
  const createAlias = async (data: any) => {
    const result = await post('/api/aliases', data)
    trackUserAction.aliasCreate(data.domain || data.email)
    return result
  }
  
  const updateAlias = (id: string, data: any) => put(`/api/aliases/${id}`, data)
  
  const deleteAlias = async (id: string, domain?: string) => {
    const result = await del(`/api/aliases/${id}`)
    trackUserAction.aliasDelete(domain || id)
    return result
  }

  return {
    loading,
    error,
    getAliases,
    createAlias,
    updateAlias,
    deleteAlias
  }
}
import { ref, computed } from 'vue'
import { api } from '../utils/api'
import { logger } from '../utils/logger';

export interface FileTypeSettings {
  text: boolean
  images: boolean
  documents: boolean
  archives: boolean
  media: boolean
}

export interface UserSettings {
  id?: string | null
  maxInlineSize: number
  fileTypeSettings: FileTypeSettings | null
  storageProvider: string
  dataRetentionHours: number | null
  s3Config: {
    region: string
    bucket: string
    accessKey: string
    secretKey: string
    endpoint: string
  } | null
  allowFallbackStorage: boolean
  fallbackNotification: boolean
  useGravatar: boolean
  createdAt?: string | null
  updatedAt?: string | null
}

export interface UpdateUserSettingsData {
  maxInlineSize?: number
  fileTypeSettings?: FileTypeSettings
  storageProvider?: string
  dataRetentionHours?: number | null
  s3Config?: {
    region?: string
    bucket?: string
    accessKey?: string
    secretKey?: string
    endpoint?: string
  } | null
  allowFallbackStorage?: boolean
  fallbackNotification?: boolean
  useGravatar?: boolean
}

const settings = ref<UserSettings>({
  maxInlineSize: 1.0,
  fileTypeSettings: {
    text: false,
    images: false,
    documents: false,
    archives: false,
    media: false
  },
  storageProvider: 'default',
  dataRetentionHours: null,
  s3Config: null,
  allowFallbackStorage: true,
  fallbackNotification: true,
  useGravatar: false
})

const loading = ref(false)
const saving = ref(false)
const error = ref<string | null>(null)

export function useUserSettings() {
  const loadSettings = async () => {
    loading.value = true
    error.value = null

    try {
      const data = await api.get('/api/settings')
      
      // Update settings with loaded data
      settings.value = {
        ...data.settings,
        fileTypeSettings: data.settings.fileTypeSettings || {
          text: false,
          images: false,
          documents: false,
          archives: false,
          media: false
        },
        s3Config: data.settings.s3Config || {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        }
      }
    } catch (err: any) {
      error.value = err.message
      logger.error('Failed to load user settings:', err)
    } finally {
      loading.value = false
    }
  }

  const saveSettings = async (updates: UpdateUserSettingsData) => {
    saving.value = true
    error.value = null
    
    try {
      const data = await api.put('/api/settings', updates)
      
      // Update local settings with saved data
      settings.value = {
        ...data.settings,
        s3Config: data.settings.s3Config || {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        }
      }

      return data
    } catch (err: any) {
      error.value = err.message
      logger.error('Failed to save user settings:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  const resetSettings = async () => {
    saving.value = true
    error.value = null
    
    try {
      const data = await api.delete('/api/settings')

      // Reset to default values
      settings.value = {
        maxInlineSize: 1.0,
        fileTypeSettings: {
          text: false,
          images: false,
          documents: false,
          archives: false,
          media: false
        },
        storageProvider: 'default',
        dataRetentionHours: null,
        s3Config: {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        },
        allowFallbackStorage: true,
        fallbackNotification: true,
        useGravatar: false
      }

      return data
    } catch (err: any) {
      error.value = err.message
      logger.error('Failed to reset user settings:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  // Computed properties
  const hasCustomS3Config = computed(() => {
    return settings.value.storageProvider === 's3-compatible' && 
           settings.value.s3Config &&
           settings.value.s3Config.region &&
           settings.value.s3Config.bucket &&
           settings.value.s3Config.accessKey &&
           settings.value.s3Config.secretKey
  })

  return {
    // State
    settings: computed(() => settings.value),
    loading: computed(() => loading.value),
    saving: computed(() => saving.value),
    error: computed(() => error.value),
    
    // Computed
    hasCustomS3Config,
    
    // Methods
    loadSettings,
    saveSettings,
    resetSettings
  }
}

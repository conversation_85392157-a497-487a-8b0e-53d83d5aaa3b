import { onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'

export function useSEO() {
  const route = useRoute()
  
  const updateCanonicalUrl = () => {
    // Remove any existing canonical link
    const existingCanonical = document.querySelector('link[rel="canonical"]')
    if (existingCanonical) {
      existingCanonical.remove()
    }
    
    // Add new canonical link
    const canonical = document.createElement('link')
    canonical.rel = 'canonical'
    
    // Build the canonical URL
    const baseUrl = 'https://emailconnect.eu'
    const path = route.path === '/' ? '' : route.path
    canonical.href = `${baseUrl}${path}`
    
    document.head.appendChild(canonical)
  }
  
  const updateMetaTag = (name: string, content: string, property = false) => {
    const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`
    let meta = document.querySelector(selector) as HTMLMetaElement
    
    if (!meta) {
      meta = document.createElement('meta')
      if (property) {
        meta.setAttribute('property', name)
      } else {
        meta.name = name
      }
      document.head.appendChild(meta)
    }
    
    meta.content = content
  }
  
  const setPageMeta = (options: {
    title?: string
    description?: string
    keywords?: string
    ogTitle?: string
    ogDescription?: string
    ogUrl?: string
    noindex?: boolean
  }) => {
    if (options.title) {
      document.title = `${options.title} | EmailConnect.eu`
      updateMetaTag('og:title', options.ogTitle || options.title, true)
    }
    
    if (options.description) {
      updateMetaTag('description', options.description)
      updateMetaTag('og:description', options.ogDescription || options.description, true)
    }
    
    if (options.keywords) {
      updateMetaTag('keywords', options.keywords)
    }
    
    if (options.ogUrl) {
      updateMetaTag('og:url', options.ogUrl, true)
    }
    
    // Handle noindex for pages that shouldn't be indexed
    if (options.noindex) {
      updateMetaTag('robots', 'noindex, nofollow')
    } else {
      // Remove noindex if it exists
      const robotsMeta = document.querySelector('meta[name="robots"]')
      if (robotsMeta) {
        robotsMeta.remove()
      }
    }
  }
  
  onMounted(() => {
    updateCanonicalUrl()
  })
  
  watch(() => route.path, () => {
    updateCanonicalUrl()
  })
  
  return {
    setPageMeta,
    updateCanonicalUrl
  }
}
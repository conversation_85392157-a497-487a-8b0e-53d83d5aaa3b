import { ref, readonly, computed } from 'vue'
import { useApi } from './useApi'
import { Sentry } from '../lib/sentry'

export interface WebhookTestEndpoint {
  success: boolean
  webhookEndpoint: string
  testId: string
  verificationCode: string
  expiresAt?: string
  user?: {
    id: string
    email: string
    isLinked: boolean
    isPermanent: boolean
    planType: string
  }
  endpoint?: {
    id: string
    friendlyUrl: string
    createdAt: string
  }
}

export interface WebhookTestResult {
  success: boolean
  webhookEndpoint: string
  testId: string
  results?: {
    status: number
    responseTime: number
    headers: object
    body: string
    timestamp: string
  }
  verificationCode: string
  error?: string
}

export interface WebhookTestHealth {
  status: 'ok' | 'error'
  integration: 'configured' | 'not_configured'
  timestamp: string
}

export function useWebhookTest() {
  const { loading, error, get, post } = useApi()
  
  const creating = ref(false)
  const testing = ref(false)
  const lastCreatedTime = ref<number>(0)
  const COOLDOWN_MS = 10000 // 10 seconds cooldown

  // Computed properties
  const canCreateEndpoint = computed(() => {
    const now = Date.now()
    const timeSinceLastCreation = now - lastCreatedTime.value
    return timeSinceLastCreation >= COOLDOWN_MS && !creating.value
  })

  const cooldownRemaining = computed(() => {
    const now = Date.now()
    const timeSinceLastCreation = now - lastCreatedTime.value
    if (timeSinceLastCreation >= COOLDOWN_MS) return 0
    return Math.ceil((COOLDOWN_MS - timeSinceLastCreation) / 1000)
  })

  /**
   * Create a new WebhookTest endpoint
   */
  const createEndpoint = async (): Promise<WebhookTestEndpoint> => {
    // Check cooldown period
    const now = Date.now()
    const timeSinceLastCreation = now - lastCreatedTime.value
    if (timeSinceLastCreation < COOLDOWN_MS) {
      const remainingTime = Math.ceil((COOLDOWN_MS - timeSinceLastCreation) / 1000)
      throw new Error(`Please wait ${remainingTime} seconds before creating another WebhookTest endpoint`)
    }

    creating.value = true
    try {
      const result = await post<WebhookTestEndpoint>('/api/webhooktest/create-endpoint', {})
      lastCreatedTime.value = now
      return result
    } finally {
      creating.value = false
    }
  }

  /**
   * Test a webhook URL and get immediate results
   */
  const testWebhook = async (
    webhookUrl: string,
    testPayload?: object,
    timeout?: number
  ): Promise<WebhookTestResult> => {
    testing.value = true
    
    // Add Sentry context for webhook testing
    Sentry.addBreadcrumb({
      message: 'Webhook test initiated',
      category: 'webhook-test',
      level: 'info',
      data: { webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...') }
    })
    
    Sentry.setTag('feature', 'webhook-test')
    
    try {
      const result = await post<WebhookTestResult>('/api/webhooktest/test-webhook', {
        webhookUrl,
        testPayload,
        timeout
      })
      
      Sentry.addBreadcrumb({
        message: `Webhook test completed: ${result.success ? 'success' : 'failed'}`,
        category: 'webhook-test',
        level: result.success ? 'info' : 'warning',
        data: { status: result.results?.status }
      })
      
      return result
    } catch (error) {
      // Add context that this is a webhook test error
      Sentry.setContext('webhook-test', {
        isUserTest: true,
        webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...'),
        timestamp: new Date().toISOString()
      })
      
      throw error
    } finally {
      testing.value = false
      // Clean up Sentry context
      Sentry.setTag('feature', undefined)
      Sentry.setContext('webhook-test', null)
    }
  }

  /**
   * Get test results by verification code
   */
  const getResults = async (verificationCode: string): Promise<any> => {
    return await get(`/api/webhooktest/results/${verificationCode}`)
  }

  /**
   * Get received payloads by verification code (legacy)
   */
  const getPayloads = async (verificationCode: string): Promise<any[]> => {
    const result = await get<{ payloads: any[] }>(`/api/webhooktest/payloads/${verificationCode}`)
    return result.payloads || []
  }

  /**
   * Check WebhookTest integration health
   */
  const checkHealth = async (): Promise<WebhookTestHealth> => {
    return await get<WebhookTestHealth>('/api/webhooktest/health')
  }

  /**
   * Create a webhook using WebhookTest and add it to EmailConnect
   * This is a convenience method that combines endpoint creation with webhook creation
   * ENHANCED: Now creates permanent user accounts with user linking info
   */
  const createWebhookWithTest = async (webhookData: {
    name?: string
    description?: string
  } = {}) => {
    const endpoint = await createEndpoint()
    
    // Extract the three-word name from the webhook URL
    const urlPath = new URL(endpoint.webhookEndpoint).pathname.substring(1) // Remove leading /
    const threeWordName = urlPath.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
    
    const defaultName = threeWordName || `WebhookTest - ${new Date().toLocaleDateString()}`
    let defaultDescription = `WebhookTest endpoint: ${urlPath}`
    
    // Add user linking information if available
    if (endpoint.user?.isLinked) {
      defaultDescription += ` (Linked to ${endpoint.user.isPermanent ? 'permanent' : 'temporary'} WebhookTest account)`
    }

    return {
      endpoint,
      webhookData: {
        name: webhookData.name || defaultName,
        url: endpoint.webhookEndpoint,
        description: webhookData.description || defaultDescription
      },
      // Include user linking information for UI feedback
      userInfo: endpoint.user ? {
        isLinked: endpoint.user.isLinked,
        isPermanent: endpoint.user.isPermanent,
        planType: endpoint.user.planType,
        webhookTestUserId: endpoint.user.id
      } : null
    }
  }

  return {
    // State
    loading: readonly(loading),
    error: readonly(error),
    creating: readonly(creating),
    testing: readonly(testing),
    canCreateEndpoint: readonly(canCreateEndpoint),
    cooldownRemaining: readonly(cooldownRemaining),

    // Methods
    createEndpoint,
    testWebhook,
    getResults,
    getPayloads,
    checkHealth,
    createWebhookWithTest
  }
}


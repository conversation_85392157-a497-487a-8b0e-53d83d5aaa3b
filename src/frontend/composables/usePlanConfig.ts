import { ref, computed } from 'vue'
import { logger } from '../utils/logger';

// Shared state for plan configuration data
const planConfigData = ref<any>(null)
const isLoading = ref(false)
const lastUpdated = ref<Date | null>(null)
const error = ref<string | null>(null)

// Single API call function
const fetchPlanConfig = async () => {
  if (isLoading.value) return // Prevent duplicate calls

  try {
    isLoading.value = true
    error.value = null
    
    const response = await fetch('/api/public/plans')
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    if (data.success) {
      planConfigData.value = data
      lastUpdated.value = new Date()
    } else {
      throw new Error('Invalid response format')
    }
    
    return data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load plan configurations'
    logger.error('Failed to load plan configurations:', err)
    throw err
  } finally {
    isLoading.value = false
  }
}

// Composable hook
export function usePlanConfig() {
  // Computed values for easy access
  const plans = computed(() => planConfigData.value?.plans || {})
  const creditPricing = computed(() => planConfigData.value?.creditPricing || {})
  
  const freePlan = computed(() => plans.value.free || {
    name: 'Free',
    monthlyEmailLimit: 50,
    domains: 1,
    aliases: 2, // catch-all + 1 additional
    webhooks: 2,
    features: []
  })
  
  const proPlan = computed(() => plans.value.pro || {
    name: 'Pro',
    monthlyEmailLimit: 1000,
    domains: 5,
    aliases: 10,
    webhooks: 10,
    features: [],
    price: {
      monthly: 9.95,
      yearly: 99.50,
      currency: 'EUR'
    }
  })
  
  const freeCreditPricing = computed(() => creditPricing.value.free || {
    pricePerHundred: 1.00,
    currency: 'EUR'
  })
  
  const proCreditPricing = computed(() => creditPricing.value.pro || {
    pricePerHundred: 0.80,
    currency: 'EUR'
  })

  // Load plan configurations if not already loaded
  const loadPlanConfig = async () => {
    if (!planConfigData.value || (lastUpdated.value && Date.now() - lastUpdated.value.getTime() > 300000)) {
      // Load if no data or data is older than 5 minutes
      await fetchPlanConfig()
    }
    return planConfigData.value
  }

  // Force refresh - bypasses cache completely
  const refreshPlanConfig = async () => {
    // Reset lastUpdated to force a fresh fetch
    lastUpdated.value = null
    await fetchPlanConfig()
    return planConfigData.value
  }

  return {
    // State
    planConfigData: computed(() => planConfigData.value),
    isLoading: computed(() => isLoading.value),
    lastUpdated: computed(() => lastUpdated.value),
    error: computed(() => error.value),
    
    // Computed data
    plans,
    creditPricing,
    freePlan,
    proPlan,
    freeCreditPricing,
    proCreditPricing,
    
    // Methods
    loadPlanConfig,
    refreshPlanConfig
  }
}

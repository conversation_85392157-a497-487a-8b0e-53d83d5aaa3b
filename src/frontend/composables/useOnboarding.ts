import { ref, computed, watch } from 'vue'
import { useMetrics } from './useMetrics'
import { logger } from '../utils/logger';

export interface OnboardingStep {
  id: string
  title: string
  description: string
  completed: boolean
  optional?: boolean
}


// Global onboarding state
const completedSteps = ref<Set<string>>(new Set())
const onboardingDismissed = ref(false)

// Load from localStorage with user-specific key and expiration check
const loadOnboardingState = (userId?: string) => {
  try {
    const storageKey = userId ? `onboarding_state_${userId}` : 'onboarding_state'
    const saved = localStorage.getItem(storageKey)
    if (saved) {
      const state = JSON.parse(saved)

      // Check if state has expired (24 hours)
      const now = Date.now()
      const savedTime = state.timestamp || 0
      const twentyFourHours = 24 * 60 * 60 * 1000

      if (now - savedTime > twentyFourHours) {
        // State has expired, remove it
        localStorage.removeItem(storageKey)
        return
      }

      completedSteps.value = new Set(state.completedSteps || [])
      onboardingDismissed.value = state.dismissed || false
    }
  } catch (error) {
    logger.warn('Failed to load onboarding state:', error)
  }
}

// Save to localStorage with user-specific key and timestamp
const saveOnboardingState = (userId?: string) => {
  try {
    const storageKey = userId ? `onboarding_state_${userId}` : 'onboarding_state'
    const state = {
      completedSteps: Array.from(completedSteps.value),
      dismissed: onboardingDismissed.value,
      timestamp: Date.now() // Add timestamp for expiration
    }
    localStorage.setItem(storageKey, JSON.stringify(state))
  } catch (error) {
    logger.warn('Failed to save onboarding state:', error)
  }
}

export function useOnboarding() {
  const { counts, metricsData } = useMetrics()

  // Get user ID from metrics data
  const userId = computed(() => metricsData.value?.user?.id)

  // Initialize state when user ID is available
  watch(userId, (newUserId) => {
    if (newUserId && completedSteps.value.size === 0) {
      loadOnboardingState(newUserId)
    }
  }, { immediate: true })

  // Watch for changes and save with user ID
  watch([completedSteps, onboardingDismissed, userId], () => {
    if (userId.value) {
      saveOnboardingState(userId.value)
    }
  }, { deep: true })

  // Computed properties
  const isNewUser = computed(() => {
    // Check for non-test webhooks if available, otherwise fall back to total webhooks
    const nonTestWebhooks = metricsData.value?.non_test_webhooks ?? counts.value.webhooks;
    return counts.value.domains === 0 && 
           counts.value.aliases === 0 && 
           nonTestWebhooks === 0
  })

  const shouldShowOnboarding = computed(() => {
    return isNewUser.value && !onboardingDismissed.value
  })

  // Methods

  const completeStep = (stepId: string) => {
    completedSteps.value.add(stepId)
  }

  const uncompleteStep = (stepId: string) => {
    completedSteps.value.delete(stepId)
  }

  const dismissOnboarding = () => {
    onboardingDismissed.value = true
  }

  const resetOnboarding = () => {
    completedSteps.value.clear()
    onboardingDismissed.value = false

    // Remove both user-specific and legacy storage
    if (userId.value) {
      localStorage.removeItem(`onboarding_state_${userId.value}`)
    }
    localStorage.removeItem('onboarding_state')
    // Clean up any old return context from previous complex implementation
    localStorage.removeItem('onboarding-return-context')
  }

  const isStepCompleted = (stepId: string) => {
    return completedSteps.value.has(stepId)
  }

  return {
    // State
    completedSteps,
    onboardingDismissed,
    
    // Computed
    isNewUser,
    shouldShowOnboarding,
    
    // Methods
    completeStep,
    uncompleteStep,
    dismissOnboarding,
    resetOnboarding,
    isStepCompleted
  }
}

import { Sentry } from './sentry';

/**
 * Consolidated error capture helper with service context
 */

export function captureError(
  error: Error, 
  service: 'auth' | 'domains' | 'aliases' | 'webhooks' | 'billing' | 'api' | string,
  context: Record<string, any> = {}
) {
  Sentry.captureException(error, {
    tags: {
      service,
      feature: context.feature || 'general',
    },
    extra: context,
  });
}

// Backward compatibility functions - deprecated, use captureError instead
export function captureAuthError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'auth', context);
}

export function captureDomainError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'domains', context);
}

export function captureAliasError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'aliases', context);
}

export function captureWebhookError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'webhooks', context);
}

export function captureBillingError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'billing', context);
}

export function captureApiError(error: Error, context: Record<string, any> = {}) {
  return captureError(error, 'api', context);
}

/**
 * Add breadcrumb for tracking user actions
 */
export function addBreadcrumb(message: string, category: string, data?: Record<string, any>) {
  Sentry.addBreadcrumb({
    message,
    category,
    level: 'info',
    data,
    timestamp: Date.now() / 1000,
  });
}

/**
 * Set user context for frontend errors
 */
export function setUser(user: { id: string; email: string }) {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.email,
  });
}

/**
 * Clear user context on logout
 */
export function clearUser() {
  Sentry.setUser(null);
}
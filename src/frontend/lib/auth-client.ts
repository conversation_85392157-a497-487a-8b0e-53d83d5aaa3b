import { createAuthClient } from 'better-auth/client';

// Create the BetterAuth client
export const authClient = createAuthClient({
  baseURL: import.meta.env.VITE_API_URL || window.location.origin,
});

// Helper types
export interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  emailVerified: boolean;
  image?: string | null;
}

export interface AuthSession {
  user: AuthUser;
  expiresAt: Date;
}

// Helper function for GitHub OAuth
export async function signInWithGitHub() {
  return authClient.signIn.social({
    provider: 'github',
    callbackURL: '/domains',
  });
}

// Helper function for magic link sign in  
export async function signInWithMagicLink(email: string) {
  // Try different BetterAuth magic link endpoints
  const endpoints = [
    '/api/auth/magic-link',
    '/api/auth/send-magic-link', 
    '/api/auth/magic-link/send',
    '/api/auth/sign-in/magic-link'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Trying magic link endpoint: ${endpoint}`);
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email,
          callbackURL: '/domains',
        }),
      });
      
      if (response.status !== 404) {
        console.log(`Success with endpoint: ${endpoint}, status: ${response.status}`);
        return response;
      }
    } catch (error) {
      console.error(`Failed with endpoint ${endpoint}:`, error);
    }
  }
  
  // If all endpoints fail, throw error
  throw new Error('No working magic link endpoint found');
}

// Helper function for password reset request
export async function requestPasswordReset(email: string) {
  try {
    const response = await authClient.requestPasswordReset({
      email,
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });
    
    if (response.error) {
      throw new Error(response.error.message || 'Failed to send password reset email');
    }
    
    return { ok: true, data: response.data };
  } catch (error) {
    console.error('Password reset request failed:', error);
    throw error;
  }
}

// Export commonly used methods
export const signIn = authClient.signIn;
export const signUp = authClient.signUp;  
export const signOut = authClient.signOut;
export const useSession = authClient.useSession;
export const resetPassword = authClient.resetPassword;

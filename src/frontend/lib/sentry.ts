import * as Sentry from '@sentry/vue';
import { Router } from 'vue-router';

export function initializeSentry(app: any, router: Router) {
  // Only initialize in production or if explicitly enabled
  const isProduction = import.meta.env.PROD;
  const sentryEnabled = import.meta.env.VITE_SENTRY_ENABLED === 'true';
  const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
  
  if (isProduction || sentryEnabled) {
    // Skip initialization if no DSN is provided
    if (!sentryDsn) {
      console.log('🐛 Error tracking DSN not configured, skipping initialization');
      return;
    }
    
    Sentry.init({
      app,
      dsn: sentryDsn,
      
      // Use package version as release
      release: `emailconnect-frontend@${import.meta.env.VITE_APP_VERSION || '1.0.0'}`,
      
      environment: import.meta.env.MODE || 'development',
      
      // Performance monitoring from env
      tracesSampleRate: Number(import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE || 0),
      
      // Tags to identify frontend errors
      initialScope: {
        tags: {
          component: 'frontend',
          runtime: 'browser',
          framework: 'vue3',
        },
      },
      
      // Vue Router integration
      integrations: [
        Sentry.browserTracingIntegration({ router }),
        Sentry.replayIntegration({
          // Only capture replays on errors
          maskAllText: true,
          blockAllMedia: true,
        }),
      ],
      
      // Capture replay only on errors (saves bandwidth)
      replaysSessionSampleRate: Number(import.meta.env.VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE || 0),
      replaysOnErrorSampleRate: Number(import.meta.env.VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE || 1.0),
      
      // Additional options
      beforeSend(event, hint) {
        // Filter out sensitive data
        if (event.request) {
          // Remove authorization headers
          if (event.request.headers) {
            delete event.request.headers.authorization;
            delete event.request.headers.cookie;
          }
        }
        
        // Don't send events in development unless explicitly enabled
        if (!isProduction && !sentryEnabled) {
          return null;
        }
        
        // Filter out known non-critical errors
        const error = hint.originalException;
        if (error && typeof error === 'object' && 'message' in error) {
          const message = error.message as string;
          
          // Filter out webhook testing errors (expected when users test endpoints)
          const webhookTestErrors = [
            'Request failed with status code 404',
            'Request failed with status code 503',
            'Request failed with status code 500',
            'Request failed with status code 400',
            'Network Error',
            'timeout',
            'ECONNREFUSED',
            'EHOSTUNREACH'
          ];
          
          // Check if this is from webhook testing context
          if (webhookTestErrors.some(ignored => message.includes(ignored))) {
            // Look for webhook test context in the stack trace or tags
            const isWebhookTest = event.tags?.feature === 'webhook-test' ||
                                event.extra?.context === 'webhook-test' ||
                                event.breadcrumbs?.some(b => b.category?.includes('webhook') || b.message?.includes('test'));
            
            if (isWebhookTest) {
              return null;
            }
          }
          
          // Filter out common non-critical errors
          const ignoredErrors = [
            'ResizeObserver loop limit exceeded',
            'ResizeObserver loop completed with undelivered notifications',
            'Non-Error promise rejection captured',
            'ChunkLoadError',
            'Loading chunk',
            'Loading CSS chunk'
          ];
          
          if (ignoredErrors.some(ignored => message.includes(ignored))) {
            return null;
          }
        }
        
        return event;
      },
    });
    
    console.log('🐛 Error tracking initialized for frontend');
  } else {
    console.log('🐛 Error tracking disabled (development mode)');
  }
}

// Helper function to capture exceptions with additional context
export function captureException(error: Error, context?: Record<string, any>) {
  if (import.meta.env.PROD || import.meta.env.VITE_SENTRY_ENABLED === 'true') {
    Sentry.captureException(error, {
      extra: context,
    });
  }
}

// Helper function to capture messages
export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) {
  if (import.meta.env.PROD || import.meta.env.VITE_SENTRY_ENABLED === 'true') {
    Sentry.captureMessage(message, {
      level,
      extra: context,
    });
  }
}

// Export Sentry for direct use if needed
export { Sentry };
/**
 * Router navigation guards for authentication and authorization
 */

import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuth } from '../composables/useAuth'

// Initialize auth composable (singleton)
const { checkAuth, isAuthenticated, clearAuth } = useAuth()

/**
 * Main authentication guard
 * Handles authentication checks for protected and public routes
 */
export const authGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const requiresAuth = to.meta.requiresAuth as boolean

  // Special handling for login/register pages to prevent loops
  if (to.path === '/login') {
    // If already on login/register, just proceed
    if (from.path === to.path) {
      next()
      return
    }
    
    // Check if user is authenticated
    try {
      const user = await checkAuth()
      if (user) {
        // User is authenticated, redirect to dashboard
        const intendedPath = sessionStorage.getItem('intended_path')
        sessionStorage.removeItem('intended_path')
        next(intendedPath || '/domains')
        return
      }
    } catch (error) {
      // Auth check failed, allow access to login/register
      console.error('Auth check error:', error)
    }
    
    // Allow access to login/register
    next()
    return
  }

  try {
    // Check authentication status
    const user = await checkAuth()
    const isUserAuthenticated = !!user

    if (requiresAuth && !isUserAuthenticated) {
      // Protected route but user not authenticated
      console.warn(`Access denied to ${to.path} - authentication required`)
      
      // Store intended destination for post-login redirect
      const intendedPath = to.fullPath
      sessionStorage.setItem('intended_path', intendedPath)
      
      next('/login')
      return
    }

    // All checks passed, proceed
    next()
    
  } catch (error) {
    console.error('Auth guard error:', error)
    
    if (requiresAuth) {
      // Clear potentially stale auth state
      clearAuth()
      
      // For protected routes, redirect to login on error
      next('/login')
    } else {
      // For public routes, allow access even if auth check fails
      next()
    }
  }
}

/**
 * Admin authorization guard
 * Additional check for admin-only routes
 */
export const adminGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  // Check if route requires admin access
  const requiresAdmin = to.meta.requiresAdmin as boolean
  
  // If route doesn't require admin, just proceed
  if (!requiresAdmin) {
    next()
    return
  }

  const { user } = useAuth()
  
  // Ensure user is authenticated (auth guard should run before this)
  if (!user.value) {
    // Don't redirect here - let auth guard handle it
    next()
    return
  }

  try {
    // Check admin permissions via BetterAuth status API
    const response = await fetch('/api/auth-status', {
      credentials: 'include'
    })
    
    if (response.ok) {
      const data = await response.json()
      if (data?.user?.role === 'admin') {
        // User is admin, allow access
        next()
        return
      }
    }
    
    // User is not admin, show 404
    console.warn(`Access denied to ${to.path} - admin privileges required`)
    next('/404')
    
  } catch (error) {
    console.error('Admin guard error:', error)
    
    // On error, also block access with 404
    next('/404')
  }
}

/**
 * Development guard - only allow access in development
 */
export const devOnlyGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  if (import.meta.env.DEV) {
    next()
  } else {
    next('/domains')
  }
}

/**
 * Plan-based access guard
 * Restrict certain features based on user plan
 */
export const planGuard = (requiredPlan: 'pro' | 'enterprise') => {
  return async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) => {
    const { user, isPro } = useAuth()
    
    if (!user.value) {
      next('/login')
      return
    }

    const userPlan = user.value.planType || 'free'
    
    // Check plan access
    if (requiredPlan === 'pro' && !isPro.value) {
      // Redirect to upgrade page or show upgrade modal
      next({ path: '/settings', hash: '#billing' })
      return
    }
    
    if (requiredPlan === 'enterprise' && userPlan !== 'enterprise') {
      next({ path: '/settings', hash: '#billing' })
      return
    }
    
    next()
  }
}
// Legacy JWT utilities disabled. BetterAuth-only.
// Keeping type for backward compatibility in tests/types.
export interface UserTokenPayload {
  userId: string;
  email: string;
  iat?: number;
  exp?: number;
}

export class JWTUtil {
  static verifyToken(_token: string): { success: boolean; payload?: UserTokenPayload; error?: string } {
    return { success: false, error: 'JWT authentication is disabled. Use BetterAuth sessions.' };
  }
  static generateToken(): string {
    throw new Error('JWT authentication is disabled. Use BetterAuth sessions.');
  }
}

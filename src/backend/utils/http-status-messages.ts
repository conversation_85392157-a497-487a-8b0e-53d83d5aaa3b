export interface HttpStatusInfo {
  code: number;
  text: string;
  userMessage: string;
  isRetryable: boolean;
}

export const httpStatusMessages: Record<number, HttpStatusInfo> = {
  200: {
    code: 200,
    text: 'OK',
    userMessage: 'Request was successful',
    isRetryable: false
  },
  201: {
    code: 201,
    text: 'Created',
    userMessage: 'Resource was created successfully',
    isRetryable: false
  },
  204: {
    code: 204,
    text: 'No Content',
    userMessage: 'Request was successful with no content to return',
    isRetryable: false
  },
  400: {
    code: 400,
    text: 'Bad Request',
    userMessage: 'The webhook endpoint rejected the request due to invalid data format',
    isRetryable: false
  },
  401: {
    code: 401,
    text: 'Unauthorized',
    userMessage: 'The webhook endpoint requires authentication or the credentials were invalid',
    isRetryable: false
  },
  403: {
    code: 403,
    text: 'Forbidden',
    userMessage: 'The webhook endpoint denied access to this resource',
    isRetryable: false
  },
  404: {
    code: 404,
    text: 'Not Found',
    userMessage: 'The webhook endpoint URL does not exist',
    isRetryable: false
  },
  405: {
    code: 405,
    text: 'Method Not Allowed',
    userMessage: 'The webhook endpoint does not accept POST requests',
    isRetryable: false
  },
  406: {
    code: 406,
    text: 'Not Acceptable',
    userMessage: 'The webhook endpoint cannot process the content type',
    isRetryable: false
  },
  408: {
    code: 408,
    text: 'Request Timeout',
    userMessage: 'The webhook endpoint took too long to respond',
    isRetryable: true
  },
  409: {
    code: 409,
    text: 'Conflict',
    userMessage: 'The request conflicts with the current state of the webhook endpoint',
    isRetryable: false
  },
  410: {
    code: 410,
    text: 'Gone',
    userMessage: 'The webhook endpoint has been permanently removed',
    isRetryable: false
  },
  413: {
    code: 413,
    text: 'Payload Too Large',
    userMessage: 'The email or attachments exceed the size limit configured for your webhook endpoint',
    isRetryable: false
  },
  415: {
    code: 415,
    text: 'Unsupported Media Type',
    userMessage: 'The webhook endpoint does not support JSON content',
    isRetryable: false
  },
  422: {
    code: 422,
    text: 'Unprocessable Entity',
    userMessage: 'The webhook endpoint understood the request but could not process it',
    isRetryable: false
  },
  429: {
    code: 429,
    text: 'Too Many Requests',
    userMessage: 'The webhook endpoint is rate limiting requests',
    isRetryable: true
  },
  500: {
    code: 500,
    text: 'Internal Server Error',
    userMessage: 'The webhook endpoint encountered an internal error',
    isRetryable: true
  },
  502: {
    code: 502,
    text: 'Bad Gateway',
    userMessage: 'The webhook endpoint\'s upstream server is unavailable',
    isRetryable: true
  },
  503: {
    code: 503,
    text: 'Service Unavailable',
    userMessage: 'The webhook endpoint is temporarily unavailable',
    isRetryable: true
  },
  504: {
    code: 504,
    text: 'Gateway Timeout',
    userMessage: 'The webhook endpoint\'s upstream server timed out',
    isRetryable: true
  }
};

export function getHttpStatusInfo(statusCode: number): HttpStatusInfo {
  return httpStatusMessages[statusCode] || {
    code: statusCode,
    text: 'Unknown Error',
    userMessage: `The webhook endpoint returned an unexpected status code (${statusCode})`,
    isRetryable: statusCode >= 500 && statusCode < 600
  };
}

export function isRetryableHttpStatus(statusCode: number): boolean {
  const info = getHttpStatusInfo(statusCode);
  return info.isRetryable;
}

export function formatHttpError(statusCode: number, includeHint: boolean = true): string {
  const info = getHttpStatusInfo(statusCode);
  let message = `${info.code} ${info.text}: ${info.userMessage}`;
  
  if (includeHint && statusCode === 413) {
    message += '. You can adjust your size limits in the Storage settings.';
  }
  
  return message;
}
import { FastifyPluginAsync } from 'fastify';

// User management routes
import { userWebhooksRoutes } from '../routes/user-webhooks.routes.js';
import { userAliasRoutes } from '../routes/user-aliases.routes.js';
import { webhookAliasRoutes } from '../routes/user-webhook-alias.routes.js';
import { userSettingsRoutes } from '../routes/user-settings.routes.js';
import { profileRoutes } from '../routes/profile.js';
import { dashboardRoutes } from '../routes/dashboard.js';

/**
 * User management plugin
 * Handles user dashboard, webhooks, aliases, settings, and profile management
 */
export const userManagementPlugin: FastifyPluginAsync = async (fastify) => {
  // User dashboard and metrics
  await fastify.register(dashboardRoutes, { prefix: '/api' });
  
  // User webhook management
  await fastify.register(userWebhooksRoutes, { prefix: '/api' });
  
  // User alias management
  await fastify.register(userAliasRoutes, { prefix: '/api' });
  await fastify.register(webhookAliasRoutes, { prefix: '' });
  
  // User settings and profile
  await fastify.register(userSettingsRoutes, { prefix: '/api' });
  await fastify.register(profileRoutes, { prefix: '/api' });
};
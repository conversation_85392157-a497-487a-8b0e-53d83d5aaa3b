import { FastifyPluginAsync } from 'fastify';

// Core email processing routes
import { emailRoutes } from '../routes/email.js';
import { webhookRoutes } from '../routes/webhook.js';
import { domainsRoutes } from '../routes/domains.routes.js';
import { attachmentRoutes } from '../routes/attachments.routes.js';
import { scalewayEmailWebhooksRoutes } from '../routes/scaleway-email-webhooks.routes.js';

/**
 * Core email processing plugin
 * Handles email ingestion, processing, domain management, and webhooks
 */
export const coreEmailPlugin: FastifyPluginAsync = async (fastify) => {
  // Email processing and ingestion
  await fastify.register(emailRoutes, { prefix: '/api/email' });
  
  // Webhook delivery and management (admin)
  await fastify.register(webhookRoutes, { prefix: '/admin/api' });
  
  // Domain verification and management
  await fastify.register(domainsRoutes, { prefix: '/api' });
  
  // Email attachment handling (public endpoint)
  await fastify.register(attachmentRoutes, { prefix: '' });
  
  // External email service webhooks (Scaleway)
  await fastify.register(scalewayEmailWebhooksRoutes, { prefix: '/api/webhooks' });
};
import { FastifyPluginAsync } from 'fastify';

// Billing and payment routes
import { billingRoutes } from '../routes/billing.js';
import { subscriptionsRoutes } from '../routes/subscriptions.routes.js';
import { invoicesRoutes } from '../routes/invoices.routes.js';
import { mollieWebhookRoutes } from '../routes/mollie-webhooks.routes.js';
import { paymentsRoutes } from '../routes/payments.routes.js';

/**
 * Billing and payments plugin
 * Handles subscriptions, invoices, payments, and payment provider webhooks
 */
export const billingPlugin: FastifyPluginAsync = async (fastify) => {
  // Core billing functionality
  await fastify.register(billingRoutes, { prefix: '/api/billing' });
  
  // Subscription management
  await fastify.register(subscriptionsRoutes, { prefix: '/api' });
  
  // Invoice management
  await fastify.register(invoicesRoutes, { prefix: '/api' });
  
  // Payment processing
  await fastify.register(paymentsRoutes, { prefix: '/api/payments' });
  
  // Payment provider webhooks (<PERSON><PERSON>)
  await fastify.register(mollieWebhookRoutes, { prefix: '/api/webhooks/mollie' });
  
  // Public invoice download alias (non-API path)
  await fastify.register((await import('../routes/invoices-public.routes.js')).invoicesPublicRoutes, { prefix: '' });
};
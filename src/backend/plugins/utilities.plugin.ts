import { FastifyPluginAsync } from 'fastify';

// Utility routes
import { templateRoutes } from '../routes/templates.js';
import { logsRoutes } from '../routes/logs.routes.js';
import { apiKeyRoutes } from '../routes/api-keys.routes.js';
import { notificationsRoutes } from '../routes/notifications.routes.js';
import { publicRoutes } from '../routes/public.js';
import { cspRoutes } from '../routes/csp.routes.js';
import { queueHealthRoutes } from '../routes/queue-health.routes.js';

/**
 * Utilities plugin
 * Handles templates, logging, API keys, notifications, public endpoints,
 * CSP reporting, and queue health monitoring
 */
export const utilitiesPlugin: FastifyPluginAsync = async (fastify) => {
  // Template management
  await fastify.register(templateRoutes, { prefix: '/api' });
  
  // Logging and monitoring
  await fastify.register(logsRoutes, { prefix: '/api' });
  await fastify.register(queueHealthRoutes, { prefix: '/api/admin' });
  
  // API key management
  await fastify.register(apiKeyRoutes, { prefix: '/api' });
  
  // Notifications system
  await fastify.register(notificationsRoutes, { prefix: '/api' });
  
  // Public endpoints (landing page, etc.)
  await fastify.register(publicRoutes, { prefix: '/api/public' });
  await fastify.register((await import('../routes/notion-public.routes.js')).notionPublicRoutes, { prefix: '/api/public' });
  
  // Content Security Policy reporting
  await fastify.register(cspRoutes, { prefix: '' });
};
import { FastifyPluginAsync } from 'fastify';

// Testing and development routes
import { webhookTestRoutes } from '../routes/webhooktest.routes.js';
import { oauthRoutes } from '../routes/oauth.routes.js';
import { testErrorRoutes } from '../routes/test-error.js';

/**
 * Testing and development plugin
 * Handles webhook testing, OAuth integration testing, and error testing
 */
export const testingPlugin: FastifyPluginAsync = async (fastify) => {
  // Webhook testing functionality
  await fastify.register(webhookTestRoutes, { prefix: '/api/webhooktest' });
  
  // OAuth integration testing
  await fastify.register(oauthRoutes, { prefix: '/oauth' });
  
  // Error handling testing
  await fastify.register(testErrorRoutes);
};
import { FastifyPluginAsync } from 'fastify';

// Authentication routes
import { betterAuthRoutes } from '../routes/better-auth.routes.js';
import { accountLinkingRoutes } from '../routes/account-linking.routes.js';

/**
 * Authentication plugin
 * Handles user authentication and account linking
 */
export const authPlugin: FastifyPluginAsync = async (fastify) => {
  // BetterAuth authentication system
  await fastify.register(betterAuthRoutes, { prefix: '/' });
  
  // Account linking functionality
  await fastify.register(accountLinkingRoutes, { prefix: '/api/account-linking' });
};
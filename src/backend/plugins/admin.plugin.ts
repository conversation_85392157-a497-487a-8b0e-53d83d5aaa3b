import { FastifyPluginAsync } from 'fastify';

// Admin routes
import { adminUserManagementRoutes } from '../routes/admin/user-management.routes.js';
import { betterAuthAdminRoutes } from '../routes/admin/better-auth-admin.routes.js';
import { adminGrandfatheringRoutes } from '../routes/admin/grandfathering.routes.js';
import { adminTrialsRoutes } from '../routes/admin-trials.routes.js';
import { adminSubscriptionsRoutes } from '../routes/admin/subscription-admin.routes.js';
import { adminImpersonationRoutes } from '../routes/admin/impersonation.routes.js';
import { auditLogRoutes } from '../routes/audit-logs.routes.js';

/**
 * Admin routes plugin
 * Handles all administrative functionality including user management,
 * grandfathering, trials, subscriptions, and audit logging
 */
export const adminPlugin: FastifyPluginAsync = async (fastify) => {
  // User management and impersonation
  await fastify.register(adminUserManagementRoutes, { prefix: '/api/admin' });
  await fastify.register(betterAuthAdminRoutes, { prefix: '/api/admin/better-auth' });
  await fastify.register(adminImpersonationRoutes, { prefix: '/api/admin/impersonation' });
  
  // Plan and subscription management
  await fastify.register(adminGrandfatheringRoutes, { prefix: '/api/admin/grandfathering' });
  await fastify.register(adminTrialsRoutes, { prefix: '/api' });
  await fastify.register(adminSubscriptionsRoutes, { prefix: '/api/admin/subscriptions' });
  
  // Audit and logging
  await fastify.register(auditLogRoutes);
  await fastify.register((await import('../routes/admin/audit-logs-admin.routes.js')).adminAuditLogsRoutes, { prefix: '/api/admin' });
};
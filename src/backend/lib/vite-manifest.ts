import { logger } from '../utils/logger.js';
import fs from 'fs'
import path from 'path'

interface ViteManifestEntry {
  file: string
  name?: string
  src?: string
  isEntry?: boolean
  imports?: string[]
  css?: string[]
}

interface ViteManifest {
  [key: string]: ViteManifestEntry
}

let manifestCache: ViteManifest | null = null

export function getViteManifest(): ViteManifest {
  if (manifestCache) {
    return manifestCache
  }

  try {
    const manifestPath = path.join(process.cwd(), 'dist/frontend/.vite/manifest.json')
    const manifestContent = fs.readFileSync(manifestPath, 'utf-8')
    manifestCache = JSON.parse(manifestContent)
    return manifestCache!
  } catch (error) {
    logger.warn('Vite manifest not found, using fallback paths')
    return {}
  }
}

export function getViteAssetPath(entryName: string): string {
  const manifest = getViteManifest()
  const entryKey = `src/frontend/${entryName}.ts`
  const entry = manifest[entryKey]

  if (entry && entry.file) {
    return `/${entry.file}`
  }

  // Fallback for development or when manifest is not available
  return `/assets/${entryName}.js`
}

export function getViteAssetCss(entryName: string): string[] {
  const manifest = getViteManifest()
  const entryKey = `src/frontend/${entryName}.ts`
  const entry = manifest[entryKey]

  if (!entry) {
    return []
  }

  const cssFiles: string[] = []

  // Add direct CSS files from the entry
  if (entry.css) {
    cssFiles.push(...entry.css)
  }

  // Add CSS files from imported chunks
  if (entry.imports) {
    for (const importKey of entry.imports) {
      const importedEntry = manifest[importKey]
      if (importedEntry && importedEntry.css) {
        cssFiles.push(...importedEntry.css)
      }
    }
  }

  return cssFiles.map(cssFile => `/${cssFile}`)
}

// Handlebars helpers
export const viteHelpers = {
  viteAsset: (entryName: string) => getViteAssetPath(entryName),
  viteAssetCss: (entryName: string) => getViteAssetCss(entryName)
}

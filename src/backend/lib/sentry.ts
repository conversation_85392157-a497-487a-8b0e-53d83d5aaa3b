import * as Sentry from '@sentry/node';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';

export function initializeSentry() {
  // Only initialize in production or if explicitly enabled
  if (env.NODE_ENV === 'production' || env.SENTRY_ENABLED === 'true') {
    // Skip initialization if no DSN is provided
    if (!env.SENTRY_DSN) {
      logger.info('🐛 Error tracking DSN not configured, skipping initialization');
      return;
    }
    
    Sentry.init({
      dsn: env.SENTRY_DSN,
      
      // Use package version as release
      release: `emailconnect-backend@${process.env.npm_package_version || '1.0.0'}`,
      
      environment: env.NODE_ENV || 'development',
      
      // Performance monitoring from env
      tracesSampleRate: env.SENTRY_TRACES_SAMPLE_RATE,
      
      // Tags to identify backend errors
      initialScope: {
        tags: {
          component: 'backend',
          runtime: 'node',
        },
      },
      
      // Integrations
      integrations: [
        // Default integrations are automatically included
      ],
      
      // Additional options
      beforeSend(event, hint) {
        // Filter out sensitive data
        if (event.request) {
          // Remove authorization headers
          if (event.request.headers) {
            delete event.request.headers.authorization;
            delete event.request.headers.cookie;
          }
          // Remove sensitive data from request body
          if (event.request.data) {
            const sensitiveFields = ['password', 'token', 'api_key', 'secret'];
            sensitiveFields.forEach(field => {
              if (event.request.data[field]) {
                event.request.data[field] = '[FILTERED]';
              }
            });
          }
        }
        
        // Don't send events in development unless explicitly enabled
        if (env.NODE_ENV === 'development' && env.SENTRY_ENABLED !== 'true') {
          return null;
        }
        
        // Filter out expected user workflow errors
        const error = hint.originalException;
        if (error && typeof error === 'object' && 'message' in error) {
          const message = error.message as string;
          
          // DNS verification errors (expected when users haven't set up records yet)
          const dnsErrors = [
            'queryTxt ENODATA',
            'queryTxt ENOTFOUND',
            'queryTxt ETIMEDOUT',
            'DNS_PROBE_FINISHED_NXDOMAIN'
          ];
          
          // Webhook testing errors (expected when users test endpoints)
          const webhookTestErrors = [
            'Request failed with status code 404',
            'Request failed with status code 503',
            'Request failed with status code 500',
            'Request failed with status code 400',
            'ECONNREFUSED',
            'EHOSTUNREACH',
            'ETIMEDOUT'
          ];
          
          // Filter out DNS errors only if they're from domain verification
          if (dnsErrors.some(ignored => message.includes(ignored))) {
            // Check if this is from domain verification context
            const isDomainVerification = event.tags?.service === 'domains' && 
                                       event.tags?.feature === 'verification';
            if (isDomainVerification) {
              return null;
            }
          }
          
          // Filter out webhook test errors only if they're from testing context
          if (webhookTestErrors.some(ignored => message.includes(ignored))) {
            // Check if this is from webhook testing context
            const isWebhookTest = event.tags?.service === 'webhooks' && 
                                (event.tags?.feature === 'testing' || 
                                 event.extra?.isUserTest === true);
            if (isWebhookTest) {
              return null;
            }
          }
          
          // Filter out other common non-critical errors
          const generalIgnoredErrors = [
            'ResizeObserver loop limit exceeded',
            'ResizeObserver loop completed with undelivered notifications',
            'Non-Error promise rejection captured',
          ];
          
          if (generalIgnoredErrors.some(ignored => message.includes(ignored))) {
            return null;
          }
        }
        
        return event;
      },
    });
    
    logger.info('🐛 Error tracking initialized (GlitchTip/Sentry)');
  } else {
    logger.info('🐛 Error tracking disabled (development mode)');
  }
}

// Helper function to capture exceptions with additional context
export function captureException(error: Error, context?: Record<string, any>) {
  if (env.NODE_ENV === 'production' || env.SENTRY_ENABLED === 'true') {
    Sentry.captureException(error, {
      extra: context,
    });
  }
}

// Helper function to capture messages
export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) {
  if (env.NODE_ENV === 'production' || env.SENTRY_ENABLED === 'true') {
    Sentry.captureMessage(message, {
      level,
      extra: context,
    });
  }
}

// Export Sentry for direct use if needed
export { Sentry };
import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { magicLink } from 'better-auth/plugins/magic-link';
import { twoFactor } from 'better-auth/plugins/two-factor';
import { admin } from 'better-auth/plugins/admin';
import { prisma } from './prisma.js';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';
import { scalewayEmailService } from '../services/notifications/scaleway-email.service.js';
import { EmailTemplateService } from '../services/notifications/email-template.service.js';
import bcrypt from 'bcrypt';
import { createId } from '@paralleldrive/cuid2';

// Initialize BetterAuth with comprehensive configuration
export const auth: ReturnType<typeof betterAuth> = betterAuth({
  database: prismaAdapter(prisma, {
    provider: 'postgresql',
  }),
  
  // Basic configuration
  baseURL: env.BETTER_AUTH_URL || env.URL,
  secret: env.BETTER_AUTH_SECRET,
  
  // Email/Password authentication
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Keep same as current behavior
    minPasswordLength: 8,
    password: {
      // Use bcrypt for password hashing (same as existing system)
      hash: async (password: string) => {
        const saltRounds = 10;
        return await bcrypt.hash(password, saltRounds);
      },
      verify: async ({ hash, password }: { hash: string; password: string }) => {
        return await bcrypt.compare(password, hash);
      }
    },
    sendResetPassword: async ({ user, url }) => {
      try {
        logger.info(`Sending password reset email to ${user.email}`);
        
        const { html, text } = EmailTemplateService.generateEmail({
          variables: {
            firstName: EmailTemplateService.extractFirstName(user.name, user.email),
            title: 'Reset your password',
            body: `
              <p>You requested to reset your password for your EmailConnect account.</p>
              <p>This link will expire in 1 hour for security reasons.</p>
              <p>If you didn't request this password reset, you can safely ignore this email.</p>
            `,
            cta: {
              text: 'Reset password',
              url: url,
              style: 'primary'
            }
          }
        });
        
        await scalewayEmailService.sendEmail({
          to: [{ email: user.email, name: user.name || user.email }],
          subject: 'Reset your EmailConnect password',
          html,
          text
        });
        
        // Log audit event
        logger.info({
          event: 'auth.password_reset_sent',
          userId: user.id,
          userEmail: user.email
        }, 'Password reset email sent');
      } catch (error) {
        logger.error({ error }, 'Failed to send password reset email');
        throw error;
      }
    },
    onPasswordReset: async ({ user }) => {
      logger.info(`Password reset completed for user ${user.email}`);
      // Could add additional logic here like:
      // - Invalidate other sessions
      // - Log security event
      // - Send confirmation email
    },
  },
  
  // Social providers
  socialProviders: {
    github: {
      clientId: env.GITHUB_CLIENT_ID || '',
      clientSecret: env.GITHUB_CLIENT_SECRET || '',
      redirectURI: `${env.BETTER_AUTH_URL || env.URL}/api/auth/callback/github`,
      scope: ['user:email'], // Request email access explicitly
      enabled: !!env.GITHUB_CLIENT_ID,
      mapProfileToUser: (profile: any) => {
        logger.info(`GitHub profile received for user ${profile.login || profile.id}`);
        
        // GitHub might not provide email if user has privacy settings enabled
        if (!profile.email) {
          logger.warn(`GitHub user ${profile.login} (${profile.id}) has no public email`);
          // You could throw an error here to require email:
          // throw new Error('Email address is required. Please make your GitHub email public or use a different login method.');
        }
        
        return {
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
        };
      },
    },
  },
  
  // Session configuration
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update session if older than 1 day
    cookieName: 'better-auth.session',
  },
  
  // Plugins
  plugins: [
    // Magic link authentication
    magicLink({
      sendMagicLink: async ({ email, url }) => {
        try {
          logger.info(`Sending magic link to ${email}`);
          
          const { html, text } = EmailTemplateService.generateEmail({
            variables: {
              firstName: EmailTemplateService.extractFirstName(undefined, email),
              title: 'Sign in to your account',
              body: `
                <p>Click the link below to sign in to your EmailConnect account.</p>
                <p>This link will expire in 10 minutes for security reasons.</p>
                <p>If you didn't request this, you can safely ignore this email.</p>
              `,
              cta: {
                text: 'Sign In',
                url: url,
                style: 'primary'
              }
            }
          });
          
          await scalewayEmailService.sendEmail({
            to: [{ email, name: email }],
            subject: 'Sign in to EmailConnect',
            html,
            text
          });
          
          // Log audit event
          logger.info({
            event: 'auth.magic_link_sent',
            userEmail: email
          }, 'Magic link email sent');
        } catch (error) {
          logger.error({ error }, 'Failed to send magic link email');
          throw error;
        }
      },
      expiresIn: 60 * 10, // 10 minutes
    }),
    
    // Two-factor authentication
    twoFactor({
      issuer: 'EmailConnect',
    }),
    
    // Admin features (user impersonation, management)
    admin({
      impersonationSessionDuration: 60 * 60, // 1 hour
      adminRoles: ['admin'], // Users with role='admin' can impersonate
    }),
    
    // Generic OAuth for potential future providers (removed for now to avoid initialization error)
  ],
  
  // Email configuration for password reset
  emailVerification: {
    enabled: false,
    sendVerificationEmail: async ({ user, url }) => {
      try {
        logger.info(`Sending verification email to ${user.email}`);
        
        const { html, text } = EmailTemplateService.generateEmail({
          variables: {
            firstName: EmailTemplateService.extractFirstName(user.name, user.email),
            title: 'Verify your email address',
            body: `
              <p>Welcome to EmailConnect! Please verify your email address to activate your account.</p>
              <p>This link will expire in 24 hours.</p>
            `,
            cta: {
              text: 'Verify email',
              url: url,
              style: 'success'
            },
            closing: 'Welcome aboard!'
          }
        });
        
        await scalewayEmailService.sendEmail({
          to: [{ email: user.email, name: user.name || user.email }],
          subject: 'Verify your EmailConnect account',
          html,
          text
        });
        
        // Log audit event
        logger.info({
          event: 'auth.verification_email_sent',
          userId: user.id,
          userEmail: user.email
        }, 'Verification email sent');
      } catch (error) {
        logger.error({ error }, 'Failed to send verification email');
        throw error;
      }
    },
    autoSignInAfterVerification: true,
  },
  
  // Password reset configuration
  forgetPassword: {
    enabled: true,
    sendResetPasswordEmail: async ({ user, url }) => {
      try {
        logger.info(`Sending password reset email to ${user.email}`);
        
        const { html, text } = EmailTemplateService.generateEmail({
          variables: {
            firstName: EmailTemplateService.extractFirstName(user.name, user.email),
            title: 'Reset your password',
            body: `
              <p>You requested to reset your password for your EmailConnect account.</p>
              <p>This link will expire in 1 hour for security reasons.</p>
              <p>If you didn't request this, you can safely ignore this email.</p>
            `,
            cta: {
              text: 'Reset password',
              url: url,
              style: 'primary'
            }
          }
        });
        
        await scalewayEmailService.sendEmail({
          to: [{ email: user.email, name: user.name || user.email }],
          subject: 'Reset your EmailConnect password',
          html,
          text
        });
        
        // Log audit event
        logger.info({
          event: 'auth.forget_password_email_sent',
          userId: user.id,
          userEmail: user.email
        }, 'Forget password email sent');
      } catch (error) {
        logger.error({ error }, 'Failed to send password reset email');
        throw error;
      }
    },
    expiresIn: 60 * 60, // 1 hour
  },
  
  // Advanced configuration
  advanced: {
    database: {
      generateId: () => {
        // Use CUID2 to match existing Prisma schema pattern
        return createId();
      },
    },
    cookiePrefix: 'ec', // EmailConnect prefix
    defaultCookieAttributes: {
      sameSite: 'lax',
      secure: env.NODE_ENV === 'production',
      httpOnly: true,
    },
  },
  
  // Rate limiting configuration
  rateLimit: {
    enabled: true,
    window: 60, // 1 minute
    max: 30, // 30 requests per minute (increased for 2FA setup flows)
  },
  
  // Trusted origins for CORS
  trustedOrigins: env.ALLOWED_ORIGINS
    ? env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()).filter(Boolean)
    : [
        env.URL,
        env.FRONTEND_URL,
        'http://localhost:3000', // Local development fallback
      ].filter(Boolean),
});

// Export auth for frontend use (removed .client as it doesn't exist)
// Frontend will use createAuthClient separately

// Helper function to verify if user has 2FA enabled
export async function userHas2FA(userId: string): Promise<boolean> {
  try {
    const result = await prisma.twoFactor.findUnique({
      where: { userId },
    });
    return !!result;
  } catch (error) {
    logger.error({ error, userId }, 'Failed to check 2FA status');
    return false;
  }
}

// Helper function to check if user is admin
export async function isUserAdmin(userId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });
  // Only trust role flag for admin
  return user?.role === 'admin';
}

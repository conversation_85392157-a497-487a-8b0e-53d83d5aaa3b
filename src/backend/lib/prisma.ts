import { PrismaClient } from '@prisma/client';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';

// Database connection pool configuration
const DATABASE_CONFIG = {
  // Connection pool settings
  connectionLimit: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '20'),

  // Query timeout settings
  queryTimeout: parseInt(process.env.DATABASE_QUERY_TIMEOUT || '30000'), // 30 seconds

  // Connection timeout settings
  connectTimeout: parseInt(process.env.DATABASE_CONNECT_TIMEOUT || '10000'), // 10 seconds

  // Pool timeout settings
  poolTimeout: parseInt(process.env.DATABASE_POOL_TIMEOUT || '10000'), // 10 seconds

  // Enable query logging in development
  logQueries: process.env.NODE_ENV === 'development' || process.env.DATABASE_LOG_QUERIES === 'true',

  // Enable slow query logging
  logSlowQueries: process.env.DATABASE_LOG_SLOW_QUERIES === 'true',
  slowQueryThreshold: parseInt(process.env.DATABASE_SLOW_QUERY_THRESHOLD || '1000'), // 1 second
};

// Global variable to store the Prisma client instance
let client: PrismaClient | null = null;

/**
 * Get or create a singleton Prisma client instance
 * This ensures we don't create multiple connections in serverless environments
 */
export function getPrismaClient(): PrismaClient {
  if (!client) {
    logger.info('Initializing Prisma client with connection pooling...');

    // Build database URL with connection pooling parameters
    const databaseUrl = new URL(env.DATABASE_URL);

    // Add connection pooling parameters to the URL
    databaseUrl.searchParams.set('connection_limit', DATABASE_CONFIG.connectionLimit.toString());
    databaseUrl.searchParams.set('pool_timeout', Math.floor(DATABASE_CONFIG.poolTimeout / 1000).toString());
    databaseUrl.searchParams.set('connect_timeout', Math.floor(DATABASE_CONFIG.connectTimeout / 1000).toString());

    // Configure logging based on environment and settings
    const logConfig = [];
    if (DATABASE_CONFIG.logQueries) {
      logConfig.push('query');
    }
    if (DATABASE_CONFIG.logSlowQueries) {
      logConfig.push('info', 'warn');
    }
    logConfig.push('error');

    client = new PrismaClient({
      datasources: {
        db: {
          url: databaseUrl.toString(),
        },
      },
      log: logConfig as any,
      errorFormat: 'pretty',
    });

    // Query performance monitoring is handled via Prisma's built-in logging
    // Slow query detection is available through the database performance service

    // Add connection monitoring
    client.$on('query', (e) => {
      if (DATABASE_CONFIG.logQueries) {
        logger.debug({
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
          target: e.target,
        }, 'Database query executed');
      }
    });

    logger.info({
      connectionLimit: DATABASE_CONFIG.connectionLimit,
      queryTimeout: DATABASE_CONFIG.queryTimeout,
      connectTimeout: DATABASE_CONFIG.connectTimeout,
      poolTimeout: DATABASE_CONFIG.poolTimeout,
      logQueries: DATABASE_CONFIG.logQueries,
      logSlowQueries: DATABASE_CONFIG.logSlowQueries,
    }, 'Prisma client initialized successfully with enhanced configuration');
  }

  return client;
}

/**
 * Connect to the database
 * Call this during application startup
 */
export async function connectDatabase(): Promise<void> {
  try {
    const prismaClient = getPrismaClient();
    await prismaClient.$connect();
    logger.info('Database connected successfully');
  } catch (error) {
    logger.error({ error: error.message }, 'Failed to connect to database');
    throw error;
  }
}

/**
 * Disconnect from the database
 * Call this during application shutdown
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    if (client) {
      await client.$disconnect();
      client = null;
      logger.info('Database disconnected successfully');
    }
  } catch (error) {
    logger.error({ error: error.message }, 'Error disconnecting from database');
    throw error;
  }
}

/**
 * Check database connection health
 * Used for health checks and monitoring
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const prismaClient = getPrismaClient();
    await prismaClient.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error({ error: error.message }, 'Database health check failed');
    return false;
  }
}

/**
 * Create Prisma client with extensions for user creation notifications
 */
function createExtendedPrismaClient() {
  const baseClient = getPrismaClient();
  
  // Extend the client with custom behavior for user creation
  const extendedClient = baseClient.$extends({
    query: {
      user: {
        async create({ args, query }) {
          // Force emailVerified=true on create (verified by default)
          if (args.data) {
            args.data.emailVerified = true;
          }
          
          // Execute the create operation
          const result = await query(args);
          
          // Send welcome email to new user
          try {
            const { userNotifications } = await import('../services/notifications/index.js');
            await userNotifications.registered(result.id, {
              email: result.email,
              name: result.name
            });
            logger.info({ userId: result.id, email: result.email }, 'Welcome email sent to new user');
          } catch (err) {
            logger.warn({ err, userId: result.id }, 'Failed to send welcome email to new user');
          }

          // Emit Telegram admin notification (no PII)
          try {
            const { notificationEvents } = await import('../services/notifications/notification-events.service.js');
            await notificationEvents.notifyChannel('telegram', 'user.registered', {
              type: 'user.registered',
              userId: result.id,
              title: 'User registered',
              message: 'New user registration',
              category: 'SYSTEM',
              priority: 'MEDIUM'
            } as any);
            logger.info({ userId: result.id }, 'Telegram notification sent for new user registration');
          } catch (err) {
            logger.warn({ err }, 'Failed to emit Telegram on user create');
          }
          
          // Create audit log for user.created
          try {
            await baseClient.auditLog.create({
              data: {
                action: 'user.created',
                resourceType: 'user',
                resourceId: result.id,
                metadata: {
                  userId: result.id,
                  userEmail: result.email,
                  name: result.name || null
                },
                expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
              }
            });
          } catch (err) {
            logger.warn({ err }, 'Failed to create user.created audit log');
          }

          // Onboarding: optionally auto-create a default WebhookTest webhook for new users
          // Controlled by AUTO_CREATE_ONBOARDING_WEBHOOK; defaults to enabled outside tests, disabled in tests
          try {
            const autoCreateEnabled = (
              process.env.AUTO_CREATE_ONBOARDING_WEBHOOK ?? (env.NODE_ENV !== 'test' ? 'true' : 'false')
            ) === 'true';

            if (autoCreateEnabled) {
              const existing = await baseClient.webhook.findFirst({ where: { userId: result.id } });
              if (!existing) {
                try {
                  const { WebhookTestIntegrationService } = await import('../services/webhooktest-integration.service.js');
                  const endpoint = await WebhookTestIntegrationService.createWebhookTestEndpoint(result.id, result.email);
                  await baseClient.webhook.create({
                    data: {
                      userId: result.id,
                      name: 'Test webhook',
                      url: endpoint.webhookEndpoint,
                      description: 'Auto-created during onboarding',
                      active: true,
                      verified: true,
                      webhookSecret: null,
                      customHeaders: null
                    }
                  });
                } catch (integrationErr) {
                  logger.warn({ integrationErr }, 'WebhookTest integration failed or not configured; skipping onboarding webhook');
                }
              }
            }
          } catch (err) {
            logger.warn({ err }, 'Onboarding: failed to auto-create default webhook');
          }
          
          return result;
        }
      }
    }
  });
  
  return extendedClient;
}

/**
 * Export the client instance for direct use
 * This is the main export that other modules should use
 */
export const prisma = createExtendedPrismaClient();

// Default export for convenience
export default prisma;

import { Sentry } from './sentry.js';

/**
 * Service-specific error capture helpers with contextual tags
 */

export function captureAuthError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'auth',
      feature: context.feature || 'general', // login, register, jwt, api-key
    },
    extra: context,
  });
}

export function captureDomainError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'domains',
      feature: context.feature || 'general', // verification, crud, dns
    },
    extra: context,
  });
}

export function captureAliasError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'aliases',
      feature: context.feature || 'general', // crud, validation, limits
    },
    extra: context,
  });
}

export function captureWebhookError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'webhooks',
      feature: context.feature || 'general', // delivery, retry, validation
    },
    extra: context,
  });
}

export function captureEmailError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'email',
      feature: context.feature || 'general', // parsing, processing, storage
    },
    extra: context,
  });
}

export function captureBillingError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'billing',
      feature: context.feature || 'general', // payment, subscription, credits
    },
    extra: context,
  });
}

export function captureQueueError(error: Error, context: Record<string, any> = {}) {
  Sentry.captureException(error, {
    tags: {
      service: 'queue',
      feature: context.feature || 'general', // processing, retry, dead-letter
    },
    extra: context,
  });
}

/**
 * Add breadcrumb for tracking user actions
 */
export function addBreadcrumb(message: string, category: string, data?: Record<string, any>) {
  Sentry.addBreadcrumb({
    message,
    category,
    level: 'info',
    data,
    timestamp: Date.now() / 1000,
  });
}

/**
 * Set transaction name for better grouping
 */
export function setTransaction(name: string) {
  Sentry.getCurrentScope().setTransactionName(name);
}
/**
 * SPA Routes Configuration
 * 
 * This array defines all valid routes that should be served through index.html
 * for the Vue.js single-page application. These routes are handled client-side
 * by Vue Router.
 */

// Static SPA routes that map directly to Vue components
export const spaRoutes = [
  // Public routes
  '/',                    // Landing page
  '/login',              // Authentication
  '/auth/forgot-password', // Password reset request
  '/auth/reset-password',  // Password reset form
  '/terms-of-service',   // Legal documents
  '/privacy-policy',     // Legal documents
  '/changelog',          // Product updates
  '/help',               // Help center index
  
  // Protected dashboard routes (require authentication)
  '/dashboard',          // Redirects to /domains
  '/domains',            // Domain management
  '/aliases',            // Email alias management
  '/webhooks',           // Webhook configuration
  '/logs',               // Activity logs
  '/settings',           // User settings (legacy hash-based)
  '/settings/profile',   // User profile settings
  '/settings/api-keys',  // API key management
  '/settings/storage',   // Storage settings
  '/settings/retention', // Data retention settings
  '/settings/billing',   // Billing and subscription management
  
  // Admin SPA route
  '/admin'
];

// Dynamic route patterns that need regex matching
export const dynamicSpaRoutes = [
  // Help articles with dynamic slugs
  /^\/help\/[a-zA-Z0-9-]+$/,  // e.g., /help/getting-started
  
  // Admin nested routes
  /^\/admin(\/.*)?$/,
  
  // Catch-all for dynamic page resolution
  // This handles any unmatched routes and lets the frontend decide
  // whether to show a static page or 404
  /^\/[^/]+$/,  // Single-level paths like /about, /features, etc.
];

/**
 * Check if a given path should be served through the SPA
 * @param path - The request path to check
 * @returns true if the path should serve index.html
 */
export function isSpaRoute(path: string): boolean {
  // Remove query parameters and hash fragments
  const cleanPath = path.split('?')[0].split('#')[0];
  
  // Check static routes first (exact match)
  if (spaRoutes.includes(cleanPath)) {
    return true;
  }
  
  // Check dynamic routes (regex match)
  return dynamicSpaRoutes.some(regex => regex.test(cleanPath));
}

/**
 * Routes that should NOT be handled by the SPA
 * These are API endpoints, static assets, and special routes
 */
export const excludedPaths = [
  '/api',        // API endpoints
  '/docs',       // API documentation
  '/health',     // Health check endpoint
  '/sitemap.xml' // SEO sitemap
];

/**
 * File extensions that should NOT be handled by the SPA
 * These are static assets that should 404 if not found
 */
export const excludedExtensions = [
  '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',  // Images
  '.js', '.css', '.map',                            // Scripts and styles
  '.json', '.xml',                                   // Data files
  '.woff', '.woff2', '.ttf', '.eot',               // Fonts
  '.pdf', '.doc', '.docx'                           // Documents
];

/**
 * Check if a given path should be excluded from SPA handling
 * @param path - The request path to check
 * @returns true if the path should NOT serve index.html
 */
export function isExcludedPath(path: string): boolean {
  // Check if path starts with any excluded prefix
  if (excludedPaths.some(prefix => path.startsWith(prefix))) {
    return true;
  }
  
  // Check if path ends with any excluded extension
  return excludedExtensions.some(ext => path.endsWith(ext));
}

import { z } from 'zod';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';

// Load environment variables from .env file with variable expansion support
const myEnv = dotenv.config();
dotenvExpand.expand(myEnv);

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3000),
  HOST: z.string().default('0.0.0.0'),
  URL: z.string().default('http://localhost:3000'),
  FRONTEND_URL: z.string().optional(),
  
  // Database
  DB_USER: z.string().default('postgres'),
  DB_PASSWORD: z.string().default('password'),
  DB_NAME: z.string().default('eu_email_webhook'),
  DATABASE_URL: z.string().default('postgresql://user:password@localhost:5432/eu_email_webhook'),
  
  // Redis
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // Postfix Manager Service
  POSTFIX_MANAGER_URL: z.string().default('http://localhost:3001'),
  

  // BetterAuth configuration
  BETTER_AUTH_SECRET: z.string().min(32, { message: "BETTER_AUTH_SECRET must be at least 32 characters" }),
  BETTER_AUTH_URL: z.string().optional(),
  GITHUB_CLIENT_ID: z.string().optional(),
  GITHUB_CLIENT_SECRET: z.string().optional(),

  // Encryption key for sensitive data at rest
  ENCRYPTION_MASTER_KEY: z.string().min(32, { message: "ENCRYPTION_MASTER_KEY must be at least 32 characters" }),

  // WebhookTest integration
  WEBHOOKTEST_API_URL: z.string().optional(),
  WEBHOOKTEST_JWT_SECRET: z.string().optional(),
  
  // Email processing
  MAX_EMAIL_SIZE_MB: z.coerce.number().default(25),
  WEBHOOK_TIMEOUT_MS: z.coerce.number().default(30000),
  WEBHOOK_RETRY_ATTEMPTS: z.coerce.number().default(3),
  
  // DNS verification
  DNS_VERIFICATION_TIMEOUT_MS: z.coerce.number().default(5000),
  DNS_VERIFICATION_CACHE_TTL_MS: z.coerce.number().default(300000), // 5 minutes
  DNS_VERIFICATION_RETRY_ATTEMPTS: z.coerce.number().default(3),
  
  // GDPR compliance
  EMAIL_RETENTION_DAYS: z.coerce.number().default(30),
  LOG_RETENTION_DAYS: z.coerce.number().default(90),

  // Usage tracking and billing
  DEFAULT_MONTHLY_EMAIL_LIMIT: z.coerce.number().default(50),
  FREE_PLAN_EMAIL_LIMIT: z.coerce.number().default(50),
  PRO_PLAN_EMAIL_LIMIT: z.coerce.number().default(1000),
  ENTERPRISE_PLAN_EMAIL_LIMIT: z.coerce.number().default(10000),

  // Payment processing (Mollie)
  MOLLIE_API_KEY: z.string().min(1, { message: "MOLLIE_API_KEY must be set and not empty" }),
  MOLLIE_WEBHOOK_URL: z.string().min(1, { message: "MOLLIE_WEBHOOK_URL must be set and not empty" }),
  MOLLIE_WEBHOOK_SECRET: z.string().optional(),
  MOLLIE_TEST_MODE: z.coerce.boolean().default(true),

  // CORS configuration (websocket, BetterAuth)
  ALLOWED_ORIGINS: z.string().default('https://emailconnect.eu'),

  // Scaleway Transactional Email
  SCALEWAY_ACCESS_KEY: z.string().optional(),
  SCALEWAY_SECRET_KEY: z.string().optional(),
  SCALEWAY_PROJECT_ID: z.string().optional(),
  SCALEWAY_REGION: z.string().default('fr-par'),
  SCALEWAY_FROM_EMAIL: z.string().default('<EMAIL>'),
  SCALEWAY_FROM_NAME: z.string().default('EmailConnect'),

  // Attachment processing
  MAX_INLINE_ATTACHMENT_SIZE_KB: z.coerce.number().default(128),
  DEFAULT_ATTACHMENT_RETENTION_HOURS: z.coerce.number().default(1),
  PAID_ATTACHMENT_RETENTION_HOURS: z.coerce.number().default(24),
  
  // S3 Storage Configuration
  S3_REGION: z.string().default('eu-central-1'),
  S3_BUCKET: z.string().optional(),
  S3_ACCESS_KEY_ID: z.string().optional(),
  S3_SECRET_ACCESS_KEY: z.string().optional(),
  S3_ENDPOINT: z.string().optional(),
  S3_ATTACHMENT_EXPIRY_HOURS: z.coerce.number().default(168), // 7 days
  S3_MAX_FILE_SIZE_MB: z.coerce.number().default(50),
  S3_PRESIGNED_URL_EXPIRY_SECONDS: z.coerce.number().default(3600),
  ATTACHMENT_STORAGE_ENABLED: z.coerce.boolean().default(false),
  
  // Signing secret for generic signed links (magic, renewal, etc.)
  SIGNED_LINK_SECRET: z.string().optional(),

  // Error tracking
  SENTRY_ENABLED: z.string().optional(),
  SENTRY_DSN: z.string().optional(),
  SENTRY_TRACES_SAMPLE_RATE: z.coerce.number().default(0),
  SENTRY_REPLAYS_SESSION_SAMPLE_RATE: z.coerce.number().default(0),
  SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE: z.coerce.number().default(1.0),

  // Notion content integration (optional)
  NOTION_API_KEY: z.string().optional(),
  NOTION_ANNOUNCEMENTS_DB_ID: z.string().optional(),
  NOTION_FAQ_DB_ID: z.string().optional(),
  NOTION_CACHE_TTL_SECONDS: z.coerce.number().default(300),
  NOTION_REDIRECT_HOST_WHITELIST: z.string().optional(), // comma-separated hostnames

  // Heartbeat monitoring
  HEARTBEAT_URL: z.string().optional(),
  HEARTBEAT_INTERVAL_MINUTES: z.coerce.number().default(5),

  // Telegram admin notifications
  TELEGRAM_ENABLED: z.coerce.boolean().default(false),
  TELEGRAM_BOT_TOKEN: z.string().optional(),
  TELEGRAM_ADMIN_CHAT_ID: z.string().optional(),

  // Security reporting
  CSP_REPORT_URI: z.string().optional()
});

export const env = envSchema.parse(process.env);

export type Env = z.infer<typeof envSchema>;

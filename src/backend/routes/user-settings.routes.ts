import { FastifyPluginAsync } from 'fastify';
import { UserSettingsController } from '../controllers/user/user-settings.controller.js';
import { userSettingsSchemas } from '../schemas/openapi-schemas.js';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';

const userSettingsController = new UserSettingsController();

export const userSettingsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user settings
  fastify.get('/settings', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Settings'],
      summary: 'Get user settings',
      description: 'Retrieve the current user\'s settings for attachment processing and storage configuration.',
      response: {
        200: userSettingsSchemas.GetUserSettingsResponse,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userSettingsController.getSettings.bind(userSettingsController));

  // Update user settings
  fastify.put('/settings', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Settings'],
      summary: 'Update user settings',
      description: 'Update the current user\'s settings. Pro features require an active Pro subscription.',
      body: userSettingsSchemas.UpdateUserSettingsRequest,
      response: {
        200: userSettingsSchemas.UpdateUserSettingsResponse,
        400: errorResponseSchema,
        401: errorResponseSchema,
        402: errorResponseSchema, // Payment Required for Pro features
        500: errorResponseSchema
      }
    }
  }, userSettingsController.updateSettings.bind(userSettingsController));

  // Reset user settings to defaults
  fastify.delete('/settings', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Settings'],
      summary: 'Reset user settings',
      description: 'Reset the current user\'s settings to default values.',
      response: {
        200: userSettingsSchemas.ResetUserSettingsResponse,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userSettingsController.resetSettings.bind(userSettingsController));

  // Test S3 connection
  fastify.post('/settings/test-s3', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Settings'],
      summary: 'Test S3 connection',
      description: 'Test the provided S3 configuration to ensure it\'s valid and accessible.',
      body: {
        type: 'object',
        properties: {
          region: { type: 'string' },
          bucket: { type: 'string' },
          accessKey: { type: 'string' },
          secretKey: { type: 'string' },
          endpoint: { type: 'string', nullable: true }
        },
        required: ['region', 'bucket', 'accessKey', 'secretKey']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            details: {
              type: 'object',
              properties: {
                canWrite: { type: 'boolean' },
                canRead: { type: 'boolean' },
                canDelete: { type: 'boolean' },
                bucketExists: { type: 'boolean' }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userSettingsController.testS3Connection.bind(userSettingsController));
};

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { auditLogService } from '../services/audit-log.service.js';
import { logger } from '../utils/logger.js';

// Types per CSP specs
interface CspReportLegacy {
  "csp-report"?: Record<string, any>;
}

interface ReportToEnvelope {
  type?: string;
  body?: Record<string, any>;
  age?: number;
  url?: string;
}

export async function cspRoutes(fastify: FastifyInstance) {
  // Receive CSP reports (legacy report-uri and modern Report-To)
  fastify.post('/csp-report', {
    config: {
      // Local rate limit to avoid floods; increase if needed
      rateLimit: { max: 200, timeWindow: '1 minute' }
    },
    schema: {
      consumes: ['application/csp-report', 'application/json', 'application/reports']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const contentType = String(request.headers['content-type'] || '').toLowerCase();
      let report: any = {};

      if (contentType.includes('application/csp-report')) {
        const body = request.body as CspReportLegacy;
        report = (body && body['csp-report']) ? body['csp-report'] : body;
      } else if (contentType.includes('application/reports')) {
        const arr = Array.isArray(request.body) ? request.body as ReportToEnvelope[] : [];
        report = arr[0]?.body || {};
      } else {
        report = request.body || {};
      }

      // Basic redaction and normalization
      const normalized = {
        'violated-directive': report['violated-directive'] || report['violatedDirective'] || report['effective-directive'],
        'blocked-uri': report['blocked-uri'] || report['blockedURL'] || report['blockedURI'],
        'document-uri': report['document-uri'] || report['documentURL'],
        'original-policy': report['original-policy'] || report['policy'] || undefined,
        'referrer': report['referrer'] || undefined,
        'source-file': report['source-file'] || report['sourceFile'] || undefined,
        'line-number': report['line-number'] || report['lineNumber'] || undefined,
        'column-number': report['column-number'] || report['columnNumber'] || undefined
      };

      // Log for ops visibility
      logger.warn({ csp: true, normalized, raw: undefined }, 'CSP violation received');

      // Persist as a security audit event (summarized) without heavy payloads
      await auditLogService.log({
        action: 'security.suspicious_activity',
        resourceType: 'csp',
        metadata: {
          ...normalized,
          // store a minimal subset to control growth
          hasSource: !!normalized['source-file'],
          hasReferrer: !!normalized['referrer']
        },
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'] as string,
        success: false
      });

      return reply.code(204).send();
    } catch (err) {
      // Never fail CSP ingest; respond 204 to avoid client retries
      logger.error({ err }, 'Failed to process CSP report');
      return reply.code(204).send();
    }
  });
}


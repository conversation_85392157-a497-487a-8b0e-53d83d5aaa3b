import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
// AdminPermissions removed - use role checks
import { prisma } from '../lib/prisma.js';
import { S3StorageService } from '../services/storage/s3-storage.service.js';
import { GetObjectCommand } from '@aws-sdk/client-s3';
import { env } from '../config/env.js';

export const invoicesRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's invoices
  fastify.get('/invoices', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:read')],
    schema: {
      tags: ['Invoices'],
      summary: 'Get user invoices',
      description: 'Retrieve list of invoices for the authenticated user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            invoices: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  invoiceNumber: { type: 'string' },
                  amount: { type: 'number' },
                  currency: { type: 'string' },
                  description: { type: 'string' },
                  billingPeriod: { type: 'string', nullable: true },
                  generatedAt: { type: 'string', format: 'date-time' },
                  paymentId: { type: 'string' },
                  paidAt: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      
      const invoices = await Promise.race([
        prisma.invoice.findMany({
          where: {
            userId: user.id
          },
          include: {
            payment: {
              select: {
                paidAt: true
              }
            }
          },
          orderBy: {
            generatedAt: 'desc'
          }
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Query timeout')), 10000)
        )
      ]);

      const formattedInvoices = (invoices as any[]).map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        amount: Number(invoice.amount),
        currency: invoice.currency,
        description: invoice.description,
        billingPeriod: invoice.billingPeriod,
        generatedAt: invoice.generatedAt.toISOString(),
        paymentId: invoice.paymentId,
        paidAt: invoice.payment.paidAt?.toISOString()
      }));

      return reply.send({
        success: true,
        invoices: formattedInvoices
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get invoices');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve invoices'
      });
    }
  });

  // Download invoice PDF
  fastify.get('/invoices/:invoiceId/download', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:read')],
    schema: {
      tags: ['Invoices'],
      summary: 'Download invoice PDF',
      description: 'Download PDF file for a specific invoice',
      params: {
        type: 'object',
        properties: {
          invoiceId: { type: 'string' }
        },
        required: ['invoiceId']
      },
      response: {
        200: {
          type: 'string',
          format: 'binary',
          description: 'PDF file'
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { invoiceId } = request.params as { invoiceId: string };

      // Find invoice and verify ownership
      const invoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          userId: user.id
        }
      });

      if (!invoice) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice not found'
        });
      }

      // Check if we have S3 key
      if (!invoice.s3Key) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice PDF not available'
        });
      }

      const fileName = `${invoice.invoiceNumber}.pdf`;
      reply.header('Content-Type', 'application/pdf');
      reply.header('Content-Disposition', `attachment; filename="${fileName}"`);

      // Retrieve invoice from S3
      try {
        const s3Service = new S3StorageService();
        const getCommand = new GetObjectCommand({
          Bucket: env.S3_BUCKET,
          Key: invoice.s3Key
        });
        
        const response = await s3Service['client'].send(getCommand);
        if (response.Body) {
          const chunks: Uint8Array[] = [];
          const reader = response.Body.transformToWebStream().getReader();
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
          }
          
          const buffer = Buffer.concat(chunks);
          return reply.send(buffer);
        } else {
          return reply.code(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: 'Invoice PDF not found in storage'
          });
        }
      } catch (s3Error) {
        fastify.log.error({ error: s3Error, s3Key: invoice.s3Key }, 'Failed to retrieve invoice from S3');
        return reply.code(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Failed to retrieve invoice PDF'
        });
      }

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to download invoice');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to download invoice'
      });
    }
  });

  // Generate invoice for a payment (admin/testing endpoint)
  fastify.post('/invoices/generate/:paymentId', {
    preHandler: [async (request, reply) => {
      // Use BetterAuth session to authenticate
      const { auth } = await import('../lib/better-auth.js');
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Authentication required'
        });
      }

      // Get basic user info
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true, email: true, role: true, planType: true }
      });

      if (!user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      (request as any).user = user;
    }],
    schema: {
      tags: ['Invoices'],
      summary: 'Generate invoice for payment',
      description: 'Generate an invoice for a specific payment (testing/admin use)',
      params: {
        type: 'object',
        properties: {
          paymentId: { type: 'string' }
        },
        required: ['paymentId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            invoiceId: { type: 'string' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { paymentId } = request.params as { paymentId: string };

      // Check if current user is admin (allowed to generate invoices for any payment)
      const isAdmin = (user as any).role === 'admin';

      // For admin users, allow generating invoices for any payment
      // For regular users, only allow their own payments
      const whereClause: any = {
        id: paymentId,
        status: 'PAID'
      };

      // If not admin, restrict to user's own payments
      if (!isAdmin) {
        whereClause.userId = user.id;
      }

      const payment = await prisma.payment.findFirst({
        where: whereClause
      });

      if (!payment) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Payment not found or not paid'
        });
      }

      // Check if invoice already exists
      const existingInvoice = await prisma.invoice.findFirst({
        where: {
          paymentId: paymentId
        }
      });

      if (existingInvoice) {
        return reply.send({
          success: true,
          invoiceId: existingInvoice.id,
          message: 'Invoice already exists'
        });
      }

      // Generate invoice
      const { InvoiceGenerationService } = await import('../services/billing/invoice-generation.service.js');
      const invoiceId = await InvoiceGenerationService.createInvoice(paymentId);

      return reply.send({
        success: true,
        invoiceId,
        message: 'Invoice generated successfully'
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to generate invoice');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to generate invoice'
      });
    }
  });
};

import { FastifyPluginAsync } from 'fastify';
import { WebhooksController } from '../controllers/user/webhooks.controller.js';
import { webhookSchemas } from '../schemas/user/webhook.schemas.js';
import {
  requireWebhookRead,
  requireWebhookWrite,
  requireWebhookCreate
} from '../middleware/unified-auth.middleware.js';
import { smartWebhookAuth, webhookOwnerAuth } from '../middleware/enhanced-auth.middleware.js';

const webhooksController = new WebhooksController();

export const userWebhooksRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's webhooks
  fastify.get('/webhooks', {
    preHandler: [requireWebhookRead()],
    schema: {
      tags: ['User Webhooks'],
      summary: 'List user webhooks',
      description: 'Retrieves a list of webhooks for the authenticated user. Requires webhooks:read scope.',
      response: {
        200: {
          description: 'A list of user webhooks.',
          type: 'object',
          properties: {
            webhooks: {
              type: 'array',
              items: webhookSchemas.WebhookResponse
            },
            total: { type: 'integer' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, webhooksController.getWebhooks.bind(webhooksController));

  // Create new webhook - UNIFIED SECURITY: validates scope + plan + limits in one middleware
  fastify.post('/webhooks', {
    preHandler: [smartWebhookAuth], // Auto-detects if custom headers needed, applies appropriate permissions
    schema: {
      tags: ['User Webhooks'],
      summary: 'Create a new webhook',
      description: 'Creates a new webhook. Automatically detects if custom headers are used and validates appropriate plan permissions.',
      body: webhookSchemas.CreateWebhookRequest,
      response: {
        201: {
          description: 'Webhook created.',
          ...webhookSchemas.CreateWebhookResponse
        },
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' }, // Payment Required
        403: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.createWebhook.bind(webhooksController));

  // Get specific webhook
  fastify.get('/webhooks/:webhookId', {
    preHandler: [webhookOwnerAuth], // Validates ownership + read permissions
    schema: {
      tags: ['User Webhooks'],
      summary: 'Get webhook by ID',
      description: 'Retrieves a specific webhook by ID. Validates ownership and webhooks:read scope.',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.WebhookDetailResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.getWebhook.bind(webhooksController));

  // Update webhook
  fastify.put('/webhooks/:webhookId', {
    preHandler: [webhookOwnerAuth], // Validates ownership + write permissions
    schema: {
      tags: ['User Webhooks'],
      summary: 'Update webhook',
      description: 'Updates a webhook. Validates ownership and permissions for custom headers if used.',
      params: webhookSchemas.WebhookIdParam,
      body: webhookSchemas.UpdateWebhookRequest,
      response: {
        200: webhookSchemas.UpdateWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.updateWebhook.bind(webhooksController));

  // Delete webhook
  fastify.delete('/webhooks/:webhookId', {
    preHandler: [webhookOwnerAuth],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Delete webhook',
      description: 'Deletes a webhook. Validates ownership and webhooks:write scope.',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.DeleteWebhookResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.deleteWebhook.bind(webhooksController));

  // Verify webhook
  fastify.post('/webhooks/:webhookId/verify', {
    preHandler: [webhookOwnerAuth],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Verify webhook',
      description: 'Sends a test request to verify the webhook endpoint. Validates ownership and webhooks:write scope.',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.VerifyWebhookResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.verifyWebhook.bind(webhooksController));

  // Complete webhook verification with token
  fastify.post('/webhooks/:webhookId/verify/complete', {
    preHandler: [webhookOwnerAuth],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Complete webhook verification',
      description: 'Completes webhook verification by providing the verification token. Validates ownership and webhooks:write scope.',
      params: webhookSchemas.WebhookIdParam,
      body: {
        type: 'object',
        required: ['verificationToken'],
        properties: {
          verificationToken: { type: 'string', description: 'The verification token received in the webhook payload' }
        }
      },
      response: {
        200: webhookSchemas.VerifyWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.completeWebhookVerification.bind(webhooksController));

  // Test webhook - uses custom middleware that checks ownership but not creation limits  
  fastify.post('/webhooks/:webhookId/test', {
    preHandler: [async (request: any, reply: any) => {
      // Check authentication first
      await requireWebhookRead()(request, reply);
      if (reply.sent) return;
      
      // Check webhook ownership without write limits
      const user = request.user;
      const { webhookId } = request.params;
      
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();
      
      try {
        const webhook = await prisma.webhook.findFirst({
          where: { id: webhookId, userId: user.id },
          select: { id: true }
        });
        
        if (!webhook) {
          return reply.status(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: 'Webhook not found'
          });
        }
      } finally {
        await prisma.$disconnect();
      }
    }],
    schema: {
      tags: ['User Webhooks'],
      summary: 'Test webhook',
      description: 'Sends a test email payload to the webhook endpoint for testing purposes. Validates ownership and webhooks:write scope.',
      params: webhookSchemas.WebhookIdParam,
      response: {
        200: webhookSchemas.TestWebhookResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, webhooksController.testWebhook.bind(webhooksController));

  /** [todo] Preparation for integration with webhooktest.eu */
  // fastify.post('/webhooks/:webhookId/test-with-webhooktest', {
  //   schema: {
  //     description: 'Test webhook using WebhookTest integration',
  //     tags: ['Webhooks'],
  //     params: {
  //       type: 'object',
  //       required: ['webhookId'],
  //       properties: {
  //         webhookId: { type: 'string' }
  //       }
  //     },
  //     body: {
  //       type: 'object',
  //       properties: {
  //         customPayload: { type: 'object' }
  //       }
  //     }
  //   },
  //   preHandler: [webhookOwnerAuth],
  // }, async (request, reply) => {
  //   const { webhookId } = request.params as { webhookId: string };
  //   const { customPayload } = request.body as { customPayload?: object };
  //   const user = request.user;

  //   try {
  //     // Get webhook details
  //     const webhook = await prisma.webhook.findFirst({
  //       where: {
  //         id: webhookId,
  //         userId: user.id
  //       }
  //     });

  //     if (!webhook) {
  //       return reply.status(404).send({ error: 'Webhook not found' });
  //     }

  //     if (!webhook.verified) {
  //       return reply.status(400).send({ error: 'Webhook must be verified before testing' });
  //     }

  //     // Test using WebhookTest integration
  //     const result = await WebhookTestIntegrationService.testWebhook(
  //       user.id,
  //       user.email,
  //       webhook.url,
  //       customPayload
  //     );

  //     reply.send({
  //       success: true,
  //       webhookTestResult: result,
  //       message: 'Webhook test completed via WebhookTest integration'
  //     });

  //   } catch (error) {
  //     reply.status(500).send({
  //       success: false,
  //       error: error instanceof Error ? error.message : 'Failed to test webhook'
  //     });
  //   }
  // });
};

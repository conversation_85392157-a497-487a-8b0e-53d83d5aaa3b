import { FastifyPluginAsync } from 'fastify'
import { NotionContentService } from '../services/notion-content.service.js'
import { env } from '../config/env.js'

export const notionPublicRoutes: FastifyPluginAsync = async (fastify) => {
  const service = new NotionContentService()

  fastify.get('/announcements', {
    schema: {
      tags: ['Public'],
      summary: 'Get a random active announcement banner',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            announcement: {
              type: 'object',
              nullable: true,
              properties: {
                title: { type: 'string' },
                description: { type: 'string' },
                ctaLabel: { type: 'string' },
                ctaUrl: { type: 'string' },
                icon: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (_req, reply) => {
    try {
      const announcement = await service.getRandomActiveAnnouncement()
      return reply.send({ success: true, announcement })
    } catch (e: any) {
      fastify.log.error({ err: e?.message }, 'Failed to fetch announcement')
      return reply.send({ success: true, announcement: null })
    }
  })

  fastify.get('/faqs', {
    schema: {
      tags: ['Public'],
      summary: 'Get public FAQs from Notion',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            faqs: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  question: { type: 'string' },
                  answer: { type: 'string' },
                  helpUrl: { type: 'string' },
                  order: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  }, async (_req, reply) => {
    try {
      const faqs = await service.getPublicFaqs()
      return reply.send({ success: true, faqs })
    } catch (e: any) {
      fastify.log.error({ err: e?.message }, 'Failed to fetch FAQs')
      return reply.send({ success: true, faqs: [] })
    }
  })

  // Redirect proxy for CTA clicks (with host allowlist)
  fastify.get('/redirect', {
    schema: {
      tags: ['Public'],
      querystring: {
        type: 'object',
        properties: { to: { type: 'string' }, label: { type: 'string' }, source: { type: 'string' } },
        required: ['to']
      }
    }
  }, async (request, reply) => {
    try {
      const q = request.query as { to: string; label?: string; source?: string }
      let to = (q.to || '').trim()
      try { to = decodeURIComponent(to) } catch {}
      // If scheme missing, default to https
      if (!/^[a-zA-Z][a-zA-Z0-9+.-]*:/.test(to)) {
        to = 'https://' + to.replace(/^\/+/, '')
      }
      let url: URL
      try {
        url = new URL(to)
      } catch {
        return reply.code(400).send({ error: 'Invalid URL' })
      }
      if (!['http:', 'https:'].includes(url.protocol)) {
        return reply.code(400).send({ error: 'Invalid URL' })
      }

const whitelist = (env.NOTION_REDIRECT_HOST_WHITELIST || '').split(',').map(s => s.trim()).filter(Boolean)
      const allowed = whitelist.length > 0 && whitelist.includes(url.hostname)
      if (!allowed) {
        return reply.code(400).send({ error: 'Host not allowed' })
      }

      // Track click via Plausible custom event name (optional)
      try {
        const eventName = 'Notion CTA Click'
        fastify.log.info({ event: eventName, label: q.label, source: q.source, to: url.hostname }, 'CTA click')
      } catch {}

      // Always perform a standard HTTP redirect; new-tab behavior is controlled client-side via target="_blank"
      reply.redirect(url.toString())
    } catch (e: any) {
      return reply.code(400).send({ error: 'Invalid URL' })
    }
  })
}


import { FastifyPluginAsync } from 'fastify';
import { PaymentController } from '../controllers/user/payment.controller.js';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';

const paymentController = new PaymentController();

export const paymentsRoutes: FastifyPluginAsync = async (fastify) => {
  // Public renewal wrapper: accepts signed token, creates payment and redirects
  fastify.get('/renew-by-token', {
    schema: {
      tags: ['Payments'],
      summary: 'Public renewal entrypoint',
      description: 'Verifies a signed token, creates a fresh renewal payment, redirects to Mollie checkout.',
      querystring: {
        type: 'object',
        properties: {
          token: { type: 'string' }
        },
        required: ['token']
      }
    }
  }, async (request, reply) => {
    const { verifySignedToken } = await import('../services/security/signed-link.util.js');
    const { prisma } = await import('../lib/prisma.js');
    const { mollieService } = await import('../services/payment/mollie.service.js');

    const { token } = request.query as any;
    const payload = await verifySignedToken(token, 'renewal', { useReplayProtection: true });
    if (!payload) return reply.code(401).send({ statusCode: 401, error: 'Unauthorized', message: 'Invalid or expired token' });

    const subscription = await prisma.subscription.findUnique({ where: { id: payload.subscriptionId } });
    if (!subscription || subscription.userId !== payload.userId || subscription.status !== 'ACTIVE' || subscription.mollieId !== null) {
      return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'Invalid subscription' });
    }

    // Audit this signed-link usage
    try {
      const ua = request.headers['user-agent'] || null;
      const ip = (request.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() || (request.socket as any).remoteAddress || null;
      await prisma.auditLog.create({
        data: {
          action: 'signed_link.used',
          resourceType: 'payment',
          resourceId: subscription.id,
          metadata: {
            kind: 'renewal',
            userId: subscription.userId,
            subscriptionId: subscription.id,
            ip,
            userAgent: ua
          },
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        }
      });
    } catch {}

    const payment = await mollieService.createPayment({
      amount: { value: subscription.amount.toFixed(2), currency: subscription.currency },
      description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`,
      redirectUrl: `${process.env.URL || ''}/settings/billing?renewal=success`,
      webhookUrl: `${process.env.URL || ''}/api/webhooks/mollie/payment`,
      metadata: {
        type: 'subscription_renewal',
        subscriptionId: subscription.id,
        userId: subscription.userId,
        planType: subscription.planType,
        billingPeriod: subscription.interval
      }
    });

    await prisma.payment.create({
      data: {
        mollieId: payment.id,
        userId: subscription.userId,
        subscriptionId: subscription.id,
        status: 'PENDING',
        amount: subscription.amount,
        currency: subscription.currency,
        description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`
      }
    });

    return reply.redirect(payment.getCheckoutUrl());
  });

  // Authenticated renewal wrapper: re-creates a fresh Mollie link and redirects
  fastify.get('/renew/:subscriptionId', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:update')],
    schema: {
      tags: ['Payments'],
      summary: 'Authenticated renewal entrypoint',
      params: { type: 'object', properties: { subscriptionId: { type: 'string' } }, required: ['subscriptionId'] }
    }
  }, async (request, reply) => {
    const { subscriptionId } = request.params as any;
    const user = (request as any).user;

    const { prisma } = await import('../lib/prisma.js');
    const { mollieService } = await import('../services/payment/mollie.service.js');

    const subscription = await prisma.subscription.findUnique({ where: { id: subscriptionId } });
    if (!subscription || subscription.userId !== user.id || subscription.mollieId !== null) {
      return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Subscription not found' });
    }

    const payment = await mollieService.createPayment({
      amount: { value: subscription.amount.toFixed(2), currency: subscription.currency },
      description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`,
      redirectUrl: `${process.env.URL || ''}/settings/billing?renewal=success`,
      webhookUrl: `${process.env.URL || ''}/api/webhooks/mollie/payment`,
      metadata: {
        type: 'subscription_renewal',
        subscriptionId: subscription.id,
        userId: subscription.userId,
        planType: subscription.planType,
        billingPeriod: subscription.interval
      }
    });

    await prisma.payment.create({
      data: {
        mollieId: payment.id,
        userId: subscription.userId,
        subscriptionId: subscription.id,
        status: 'PENDING',
        amount: subscription.amount,
        currency: subscription.currency,
        description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`
      }
    });

    return reply.redirect(payment.getCheckoutUrl());
  });

  // Authenticated renewal without ID: find user's active virtual subscription
  fastify.get('/renew', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:update')],
    schema: {
      tags: ['Payments'],
      summary: 'Authenticated renewal for active manual subscription',
      description: 'Finds the active manual (virtual) subscription and redirects to a fresh Mollie checkout URL.'
    }
  }, async (request, reply) => {
    const user = (request as any).user;
    const { prisma } = await import('../lib/prisma.js');
    const { mollieService } = await import('../services/payment/mollie.service.js');

    const subscription = await prisma.subscription.findFirst({
      where: { userId: user.id, status: 'ACTIVE', mollieId: null },
      orderBy: { updatedAt: 'desc' }
    });

    if (!subscription) {
      return reply.code(409).send({ statusCode: 409, error: 'Conflict', message: 'No active manual subscription to renew' });
    }

    const payment = await mollieService.createPayment({
      amount: { value: subscription.amount.toFixed(2), currency: subscription.currency },
      description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`,
      redirectUrl: `${process.env.URL || ''}/settings/billing?renewal=success`,
      webhookUrl: `${process.env.URL || ''}/api/webhooks/mollie/payment`,
      metadata: {
        type: 'subscription_renewal',
        subscriptionId: subscription.id,
        userId: subscription.userId,
        planType: subscription.planType,
        billingPeriod: subscription.interval
      }
    });

    await prisma.payment.create({
      data: {
        mollieId: payment.id,
        userId: subscription.userId,
        subscriptionId: subscription.id,
        status: 'PENDING',
        amount: subscription.amount,
        currency: subscription.currency,
        description: `Renewal: ${subscription.planType} plan - ${subscription.interval}`
      }
    });

    return reply.redirect(payment.getCheckoutUrl());
  });

  // Create one-off payment for manual billing
  fastify.post('/create', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:update')],
    schema: {
      tags: ['Payments'],
      summary: 'Create one-off payment',
      description: 'Create a one-off payment for manual billing (used for manual payment subscriptions). Requires billing:write permission.',
      body: {
        type: 'object',
        properties: {
          planType: {
            type: 'string',
            enum: ['pro', 'enterprise'],
            description: 'The plan to purchase'
          },
          interval: {
            type: 'string',
            enum: ['one-time'],
            description: 'Payment interval (must be one-time for manual payments)'
          },
          amount: {
            type: 'number',
            description: 'Payment amount'
          },
          currency: {
            type: 'string',
            enum: ['EUR'],
            description: 'Payment currency'
          },
          description: {
            type: 'string',
            description: 'Payment description'
          },
          successUrl: {
            type: 'string',
            description: 'URL to redirect to after successful payment'
          },
          cancelUrl: {
            type: 'string',
            description: 'URL to redirect to after cancelled payment'
          },
          metadata: {
            type: 'object',
            description: 'Additional payment metadata'
          }
        },
        required: ['planType', 'interval', 'amount', 'currency', 'description', 'successUrl', 'cancelUrl']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            checkoutUrl: { type: 'string' },
            paymentId: { type: 'string' },
            mollieId: { type: 'string' }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, paymentController.createOneOffPayment);
};
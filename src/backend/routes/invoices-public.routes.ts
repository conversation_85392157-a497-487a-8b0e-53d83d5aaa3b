import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { prisma } from '../lib/prisma.js';
import { S3StorageService } from '../services/storage/s3-storage.service.js';
import { GetObjectCommand } from '@aws-sdk/client-s3';
import { env } from '../config/env.js';

// Public invoice download route (not under /api prefix)
export const invoicesPublicRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Download invoice PDF (public path)
  fastify.get('/invoices/:invoiceId/download', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('billing:read')],
    schema: {
      tags: ['Invoices'],
      summary: 'Download invoice PDF',
      description: 'Download PDF file for a specific invoice (public route)',
      params: {
        type: 'object',
        properties: {
          invoiceId: { type: 'string' }
        },
        required: ['invoiceId']
      },
      response: {
        200: {
          type: 'string',
          format: 'binary',
          description: 'PDF file'
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { invoiceId } = request.params as { invoiceId: string };

      // Find invoice and verify ownership
      const invoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          userId: user.id
        }
      });

      if (!invoice) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice not found'
        });
      }

      // Check if we have S3 key
      if (!invoice.s3Key) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice PDF not available'
        });
      }

      const fileName = `${invoice.invoiceNumber}.pdf`;
      reply.header('Content-Type', 'application/pdf');
      reply.header('Content-Disposition', `attachment; filename="${fileName}"`);

      // Retrieve invoice from S3
      try {
        const s3Service = new S3StorageService();
        const getCommand = new GetObjectCommand({
          Bucket: env.S3_BUCKET,
          Key: invoice.s3Key
        });
        
        const response = await s3Service['client'].send(getCommand);
        if (response.Body) {
          const chunks: Uint8Array[] = [];
          const reader = response.Body.transformToWebStream().getReader();
          
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            chunks.push(value);
          }
          
          const buffer = Buffer.concat(chunks);
          return reply.send(buffer);
        } else {
          return reply.code(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: 'Invoice PDF not found in storage'
          });
        }
      } catch (s3Error) {
        fastify.log.error({ error: s3Error, s3Key: invoice.s3Key }, 'Failed to retrieve invoice from S3');
        return reply.code(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Failed to retrieve invoice PDF'
        });
      }

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to download invoice');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to download invoice'
      });
    }
  });
}


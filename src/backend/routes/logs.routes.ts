import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { LogsController } from '../controllers/user/logs.controller.js';
import { manualRetryWebhook } from '../services/queue.js';
import { prisma } from '../lib/prisma.js';

const logsController = new LogsController();

export const logsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's email logs
  fastify.get('/logs', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Logs'],
      summary: 'List user email logs',
      description: 'Retrieves email processing logs for the authenticated user.',
      querystring: {
        type: 'object',
        properties: {
          domainId: { 
            type: 'string', 
            description: 'Filter logs by domain ID' 
          },
          aliasId: { 
            type: 'string', 
            description: 'Filter logs by alias ID' 
          },
          testWebhooksOnly: { 
            type: 'boolean', 
            description: 'Show only test webhook calls' 
          },
          status: { 
            type: 'string', 
            enum: ['PENDING', 'DELIVERED', 'FAILED', 'RETRYING', 'EXPIRED'],
            description: 'Filter logs by delivery status' 
          },
          limit: { 
            type: 'integer', 
            default: 50, 
            minimum: 1, 
            maximum: 100,
            description: 'Number of logs to return' 
          },
          offset: { 
            type: 'integer', 
            default: 0, 
            minimum: 0,
            description: 'Number of logs to skip for pagination' 
          }
        }
      },
      response: {
        200: {
          description: 'A list of email logs.',
          type: 'object',
          properties: {
            logs: {
              type: 'array',
              items: { $ref: 'EmailLog#' }
            },
            total: { type: 'integer' },
            hasMore: { type: 'boolean' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, logsController.getLogs.bind(logsController));

  // Manual retry endpoint for failed webhooks
  fastify.post('/logs/:messageId/retry', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('auth:basic')],
    schema: {
      tags: ['User Logs'],
      summary: 'Retry failed webhook delivery',
      description: 'Manually retry a failed webhook delivery with rate limiting (30s cooldown).',
      params: {
        type: 'object',
        properties: {
          messageId: { 
            type: 'string', 
            description: 'The message ID to retry' 
          }
        },
        required: ['messageId']
      },
      response: {
        200: {
          description: 'Webhook retry queued successfully.',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            jobId: { type: 'string' }
          }
        },
        400: {
          description: 'Bad request - rate limited or invalid message',
          ...errorResponseSchema
        },
        404: {
          description: 'Message not found',
          ...errorResponseSchema
        },
        401: { $ref: 'ErrorResponse#' },
        429: {
          description: 'Too many requests',
          ...errorResponseSchema
        },
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    const { messageId } = request.params as { messageId: string };
    const user = (request as any).user;

    try {
      // Find the email record with alias and domain
      const email = await prisma.email.findUnique({
        where: { messageId },
        include: {
          domain: true,
          alias: {
            include: { webhook: true }
          }
        }
      });

      if (!email || !email.domain || email.domain.userId !== user.id) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Message not found or access denied'
        });
      }

      // Check if email is in a retryable state
      if (email.deliveryStatus !== 'FAILED' && email.deliveryStatus !== 'EXPIRED') {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Only failed or expired messages can be retried'
        });
      }

      // Get webhook from the alias relation if available
      let webhook = email.alias?.webhook;
      
      // If no alias relation exists, fall back to finding the webhook manually
      if (!webhook) {
        // Find the webhook through the alias relationship
        // First try to find specific alias, then fall back to catch-all
        const targetEmail = email.toAddresses[0]; // Get the target email address
        let alias = await prisma.alias.findFirst({
          where: {
            domainId: email.domain.id,
            email: targetEmail,
            active: true
          },
          include: { webhook: true }
        });

        // If no specific alias found, try catch-all
        if (!alias) {
          alias = await prisma.alias.findFirst({
            where: {
              domainId: email.domain.id,
              email: { startsWith: '*@' },
              active: true
            },
            include: { webhook: true }
          });
        }

        if (!alias || !alias.webhook) {
          return reply.code(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'No webhook configured for this email address'
          });
        }

        webhook = alias.webhook;
      }

      // Use the stored webhook payload if available, otherwise reconstruct basic payload
      let webhookPayload;
      if (email.webhookPayload) {
        webhookPayload = email.webhookPayload;
      } else {
        // Reconstruct basic webhook payload
        webhookPayload = {
          messageId: email.messageId,
          envelope: {
            from: email.fromAddress,
            to: email.toAddresses,
            messageId: email.messageId
          },
          message: {
            sender: { email: email.fromAddress },
            recipient: { email: email.toAddresses[0] },
            subject: email.subject,
            textBody: '', // Not stored in email table
            htmlBody: '', // Not stored in email table
            attachments: [],
            headers: [],
            date: email.createdAt.toISOString()
          },
          domain: email.domain.domain,
          timestamp: new Date().toISOString()
        };
      }

      // Call manual retry function
      const result = await manualRetryWebhook(
        messageId,
        user.id,
        webhook.url,
        webhookPayload,
        webhook.webhookSecret || undefined,
        typeof webhook.customHeaders === 'object' && webhook.customHeaders !== null && !Array.isArray(webhook.customHeaders) 
          ? webhook.customHeaders as Record<string, string>
          : undefined
      );

      return reply.send(result);

    } catch (error: any) {
      // Handle rate limiting error
      if (error.message.includes('Rate limited')) {
        return reply.code(429).send({
          statusCode: 429,
          error: 'Too Many Requests',
          message: error.message
        });
      }

      fastify.log.error({ messageId, userId: user.id, error: error.message }, 'Manual retry failed');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to queue webhook retry'
      });
    }
  });
};

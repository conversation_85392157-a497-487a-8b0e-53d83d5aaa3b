import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { auditLogService } from '../services/audit-log.service.js';
import { authenticateUser } from '../middleware/unified-auth.middleware.js';
import { logger } from '../utils/logger.js';

interface AuditLogQueryParams {
  startDate?: string;
  endDate?: string;
  actions?: string;       // comma-separated, supports '*' suffix
  categories?: string;    // comma-separated (auth, security, data, billing, settings, webhook)
  success?: string;       // 'true' | 'false'
  userEmail?: string;     // optional (not yet wired server-side)
  userId?: string;        // optional (defaults to current user)
  resourceType?: string;
  apiKeySuffix?: string;
  ipContains?: string;
  limit?: number;
  offset?: number;
  format?: 'json' | 'csv';
}

export async function auditLogRoutes(fastify: FastifyInstance) {
  // Get audit logs for the authenticated user
  fastify.get('/api/audit-logs', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Audit'],
      summary: 'Get audit logs for the authenticated user',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date-time', description: 'Start date for logs' },
          endDate: { type: 'string', format: 'date-time', description: 'End date for logs' },
          actions: { type: 'string', description: 'Comma-separated list of actions to filter' },
          limit: { type: 'integer', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'integer', minimum: 0, default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            logs: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  action: { type: 'string' },
                  resourceType: { type: 'string' },
                  resourceId: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  ipAddress: { type: 'string' },
                  userAgent: { type: 'string' },
                  metadata: { type: 'object' }
                }
              }
            },
            total: { type: 'integer' },
            limit: { type: 'integer' },
            offset: { type: 'integer' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: AuditLogQueryParams }>, reply: FastifyReply) => {
    try {
      const userId = request.user.id;
      const { startDate, endDate, actions, categories, success, resourceType, apiKeySuffix, ipContains, limit = 100, offset = 0 } = request.query;

      // Parse dates
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;

      // Validate date range
      if (startDateObj && endDateObj && startDateObj > endDateObj) {
        return reply.status(400).send({
          error: 'Invalid date range',
          message: 'Start date must be before end date'
        });
      }

      // Parse filters
      const actionsList = actions ? actions.split(',').map(a => a.trim()).filter(Boolean) : undefined;
      const categoriesList = categories ? categories.split(',').map(c => c.trim()).filter(Boolean) : undefined;
      const successBool = typeof success === 'string' ? (success.toLowerCase() === 'true' ? true : success.toLowerCase() === 'false' ? false : undefined) : undefined;

      // Get audit logs with filters
      const result = await auditLogService.getUserAuditLogs(userId, {
        startDate: startDateObj,
        endDate: endDateObj,
        actions: actionsList,
        categories: categoriesList,
        success: successBool,
        resourceType: resourceType || undefined,
        apiKeySuffix: apiKeySuffix || undefined,
        ipContains: ipContains || undefined,
        limit,
        offset
      });

      // Log the audit log access (meta!)
      await auditLogService.logDataAccess(userId, 'audit_log' as any, 'multiple', 'read', request);

      return reply.send({
        ...result,
        limit,
        offset
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to fetch audit logs');
      return reply.status(500).send({
        error: 'Internal server error',
        message: 'Failed to fetch audit logs'
      });
    }
  });

  // Export audit logs for compliance
  fastify.get('/api/audit-logs/export', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Audit'],
      summary: 'Export audit logs for compliance',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        required: ['startDate', 'endDate'],
        properties: {
          startDate: { type: 'string', format: 'date-time', description: 'Start date for export' },
          endDate: { type: 'string', format: 'date-time', description: 'End date for export' },
          format: { type: 'string', enum: ['json', 'csv'], default: 'json', description: 'Export format' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: AuditLogQueryParams }>, reply: FastifyReply) => {
    try {
      const userId = request.user.id;
      const { startDate, endDate, format = 'json', actions, categories, success, resourceType, apiKeySuffix, ipContains } = request.query;

      if (!startDate || !endDate) {
        return reply.status(400).send({
          error: 'Missing required parameters',
          message: 'Both startDate and endDate are required'
        });
      }

      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      // Validate date range
      if (startDateObj > endDateObj) {
        return reply.status(400).send({
          error: 'Invalid date range',
          message: 'Start date must be before end date'
        });
      }

      // Limit export to maximum 90 days
      const daysDiff = (endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 90) {
        return reply.status(400).send({
          error: 'Date range too large',
          message: 'Export is limited to 90 days of data'
        });
      }

      // Reuse filtered fetch then export selected fields
      const actionsList = actions ? actions.split(',').map(a => a.trim()).filter(Boolean) : undefined;
      const categoriesList = categories ? categories.split(',').map(c => c.trim()).filter(Boolean) : undefined;
      const successBool = typeof success === 'string' ? (success.toLowerCase() === 'true' ? true : success.toLowerCase() === 'false' ? false : undefined) : undefined;

      const { logs: filteredLogs } = await auditLogService.getUserAuditLogs(userId, {
        startDate: startDateObj,
        endDate: endDateObj,
        actions: actionsList,
        categories: categoriesList,
        success: successBool,
        resourceType: resourceType || undefined,
        apiKeySuffix: apiKeySuffix || undefined,
        ipContains: ipContains || undefined,
        limit: 10000,
        offset: 0
      });

      const logs = filteredLogs.map(l => ({
        id: l.id,
        timestamp: l.createdAt,
        action: l.action,
        resourceType: l.resourceType,
        resourceId: l.resourceId,
        ipAddress: l.ipAddress,
        userAgent: l.userAgent,
        success: l.metadata?.success ?? true,
        metadata: l.metadata
      }));

      // Log the export event
      await auditLogService.log({
        userId,
        action: 'data.exported',
        resourceType: 'audit_logs',
        metadata: {
          startDate: startDateObj.toISOString(),
          endDate: endDateObj.toISOString(),
          format,
          recordCount: logs.length
        },
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'] as string
      });

      // Format based on requested type
      if (format === 'csv') {
        const csv = convertToCSV(logs);
        const filename = `audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.csv`;
        
        reply
          .header('Content-Type', 'text/csv')
          .header('Content-Disposition', `attachment; filename="${filename}"`)
          .send(csv);
      } else {
        const filename = `audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.json`;
        
        reply
          .header('Content-Type', 'application/json')
          .header('Content-Disposition', `attachment; filename="${filename}"`)
          .send(logs);
      }
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to export audit logs');
      return reply.status(500).send({
        error: 'Internal server error',
        message: 'Failed to export audit logs'
      });
    }
  });

  // Get audit log summary/statistics
  fastify.get('/api/audit-logs/summary', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Audit'],
      summary: 'Get audit log summary and statistics',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            totalLogs: { type: 'integer' },
            dateRange: {
              type: 'object',
              properties: {
                earliest: { type: 'string', format: 'date-time' },
                latest: { type: 'string', format: 'date-time' }
              }
            },
            actionCounts: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            },
            recentActivity: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', format: 'date' },
                  count: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user.id;

      // Get overall stats
      const { logs: allLogs, total } = await auditLogService.getUserAuditLogs(userId, {
        limit: 10000
      });

      if (allLogs.length === 0) {
        return reply.send({
          totalLogs: 0,
          dateRange: null,
          actionCounts: {},
          recentActivity: []
        });
      }

      // Calculate date range
      const dates = allLogs.map(log => new Date(log.createdAt).getTime());
      const dateRange = {
        earliest: new Date(Math.min(...dates)).toISOString(),
        latest: new Date(Math.max(...dates)).toISOString()
      };

      // Count actions
      const actionCounts: Record<string, number> = {};
      allLogs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
      });

      // Get recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentLogs = allLogs.filter(log => 
        new Date(log.createdAt) >= thirtyDaysAgo
      );

      // Group by day
      const dailyCounts: Record<string, number> = {};
      recentLogs.forEach(log => {
        const date = new Date(log.createdAt).toISOString().split('T')[0];
        dailyCounts[date] = (dailyCounts[date] || 0) + 1;
      });

      const recentActivity = Object.entries(dailyCounts)
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => b.date.localeCompare(a.date))
        .slice(0, 30);

      return reply.send({
        totalLogs: total,
        dateRange,
        actionCounts,
        recentActivity
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get audit log summary');
      return reply.status(500).send({
        error: 'Internal server error',
        message: 'Failed to get audit log summary'
      });
    }
  });
}

/**
 * Convert audit logs to CSV format
 */
function convertToCSV(logs: any[]): string {
  if (logs.length === 0) {
    return 'timestamp,action,resource_type,resource_id,ip_address,user_agent,success\n';
  }

  // Headers
  const headers = [
    'timestamp',
    'action',
    'resource_type',
    'resource_id',
    'ip_address',
    'user_agent',
    'success',
    'metadata'
  ];

  // Rows
  const rows = logs.map(log => {
    return [
      log.timestamp || '',
      log.action || '',
      log.resourceType || '',
      log.resourceId || '',
      log.ipAddress || '',
      (log.userAgent || '').replace(/,/g, ';'), // Replace commas in user agent
      log.success !== false ? 'true' : 'false',
      JSON.stringify(log.metadata || {}).replace(/"/g, '""') // Escape quotes in JSON
    ].map(field => `"${field}"`).join(',');
  });

  return [headers.join(','), ...rows].join('\n');
}

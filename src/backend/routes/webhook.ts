import { FastifyPluginAsync } from 'fastify';
import { getQueue } from '../services/queue.js';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';

export const webhookRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get webhook delivery statistics (maps to /admin/stats/delivery conceptually)
  fastify.get('/stats', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'), // ⭐ ADMIN AUTH
    schema: {
      description: 'Get current webhook queue statistics.',
      tags: ['Admin', 'Webhook Operations'],
      summary: 'Get webhook queue statistics',
      response: {
        200: {
          description: 'Current queue statistics.',
          type: 'object',
          properties: {
            stats: {
              type: 'object',
              properties: {
                waiting: { type: 'integer', description: 'Number of jobs waiting in the queue.' },
                active: { type: 'integer', description: 'Number of jobs currently active.' },
                completed: { type: 'integer', description: 'Number of jobs completed.' }, // This might be for a specific timeframe if queue is large
                failed: { type: 'integer', description: 'Number of jobs failed.' },
              },
            },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
        503: { description: 'Queue not available', ...errorResponseSchema },
      },
      // security: [{ adminAuth: [] }], // If this should be admin protected
    },
  }, async (request, reply) => {
    // Admin access is already enforced by requireAdmin middleware
    const user = (request as any).user;
    fastify.log.info({ 
      adminEmail: user.email,
      action: 'webhook.stats.access'
    }, 'Admin accessing webhook queue statistics');

    const queue = getQueue();
    
    if (!queue) {
      return reply.code(503).send({ statusCode: 503, error: 'Service Unavailable', message: 'Queue not available' });
    }

    // Note: BullMQ's getCompleted() and getFailed() might return recent jobs, not total counts.
    // For total counts, specific metrics or BullMQ Dashboard integration might be better.
    // The current implementation provides counts of jobs currently in those states or recently processed.
    const waitingCount = await queue.getWaitingCount();
    const activeCount = await queue.getActiveCount();
    const completedCount = await queue.getCompletedCount(); // Count of jobs in completed set
    const failedCount = await queue.getFailedCount(); // Count of jobs in failed set


    return reply.send({
      stats: {
        waiting: waitingCount,
        active: activeCount,
        completed: completedCount,
        failed: failedCount,
      },
      timestamp: new Date().toISOString(),
    });
  });

  // Get failed webhook jobs for debugging (maps to /admin/logs/failed)
  fastify.get('/failed', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'), // ⭐ ADMIN AUTH
    schema: {
      description: 'Retrieves a list of recently failed webhook jobs for debugging purposes.',
      tags: ['Admin', 'Webhook Operations'],
      summary: 'List failed webhook jobs',
      querystring: {
        type: 'object',
        properties: {
          start: { type: 'integer', default: 0, description: 'Start index for pagination.' },
          end: { type: 'integer', default: 49, description: 'End index for pagination (max 50 jobs shown).' },
        },
      },
      response: {
        200: {
          description: 'A list of failed webhook jobs.',
          type: 'object',
          properties: {
            failedJobs: {
              type: 'array',
              items: { // This structure should align with WebhookLog or a similar schema if possible
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  data: { type: 'object', additionalProperties: true, description: 'Job payload' }, // Original payload sent to webhook
                  failedReason: { type: 'string', nullable: true },
                  processedOn: { type: 'number', nullable: true, description: 'Timestamp when processing started' },
                  finishedOn: { type: 'number', nullable: true, description: 'Timestamp when job finished (failed)' },
                  attemptsMade: { type: 'integer' },
                },
              },
            },
          },
        },
        503: { description: 'Queue not available', ...errorResponseSchema },
      },
      // security: [{ adminAuth: [] }], // If this should be admin protected
    },
  }, async (request, reply) => {
    // Admin access is already enforced by requireAdmin middleware
    const user = (request as any).user;
    const { start, end } = request.query as { start?: number, end?: number };
    
    fastify.log.info({ 
      adminEmail: user.email,
      action: 'webhook.failed_jobs.access',
      start: start || 0,
      end: end || 49
    }, 'Admin accessing failed webhook jobs');

    const queue = getQueue();
    
    if (!queue) {
      return reply.code(503).send({ statusCode: 503, error: 'Service Unavailable', message: 'Queue not available' });
    }

    const failedJobs = await queue.getFailed(start || 0, end || 49);
    
    const jobs = failedJobs.map(job => ({
      id: job.id,
      data: job.data, // This is the webhook payload
      failedReason: job.failedReason,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      attemptsMade: job.attemptsMade,
    }));

    return reply.send({ failedJobs: jobs });
  });

  // Retry a specific failed job
  fastify.post('/retry/:jobId', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'), // ⭐ ADMIN AUTH
    schema: {
      description: 'Retries a specific failed webhook job.',
      tags: ['Admin', 'Webhook Operations'],
      summary: 'Retry a failed job',
      params: {
        type: 'object',
        properties: {
          jobId: { type: 'string', description: 'The ID of the job to retry.' },
        },
        required: ['jobId'],
      },
      response: {
        200: {
          description: 'Job successfully queued for retry.',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string' },
          },
        },
        400: { description: 'Bad request - job cannot be retried', ...errorResponseSchema },
        404: { description: 'Job not found', ...errorResponseSchema },
        500: { description: 'Failed to retry job', ...errorResponseSchema },
        503: { description: 'Queue not available', ...errorResponseSchema },
      },
      // security: [{ adminAuth: [] }], // If this should be admin protected
    },
  }, async (request, reply) => {
    // Admin access is already enforced by requireAdmin middleware
    const user = (request as any).user;
    const { jobId } = request.params as { jobId: string };
    
    fastify.log.info({ 
      adminEmail: user.email,
      action: 'webhook.job.retry_attempt',
      jobId
    }, 'Admin attempting to retry webhook job');

    const queue = getQueue();
    
    if (!queue) {
      return reply.code(503).send({ statusCode: 503, error: 'Service Unavailable', message: 'Queue not available' });
    }

    try {
      const job = await queue.getJob(jobId);
      
      if (!job) {
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Job not found' });
      }
      if (typeof job.retry !== 'function') {
        // This can happen if the job is not in a state that allows retry (e.g., already active or completed)
        // Or if it's not a job object from BullMQ that has .retry()
        return reply.code(400).send({statusCode: 400, error: 'Bad Request', message: 'Job cannot be retried in its current state.'});
      }

      await job.retry();
      
      return reply.send({
        success: true,
        message: `Job ${jobId} queued for retry`
      });
    } catch (error: any) {
      fastify.log.error({jobId, error: error.message, stack: error.stack }, 'Error retrying job');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retry job',
        // details: error.message // Be cautious with exposing internal error messages
      });
    }
  });

  // Manual webhook test endpoint
  fastify.post('/test', {
    schema: {
      description: 'Allows sending a test payload to a specified webhook URL via the queue.',
      tags: ['Webhook Operations', 'Testing'],
      summary: 'Test a webhook endpoint',
      body: {
        type: 'object',
        required: ['webhookUrl'],
        properties: {
          webhookUrl: { type: 'string', format: 'url', description: 'The URL to send the test payload to.' },
          testPayload: {
            type: 'object',
            additionalProperties: true,
            description: 'Optional custom payload. If not provided, a default test payload is used.',
            example: { message: "Hello World", testValue: 123 }
          },
        },
      },
      response: {
        200: {
          description: 'Test webhook successfully queued.',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            jobId: { type: 'string', nullable: true, description: 'The ID of the queued job.' },
            message: { type: 'string' },
          },
        },
        400: { description: 'webhookUrl is required or payload is invalid', ...errorResponseSchema },
        500: { description: 'Failed to queue test webhook', ...errorResponseSchema },
      },
      // security: [{ userApiKey: [] }], // Or admin, depending on who should access this
    },
  }, async (request, reply) => {
    const { webhookUrl, testPayload } = request.body as { webhookUrl: string; testPayload?: Record<string, any> };
    
    if (!webhookUrl) { // Redundant due to schema but good practice
      return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'webhookUrl is required' });
    }

    const payload = testPayload || {
      messageId: `test-${Date.now()}@local-test.com`,
      envelope: {
        from: '<EMAIL>',
        to: ['<EMAIL>'],
        messageId: `test-msg-${Date.now()}`,
      },
      message: {
        sender: { email: '<EMAIL>', name: 'Test Sender' },
        recipient: { email: '<EMAIL>', name: 'Test Recipient' },
        subject: 'Test Email from Webhook Test Endpoint',
        textBody: 'This is a test email for webhook validation, sent via /api/webhook/test.',
        htmlBody: '<p>This is a test email for webhook validation, sent via /api/webhook/test.</p>',
        attachments: [],
        headers: [{key: 'X-Test-Webhook', value: 'true'}],
        date: new Date().toISOString(),
      },
      domain: 'example.com', // Example domain
      timestamp: new Date().toISOString(),
    };

    try {
      // Assuming queueWebhookDelivery can handle this payload structure
      // It might be better to have a dedicated "test queue" or a specific job type
      const { queueWebhookDelivery } = await import('../services/queue.js');
      const jobId = await queueWebhookDelivery(webhookUrl, payload as any); // No secret for admin test endpoint
      
      return reply.send({
        success: true,
        jobId: jobId?.toString(), // Ensure jobId is string or null
        message: 'Test webhook queued for delivery',
      });
    } catch (error: any) {
      fastify.log.error({ webhookUrl, error: error.message, stack: error.stack }, 'Error queueing test webhook');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to queue test webhook',
        // details: error.message,
      });
    }
  });
};

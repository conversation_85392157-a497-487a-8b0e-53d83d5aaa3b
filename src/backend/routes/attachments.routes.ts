import { FastifyPluginAsync } from 'fastify';
import fastifyRateLimit from '@fastify/rate-limit';
import { prisma } from '../lib/prisma.js';
import { S3StorageService } from '../services/storage/s3-storage.service.js';
import { logger } from '../utils/logger.js';
import { AttachmentStatus } from '@prisma/client';

interface DownloadParams {
  fileId: string;
}

interface StatusParams {
  fileId: string;
}

export const attachmentRoutes: FastifyPluginAsync = async (fastify) => {
  // We'll instantiate S3StorageService per request based on stored s3Config
  const defaultS3 = new S3StorageService();

  // Register rate limiting for attachment downloads
  // Security model: We don't require authentication since webhook recipients need access.
  // Protection is provided by: 1) Hard-to-guess fileIds, 2) Expiration times, 3) Rate limiting
  await fastify.register(fastifyRateLimit, {
    max: 100, // Maximum 100 requests
    timeWindow: '15 minutes', // Per 15 minute window
    keyGenerator: (request) => {
      // Rate limit by IP + fileId combination to prevent abuse of specific files
      const fileId = (request.params as any)?.fileId || 'unknown';
      return `${request.ip}:${fileId}`;
    },
    errorResponseBuilder: (request, context) => {
      // Log rate limit violations for monitoring
      logger.warn({
        ip: request.ip,
        fileId: (request.params as any)?.fileId,
        path: request.url,
        limit: context.max,
        ttl: context.ttl
      }, 'Rate limit exceeded for attachment download');
      
      return {
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Please wait ${Math.ceil(context.ttl / 1000)} seconds before trying again.`,
        retry_after: Math.ceil(context.ttl / 1000),
        limit: context.max
      };
    }
  });

  // Download attachment endpoint
  fastify.get<{ Params: DownloadParams }>('/attachments/:fileId/download', {
    schema: {
      description: 'Download an attachment file or get upload status',
      tags: ['Attachments'],
      summary: 'Download attachment',
      params: {
        type: 'object',
        properties: {
          fileId: { type: 'string', description: 'Unique file identifier' }
        },
        required: ['fileId']
      },
      response: {
        200: {
          description: 'File download redirect',
          type: 'string',
          format: 'binary'
        },
        202: {
          description: 'File still processing',
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['pending', 'uploading'] },
            message: { type: 'string' },
            retryAfter: { type: 'number', description: 'Retry after seconds' },
            estimatedCompletion: { type: 'string', format: 'date-time', nullable: true }
          }
        },
        404: {
          description: 'File not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        410: {
          description: 'File expired or failed',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { fileId } = request.params;

    try {
      // Fetch attachment with email and user info
      const attachment = await prisma.attachmentFile.findUnique({
        where: { id: fileId },
        include: {
          email: {
            include: {
              user: true,
              domain: true
            }
          }
        }
      });

      if (!attachment) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'File not found'
        });
      }

      // Check if file has expired
      if (attachment.expiresAt < new Date()) {
        // Mark as expired if not already
        if (attachment.uploadStatus !== AttachmentStatus.EXPIRED) {
          await prisma.attachmentFile.update({
            where: { id: fileId },
            data: { uploadStatus: AttachmentStatus.EXPIRED }
          });
        }

        return reply.code(410).send({
          error: 'Gone',
          message: 'File has expired and is no longer available'
        });
      }

      // Handle different upload statuses
      switch (attachment.uploadStatus) {
        case AttachmentStatus.PENDING:
        case AttachmentStatus.UPLOADING:
          // File is still being processed
          const estimatedTime = estimateCompletionTime(attachment);
          return reply.code(202).send({
            status: attachment.uploadStatus.toLowerCase(),
            message: 'File is still being processed. Please try again in a moment.',
            retryAfter: 5, // Retry after 5 seconds
            estimatedCompletion: estimatedTime
          });

        case AttachmentStatus.FAILED:
          return reply.code(410).send({
            error: 'Upload Failed',
            message: attachment.errorMessage || 'File upload failed. Please contact support if this persists.'
          });

        case AttachmentStatus.EXPIRED:
          return reply.code(410).send({
            error: 'Gone',
            message: 'File has expired and is no longer available'
          });

        case AttachmentStatus.COMPLETED:
          // File is ready for download
          if (!attachment.s3Key) {
            logger.error({ fileId, attachment }, 'Completed attachment missing S3 key');
            return reply.code(500).send({
              error: 'Internal Server Error',
              message: 'File data is corrupted. Please contact support.'
            });
          }

          // Update download tracking
          await prisma.attachmentFile.update({
            where: { id: fileId },
            data: {
              downloadCount: { increment: 1 },
              lastDownloadAt: new Date()
            }
          });

          // Generate presigned URL for S3 download
          try {
            // Use per-file s3Config when present; fall back to default env config
            const s3Service = attachment.s3Config ? new S3StorageService(attachment.s3Config as any) : defaultS3;
            const downloadUrl = await s3Service.generatePresignedUrl(
              attachment.s3Key,
              3600 // 1 hour expiry for presigned URL
            );

            // Log download event
            logger.info({
              fileId,
              filename: attachment.filename,
              userId: attachment.email.userId,
              downloadCount: attachment.downloadCount + 1
            }, 'Attachment download initiated');

            // Redirect to S3 presigned URL
            return reply.redirect(downloadUrl);
          } catch (error: any) {
            logger.error({
              fileId,
              s3Key: attachment.s3Key,
              error: error.message
            }, 'Failed to generate download URL');

            return reply.code(500).send({
              error: 'Internal Server Error',
              message: 'Failed to generate download link. Please try again.'
            });
          }

        default:
          logger.error({ fileId, status: attachment.uploadStatus }, 'Unknown attachment status');
          return reply.code(500).send({
            error: 'Internal Server Error',
            message: 'Unknown file status'
          });
      }
    } catch (error: any) {
      logger.error({
        fileId,
        error: error.message,
        stack: error.stack
      }, 'Error processing attachment download');

      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Failed to process download request'
      });
    }
  });

  // Check attachment status (without triggering download)
  fastify.get<{ Params: StatusParams }>('/attachments/:fileId/status', {
    schema: {
      description: 'Check attachment upload status without downloading',
      tags: ['Attachments'],
      summary: 'Get attachment status',
      params: {
        type: 'object',
        properties: {
          fileId: { type: 'string', description: 'Unique file identifier' }
        },
        required: ['fileId']
      },
      response: {
        200: {
          description: 'Attachment status',
          type: 'object',
          properties: {
            fileId: { type: 'string' },
            filename: { type: 'string' },
            size: { type: 'number' },
            contentType: { type: 'string' },
            status: { 
              type: 'string', 
              enum: ['pending', 'uploading', 'completed', 'failed', 'expired'] 
            },
            uploadedAt: { type: 'string', format: 'date-time', nullable: true },
            expiresAt: { type: 'string', format: 'date-time' },
            downloadCount: { type: 'number' },
            lastDownloadAt: { type: 'string', format: 'date-time', nullable: true },
            errorMessage: { type: 'string', nullable: true }
          }
        },
        404: {
          description: 'File not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { fileId } = request.params;

    try {
      const attachment = await prisma.attachmentFile.findUnique({
        where: { id: fileId }
      });

      if (!attachment) {
        return reply.code(404).send({
          error: 'Not Found',
          message: 'File not found'
        });
      }

      return reply.send({
        fileId: attachment.id,
        filename: attachment.filename,
        size: attachment.size,
        contentType: attachment.contentType,
        status: attachment.uploadStatus.toLowerCase(),
        uploadedAt: attachment.uploadStatus === AttachmentStatus.COMPLETED 
          ? attachment.updatedAt.toISOString() 
          : null,
        expiresAt: attachment.expiresAt.toISOString(),
        downloadCount: attachment.downloadCount,
        lastDownloadAt: attachment.lastDownloadAt?.toISOString() || null,
        errorMessage: attachment.errorMessage
      });
    } catch (error: any) {
      logger.error({
        fileId,
        error: error.message
      }, 'Error fetching attachment status');

      return reply.code(500).send({
        error: 'Internal Server Error',
        message: 'Failed to fetch attachment status'
      });
    }
  });
};

/**
 * Estimate completion time based on file size and processing type
 */
function estimateCompletionTime(attachment: any): string | null {
  // For sync uploads that are still pending, estimate based on creation time
  if (attachment.processingType === 'sync') {
    const elapsed = Date.now() - attachment.createdAt.getTime();
    if (elapsed < 5000) {
      // Less than 5 seconds, should complete soon
      return new Date(Date.now() + 5000).toISOString();
    }
    // Something might be wrong if sync upload takes this long
    return null;
  }

  // For async uploads, estimate based on file size
  const sizeMB = attachment.size / (1024 * 1024);
  let estimatedSeconds = 10; // Base time

  if (sizeMB < 5) {
    estimatedSeconds = 10;
  } else if (sizeMB < 20) {
    estimatedSeconds = 30;
  } else if (sizeMB < 50) {
    estimatedSeconds = 60;
  } else {
    estimatedSeconds = 120;
  }

  // Add some buffer time
  const estimatedMs = (estimatedSeconds + 5) * 1000;
  return new Date(Date.now() + estimatedMs).toISOString();
}
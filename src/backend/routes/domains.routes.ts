import { FastifyPluginAsync } from 'fastify';
import { DomainsController } from '../controllers/user/domains.controller.js';
import { domainSchemas } from '../schemas/user/domain.schemas.js';
import {
  requireAuth,
  requireDomainRead,
  requireDomainWrite,
  requireDomainCreate,
  requireDomainConfig
} from '../middleware/unified-auth.middleware.js';
import { smartDomainAuth, domainOwnerAuth } from '../middleware/enhanced-auth.middleware.js';

const domainsController = new DomainsController();

export const domainsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's domains
  fastify.get('/domains', {
    preHandler: [requireDomainRead()],
    schema: {
      tags: ['User Domains'], 
      summary: 'List user domains',
      description: 'Retrieves a list of domains for the authenticated user. Requires domains:read scope.',
      response: {
        200: domainSchemas.DomainListResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { ...errorResponseSchema, description: 'Failed to retrieve domain configurations.'}
      },
    }
  }, domainsController.getDomains.bind(domainsController));

  // Create new domain - UNIFIED SECURITY: validates scope + plan + limits
  fastify.post('/domains', {
    preHandler: [requireDomainCreate()],
    schema: {
      tags: ['User Domains'], 
      summary: 'Create a new domain',
      description: 'Registers a new domain for the authenticated user. Validates domains:write scope and available domain slots.',
      body: domainSchemas.CreateDomainRequest,
      response: {
        201: domainSchemas.CreateDomainResponse,
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' }, 
        402: { $ref: 'ErrorResponse#' }, // Payment Required
        403: errorResponseSchema,
        409: errorResponseSchema,
      },
    }
  }, domainsController.createDomain.bind(domainsController));

  // Get specific domain by ID
  fastify.get('/domains/:domainId', {
    preHandler: [domainOwnerAuth], // Validates ownership + read permissions
    schema: {
      tags: ['User Domains'], 
      summary: 'Get domain by ID',
      description: 'Retrieves a specific domain by ID. Validates ownership and domains:read scope.',
      params: domainSchemas.DomainIdParam,
      response: { 
        200: domainSchemas.DomainDetailResponse, 
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.getDomain.bind(domainsController));

  // Update domain - Smart middleware detects advanced config needs
  fastify.put('/domains/:domainId', {
    preHandler: [smartDomainAuth], // Auto-detects if advanced config used, applies Pro+ requirements
    schema: {
      tags: ['User Domains'],
      summary: 'Update domain', 
      description: 'Updates domain settings. Automatically validates plan permissions for advanced configuration options.',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainRequest,
      response: {
        200: domainSchemas.UpdateDomainResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' }, // Payment Required for advanced features
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, domainsController.updateDomain.bind(domainsController));

  // Update domain status
  fastify.put('/domains/:domainId/status', {
    preHandler: [domainOwnerAuth],
    schema: {
      tags: ['User Domains'], 
      summary: 'Toggle domain status', 
      description: 'Toggle the active status of a domain by ID. Validates ownership and domains:write scope.',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainStatusRequest,
      response: { 
        200: domainSchemas.UpdateDomainStatusResponse, 
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.updateDomainStatus.bind(domainsController));

  // Update domain webhook
  fastify.put('/domains/:domainId/webhook', {
    preHandler: [domainOwnerAuth],
    schema: {
      tags: ['User Domains'], 
      summary: 'Update domain webhook', 
      description: 'Update the webhook for a domain by ID. Validates ownership and domains:write scope.',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainWebhookRequest,
      response: { 
        200: domainSchemas.UpdateDomainWebhookResponse, 
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.updateDomainWebhook.bind(domainsController));

  // Delete domain
  fastify.delete('/domains/:domainId', {
    preHandler: [domainOwnerAuth],
    schema: {
      tags: ['User Domains'], 
      summary: 'Delete domain by ID',
      description: 'Deletes a domain by ID. Validates ownership and domains:write scope.',
      params: domainSchemas.DomainIdParam,
      response: { 
        200: domainSchemas.DeleteDomainResponse, 
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema, 
        500: errorResponseSchema
      }
    }
  }, domainsController.deleteDomain.bind(domainsController));

  // Verify domain
  fastify.post('/domains/:domainId/verify', {
    preHandler: [domainOwnerAuth],
    schema: {
      tags: ['User Domains'], 
      summary: 'Verify domain by ID',
      description: 'Initiates domain verification process. Validates ownership and domains:write scope.',
      params: domainSchemas.DomainIdParam,
      response: { 
        200: domainSchemas.VerifyDomainResponse, 
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        429: errorResponseSchema,
        500: errorResponseSchema 
      }
    }
  }, domainsController.verifyDomain.bind(domainsController));

  // Get domain spam filtering settings
  fastify.get('/domains/:domainId/spam-filter', {
    preHandler: [domainOwnerAuth],
    schema: {
      tags: ['User Domains'],
      summary: 'Get domain spam filtering settings',
      description: 'Gets the current spam filtering configuration for a domain. Requires domain ownership.',
      params: domainSchemas.DomainIdParam,
      response: {
        200: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean' },
            thresholds: {
              type: 'object',
              properties: {
                green: { type: 'number', description: 'Score below which emails pass through normally' },
                red: { type: 'number', description: 'Score above which emails are marked as spam' }
              }
            }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, domainsController.getSpamFilterSettings.bind(domainsController));

  // Update domain spam filtering settings
  fastify.put('/domains/:domainId/spam-filter', {
    preHandler: [requireDomainConfig()], // Requires Pro+ plan
    schema: {
      tags: ['User Domains'],
      summary: 'Update domain spam filtering settings',
      description: 'Updates spam filtering configuration for a domain. Requires Pro plan or higher.',
      params: domainSchemas.DomainIdParam,
      body: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean' },
          thresholds: {
            type: 'object',
            properties: {
              green: { type: 'number', minimum: 0, maximum: 20, description: 'Score below which emails pass through normally (0-20)' },
              red: { type: 'number', minimum: 0, maximum: 20, description: 'Score above which emails are marked as spam (0-20)' }
            },
            additionalProperties: false
          }
        },
        required: ['enabled'],
        additionalProperties: false
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            settings: {
              type: 'object',
              properties: {
                enabled: { type: 'boolean' },
                thresholds: {
                  type: 'object',
                  properties: {
                    green: { type: 'number' },
                    red: { type: 'number' }
                  }
                }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' }, // Payment Required
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, domainsController.updateSpamFilterSettings.bind(domainsController));

  // Get domain status (lighter auth requirements)
  fastify.get('/domains/:domainId/status', {
    preHandler: [requireAuth()], // Only requires authentication, no ownership check
    schema: {
      tags: ['User Domains'],
      summary: 'Get domain verification status',
      description: 'Gets the current verification status of a domain. Available to all authenticated users.',
      params: domainSchemas.DomainIdParam,
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            domain: { type: 'string' },
            verificationStatus: { type: 'string' },
            verified: { type: 'boolean' },
            lastVerificationAttempt: { type: 'string', format: 'date-time' },
            nextVerificationCheck: { type: 'string', format: 'date-time' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, domainsController.getDomain.bind(domainsController));
};

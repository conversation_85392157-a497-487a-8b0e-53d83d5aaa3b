import { FastifyBaseLogger, FastifyPluginAsync } from 'fastify';
import { EmailParser } from '../services/email-parser.js';
import { queueWebhookDelivery, queueAttachmentUpload, getRedisClient } from '../services/queue.js';
import { prisma } from '../lib/prisma.js';
import { UserAccountService } from '../services/user-account.service.js';
import { webSocketService } from '../services/websocket.service.js';
import { PlanLimitMiddleware } from '../middleware/plan-limits.middleware.js';
import { NotificationService } from '../services/notifications/notification.service.js';
import { env } from '../config/env.js';
import { S3StorageService, sanitizeS3Folder } from '../services/storage/s3-storage.service.js';
import { FileTypeService } from '../services/storage/file-type.service.js';
import { AttachmentStatus } from '@prisma/client';
import crypto from 'crypto';
import fs from 'fs';
import os from 'os';
import path from 'path';

/**
 * Helper to extract attachment content from ParsedAttachment with robust validation
 * Returns Buffer or Readable depending on source
 */
function isSafeTempPath(p: string): boolean {
  try {
    const real = fs.realpathSync(p);
    const tmp = path.resolve(os.tmpdir());
    return real.startsWith(tmp + path.sep) || real === tmp;
  } catch {
    return false;
  }
}

function getAttachmentBody(attachment: any): Buffer | NodeJS.ReadableStream | null {
  // New binary source: Buffer
  if (attachment.source?.kind === 'buffer' && attachment.source.data instanceof Buffer) {
    return attachment.source.data;
  }
  // New binary source: tempfile → create read stream
if (attachment.source?.kind === 'file' && typeof attachment.source.path === 'string') {
    try {
      if (!isSafeTempPath(attachment.source.path)) {
        return null;
      }
      return fs.createReadStream(attachment.source.path);
    } catch (err) {
      return null;
    }
  }
  // Legacy base64 content
  if (attachment.content && typeof attachment.content === 'string') {
    try {
      return Buffer.from(attachment.content, 'base64');
    } catch (err) {
      return null;
    }
  }
  return null;
}

/**
 * Calculate email expiration date based on user settings and plan defaults
 */
async function calculateEmailExpiration(userId: string, planType: string): Promise<Date> {
  // Get user's retention settings
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      settings: {
        select: { dataRetentionHours: true }
      }
    }
  });

  let retentionHours: number;

  // Use custom setting if available, otherwise use plan default
  if (user?.settings?.dataRetentionHours !== null && user?.settings?.dataRetentionHours !== undefined) {
    retentionHours = user.settings.dataRetentionHours;
  } else {
    // Plan-based defaults
    switch (planType) {
      case 'free':
        retentionHours = 2;
        break;
      case 'pro':
      case 'enterprise':
        retentionHours = 24;
        break;
      default:
        retentionHours = 2; // Default to free plan retention
    }
  }

  return new Date(Date.now() + (retentionHours * 60 * 60 * 1000));
}

// This route will be called by Postfix when an email arrives
export const emailRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' }; // Reference to global schema

  // Endpoint for processing incoming emails (called by mail server)
  // This corresponds to /webhook/email in openapi-spec.ts
  fastify.post('/process', {
    schema: {
      description: 'Endpoint for Postfix (or other MTA) to forward emails to. This typically requires IP whitelisting or a secret token in the URL/header, not standard user API keys.',
      tags: ['Webhook'],
      summary: 'Receive incoming email (via MTA)',
      body: {
        // This reflects the `message/rfc822` part of the OpenAPI spec
        // Fastify handles raw body as string or buffer if content type matches.
        // For Swagger, we describe it as a binary string.
        type: 'string',
        format: 'binary', // Or just 'string' if specific format isn't critical for docs
        description: 'Raw email content (e.g., RFC 822 format)',
      },
      response: {
        202: {
          description: 'Email accepted for processing.',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            messageId: { type: 'string', example: '<<EMAIL>>' },
            status: { type: 'string', example: 'queued' },
            emailId: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
            isTestWebhook: { type: 'boolean', nullable: true, example: true }
          },
        },
        '400': { description: 'Invalid email data or missing data', ...errorResponseSchema },
        '403': { description: 'Domain not verified or inactive', ...errorResponseSchema },
        404: { description: 'Domain not configured or no webhook for address', ...errorResponseSchema },
        500: { description: 'Internal server error processing email', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    try {
      const rawEmail = request.body as string | Buffer;

      if (!rawEmail || rawEmail.length === 0) {
        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'No email data provided' });
      }

      // Get shared Redis client from queue service
      const redis = getRedisClient();

      // Check if this email was pre-processed by advanced-process-email.js
      const isAdvancedProcessing = request.headers['x-advanced-processing'] === 'true';
      const messageIdHeader = request.headers['x-message-id'] as string;

      // Fetch metadata from Redis if advanced processing
      let attachmentMetadata: any = null;
      let attachmentCount = 0;

      if (isAdvancedProcessing && messageIdHeader) {
        try {
          const normalizedMessageId = (messageIdHeader || '').replace(/[<>]/g, '');
          const metaKey = `advmeta:${normalizedMessageId}`;
          const metaStr = await redis.get(metaKey);

          if (metaStr) {
            attachmentMetadata = JSON.parse(metaStr);
            attachmentCount = attachmentMetadata.totalAttachments || 0;

            // Delete the key after reading (one-time use)
            await redis.del(metaKey);

            fastify.log.debug({
              messageId: messageIdHeader,
              attachmentCount,
              hasRejected: attachmentMetadata.rejected?.length > 0
            }, 'Retrieved advanced processing metadata from Redis');
          } else {
            fastify.log.warn({
              messageId: messageIdHeader,
              key: metaKey
            }, 'No Redis metadata found for advanced processing - falling back to parsing');
          }
        } catch (redisError) {
          fastify.log.error({
            error: redisError,
            messageId: messageIdHeader
          }, 'Failed to retrieve Redis metadata - continuing with fallback');
        }
      }

      fastify.log.debug({
        isAdvancedProcessing,
        attachmentCount,
        hasAttachmentMetadata: !!attachmentMetadata,
        processingScript: request.headers['x-processing-script']
      }, 'Email processing detection');

      // Note: No need to close Redis connection as we're using the shared client from queue service

      // Extract target domain from the raw email headers first
      const targetDomain = extractDomainFromRawEmail(rawEmail);

      if (!targetDomain) {
        // Enhanced logging for debugging SpamAssassin issues
        const emailText = typeof rawEmail === 'string' ? rawEmail : rawEmail.toString();
        const headerSection = emailText.split('\n\n')[0];
        const relevantHeaders = headerSection.split('\n')
          .filter(line => /^(X-Original-To|Delivered-To|Envelope-To|To):/i.test(line))
          .join('\n');

        // Show ALL headers for comprehensive debugging
        const allHeaders = headerSection.split('\n')
          .filter(line => line.includes(':'))
          .map(line => line.split(':')[0])
          .join(', ');

        fastify.log.warn({
          relevantHeaders,
          allHeaders,
          emailSize: emailText.length,
          isSpamProcessed: emailText.includes('X-Spam-Status'),
          processingScript: request.headers['x-processing-script'],
          firstFewLines: headerSection.split('\n').slice(0, 10).join('\n')
        }, 'No target domain found in email headers');

        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'Invalid recipient domain in email headers' });
      }

      // Handle user.emailconnect.eu domain programmatically
      if (targetDomain === 'user.emailconnect.eu') {
        const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);
        const recipientEmail = parsedEmail.message.recipient.email;
        const localPart = recipientEmail.split('@')[0];

        // Extract the user ID suffix from the local part (before any +tag)
        const userIdSuffix = localPart.includes('+') ? localPart.substring(0, localPart.indexOf('+')) : localPart;
        // Only +test is considered a test webhook
        const isTestEmail = localPart.endsWith('+test');

        const testUser = await findUserByIdSuffix(userIdSuffix);

        if (!testUser) {
          fastify.log.info({
            domain: targetDomain,
            userIdSuffix,
            email: recipientEmail,
            isTestEmail
          }, 'System email rejected - no user found with suffix');
          return reply.code(404).send({
            statusCode: 404,
            error: 'Not Found',
            message: 'No user found for this email address'
          });
        }

        // Check user's quota but do NOT increment on success for test webhooks
        const quotaValidation = await PlanLimitMiddleware.validateEmailQuota(testUser.id);
        if (!quotaValidation.allowed) {
          fastify.log.warn({
            domain: targetDomain,
            userId: testUser.id,
            currentUsage: quotaValidation.currentUsage,
            limit: quotaValidation.limit,
            reason: quotaValidation.reason
          }, 'Test email rejected - user exceeded email quota');

          return reply.code(429).send({
            statusCode: 429,
            error: 'Too Many Requests',
            message: quotaValidation.reason,
            currentUsage: quotaValidation.currentUsage,
            monthlyLimit: quotaValidation.limit,
          });
        }

        // Find user's active webhook if available (auto-provisioned during signup)
        const webhook = await prisma.webhook.findFirst({
          where: {
            userId: testUser.id,
            active: true
          },
          orderBy: { createdAt: 'desc' }
        });

        // Create a virtual alias object for the webhook delivery system
        const virtualAlias = {
          id: `test-${testUser.id}`,
          email: parsedEmail.message.recipient.email,
          webhook: webhook || null,
          active: true,
          configuration: {
            allowAttachments: false,
            includeEnvelope: false,
            includeHeaders: false
          }
        };

        // Create email record (test webhook, no domain)
        const expiresAt = await calculateEmailExpiration(testUser.id, 'free');
        const emailRecord = await prisma.email.create({
          data: {
            messageId: parsedEmail.envelope.messageId,
            fromAddress: parsedEmail.message.sender.email,
            toAddresses: [parsedEmail.message.recipient.email],
            subject: parsedEmail.message.subject,
            domainId: null,
            userId: testUser.id,
            webhookPayload: parsedEmail,
            expiresAt,
            isTestWebhook: true,
            webhookId: webhook?.id || null,
            deliveryStatus: webhook ? 'PENDING' : 'DELIVERED',
            deliveredAt: webhook ? null : new Date()
          },
        });

        // Prepare queue payload
        const filteredEmail = applyConfigurationFilters(parsedEmail, virtualAlias.configuration);
        const queuePayload = {
          ...filteredEmail,
          _internalMessageId: parsedEmail.envelope.messageId,
          domainId: null,
          aliasId: virtualAlias.id
        };

        // If user has a webhook, queue delivery; otherwise, consider delivered immediately
        if (webhook) {
          await queueWebhookDelivery(
            webhook.url,
            queuePayload,
            webhook.webhookSecret,
            webhook.customHeaders as Record<string, string> | undefined
          );
        }

        if (!isTestEmail) {
          // Count usage for non-test system emails (e.g., +support)
          await UserAccountService.incrementUserEmailUsage(testUser.id);
        }

        // Emit WebSocket event
        webSocketService.emitEmailProcessed(testUser.id, {
          messageId: parsedEmail.envelope.messageId,
          fromAddress: parsedEmail.message.sender.email,
          subject: parsedEmail.message.subject || '(no subject)',
          isTestWebhook: true,
          deliveryStatus: webhook ? 'PENDING' : 'DELIVERED',
          timestamp: new Date().toISOString()
        });

        fastify.log.info({
          messageId: parsedEmail.envelope.messageId,
          userId: testUser.id,
          email: parsedEmail.message.recipient.email,
          webhookUrl: webhook?.url
        }, webhook ? 'Test email processed and queued for webhook delivery' : 'Test email processed without webhook - marked delivered');

        return reply.code(202).send({
          success: true,
          messageId: parsedEmail.envelope.messageId,
          status: webhook ? 'queued' : 'delivered',
          emailId: emailRecord.id,
          isTestWebhook: true
        });
      }

      // Security check: Only process emails for verified domains
      const domainConfig = await prisma.domain.findUnique({
        where: { domain: targetDomain },
        include: {
          aliases: {
            include: { webhook: true },
            where: { active: true }
          }
        },
      });

      if (!domainConfig) {
        fastify.log.info({ domain: targetDomain }, 'Domain not configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Domain not configured' });
      }

      // Critical security check: Domain must be verified to receive emails
      if (!domainConfig.verified ) { // Corrected: Prisma model likely uses 'verified'
        fastify.log.warn({
        domain: targetDomain,
        verified: domainConfig.verified
        }, 'Email rejected - domain not verified');

        return reply.code(403).send({
        statusCode: 403,
        error: 'Forbidden',
        message: 'Domain not verified. Email processing is disabled until domain ownership is verified.',
          verificationStatus: domainConfig.verificationStatus,
        });
      }

      // Check user's email quota using plan limit middleware
      const user = await prisma.user.findUnique({
        where: { id: domainConfig.userId },
        select: { planType: true }
      });

      if (!user) {
        return reply.code(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'User data inconsistency'
        });
      }

      const quotaValidation = await PlanLimitMiddleware.validateEmailQuota(domainConfig.userId);
      if (!quotaValidation.allowed) {
        fastify.log.warn({
          domain: targetDomain,
          userId: domainConfig.userId,
          currentUsage: quotaValidation.currentUsage,
          limit: quotaValidation.limit,
          reason: quotaValidation.reason
        }, 'Email rejected - user exceeded email quota');

        return reply.code(429).send({
          statusCode: 429,
          error: 'Too Many Requests',
          message: quotaValidation.reason,
          currentUsage: quotaValidation.currentUsage,
          monthlyLimit: quotaValidation.limit,
        });
      }

      // Domain must be active
      if (!domainConfig.active) { // Assuming 'active' field exists, as per original logic
        fastify.log.info({ domain: targetDomain }, 'Email rejected - domain inactive');
        return reply.code(403).send({ statusCode: 403, error: 'Forbidden', message: 'Domain is inactive' });
      }

      // Parse the email using the enhanced parser or handle pre-processed attachments
      let parsedEmail: any;

      if (isAdvancedProcessing) {
        // Email was pre-processed by advanced script
        // First get a basic parse to determine email details for webhook config lookup
        const basicParsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);
        
        // Look up webhook configuration to determine if binary content is needed
        const tempWebhookConfig = await lookupWebhookConfig(domainConfig, basicParsedEmail.envelope.processed.alias, fastify.log);
        const aliasConfig = tempWebhookConfig?.configuration;
        const needsBinaryContent = aliasConfig?.attachmentHandling === 'storage';
        
        // Re-parse with binary content if needed and no advanced metadata available
        if (needsBinaryContent && basicParsedEmail.message.attachments?.length > 0 && (!attachmentMetadata || !attachmentMetadata.attachments)) {
          parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain, { 
            includeBinary: true,
            largeAttachmentStrategy: 'buffer',
            syncThresholdMB: 2
          });
          // Keep the same envelope info
          parsedEmail.envelope = basicParsedEmail.envelope;
        } else {
          parsedEmail = basicParsedEmail;
        }

        // Integrate pre-processed attachment data if attachments exist
        if (attachmentCount > 0 && attachmentMetadata && attachmentMetadata.attachments) {
          parsedEmail.message.attachments = attachmentMetadata.attachments.map((att: any) => ({
            filename: att.filename,
            contentType: att.contentType,
            size: att.size,
            downloadUrl: att.downloadUrl,
            status: att.status || 'unknown',
            storage: att.storage || 'unknown',
            uploadType: att.uploadType,
            excluded: att.excluded || false,
            excludeReason: att.excludeReason
          }));

          // Add async upload indicator
          const hasAsyncUploads = attachmentMetadata.attachments.some((att: any) => att.status === 'pending');
          if (hasAsyncUploads) {
            parsedEmail._hasAsyncUploads = true;
          }

          fastify.log.debug({
            domain: targetDomain,
            messageId: parsedEmail.envelope.messageId,
            attachmentCount: parsedEmail.message.attachments.length,
            hasAsyncUploads
          }, 'Integrated pre-processed attachments');

          // Create notification for rejected attachments if any exist
          if (attachmentMetadata.rejectedAttachments && attachmentMetadata.rejectedAttachments.length > 0) {
            try {
              await NotificationService.createAttachmentRejectedNotification(
                domainConfig.userId,
                attachmentMetadata.rejectedAttachments,
                parsedEmail.message.subject
              );

              fastify.log.info({
                userId: domainConfig.userId,
                rejectedCount: attachmentMetadata.rejectedAttachments.length,
                messageId: parsedEmail.envelope.messageId
              }, 'Created attachment rejection notification');
            } catch (notificationError: any) {
              // Don't fail email processing if notification fails
              fastify.log.warn({
                userId: domainConfig.userId,
                error: notificationError.message,
                messageId: parsedEmail.envelope.messageId
              }, 'Failed to create attachment rejection notification');
            }
          }
        } else if (attachmentCount === 0) {
          fastify.log.debug({
            domain: targetDomain,
            messageId: parsedEmail.envelope.messageId
          }, 'Advanced processing with no attachments');
        }
      } else {
        // Normal email processing flow (free users or simple processing)
        // First get a basic parse to determine email details for webhook config lookup
        const basicParsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);
        
        // Look up webhook configuration to determine if binary content is needed
        const tempWebhookConfig = await lookupWebhookConfig(domainConfig, basicParsedEmail.envelope.processed.alias, fastify.log);
        const aliasConfig = tempWebhookConfig?.configuration;
        const needsBinaryContent = aliasConfig?.attachmentHandling === 'storage';
        
        // Re-parse with binary content if needed
        if (needsBinaryContent && basicParsedEmail.message.attachments?.length > 0) {
          parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain, { 
            includeBinary: true,
            largeAttachmentStrategy: 'buffer', // Use buffer for now, can switch to tempfile later
            syncThresholdMB: 2
          });
          // Keep the same envelope info
          parsedEmail.envelope = basicParsedEmail.envelope;
        } else {
          parsedEmail = basicParsedEmail;
        }
      }

      // Look up webhook configuration for this domain/email (re-use if already looked up)
      const webhookConfig = await lookupWebhookConfig(domainConfig, parsedEmail.envelope.processed.alias, fastify.log);

      if (!webhookConfig) {
        fastify.log.info({ domain: targetDomain, messageId: parsedEmail.envelope.messageId }, 'No webhook configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'No webhook configured for this email address or domain' });
      }

      // Debug logging for configuration
      fastify.log.debug({
        domain: targetDomain,
        messageId: parsedEmail.envelope.messageId,
        webhookConfig: webhookConfig.configuration,
        includeEnvelope: webhookConfig.configuration?.includeEnvelope,
        configType: typeof webhookConfig.configuration?.includeEnvelope
      }, 'Webhook configuration before filtering');

      // Apply configuration filters to the parsed email
      const filteredEmail = applyConfigurationFilters(parsedEmail, webhookConfig.configuration);

      // Debug logging for filtered email
      fastify.log.debug({
        domain: targetDomain,
        messageId: parsedEmail.envelope.messageId,
        hasEnvelopeAfterFilter: !!filteredEmail.envelope,
        originalMessageId: parsedEmail.envelope.messageId,
        filteredMessageId: filteredEmail.envelope?.messageId
      }, 'Email after configuration filtering');

      // Calculate expiration based on user settings and plan type
      const expiresAt = await calculateEmailExpiration(domainConfig.userId, user.planType);

      // Create email record for tracking
      const emailRecord = await prisma.email.create({
        data: {
          messageId: parsedEmail.envelope.messageId,
          fromAddress: parsedEmail.message.sender.email,
          toAddresses: [parsedEmail.message.recipient.email],
          subject: parsedEmail.message.subject,
          domainId: domainConfig.id,
          userId: domainConfig.userId, // Add userId for efficient retention queries
          aliasId: webhookConfig.aliasId || null,
          webhookPayload: parsedEmail, // Store the webhook payload for logs viewing
          expiresAt,
          hasAsyncUploads: false, // Will be updated if async uploads are queued
          // deliveryStatus defaults to PENDING
        },
      });

      // Centralized attachment processing - handle both advanced and simple paths
      if (parsedEmail.message.attachments && parsedEmail.message.attachments.length > 0) {
        const aliasConfig = webhookConfig.configuration;
        const requiresStorage = aliasConfig?.attachmentHandling === 'storage';

        // Enforce system-domain rules: never use S3 for system-domain aliases; only inline allowed if enabled.
        // Use domainConfig and alias.service convention for system domain
const isSystemDomain = domainConfig?.domain === 'user.emailconnect.eu' || targetDomain === 'user.emailconnect.eu';
        const allowInline = (aliasConfig?.attachmentHandling === 'inline') && (aliasConfig?.allowAttachments !== false);

        if (isSystemDomain) {
          // System domain: force inline-only; if storage requested, mark exclusions
          const processedAttachments = [] as any[];
          for (const attachment of parsedEmail.message.attachments) {
            // Validate against user rules to determine if inline is allowed
            const validation = await FileTypeService.validateAttachment({
              filename: attachment.filename,
              contentType: attachment.contentType,
              size: attachment.size
            }, domainConfig.userId);

            if (!validation.allowed || !allowInline || validation.handling !== 'inline') {
              processedAttachments.push({
                ...attachment,
                excluded: true,
                excludeReason: !allowInline ? 'disabled-in-settings' : (validation.reason || 'system-domain-inline-only'),
                manageUrl: '/settings#storage',
                status: 'rejected'
              });
              continue;
            }

            // Include inline content (keep as-is; ensure no S3 fields added)
            processedAttachments.push({
              ...attachment,
              status: 'included',
              storage: 'inline'
            });
          }

          parsedEmail.message.attachments = processedAttachments;
        } else if (requiresStorage) {
          // Initialize storage service
          const userSettings = await prisma.userSettings.findUnique({
            where: { userId: domainConfig.userId }
          });

          const useCustomS3 = (userSettings?.storageProvider === 's3-compatible') && !!userSettings?.s3Config;
          const s3Service = useCustomS3
            ? new S3StorageService(userSettings!.s3Config as any)
            : new S3StorageService(); // Use default env config
          // Determine S3 folder: custom S3 uses alias s3Folder (or fallback), service S3 uses per-user prefix
          const aliasConfig = webhookConfig.configuration as any;
          const folder = useCustomS3
            ? (sanitizeS3Folder(aliasConfig?.s3Folder) || `users/${domainConfig.userId}`)
            : `attachments/users/${domainConfig.userId}`;


          // Process each attachment
          const processedAttachments = [];
          let hasAsyncUploads = false;

          for (const attachment of parsedEmail.message.attachments) {
            // Skip if already processed by advanced script
            if (attachment.downloadUrl && attachment.status) {
              processedAttachments.push(attachment);
              continue;
            }

            // Validate against user file type rules
            const validation = await FileTypeService.validateAttachment({
              filename: attachment.filename,
              contentType: attachment.contentType,
              size: attachment.size
            }, domainConfig.userId);

            if (!validation.allowed) {
              // Excluded attachment
              processedAttachments.push({
                ...attachment,
                excluded: true,
                excludeReason: validation.reason === 'disabled-in-settings' ? 'disabled-in-settings' : (validation.reason || 'File not allowed'),
                manageUrl: validation.reason === 'disabled-in-settings' ? '/settings#storage' : undefined,
                status: 'rejected'
              });
              continue;
            }

            const rule = validation.rule;

            if (rule.handling === 'reject') {
              // Excluded attachment
              processedAttachments.push({
                ...attachment,
                excluded: true,
                excludeReason: validation.reason === 'disabled-in-settings' ? 'disabled-in-settings' : (validation.reason || 'File type not allowed'),
                manageUrl: validation.reason === 'disabled-in-settings' ? '/settings#storage' : undefined,
                status: 'rejected'
              });
              continue;
            }

            // Generate file ID
            const fileId = crypto.randomBytes(16).toString('hex');

            // Determine if sync or async upload based on validation
            const isAsync = !validation.shouldUploadSync;

            if (isAsync) {
              // s3Key will be set after async upload completes via queue processor

              // Create AttachmentFile record for async upload
              await prisma.attachmentFile.create({
                data: {
                  id: fileId,
                  email: { connect: { id: emailRecord.id } },
                  messageId: emailRecord.id,
                  filename: attachment.filename,
                  contentType: attachment.contentType,
                  size: attachment.size,
                  s3Key: null,
                  s3Config: useCustomS3 ? (userSettings!.s3Config as any) : null,
                  downloadUrl: `${env.URL}/attachments/${fileId}/download`,
                  uploadStatus: AttachmentStatus.PENDING,
                  processingType: 'async',
                  expiresAt: new Date(Date.now() + (rule.expirationHours || 24) * 60 * 60 * 1000)
                }
              });

              // Queue async upload - robust content validation
              let attachmentBuffer: Buffer | null = null;
              let uploadError: string | null = null;

              if (attachment.source?.kind === 'buffer' && attachment.source.data instanceof Buffer) {
                attachmentBuffer = attachment.source.data;
              } else if (attachment.source?.kind === 'file' && typeof attachment.source.path === 'string') {
                try {
                  // Read file synchronously into Buffer for queue payload
if (!isSafeTempPath(attachment.source.path)) {
                  throw new Error('Unsafe tempfile path');
                }
                  const data = fs.readFileSync(attachment.source.path);
                  attachmentBuffer = data instanceof Buffer ? data : Buffer.from(data);
                } catch (err: any) {
                  uploadError = `Tempfile read failed: ${err.message}`;
                }
              } else if (attachment.content && typeof attachment.content === 'string') {
                try {
                  attachmentBuffer = Buffer.from(attachment.content, 'base64');
                } catch (err: any) {
                  uploadError = `Invalid base64 content: ${err.message}`;
                }
              } else {
                uploadError = 'No valid attachment content available (missing buffer data or base64 content)';
              }

              if (uploadError || !attachmentBuffer) {
                // Mark attachment as failed instead of crashing email processing
                await prisma.attachmentFile.update({
                  where: { id: fileId },
                  data: { 
                    uploadStatus: AttachmentStatus.FAILED,
                    errorMessage: uploadError || 'Unknown upload error'
                  }
                });

                processedAttachments.push({
                  ...attachment,
                  status: 'failed',
                  excluded: true,
                  excludeReason: 'failed-to-process'
                });

                fastify.log.error({
                  fileId,
                  filename: attachment.filename,
                  error: uploadError,
                  sourceKind: attachment.source?.kind,
                  hasContent: !!attachment.content,
                  messageId: emailRecord.id
                }, 'Failed to extract attachment content for async upload');
                
                continue;
              }

              await queueAttachmentUpload({
                fileId,
                attachment: {
                  filename: attachment.filename,
                  contentType: attachment.contentType,
                  size: attachment.size,
                  content: attachmentBuffer
                },
                messageId: emailRecord.id,
                userId: domainConfig.userId,
                folder,
                ...(useCustomS3 ? { s3Config: userSettings!.s3Config as any } : {})
              });

              hasAsyncUploads = true;

              processedAttachments.push({
                filename: attachment.filename,
                contentType: attachment.contentType,
                size: attachment.size,
                downloadUrl: `${env.URL}/attachments/${fileId}/download`,
                status: 'pending',
                storage: 's3',
                uploadType: 'async'
              });
            } else {
              // Sync upload - robust content validation
              try {
                // Get upload body (Buffer or stream) from attachment
                const body = getAttachmentBody(attachment);
                if (!body) {
                  throw new Error(`No attachment content available (source kind: ${attachment.source?.kind || 'none'}, has content: ${!!attachment.content})`);
                }

                const uploadResult = await s3Service.uploadAttachmentSync({
                  filename: attachment.filename,
                  contentType: attachment.contentType,
                  size: attachment.size,
                  content: body as any
                }, emailRecord.id, folder); // Pass messageId and folder

                // Cleanup tempfile if used
                if (attachment.source?.kind === 'file' && typeof attachment.source.path === 'string') {
                  try {
if (isSafeTempPath(attachment.source.path)) {
                    fs.unlinkSync(attachment.source.path);
                  }
                  } catch (e: any) {
                    fastify.log.warn({ path: attachment.source.path, error: e?.message }, 'Failed to cleanup tempfile after upload');
                  }
                }

                // Create AttachmentFile record for completed upload using returned s3Key
                await prisma.attachmentFile.create({
                  data: {
                    id: fileId,
                    email: { connect: { id: emailRecord.id } },
                    messageId: emailRecord.id,
                    filename: attachment.filename,
                    contentType: attachment.contentType,
                    size: attachment.size,
                    s3Key: uploadResult.s3Key, // Use returned s3Key
                    s3Config: useCustomS3 ? (userSettings!.s3Config as any) : null,
                    downloadUrl: `${env.URL}/attachments/${fileId}/download`,
                    uploadStatus: AttachmentStatus.COMPLETED,
                    processingType: 'sync',
                    expiresAt: uploadResult.expiresAt
                  }
                });

                processedAttachments.push({
                  filename: attachment.filename,
                  contentType: attachment.contentType,
                  size: attachment.size,
                  downloadUrl: `${env.URL}/attachments/${fileId}/download`,
                  status: 'completed',
                  storage: 's3',
                  uploadType: 'sync'
                });
              } catch (uploadError) {
                fastify.log.error({
                  error: uploadError,
                  filename: attachment.filename,
                  messageId: emailRecord.id
                }, 'Failed to upload attachment synchronously');

                // Mark as failed
                processedAttachments.push({
                  ...attachment,
                  status: 'failed',
                  error: 'Upload failed'
                });
              }
            }
          }

          // Update email record if async uploads were queued
          if (hasAsyncUploads) {
            await prisma.email.update({
              where: { id: emailRecord.id },
              data: { hasAsyncUploads: true }
            });
          }

          // Replace attachments in parsed email with processed versions
          parsedEmail.message.attachments = processedAttachments;
        }
      }

      // Create payload for queue with preserved messageId for tracking
      const queuePayload = {
        ...filteredEmail,
        // Always preserve messageId for internal tracking, even if envelope is filtered out
        _internalMessageId: parsedEmail.envelope.messageId,
        // Add domain/alias context for n8n filtering compatibility (same as test webhooks)
        domainId: domainConfig.id,
        aliasId: webhookConfig.aliasId || null
      };

      // Queue for webhook delivery using the payload with preserved messageId
      await queueWebhookDelivery(webhookConfig.url, queuePayload, webhookConfig.secret, webhookConfig.customHeaders);

      // Increment user's email usage count (only after successful processing)
      await UserAccountService.incrementUserEmailUsage(domainConfig.userId);

      // Emit WebSocket event for real-time update
      webSocketService.emitEmailProcessed(domainConfig.userId, {
        messageId: parsedEmail.envelope.messageId,
        fromAddress: parsedEmail.message.sender.email,
        subject: parsedEmail.message.subject || '(no subject)',
        isTestWebhook: false,
        deliveryStatus: 'DELIVERED', // Will be updated by queue processor
        timestamp: new Date().toISOString()
      });

      fastify.log.info({
        messageId: parsedEmail.envelope.messageId,
        domain: targetDomain,
        webhookUrl: webhookConfig.url,
        hasSecret: !!webhookConfig.secret,
        emailRecordId: emailRecord.id,
        userId: domainConfig.userId
      }, 'Email processed and queued for webhook delivery');

      // Changed to 202 as per OpenAPI spec
      return reply.code(202).send({
        success: true,
        messageId: parsedEmail.envelope.messageId,
        status: 'queued',
        emailId: emailRecord.id,
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message, stack: error.stack }, 'Failed to process email');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to process email' });
    }
  });

  // Get email processing status
  fastify.get('/status/:messageId', {
    schema: {
      description: 'Get the processing and delivery status of a specific email.',
      tags: ['Webhook Operations'], // A new tag, or could be 'Webhook' or 'Admin'
      summary: 'Get email delivery status',
      params: {
        type: 'object',
        properties: {
          messageId: { type: 'string', description: 'The unique message ID of the email.' },
        },
        required: ['messageId'],
      },
      response: {
        200: {
          description: 'Detailed status of the email.',
          type: 'object',
          properties: {
            messageId: { type: 'string' },
            status: { type: 'string', enum: ['queued', 'sent', 'delivered', 'failed', 'deferred'] }, // Example statuses
            deliveryAttempts: { type: 'integer' },
            lastAttempt: { type: 'string', format: 'date-time', nullable: true },
            deliveredAt: { type: 'string', format: 'date-time', nullable: true },
            errorMessage: { type: 'string', nullable: true },
            domain: { type: 'string' },
            webhookUrl: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            // expiresAt: { type: 'string', format: 'date-time' }, // If still relevant
          },
        },
        404: { description: 'Email not found', ...errorResponseSchema },
        500: { description: 'Failed to retrieve email status', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    const { messageId } = request.params as { messageId: string };

    try {
      // Get email status with domain info
      const emailRecord = await prisma.email.findUnique({
        where: { messageId },
        include: {
          domain: {
            select: { domain: true }
          }
        },
      });

      if (!emailRecord) {
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Email not found' });
      }

      // Return email status
      return reply.send({
        messageId,
        status: emailRecord.deliveryStatus.toLowerCase(),
        deliveryAttempts: emailRecord.deliveryAttempts,
        lastAttempt: emailRecord.lastAttemptAt?.toISOString(),
        deliveredAt: emailRecord.deliveredAt?.toISOString(),
        errorMessage: emailRecord.errorMessage,
        domain: emailRecord.domain?.domain,
        createdAt: emailRecord.createdAt.toISOString(),
        expiresAt: emailRecord.expiresAt.toISOString(),
      });

    } catch (error: any) {
      fastify.log.error({ messageId, error: error.message, stack: error.stack }, 'Failed to get email status');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve email status',
        // details: error.message, // Optionally include for debugging, but be careful in prod
      });
    }
  });
};

/**
 * Extract domain from raw email headers before full parsing
 * This is more efficient and handles the case where parsing might fail
 * Enhanced to handle SpamAssassin-processed emails
 */
function extractDomainFromRawEmail(rawEmail: string | Buffer): string | null {
  const emailText = typeof rawEmail === 'string' ? rawEmail : rawEmail.toString();

  // Look for common email headers that contain the recipient
  // Order matters: X-Original-To is most reliable for our setup
  // Enhanced patterns to handle SpamAssassin modifications
  const patterns = [
    // Standard X-Original-To header
    /^X-Original-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // X-Original-To with angle brackets
    /^X-Original-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    // X-Original-To with extra text
    /^X-Original-To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // Delivered-To variations
    /^Delivered-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^Delivered-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    /^Delivered-To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    // Envelope-To variations
    /^Envelope-To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^Envelope-To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    // To header variations
    /^To:\s*([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
    /^To:\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>/im,
    /^To:\s*.*?([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})/im,
  ];

  for (const pattern of patterns) {
    const match = emailText.match(pattern);
    if (match && match[1]) {
      // Extract domain from the full email address
      const emailAddress = match[1];
      const atIndex = emailAddress.indexOf('@');
      if (atIndex !== -1) {
        const domain = emailAddress.substring(atIndex + 1).toLowerCase().trim();
        // Remove any trailing characters that might be present
        const cleanDomain = domain.replace(/[>\s\r\n].*$/, '');
        if (cleanDomain && cleanDomain.includes('.')) {
          return cleanDomain;
        }
      }
    }
  }

  // Enhanced fallback: look for any email address in the headers section
  const headerSection = emailText.split('\n\n')[0]; // Get headers only
  const emailMatches = headerSection.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/gi);

  if (emailMatches) {
    // Look for addresses that are likely recipients (not senders)
    for (const emailAddr of emailMatches) {
      const domain = emailAddr.substring(emailAddr.indexOf('@') + 1).toLowerCase();
      // Skip common sender domains that are unlikely to be our target
      if (!domain.includes('gmail.com') && !domain.includes('outlook.com') &&
          !domain.includes('yahoo.com') && !domain.includes('hotmail.com')) {
        return domain;
      }
    }

    // If no non-common domains found, use the first match
    const firstEmail = emailMatches[0];
    const atIndex = firstEmail.indexOf('@');
    if (atIndex !== -1) {
      return firstEmail.substring(atIndex + 1).toLowerCase();
    }
  }

  return null;
}

/**
 * Look up webhook configuration for domain/email combination
 * Checks for specific alias first, then catch-all alias
 */
async function lookupWebhookConfig(
  domainConfig: any,
  emailAddress: string,
  log: FastifyBaseLogger
): Promise<{ url: string; secret?: string; configuration: any; customHeaders?: Record<string, string>; aliasId?: string } | null> {
  // Debug logging to see what we're comparing
  log.info({
    emailAddress,
    emailAddressType: typeof emailAddress,
    emailAddressLength: emailAddress.length,
    aliases: domainConfig.aliases.map((a: any) => ({
      email: a.email,
      emailType: typeof a.email,
      emailLength: a.email?.length,
      active: a.active,
      hasWebhook: !!a.webhook,
      exactMatch: a.email === emailAddress
    }))
  }, 'Webhook lookup debug info');

  // 1. Check for specific alias configuration first
  const specificAlias = domainConfig.aliases.find((alias: any) =>
    alias.email === emailAddress && alias.active && alias.webhook
  );

  if (specificAlias && specificAlias.webhook) {
    // Check if webhook is verified before using it
    if (!specificAlias.webhook.verified) {
      log.warn({
        emailAddress,
        webhookUrl: specificAlias.webhook.url,
        webhookName: specificAlias.webhook.name,
        webhookId: specificAlias.webhook.id
      }, 'Webhook not verified - skipping specific alias webhook');
      // Don't return this webhook, fall through to catch-all
    } else {
      log.debug({
        emailAddress,
        webhookUrl: specificAlias.webhook.url,
        webhookName: specificAlias.webhook.name,
        hasSecret: !!specificAlias.webhook.webhookSecret
      }, 'Using specific alias webhook');
      return {
        url: specificAlias.webhook.url,
        secret: specificAlias.webhook.webhookSecret || undefined,
        configuration: specificAlias.configuration || {},
        customHeaders: specificAlias.webhook.customHeaders as Record<string, string> || undefined,
        aliasId: specificAlias.id
      };
    }
  }

  // 2. Fall back to catch-all alias webhook
  const catchAllAlias = domainConfig.aliases.find((alias: any) =>
    alias.email.startsWith('*@') && alias.active && alias.webhook
  );

  if (catchAllAlias && catchAllAlias.webhook) {
    // Check if webhook is verified before using it
    if (!catchAllAlias.webhook.verified) {
      log.warn({
        domain: domainConfig.domain,
        emailAddress,
        webhookUrl: catchAllAlias.webhook.url,
        webhookName: catchAllAlias.webhook.name,
        webhookId: catchAllAlias.webhook.id
      }, 'Catch-all webhook not verified - email will be rejected');
      return null; // No verified webhook available
    }

    log.debug({
      domain: domainConfig.domain,
      emailAddress,
      webhookUrl: catchAllAlias.webhook.url,
      webhookName: catchAllAlias.webhook.name,
      hasSecret: !!catchAllAlias.webhook.webhookSecret
    }, 'Using catch-all alias webhook');
    return {
      url: catchAllAlias.webhook.url,
      secret: catchAllAlias.webhook.webhookSecret || undefined,
      configuration: catchAllAlias.configuration || domainConfig.configuration || {},
      customHeaders: catchAllAlias.webhook.customHeaders as Record<string, string> || undefined,
      aliasId: catchAllAlias.id
    };
  }

  // 3. No webhook configured
  log.warn({
    domain: domainConfig.domain,
    emailAddress,
    aliasCount: domainConfig.aliases.length,
    hasCatchAll: domainConfig.aliases.some((a: any) => a.email.startsWith('*@'))
  }, 'No webhook configured for email or domain');

  return null;
}

/**
 * Apply configuration filters to email payload
 */
function applyConfigurationFilters(email: any, configuration: any) {
  const filtered = { ...email };

  // Filter attachments if disabled
  if (configuration.allowAttachments === false) {
    if (Array.isArray(filtered.message?.attachments) && filtered.message.attachments.length > 0) {
      filtered.message.attachments = filtered.message.attachments.map((att: any) => ({
        filename: att.filename,
        contentType: att.contentType,
        size: att.size,
        excluded: true,
        excludeReason: 'disabled-in-settings'
      }));
    } else {
      filtered.message.attachments = [];
    }
  }

  // Filter envelope data if disabled
  if (configuration.includeEnvelope === false) {
    delete filtered.envelope;
  }

  return filtered;
}

/**
 * Find user by the last 8 characters of their ID
 */
async function findUserByIdSuffix(suffix: string): Promise<{ id: string } | null> {
  if (suffix.length !== 8) {
    return null;
  }

  try {
    // Find user where ID ends with the suffix
    const user = await prisma.user.findFirst({
      where: {
        id: {
          endsWith: suffix
        }
      },
      select: {
        id: true
      }
    });

    return user;
  } catch (error) {
    return null;
  }
}
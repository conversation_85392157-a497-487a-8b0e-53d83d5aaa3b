import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateUser } from '../middleware/unified-auth.middleware.js';
import { OAuthService } from '../services/oauth.service.js';
import { logger } from '../utils/logger.js';

interface AuthorizeRequest {
  Querystring: {
    client_id: string;
    redirect_uri: string;
    response_type: string;
    scope?: string;
    state?: string;
  };
}

interface TokenRequest {
  Body: {
    grant_type: string;
    code: string;
    client_id: string;
    client_secret: string;
    redirect_uri: string;
  };
}

export async function oauthRoutes(fastify: FastifyInstance) {
  // OAuth2 authorization endpoint
  fastify.get<AuthorizeRequest>('/authorize', {
    preHandler: authenticateUser,
    schema: {
      tags: ['OAuth2'],
      summary: 'OAuth2 Authorization',
      description: 'OAuth2 authorization endpoint for SSO integration',
      querystring: {
        type: 'object',
        required: ['client_id', 'redirect_uri', 'response_type'],
        properties: {
          client_id: { type: 'string' },
          redirect_uri: { type: 'string', format: 'uri' },
          response_type: { type: 'string', enum: ['code'] },
          scope: { type: 'string' },
          state: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest<AuthorizeRequest>, reply: FastifyReply) => {
    try {
      const { client_id, redirect_uri, response_type, scope, state } = request.query;
      
      // Validate request parameters
if (response_type !== 'code') {
        return reply.code(400).send({ error: 'unsupported_response_type', state: state || '' });
      }

      // Validate client
if (!OAuthService.isValidClient(client_id)) {
        return reply.code(400).send({ error: 'invalid_client', state: state || '' });
      }

      // Validate redirect URI
      if (!OAuthService.isValidRedirectUri(client_id, redirect_uri)) {
        return reply.code(400).send({ error: 'invalid_redirect_uri' });
      }

      // Get authenticated user
      const user = (request as any).user;
      if (!user) {
        return reply.code(401).send({ error: 'user_not_authenticated' });
      }

      // Generate authorization code
      const authCode = await OAuthService.generateAuthorizationCode(
        user.id,
        client_id,
        redirect_uri,
        scope || 'profile'
      );
      
      // Redirect to client with authorization code
      const redirectUrl = new URL(redirect_uri);
      redirectUrl.searchParams.set('code', authCode);
      if (state) {
        redirectUrl.searchParams.set('state', state);
      }
      
      return reply.redirect(redirectUrl.toString());
} catch (error) {
      logger.error({ data: error }, 'OAuth authorize error:');
      return reply.code(500).send({ error: 'server_error' });
    }
  });

  // OAuth2 token exchange endpoint
  fastify.post<TokenRequest>('/token', {
    schema: {
      tags: ['OAuth2'],
      summary: 'OAuth2 Token Exchange',
      description: 'Exchange authorization code for access token',
      body: {
        type: 'object',
        required: ['grant_type', 'code', 'client_id', 'client_secret', 'redirect_uri'],
        properties: {
          grant_type: { type: 'string', enum: ['authorization_code'] },
          code: { type: 'string' },
          client_id: { type: 'string' },
          client_secret: { type: 'string' },
          redirect_uri: { type: 'string', format: 'uri' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            access_token: { type: 'string' },
            token_type: { type: 'string' },
            expires_in: { type: 'number' },
            scope: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<TokenRequest>, reply: FastifyReply) => {
    try {
      const { grant_type, code, client_id, client_secret, redirect_uri } = request.body;
      
      // Validate grant type
      if (grant_type !== 'authorization_code') {
        return reply.code(400).send({ error: 'unsupported_grant_type' });
      }

      // Validate client credentials
      logger.info({ client_id, client_secret_provided: !!client_secret }, 'Token exchange request');
      if (!OAuthService.validateClientCredentials(client_id, client_secret)) {
        logger.info('Client credential validation failed');
        return reply.code(401).send({ error: 'invalid_client' });
      }

      // Exchange authorization code for user info
      const authData = await OAuthService.exchangeAuthorizationCode(code, client_id, redirect_uri);
      if (!authData) {
        logger.info({ code: code.substring(0, 20) + '...' }, 'Authorization code exchange failed');
        return reply.code(400).send({ error: 'invalid_grant' });
      }

      // Generate access token
      const accessToken = await OAuthService.generateAccessToken(authData.userId, client_id, authData.scope);
      
      return reply.send({
        access_token: accessToken,
        token_type: 'Bearer',
        expires_in: 3600, // 1 hour
        scope: authData.scope
      });
    } catch (error) {
      logger.error({ data: error }, 'OAuth token error:');
      return reply.code(500).send({ error: 'server_error' });
    }
  });

  // OAuth2 user info endpoint
  fastify.get('/userinfo', {
    schema: {
      tags: ['OAuth2'],
      summary: 'OAuth2 User Info',
      description: 'Get user information using access token',
      headers: {
        type: 'object',
        properties: {
          authorization: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            sub: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            email_verified: { type: 'boolean' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Extract bearer token
      const authorization = request.headers.authorization;
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return reply.code(401).send({ error: 'invalid_token' });
      }

      const accessToken = authorization.substring(7);
      
      // Validate access token and get user info
      const tokenData = await OAuthService.validateAccessToken(accessToken);
      if (!tokenData) {
        return reply.code(401).send({ error: 'invalid_token' });
      }

      // Get user information
      const user = await OAuthService.getUserById(tokenData.userId);
      if (!user) {
        return reply.code(401).send({ error: 'invalid_token' });
      }

      return reply.send({
        sub: user.id,
        email: user.email,
        name: user.name,
        email_verified: true
      });
    } catch (error) {
      logger.error({ data: error }, 'OAuth userinfo error:');
      return reply.code(500).send({ error: 'server_error' });
    }
  });
}
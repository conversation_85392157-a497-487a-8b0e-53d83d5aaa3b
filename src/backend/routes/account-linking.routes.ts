import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { auth } from '../lib/better-auth.js';
import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';

export const accountLinkingRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Middleware to require BetterAuth session
  const requireBetterAuthSession = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const session = await auth.api.getSession({
        headers: request.headers as any,
      });

      if (!session || !session.user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Authentication required'
        });
      }

      // Attach user to request
      (request as any).user = session.user;
      (request as any).session = session;
    } catch (error: any) {
      logger.error({ error: error.message }, 'Error in BetterAuth session check');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to verify session'
      });
    }
  };

  // Get linked accounts for current user
  fastify.get('/linked-accounts', {
    preHandler: requireBetterAuthSession,
    schema: {
      tags: ['Account Linking'],
      summary: 'Get linked accounts',
      description: 'Get all authentication providers linked to the current user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                image: { type: 'string', nullable: true }
              }
            },
            accounts: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  providerId: { type: 'string' },
                  accountId: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  hasPassword: { type: 'boolean' }
                }
              }
            },
            totalAccounts: { type: 'number' },
            hasCredentialAccount: { type: 'boolean' },
            hasGithubAccount: { type: 'boolean' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;

      // Get user details from database
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          accounts: {
            select: {
              id: true,
              providerId: true,
              accountId: true,
              createdAt: true,
              password: true
            },
            orderBy: {
              createdAt: 'asc'
            }
          }
        }
      });

      if (!userDetails) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      // Process accounts to hide sensitive information and add metadata
      const processedAccounts = userDetails.accounts.map(account => ({
        id: account.id,
        providerId: account.providerId,
        accountId: account.accountId,
        createdAt: account.createdAt.toISOString(),
        hasPassword: !!account.password
      }));

      const hasCredentialAccount = userDetails.accounts.some(a => a.providerId === 'credential');
      const hasGithubAccount = userDetails.accounts.some(a => a.providerId === 'github');

      return reply.send({
        success: true,
        user: {
          id: userDetails.id,
          email: userDetails.email,
          name: userDetails.name,
          image: userDetails.image
        },
        accounts: processedAccounts,
        totalAccounts: processedAccounts.length,
        hasCredentialAccount,
        hasGithubAccount
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get linked accounts');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to get linked accounts'
      });
    }
  });

  // Unlink an account (remove authentication method)
  fastify.delete('/unlink/:accountId', {
    preHandler: requireBetterAuthSession,
    schema: {
      tags: ['Account Linking'],
      summary: 'Unlink account',
      description: 'Remove an authentication method from the current user (requires at least one method to remain)',
      params: {
        type: 'object',
        properties: {
          accountId: { type: 'string' }
        },
        required: ['accountId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            remainingAccounts: { type: 'number' }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { accountId } = request.params as { accountId: string };
      const user = (request as any).user;

      // Check how many accounts the user has
      const accountCount = await prisma.account.count({
        where: { userId: user.id }
      });

      if (accountCount <= 1) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Cannot unlink the last authentication method. Add another method first.'
        });
      }

      // Verify the account belongs to the user
      const account = await prisma.account.findFirst({
        where: {
          id: accountId,
          userId: user.id
        }
      });

      if (!account) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Account not found or does not belong to you'
        });
      }

      // Delete the account
      await prisma.account.delete({
        where: { id: accountId }
      });

      logger.info({
        userId: user.id,
        accountId,
        providerId: account.providerId,
        action: 'account_unlinked'
      }, 'User unlinked authentication method');

      return reply.send({
        success: true,
        message: `Successfully unlinked ${account.providerId} account`,
        remainingAccounts: accountCount - 1
      });

    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to unlink account');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to unlink account'
      });
    }
  });

  // Test account linking status
  fastify.get('/linking-test', {
    preHandler: requireBetterAuthSession,
    schema: {
      tags: ['Account Linking'],
      summary: 'Test account linking',
      description: 'Test the account linking functionality and provide recommendations',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            userId: { type: 'string' },
            email: { type: 'string' },
            accountLinkingStatus: {
              type: 'object',
              properties: {
                canLinkGithub: { type: 'boolean' },
                canSetPassword: { type: 'boolean' },
                hasMultipleProviders: { type: 'boolean' },
                securityScore: { type: 'number' },
                recommendations: {
                  type: 'array',
                  items: { type: 'string' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;

      // Get user's accounts
      const accounts = await prisma.account.findMany({
        where: { userId: user.id },
        select: {
          providerId: true,
          password: true
        }
      });

      const hasCredentialAccount = accounts.some(a => a.providerId === 'credential');
      const hasGithubAccount = accounts.some(a => a.providerId === 'github');
      const hasPassword = accounts.some(a => a.password);
      const hasMultipleProviders = accounts.length > 1;

      // Calculate security score
      let securityScore = 0;
      if (hasCredentialAccount) securityScore += 30;
      if (hasGithubAccount) securityScore += 30;
      if (hasPassword) securityScore += 20;
      if (hasMultipleProviders) securityScore += 20;

      // Generate recommendations
      const recommendations: string[] = [];
      if (!hasCredentialAccount) {
        recommendations.push('Set up email/password login for backup access');
      }
      if (!hasGithubAccount) {
        recommendations.push('Link your GitHub account for convenient OAuth login');
      }
      if (!hasPassword) {
        recommendations.push('Set a password to enable email/password authentication');
      }
      if (accounts.length === 1) {
        recommendations.push('Add a second authentication method for account security');
      }

      return reply.send({
        success: true,
        userId: user.id,
        email: user.email,
        accountLinkingStatus: {
          canLinkGithub: !hasGithubAccount,
          canSetPassword: !hasPassword,
          hasMultipleProviders,
          securityScore,
          recommendations
        }
      });

    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to test account linking');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to test account linking'
      });
    }
  });
};
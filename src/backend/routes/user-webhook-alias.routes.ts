import { FastifyInstance } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { WebhookAliasController } from '../controllers/user/webhook-alias.controller.js';
import { webhookAliasSchemas } from '../schemas/user/webhook-alias.schemas.js';
import { validateWebhookLimit, validateAliasLimit } from '../middleware/plan-limits.middleware.js';

export async function webhookAliasRoutes(fastify: FastifyInstance) {
  const controller = new WebhookAliasController();

  // POST /api/webhooks/alias - Create webhook and alias in one operation
  fastify.post('/api/webhooks/alias', {
    preHandler: [
      UnifiedAuthMiddleware.requireFeature('webhook:create'),
      UnifiedAuthMiddleware.requireFeature('alias:create'),
      validateWebhookLimit,
      validateAliasLimit
    ],
    schema: {
      tags: ['Webhook-Alias'],
      summary: 'Create webhook and alias in one atomic operation',
      description: `
        Creates a webhook and alias together in a single atomic operation.
        Supports both catch-all (*@domain.com) and specific (<EMAIL>) aliases.
        For catch-all aliases, optionally syncs the webhook with the domain's webhook.
        Provides auto-verification option using the last 5 characters method.
      `,
      body: {
        type: 'object',
        required: ['domainId', 'webhookUrl', 'webhookName', 'aliasType'],
        properties: {
          domainId: { type: 'string' },
          webhookUrl: { type: 'string', format: 'uri' },
          webhookName: { type: 'string' },
          webhookDescription: { type: 'string' },
          aliasType: { type: 'string', enum: ['catchall', 'specific'] },
          localPart: { type: 'string' },
          syncWithDomain: { type: 'boolean', default: true },
          autoVerify: { type: 'boolean', default: false },
          firstOrCreate: { type: 'boolean', default: false },
          updateWebhookData: { type: 'boolean', default: true }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            webhook: { type: 'object' },
            alias: { type: 'object' },
            action: { type: 'string', enum: ['created', 'updated', 'webhook_added', 'cross_domain_update'] },
            domain: { type: 'object', nullable: true },
            warning: { type: 'string', nullable: true },
            message: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean', enum: [false] },
            error: { type: 'string' }
          }
        },
        403: {
          type: 'object',
          properties: {
            success: { type: 'boolean', enum: [false] },
            error: { type: 'string' },
            message: { type: 'string' },
            details: { type: 'object' },
            upgradeUrl: { type: 'string' }
          }
        }
      }
    }
  }, controller.createWebhookAlias.bind(controller));
}

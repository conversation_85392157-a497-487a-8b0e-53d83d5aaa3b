import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateUser } from '../middleware/unified-auth.middleware.js';
import { WebhookTestIntegrationService } from '../services/webhooktest-integration.service.js';
import { redisRateLimiter } from '../services/redis-rate-limiter.service.js';
import { logger } from '../utils/logger.js';
import { Sentry } from '../lib/sentry.js';

// Rate limiting configuration for webhook endpoint creation
const RATE_LIMIT_CONFIG = {
  windowMs: 60000, // 1 minute
  maxRequests: 3, // Max 3 endpoints per minute per user
  keyPrefix: 'webhooktest_creation',
};

// Redis-based rate limiting functions
async function checkRateLimit(userId: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  try {
    const result = await redisRateLimiter.checkRateLimit(userId, RATE_LIMIT_CONFIG);
    return {
      allowed: result.allowed,
      remaining: result.remaining,
      resetTime: result.resetTime,
    };
  } catch (error) {
    logger.error({ error: error.message, userId }, 'Rate limit check failed, allowing request');
    // Fail open - allow the request if rate limiting fails
    return {
      allowed: true,
      remaining: RATE_LIMIT_CONFIG.maxRequests - 1,
      resetTime: Date.now() + RATE_LIMIT_CONFIG.windowMs,
    };
  }
}

interface CreateEndpointRequest {
  Body: {
    userId: string;
    userEmail: string;
  };
}

interface TestWebhookRequest {
  Body: {
    webhookUrl: string;
    testPayload?: object;
    timeout?: number;
  };
}

interface GetResultsRequest {
  Params: {
    verificationCode: string;
  };
}

export async function webhookTestRoutes(fastify: FastifyInstance) {
  // All routes require authentication
  fastify.addHook('preHandler', authenticateUser);

  /**
   * Create a WebhookTest endpoint for testing
   */
  fastify.post<CreateEndpointRequest>('/create-endpoint', async (request: FastifyRequest<CreateEndpointRequest>, reply: FastifyReply) => {
    try {
      const { userId, userEmail } = request.body;
      
      // Use authenticated user info instead of body params for security
      const authenticatedUser = (request as any).user;
      if (!authenticatedUser) {
        return reply.code(401).send({ error: 'User not authenticated' });
      }

      // Check rate limit
      const rateLimitCheck = await checkRateLimit(authenticatedUser.id);
      if (!rateLimitCheck.allowed) {
        const resetTimeSeconds = Math.ceil((rateLimitCheck.resetTime - Date.now()) / 1000);
        return reply.code(429).send({ 
          error: 'Too many WebhookTest endpoints created',
          message: `Rate limit exceeded. You can create ${RATE_LIMIT_CONFIG.maxRequests} endpoints per minute. Try again in ${resetTimeSeconds} seconds.`,
          resetTime: rateLimitCheck.resetTime
        });
      }

      const result = await WebhookTestIntegrationService.createWebhookTestEndpoint(
        authenticatedUser.id,
        authenticatedUser.email
      );

      // Rate limiting is handled by Redis-based rate limiter automatically

      logger.info({ 
        userId: authenticatedUser.id, 
        testId: result.testId,
        remaining: rateLimitCheck.remaining - 1
      }, 'WebhookTest endpoint created');

      return reply.code(201).send(result);
    } catch (error) {
      logger.error({ err: error }, 'Failed to create WebhookTest endpoint');
      return reply.code(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to create WebhookTest endpoint' 
      });
    }
  });

  /**
   * Test a webhook URL using WebhookTest service
   */
  fastify.post<TestWebhookRequest>('/test-webhook', async (request: FastifyRequest<TestWebhookRequest>, reply: FastifyReply) => {
    try {
      const { webhookUrl, testPayload, timeout } = request.body;
      
      const authenticatedUser = (request as any).user;
      if (!authenticatedUser) {
        return reply.code(401).send({ error: 'User not authenticated' });
      }

      // Validate webhook URL
      if (!webhookUrl || !webhookUrl.startsWith('http')) {
        return reply.code(400).send({ error: 'Valid webhook URL is required' });
      }

      // Add Sentry context for webhook testing
      Sentry.getCurrentScope().setTag('service', 'webhooks');
      Sentry.getCurrentScope().setTag('feature', 'testing');
      Sentry.getCurrentScope().setContext('webhook-test', {
        isUserTest: true,
        userId: authenticatedUser.id,
        webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...'),
        timestamp: new Date().toISOString()
      });

      const result = await WebhookTestIntegrationService.testWebhook(
        authenticatedUser.id,
        authenticatedUser.email,
        webhookUrl,
        testPayload,
        timeout
      );

      logger.info({ 
        userId: authenticatedUser.id, 
        webhookUrl, 
        testId: result.testId,
        success: result.success 
      }, 'Webhook test completed');

      return reply.code(200).send(result);
    } catch (error) {
      logger.error({ err: error }, 'Failed to test webhook');
      return reply.code(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to test webhook' 
      });
    } finally {
      // Clean up Sentry context
      Sentry.getCurrentScope().setContext('webhook-test', null);
      Sentry.getCurrentScope().setTag('service', undefined);
      Sentry.getCurrentScope().setTag('feature', undefined);
    }
  });

  /**
   * Get test results by verification code
   */
  fastify.get<GetResultsRequest>('/results/:verificationCode', async (request: FastifyRequest<GetResultsRequest>, reply: FastifyReply) => {
    try {
      const { verificationCode } = request.params;
      
      if (!verificationCode) {
        return reply.code(400).send({ error: 'Verification code is required' });
      }

      const results = await WebhookTestIntegrationService.getTestResults(verificationCode);

      return reply.code(200).send(results);
    } catch (error) {
      logger.error({ err: error, verificationCode: request.params.verificationCode }, 'Failed to get test results');
      return reply.code(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to get test results' 
      });
    }
  });

  /**
   * Get received payloads by verification code (legacy)
   */
  fastify.get<GetResultsRequest>('/payloads/:verificationCode', async (request: FastifyRequest<GetResultsRequest>, reply: FastifyReply) => {
    try {
      const { verificationCode } = request.params;
      
      if (!verificationCode) {
        return reply.code(400).send({ error: 'Verification code is required' });
      }

      const payloads = await WebhookTestIntegrationService.getReceivedPayloads(verificationCode);

      return reply.code(200).send({ payloads });
    } catch (error) {
      logger.error({ err: error, verificationCode: request.params.verificationCode }, 'Failed to get payloads');
      return reply.code(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to get payloads' 
      });
    }
  });

  /**
   * Handle endpoint deletion notification from WebhookTest
   */
  fastify.post('/endpoint-deleted', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { webhookUrl, endpointId, userId } = request.body as {
        webhookUrl: string;
        endpointId: string;  
        userId: string;
      };

      if (!webhookUrl || !userId) {
        return reply.code(400).send({ error: 'webhookUrl and userId are required' });
      }

      // Find and delete the corresponding webhook in emailconnect
      const { prisma } = await import('../lib/prisma.js');
      
      const webhook = await prisma.webhook.findFirst({
        where: {
          url: webhookUrl,
          userId: userId
        }
      });

      if (webhook) {
        // Check if webhook is in use by aliases
        const aliasCount = await prisma.alias.count({
          where: { webhookId: webhook.id }
        });

        if (aliasCount > 0) {
          // Don't delete if webhook is in use, but log this
          logger.warn({
            webhookId: webhook.id,
            aliasCount,
            webhookUrl
          }, 'WebhookTest endpoint deleted but EmailConnect webhook is in use by aliases:');
          
          return reply.code(200).send({ 
            success: true, 
            message: 'Webhook not deleted - still in use by aliases',
            inUse: true
          });
        }

        await prisma.webhook.delete({
          where: { id: webhook.id }
        });

        logger.info({
          webhookId: webhook.id,
          webhookUrl
        }, 'Successfully deleted EmailConnect webhook after WebhookTest endpoint deletion:');

        return reply.code(200).send({ 
          success: true, 
          message: 'Webhook deleted successfully',
          deletedWebhook: {
            id: webhook.id,
            url: webhook.url
          }
        });
      } else {
        // Webhook not found, which is fine
        return reply.code(200).send({ 
          success: true, 
          message: 'Webhook not found (may have been already deleted)',
          found: false
        });
      }
    } catch (error) {
      logger.error({ data: error }, 'Failed to handle endpoint deletion notification:');
      return reply.code(500).send({ 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  });

  /**
   * Health check for WebhookTest integration
   */
  fastify.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Check if WebhookTest service is configured
      const isConfigured = process.env.WEBHOOKTEST_API_URL && process.env.WEBHOOKTEST_JWT_SECRET;
      
      return reply.code(200).send({
        status: 'ok',
        integration: isConfigured ? 'configured' : 'not_configured',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      return reply.code(500).send({ 
        status: 'error',
        error: error instanceof Error ? error.message : 'Health check failed'
      });
    }
  });


  /**
   * Get WebhookTest request data for delivered webhooks
   */
  fastify.post('/get-request-data', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const body = request.body as {
        webhookUrl: string;
        timestampStart?: string;
        timestampEnd?: string;
      };

      const authenticatedUser = (request as any).user;
      if (!authenticatedUser) {
        return reply.code(401).send({ error: 'User not authenticated' });
      }

      // Validate webhook URL
      if (!body.webhookUrl || !body.webhookUrl.startsWith('http')) {
        return reply.code(400).send({ error: 'Valid webhook URL is required' });
      }

      // Parse timestamps
      let timestampStart: Date | undefined;
      let timestampEnd: Date | undefined;
      
      if (body.timestampStart) {
        timestampStart = new Date(body.timestampStart);
        if (isNaN(timestampStart.getTime())) {
          return reply.code(400).send({ error: 'Invalid timestampStart format' });
        }
      }
      
      if (body.timestampEnd) {
        timestampEnd = new Date(body.timestampEnd);
        if (isNaN(timestampEnd.getTime())) {
          return reply.code(400).send({ error: 'Invalid timestampEnd format' });
        }
      }

      const result = await WebhookTestIntegrationService.getWebhookTestRequestData(
        authenticatedUser.id,
        authenticatedUser.email,
        body.webhookUrl,
        timestampStart,
        timestampEnd
      );

      logger.info({ 
        userId: authenticatedUser.id, 
        webhookUrl: body.webhookUrl,
        requestCount: result.requests?.length || 0
      }, 'WebhookTest request data fetched');

      return reply.code(200).send(result);
    } catch (error) {
      logger.error({ err: error }, 'Failed to get WebhookTest request data');
      return reply.code(500).send({ 
        error: error instanceof Error ? error.message : 'Failed to get WebhookTest request data' 
      });
    }
  });
}
import { FastifyInstance } from 'fastify';
import { captureException, captureMessage } from '../lib/sentry.js';

export async function testErrorRoutes(fastify: FastifyInstance) {
  // Only register these routes in development/staging
  if (process.env.NODE_ENV === 'production' && process.env.ENABLE_TEST_ROUTES !== 'true') {
    return;
  }

  fastify.get('/api/test/error', async (request, reply) => {
    // This route intentionally throws an error to test Bugsink
    throw new Error('Test error thrown on purpose to verify Bugsink integration');
  });

  fastify.get('/api/test/error-async', async (request, reply) => {
    // Test async error handling
    await new Promise((resolve, reject) => {
      setTimeout(() => {
        reject(new Error('Async test error for Bugsink verification'));
      }, 100);
    });
  });

  fastify.get('/api/test/error-manual', async (request, reply) => {
    // Test manual error capture
    try {
      // Simulate some operation that fails
      const result = JSON.parse('invalid json');
    } catch (error) {
      captureException(error as Error, {
        endpoint: '/api/test/error-manual',
        userId: request.user?.id,
        custom: 'Manual error capture test',
      });
      
      return reply.status(500).send({
        error: 'Manual error captured and sent to Bugsink',
        message: 'Check Bugsink dashboard for the error',
      });
    }
  });

  fastify.get('/api/test/message', async (request, reply) => {
    // Test message capture
    captureMessage('Test message sent to Bugsink', 'info', {
      endpoint: '/api/test/message',
      timestamp: new Date().toISOString(),
    });
    
    return {
      success: true,
      message: 'Test message sent to Bugsink',
    };
  });

  fastify.log.warn('🚨 Test error routes enabled - DO NOT use in production');
}
import { FastifyInstance } from 'fastify'
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { ProfileController } from '../controllers/user/profile.controller.js'

export async function profileRoutes(fastify: FastifyInstance) {
  const profileController = new ProfileController()

  // Get current user profile
  fastify.get('/profile', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:read')],
    schema: {
      tags: ['User Profile'],
      summary: 'Get current user profile',
      description: 'Retrieve the current user\'s profile information. Requires profile:read permission.',
      response: {
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.getProfile)

  // Update user profile
  fastify.put('/profile', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:update')],
    schema: {
      tags: ['User Profile'],
      summary: 'Update user profile',
      description: 'Update the current user\'s profile information. Requires profile:write permission.',
      body: {
        type: 'object',
        properties: {
          name: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'User display name'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                planType: { type: 'string' },
                emailVerified: { type: 'boolean' },
                updatedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.updateProfile)

  // Change password
  fastify.put('/profile/password', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:update')],
    schema: {
      tags: ['User Profile'],
      summary: 'Change user password',
      description: 'Change the current user\'s password. Requires profile:write permission.',
      body: {
        type: 'object',
        required: ['newPassword'],
        properties: {
          currentPassword: { 
            type: 'string',
            description: 'Current password for verification (optional for users without existing password)'
          },
          newPassword: { 
            type: 'string',
            minLength: 6,
            description: 'New password (minimum 6 characters)'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.changePassword)

  // Change email
  fastify.put('/profile/email', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:update')],
    schema: {
      tags: ['User Profile'],
      summary: 'Change user email',
      description: 'Change the current user\'s email address. Requires profile:write permission.',
      body: {
        type: 'object',
        required: ['newEmail', 'password'],
        properties: {
          newEmail: { 
            type: 'string',
            format: 'email',
            description: 'New email address'
          },
          password: { 
            type: 'string',
            description: 'Current password for verification'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                emailVerified: { type: 'boolean' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: { $ref: 'ErrorResponse#' },
        409: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.changeEmail)

  // Update organization details
  fastify.put('/profile/organization', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:update')],
    schema: {
      tags: ['User Profile'],
      summary: 'Update organization details',
      description: 'Update the current user\'s organization information for invoicing. Requires profile:write permission.',
      body: {
        type: 'object',
        properties: {
          name: { 
            type: 'string', 
            nullable: true,
            maxLength: 200,
            description: 'Organization name'
          },
          registrationNumber: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'Registration/VAT number'
          },
          addressStreet: { 
            type: 'string', 
            nullable: true,
            maxLength: 200,
            description: 'Street address'
          },
          addressApartment: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'Apartment/suite number'
          },
          addressZipCode: { 
            type: 'string', 
            nullable: true,
            maxLength: 20,
            description: 'Postal code'
          },
          addressCity: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'City'
          },
          addressCountry: { 
            type: 'string', 
            nullable: true,
            maxLength: 100,
            description: 'Country'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                organization: {
                  type: 'object',
                  nullable: true,
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    registrationNumber: { type: 'string', nullable: true },
                    addressStreet: { type: 'string', nullable: true },
                    addressApartment: { type: 'string', nullable: true },
                    addressZipCode: { type: 'string', nullable: true },
                    addressCity: { type: 'string', nullable: true },
                    addressCountry: { type: 'string', nullable: true }
                  }
                },
                updatedAt: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, profileController.updateOrganization)

  // Self-service account deletion
  fastify.post('/account/delete', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('profile:update')],
    schema: {
      tags: ['User Profile'],
      summary: 'Delete my account',
      description: 'Deletes the current user account using BetterAuth API. Requires profile:write permission.',
      body: {
        type: 'object',
        properties: {
          confirmToken: { type: 'string', description: 'Must be exactly "DELETE"' },
          reason: { type: 'string', nullable: true, maxLength: 1000 }
        },
        required: ['confirmToken']
      },
      response: {
        204: { description: 'Deleted' },
        400: { $ref: 'ErrorResponse#' },
        401: { $ref: 'ErrorResponse#' },
        409: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    const { confirmToken, reason } = (request.body as any) || {};
    if (confirmToken !== 'DELETE') {
      return reply.status(400).send({ statusCode: 400, error: 'Bad Request', message: 'Confirmation token must be DELETE' })
    }

    try {
      const current = (request as any).user as { id: string, email: string };

      // Block if user has an active subscription
      const { SubscriptionStatusService } = await import('../services/billing/subscription-status.service.js');
      const hasActive = await SubscriptionStatusService.hasActiveProSubscription(current.id);
      if (hasActive) {
        return reply.status(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: 'You have an active subscription. Please cancel it first from Billing before deleting your account.'
        })
      }

      const { UserDeletionService } = await import('../services/user/user-deletion.service.js');
      await UserDeletionService.deleteSelf({ request, currentUser: { id: current.id, email: current.email } as any, reason });
      return reply.status(204).send();
    } catch (error: any) {
      request.log.error({ err: error }, 'Account deletion failed')
      return reply.status(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to delete account' })
    }
  })
}

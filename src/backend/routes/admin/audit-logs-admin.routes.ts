import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../../lib/prisma.js';
import { auditLogService } from '../../services/audit-log.service.js';
import { logger } from '../../utils/logger.js';
import { UnifiedAuthMiddleware } from '../../middleware/unified-auth.middleware.js';

// Simple admin guard reusing DB user role
async function requireAdmin(request: FastifyRequest, reply: FastifyReply) {
  try {
    const user = (request as any).user;
    if (!user || !user.id) {
      return reply.status(401).send({ statusCode: 401, error: 'Unauthorized', message: 'Authentication required' });
    }

    const dbUser = await prisma.user.findUnique({ where: { id: user.id }, select: { role: true, email: true } });
    if (!dbUser || dbUser.role !== 'admin') {
      return reply.status(403).send({ statusCode: 403, error: 'Forbidden', message: 'Admin access required' });
    }
  } catch (err: any) {
    request.log.error({ err }, 'Admin check failed');
    return reply.status(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to verify admin permissions' });
  }
}

interface AdminAuditLogQueryParams {
  startDate?: string;
  endDate?: string;
  actions?: string;       // csv, supports * suffix
  categories?: string;    // csv
  success?: string;       // 'true' | 'false'
  userEmail?: string;     // optional
  userId?: string;        // optional
  resourceType?: string;
  apiKeySuffix?: string;
  ipContains?: string;
  limit?: number;
  offset?: number;
}

export const adminAuditLogsRoutes: FastifyPluginAsync = async (fastify) => {
  // GET /api/admin/audit-logs
  fastify.get('/audit-logs', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin', 'Audit'],
      summary: 'Admin: list audit logs across all users',
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          actions: { type: 'string' },
          categories: { type: 'string' },
          success: { type: 'string', enum: ['true', 'false', ''] },
          userEmail: { type: 'string' },
          userId: { type: 'string' },
          resourceType: { type: 'string' },
          apiKeySuffix: { type: 'string' },
          ipContains: { type: 'string' },
          limit: { type: 'integer', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'integer', minimum: 0, default: 0 }
        }
      },
response: {
        200: {
          type: 'object',
          properties: {
            logs: {
              type: 'array',
              items: {
                type: 'object',
                additionalProperties: true,
                properties: {
                  id: { type: 'string' },
                  action: { type: 'string' },
                  resourceType: { type: ['string', 'null'] },
                  resourceId: { type: ['string', 'null'] },
                  ipAddress: { type: ['string', 'null'] },
                  userAgent: { type: ['string', 'null'] },
                  createdAt: { type: ['string', 'null'], format: 'date-time' },
                  metadata: { type: 'object', additionalProperties: true },
                  userEmail: { type: ['string', 'null'] }
                }
              }
            },
            total: { type: 'integer' },
            limit: { type: 'integer' },
            offset: { type: 'integer' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: AdminAuditLogQueryParams }>, reply: FastifyReply) => {
    try {
      const {
        startDate, endDate, actions, categories, success,
        userEmail, userId, resourceType, apiKeySuffix, ipContains,
        limit = 100, offset = 0
      } = request.query;

      // Resolve userId by email if provided
      let resolvedUserId = userId;
      if (!resolvedUserId && userEmail) {
        const user = await prisma.user.findUnique({ where: { email: userEmail }, select: { id: true } });
        resolvedUserId = user?.id;
        // If email provided but not found, return empty list fast
        if (userEmail && !resolvedUserId) {
          return reply.send({ logs: [], total: 0, limit, offset });
        }
      }

      // Parse dates and filters
      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;
      if (startDateObj && endDateObj && startDateObj > endDateObj) {
        return reply.status(400).send({ error: 'Invalid date range', message: 'Start date must be before end date' });
      }

      const actionsList = actions ? actions.split(',').map(a => a.trim()).filter(Boolean) : undefined;
      const categoriesList = categories ? categories.split(',').map(c => c.trim()).filter(Boolean) : undefined;
      const successBool = typeof success === 'string' && success !== ''
        ? (success.toLowerCase() === 'true' ? true : success.toLowerCase() === 'false' ? false : undefined)
        : undefined;

      const result = await auditLogService.getAuditLogsAdmin({
        startDate: startDateObj,
        endDate: endDateObj,
        actions: actionsList,
        categories: categoriesList,
        success: successBool,
        resourceType: resourceType || undefined,
        apiKeySuffix: apiKeySuffix || undefined,
        ipContains: ipContains || undefined,
        userId: resolvedUserId,
        limit,
        offset,
      });

      // Resolve user emails in batch based on metadata.userId
      const userIds = Array.from(new Set((result.logs || []).map((l: any) => l?.metadata?.userId).filter(Boolean)));
      let userMap: Record<string, { email: string }> = {};
      if (userIds.length > 0) {
        const users = await prisma.user.findMany({ where: { id: { in: userIds } }, select: { id: true, email: true } });
        userMap = Object.fromEntries(users.map(u => [u.id, { email: u.email }]));
      }

      // Normalize logs for frontend (ensure strings, default metadata)
      const normalizedLogs = result.logs.map((l: any) => ({
        id: l.id,
        action: l.action || '',
        resourceType: l.resourceType || null,
        resourceId: l.resourceId || null,
        ipAddress: l.ipAddress || null,
        userAgent: l.userAgent || null,
        // Prefer createdAt; fallback to any metadata.timestamp
        createdAt: (l.createdAt && typeof l.createdAt.toISOString === 'function') ? l.createdAt.toISOString() : (l.createdAt || l.metadata?.timestamp || null),
        metadata: l.metadata || {},
        userEmail: (l.metadata?.userEmail) || (l.metadata?.userId ? (userMap[l.metadata.userId]?.email || null) : null)
      }));

      return reply.send({ logs: normalizedLogs, total: result.total, limit, offset });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Admin audit log fetch failed');
      return reply.status(500).send({ error: 'Internal server error', message: 'Failed to fetch admin audit logs' });
    }
  });

  // GET /api/admin/audit-logs/export (JSON or CSV)
  fastify.get('/audit-logs/export', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin', 'Audit'],
      summary: 'Admin: export audit logs across all users',
      querystring: {
        type: 'object',
        required: ['startDate', 'endDate'],
        properties: {
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          actions: { type: 'string' },
          categories: { type: 'string' },
          success: { type: 'string', enum: ['true', 'false', ''] },
          userEmail: { type: 'string' },
          userId: { type: 'string' },
          resourceType: { type: 'string' },
          apiKeySuffix: { type: 'string' },
          ipContains: { type: 'string' },
          format: { type: 'string', enum: ['json', 'csv'], default: 'json' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: AdminAuditLogQueryParams & { format?: 'json' | 'csv' } }>, reply: FastifyReply) => {
    try {
      const {
        startDate, endDate, actions, categories, success,
        userEmail, userId, resourceType, apiKeySuffix, ipContains,
        format = 'json'
      } = request.query;

      if (!startDate || !endDate) {
        return reply.status(400).send({ error: 'Missing required parameters', message: 'Both startDate and endDate are required' });
      }

      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      if (startDateObj > endDateObj) {
        return reply.status(400).send({ error: 'Invalid date range', message: 'Start date must be before end date' });
      }

      // Resolve userId by email if provided
      let resolvedUserId = userId;
      if (!resolvedUserId && userEmail) {
        const u = await prisma.user.findUnique({ where: { email: userEmail }, select: { id: true } });
        resolvedUserId = u?.id;
        if (userEmail && !resolvedUserId) {
          // No matching user; return empty export
          if (format === 'csv') {
            const headers = ['timestamp','action','resource_type','resource_id','ip_address','user_agent','success','user_email','metadata'];
            reply
              .header('Content-Type', 'text/csv')
              .header('Content-Disposition', `attachment; filename="audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.csv"`)
              .send(headers.join(',') + '\n');
            return;
          } else {
            reply
              .header('Content-Type', 'application/json')
              .header('Content-Disposition', `attachment; filename="audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.json"`)
              .send([]);
            return;
          }
        }
      }

      const actionsList = actions ? actions.split(',').map(a => a.trim()).filter(Boolean) : undefined;
      const categoriesList = categories ? categories.split(',').map(c => c.trim()).filter(Boolean) : undefined;
      const successBool = typeof success === 'string' && success !== ''
        ? (success.toLowerCase() === 'true' ? true : success.toLowerCase() === 'false' ? false : undefined)
        : undefined;

      const { logs } = await auditLogService.getAuditLogsAdmin({
        startDate: startDateObj,
        endDate: endDateObj,
        actions: actionsList,
        categories: categoriesList,
        success: successBool,
        resourceType: resourceType || undefined,
        apiKeySuffix: apiKeySuffix || undefined,
        ipContains: ipContains || undefined,
        userId: resolvedUserId,
        limit: 10000,
        offset: 0,
      });

      // Resolve user emails in batch
      const userIds = Array.from(new Set((logs || []).map((l: any) => l?.metadata?.userId).filter(Boolean)));
      let userMap: Record<string, { email: string }> = {};
      if (userIds.length > 0) {
        const users = await prisma.user.findMany({ where: { id: { in: userIds } }, select: { id: true, email: true } });
        userMap = Object.fromEntries(users.map(u => [u.id, { email: u.email }]));
      }

      const exportLogs = logs.map((l: any) => ({
        id: l.id,
        timestamp: (l.createdAt && typeof l.createdAt.toISOString === 'function') ? l.createdAt.toISOString() : (l.createdAt || l.metadata?.timestamp || null),
        action: l.action || '',
        resourceType: l.resourceType || '',
        resourceId: l.resourceId || '',
        ipAddress: l.ipAddress || '',
        userAgent: l.userAgent || '',
        success: l.metadata?.success ?? true,
        metadata: l.metadata || {},
        userEmail: (l.metadata?.userEmail) || (l.metadata?.userId ? (userMap[l.metadata.userId]?.email || '') : '')
      }));

      if (format === 'csv') {
        const filename = `audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.csv`;
        const csv = convertToCSVAdmin(exportLogs);
        reply
          .header('Content-Type', 'text/csv')
          .header('Content-Disposition', `attachment; filename="${filename}"`)
          .send(csv);
      } else {
        const filename = `audit-logs-${startDateObj.toISOString().split('T')[0]}-to-${endDateObj.toISOString().split('T')[0]}.json`;
        reply
          .header('Content-Type', 'application/json')
          .header('Content-Disposition', `attachment; filename="${filename}"`)
          .send(exportLogs);
      }
    } catch (error: any) {
      logger.error({ error: error.message }, 'Admin audit log export failed');
      return reply.status(500).send({ error: 'Internal server error', message: 'Failed to export admin audit logs' });
    }
  });
};

function convertToCSVAdmin(logs: any[]): string {
  const headers = [
    'timestamp',
    'action',
    'resource_type',
    'resource_id',
    'ip_address',
    'user_agent',
    'success',
    'user_email',
    'metadata'
  ];
  if (!logs || logs.length === 0) {
    return headers.join(',') + '\n';
  }
  const rows = logs.map(l => {
    const fields = [
      l.timestamp || '',
      l.action || '',
      l.resourceType || '',
      l.resourceId || '',
      l.ipAddress || '',
      String(l.userAgent || '').replace(/,/g, ';'),
      (l.success !== false ? 'true' : 'false'),
      l.userEmail || '',
      JSON.stringify(l.metadata || {}).replace(/\"/g, '""')
    ];
    return fields.map(f => `"${String(f).replace(/"/g, '""')}"`).join(',');
  });
  return [headers.join(','), ...rows].join('\n');
}


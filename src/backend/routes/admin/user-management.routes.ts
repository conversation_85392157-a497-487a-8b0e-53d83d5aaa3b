import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { UnifiedAuthMiddleware } from '../../middleware/unified-auth.middleware.js';
// Legacy AdminPermissions removed - rely on role-based checks
import { prisma } from '../../lib/prisma.js';
import { OneOffSubscriptionService } from '../../services/billing/one-off-subscription.service.js';
import { logger } from '../../utils/logger.js';
import { notify } from '../../services/notifications/index.js';

export const adminUserManagementRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Middleware to check admin permissions
  const requireAdminPermission = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = (request as any).user;
      if (!user || !user.email) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Authentication required'
        });
      }

      if (user.role !== 'admin') {
        fastify.log.warn({ 
          userEmail: user.email,
        }, 'Unauthorized admin access attempt');
        
        return reply.status(403).send({
          statusCode: 403,
          error: 'Forbidden',
          message: 'You are not authorized to manage users'
        });
      }
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Error in admin permission check');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to verify admin permissions'
      });
    }
  };

// Get all users for admin table
  // Alias: GET /api/admin/users (expected by tests)
  fastify.get('/users', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Get users for admin management',
      description: 'Alias for /users/all returning the same payload',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'number', minimum: 0, default: 0 },
          search: { type: 'string', nullable: true }
        }
      }
    }
  }, async (request, reply) => {
    // Delegate to /users/all handler logic with basic search support
    const { limit = 100, offset = 0, search } = request.query as { limit?: number; offset?: number; search?: string };
    const where = search ? { email: { contains: search, mode: 'insensitive' as const } } : {};
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        take: limit,
        skip: offset,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          createdAt: true,
          trialStartedAt: true,
          trialEndsAt: true,
          subscriptions: {
            where: { status: 'ACTIVE' },
            select: { id: true, status: true, nextPaymentDate: true },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    return reply.send({
      success: true,
      users: users.map(u => ({
        id: u.id,
        email: u.email,
        name: u.name,
        planType: u.planType,
        createdAt: u.createdAt.toISOString(),
        trialStartedAt: u.trialStartedAt?.toISOString() || null,
        trialEndsAt: u.trialEndsAt?.toISOString() || null,
        hasActiveSubscription: u.subscriptions[0] ? u.subscriptions[0].status === 'ACTIVE' : false,
        subscription: u.subscriptions[0] ? {
          id: u.subscriptions[0].id,
          status: u.subscriptions[0].status,
          nextPaymentDate: u.subscriptions[0].nextPaymentDate?.toISOString() || null
        } : null
      })),
      total
    });
  });

  // Get all users for admin table
  fastify.get('/users/all', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Get all users for admin management',
      description: 'Fetch all users with their basic information for admin table view',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'number', minimum: 0, default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            users: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  name: { type: 'string', nullable: true },
                  planType: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  trialStartedAt: { type: 'string', format: 'date-time', nullable: true },
                  trialEndsAt: { type: 'string', format: 'date-time', nullable: true },
                  subscription: {
                    type: 'object',
                    nullable: true,
                    properties: {
                      id: { type: 'string' },
                      status: { type: 'string' },
                      nextPaymentDate: { type: 'string', format: 'date-time' }
                    }
                  }
                }
              }
            },
            total: { type: 'number' }
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { limit = 100, offset = 0 } = request.query as { limit?: number; offset?: number };
      
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          take: limit,
          skip: offset,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            email: true,
            name: true,
            planType: true,
            createdAt: true,
            trialStartedAt: true,
            trialEndsAt: true,
            subscriptions: {
              where: { status: 'ACTIVE' },
              select: {
                id: true,
                status: true,
                nextPaymentDate: true
              },
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        }),
        prisma.user.count()
      ]);

      return reply.send({
        success: true,
        users: users.map(user => ({
          id: user.id,
          email: user.email,
          name: user.name,
          planType: user.planType,
          createdAt: user.createdAt.toISOString(),
          trialStartedAt: user.trialStartedAt?.toISOString() || null,
          trialEndsAt: user.trialEndsAt?.toISOString() || null,
          subscription: user.subscriptions[0] ? {
            id: user.subscriptions[0].id,
            status: user.subscriptions[0].status,
            nextPaymentDate: user.subscriptions[0].nextPaymentDate?.toISOString() || null
          } : null
        })),
        total
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to fetch all users');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to fetch users'
      });
    }
  });

  // REMOVED: Duplicate impersonation status route
  // This route is now handled by /api/admin/impersonation/status in impersonation.routes.ts
  // which uses BetterAuth authentication instead of the legacy auth system

  // REMOVED: Duplicate impersonation endpoint
  // This route is now handled by /api/admin/impersonation/impersonate/:userId in impersonation.routes.ts
  // which uses BetterAuth authentication instead of the legacy auth system

  // Lookup user by email
  fastify.get('/users/lookup', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Lookup user by email',
      description: 'Find a user by their email address for administration',
      querystring: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' }
        },
        required: ['email']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            user: {
              type: 'object',
              nullable: true,
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                planType: { type: 'string' },
                createdAt: { type: 'string', format: 'date-time' },
                subscription: {
                  type: 'object',
                  nullable: true,
                  properties: {
                    id: { type: 'string' },
                    status: { type: 'string' },
                    nextPaymentDate: { type: 'string', format: 'date-time' },
                    mollieId: { type: 'string', nullable: true }
                  }
                }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { email } = request.query as { email: string };
      
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          createdAt: true,
          subscriptions: {
            where: { status: 'ACTIVE' },
            select: {
              id: true,
              status: true,
              nextPaymentDate: true,
              mollieId: true
            },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      return reply.send({
        success: true,
        user: user ? {
          id: user.id,
          email: user.email,
          name: user.name,
          planType: user.planType,
          createdAt: user.createdAt.toISOString(),
          subscription: user.subscriptions[0] || null
        } : null
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to lookup user');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to lookup user'
      });
    }
  });

  // Delete user account (admin)
  fastify.post('/users/:userId/delete', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Delete user account',
      description: 'Deletes a user account using BetterAuth API, logs audit, and optionally emails the user.',
      params: {
        type: 'object',
        properties: { userId: { type: 'string' } },
        required: ['userId']
      },
      body: {
        type: 'object',
        properties: {
          reason: { type: 'string', nullable: true, maxLength: 1000 },
          notify: { type: 'boolean', default: true }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: { success: { type: 'boolean' } }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        403: errorResponseSchema,
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const { reason, notify } = (request.body as any) || {};
      const adminUser = (request as any).user as { id: string, email: string };

      // Ensure user exists
      const target = await prisma.user.findUnique({ where: { id: userId }, select: { id: true, email: true, name: true } });
      if (!target) {
        return reply.status(404).send({ statusCode: 404, error: 'Not Found', message: 'User not found' });
      }

      // Block if user has an active subscription
      const { SubscriptionStatusService } = await import('../../services/billing/subscription-status.service.js');
      const hasActive = await SubscriptionStatusService.hasActiveProSubscription(userId);
      if (hasActive) {
        return reply.status(409).send({ statusCode: 409, error: 'Conflict', message: 'User has an active subscription. Ask them to cancel first.' });
      }

      const { UserDeletionService } = await import('../../services/user/user-deletion.service.js');
      await UserDeletionService.deleteAsAdmin({ request, targetUserId: userId, adminUser, reason, notify, bcc: [{ email: '<EMAIL>', name: 'Xander' }] });
      return reply.send({ success: true });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to delete user (admin)');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to delete user' });
    }
  });

  // Upgrade user to Pro
  fastify.post('/users/:userId/upgrade', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Upgrade user to Pro plan',
      description: 'Manually upgrade a user to Pro plan (creates virtual subscription)',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      body: {
        type: 'object',
        properties: {
          renewalDate: { 
            type: 'string', 
            format: 'date',
            description: 'Date when user must start paying for renewal (YYYY-MM-DD)' 
          }
        },
        required: ['renewalDate']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            subscription: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                status: { type: 'string' },
                planType: { type: 'string' },
                nextPaymentDate: { type: 'string', format: 'date-time' }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const { renewalDate } = request.body as { renewalDate: string };
      const adminUser = (request as any).user;

      // Validate renewal date
      const renewalDateTime = new Date(renewalDate);
      if (isNaN(renewalDateTime.getTime()) || renewalDateTime <= new Date()) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Renewal date must be a valid future date'
        });
      }

      // Verify user exists and is not already Pro
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, email: true, planType: true, subscriptions: { where: { status: 'ACTIVE' } } }
      });

      if (!user) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      if (user.planType === 'pro') {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'User is already on Pro plan'
        });
      }

      if (user.subscriptions.length > 0) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'User already has an active subscription'
        });
      }

      // Get proper pricing from plan config
      const planConfig = await import('../../services/billing/plan-config.service.js');
      const proConfig = planConfig.PlanConfigService.getPlanConfig('pro');
      const monthlyPrice = proConfig.price?.monthly || 9.95;

      // Create a fake payment record for the upgrade
      const fakePayment = await prisma.payment.create({
        data: {
          mollieId: `admin-upgrade-${Date.now()}-${Math.random().toString(36).substring(2)}`,
          userId,
          status: 'PAID',
          amount: monthlyPrice,
          currency: 'EUR',
          description: 'Manual Pro upgrade by admin',
          method: 'admin_upgrade',
          paidAt: new Date()
        }
      });

      // Create virtual subscription using OneOffSubscriptionService pattern
      const subscription = await OneOffSubscriptionService.createVirtualSubscription({
        userId,
        planType: 'pro',
        amount: monthlyPrice,
        currency: 'EUR',
        billingPeriod: 'monthly',
        paymentId: fakePayment.id,
        description: 'Pro plan - Manual admin upgrade'
      });

      // Override the next payment date with the admin-specified date
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { nextPaymentDate: renewalDateTime }
      });

      // Update user plan type
      await prisma.user.update({
        where: { id: userId },
        data: { planType: 'pro' }
      });

      // Send upgrade notification with renewal date
      await notify.subscription.upgraded(userId, {
        planType: 'pro',
        upgradedBy: 'admin',
        renewalDate: renewalDateTime.toISOString()
      });

      logger.info({
        userId,
        userEmail: user.email,
        adminEmail: adminUser.email,
        subscriptionId: subscription.id,
        action: 'manual_upgrade'
      }, 'Admin manually upgraded user to Pro');

      return reply.send({
        success: true,
        message: `Successfully upgraded ${user.email} to Pro plan`,
        subscription: {
          id: subscription.id,
          status: subscription.status,
          planType: subscription.planType,
          nextPaymentDate: subscription.nextPaymentDate?.toISOString()
        }
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to upgrade user');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to upgrade user'
      });
    }
  });

  // Downgrade user to Free
  fastify.post('/users/:userId/downgrade', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdminPermission],
    schema: {
      tags: ['Admin', 'Users'],
      summary: 'Downgrade user to Free plan',
      description: 'Manually downgrade a user to Free plan (cancels subscription, preserves data within limits)',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            preservedData: {
              type: 'object',
              properties: {
                domainsKept: { type: 'number' },
                aliasesKept: { type: 'number' },
                webhooksKept: { type: 'number' }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const adminUser = (request as any).user;

      // Verify user exists and is currently Pro
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true, 
          email: true, 
          planType: true,
          subscriptions: { where: { status: 'ACTIVE' } },
          domains: { 
            select: { id: true, domain: true },
            orderBy: { createdAt: 'asc' } 
          }
        }
      });

      if (!user) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      if (user.planType === 'free') {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'User is already on Free plan'
        });
      }

      // Handle data preservation within free limits
      const freeLimit = 2; // Free users get 2 domains
      const preservedData = {
        domainsKept: Math.min(user.domains.length, freeLimit),
        aliasesKept: 0,
        webhooksKept: 0
      };

      // If user has more domains than free limit, we need to deactivate excess ones
      if (user.domains.length > freeLimit) {
        const excessDomains = user.domains.slice(freeLimit);
        
        // Deactivate excess domains (keep them in DB but mark as inactive)
        await prisma.domain.updateMany({
          where: { 
            id: { in: excessDomains.map(d => d.id) }
          },
          data: { 
            verified: false, // This effectively deactivates them
            updatedAt: new Date()
          }
        });

        logger.info({
          userId,
          domainsDeactivated: excessDomains.length,
          deactivatedDomains: excessDomains.map(d => d.domain)
        }, 'Deactivated excess domains during downgrade');
      }

      // Count preserved aliases and webhooks (for the first 2 domains)
      const keptDomainIds = user.domains.slice(0, freeLimit).map(d => d.id);
      
      const [aliasCount, webhookCount] = await Promise.all([
        prisma.alias.count({ where: { domainId: { in: keptDomainIds } } }),
        prisma.webhook.count({ where: { userId } }) // Webhooks are user-level, not domain-level
      ]);

      preservedData.aliasesKept = aliasCount;
      preservedData.webhooksKept = webhookCount;

      // Cancel active subscriptions
      await prisma.subscription.updateMany({
        where: { 
          userId,
          status: 'ACTIVE'
        },
        data: { 
          status: 'CANCELLED',
          cancelledAt: new Date(),
          updatedAt: new Date()
        }
      });

      // Update user plan type
      await prisma.user.update({
        where: { id: userId },
        data: { planType: 'free' }
      });

      // Send downgrade notification
      await notify.subscription.downgraded(userId, {
        planType: 'free',
        downgradedBy: 'admin',
        preservedData
      });

      logger.info({
        userId,
        userEmail: user.email,
        adminEmail: adminUser.email,
        preservedData,
        action: 'manual_downgrade'
      }, 'Admin manually downgraded user to Free');

      return reply.send({
        success: true,
        message: `Successfully downgraded ${user.email} to Free plan`,
        preservedData
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to downgrade user');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to downgrade user'
      });
    }
  });
};
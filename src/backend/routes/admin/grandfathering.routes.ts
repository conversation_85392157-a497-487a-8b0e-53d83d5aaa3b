
import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../../middleware/unified-auth.middleware.js';
import { GrandfatheringService } from '../../services/billing/grandfathering.service.js';
import { prisma } from '../../lib/prisma.js';

export const adminGrandfatheringRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Apply grandfathering to existing subscriptions (bulk operation)
  fastify.post('/apply-bulk', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'),
    schema: {
      tags: ['Admin', 'Grandfathering'],
      summary: 'Apply grandfathering to existing subscriptions',
      description: 'Bulk apply grandfathering when prices increase',
      body: {
        type: 'object',
        properties: {
          planType: { type: 'string', enum: ['pro', 'enterprise'] },
          interval: { type: 'string', enum: ['monthly', 'yearly'] },
          oldPrice: { type: 'number' },
          newPrice: { type: 'number' },
          reason: { type: 'string' },
          gracePeriodDays: { type: 'number', default: 30 }
        },
        required: ['planType', 'interval', 'oldPrice', 'newPrice', 'reason']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            grandfatheredCount: { type: 'number' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      // Admin access is already enforced by requireAdmin middleware
      const user = (request as any).user;
      fastify.log.info({ 
        adminEmail: user.email,
        action: 'grandfathering.bulk.attempt',
        planType: (request.body as any).planType,
        interval: (request.body as any).interval
      }, 'Admin attempting bulk grandfathering');

      const { planType, interval, oldPrice, newPrice, reason, gracePeriodDays } = request.body as {
        planType: string;
        interval: string;
        oldPrice: number;
        newPrice: number;
        reason: string;
        gracePeriodDays?: number;
      };

      const grandfatheredCount = await GrandfatheringService.grandfatherExistingSubscriptions({
        planType,
        interval,
        oldPrice,
        newPrice,
        reason,
        gracePeriodDays
      });

      return reply.send({
        success: true,
        grandfatheredCount,
        message: `Successfully applied grandfathering to ${grandfatheredCount} subscriptions`
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to apply bulk grandfathering');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to apply grandfathering'
      });
    }
  });

  // Apply early adopter grandfathering to specific users
  fastify.post('/early-adopter', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'),
    schema: {
      tags: ['Admin', 'Grandfathering'],
      summary: 'Apply early adopter grandfathering',
      description: 'Grant early adopter grandfathering to specific users',
      body: {
        type: 'object',
        properties: {
          userIds: {
            type: 'array',
            items: { type: 'string' }
          },
          userEmails: {
            type: 'array',
            items: { type: 'string' }
          },
          reason: { type: 'string', default: 'early_adopter' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            grandfatheredCount: { type: 'number' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      // Admin access is already enforced by requireAdmin middleware
      const user = (request as any).user;
      fastify.log.info({ 
        adminEmail: user.email,
        action: 'grandfathering.early_adopter.attempt',
        targetCount: ((request.body as any).userIds?.length || 0) + ((request.body as any).userEmails?.length || 0)
      }, 'Admin attempting early adopter grandfathering');

      const { userIds, userEmails, reason } = request.body as {
        userIds?: string[];
        userEmails?: string[];
        reason?: string;
      };

      let targetUserIds: string[] = [];

      if (userIds) {
        targetUserIds = userIds;
      } else if (userEmails) {
        // Convert emails to user IDs
        const users = await prisma.user.findMany({
          where: {
            email: {
              in: userEmails
            }
          },
          select: {
            id: true,
            email: true
          }
        });
        targetUserIds = users.map(user => user.id);
        
        if (users.length !== userEmails.length) {
          const foundEmails = users.map(u => u.email);
          const notFound = userEmails.filter(email => !foundEmails.includes(email));
          fastify.log.warn({ notFound }, 'Some emails not found');
        }
      } else {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Either userIds or userEmails must be provided'
        });
      }

      if (targetUserIds.length === 0) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'No valid users found'
        });
      }

      const grandfatheredCount = await GrandfatheringService.applyEarlyAdopterGrandfathering(
        targetUserIds,
        reason || 'early_adopter'
      );

      return reply.send({
        success: true,
        grandfatheredCount,
        message: `Successfully applied early adopter grandfathering to ${grandfatheredCount} users`
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to apply early adopter grandfathering');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to apply early adopter grandfathering'
      });
    }
  });

  // Remove grandfathering from a subscription
  fastify.delete('/:subscriptionId', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'),
    schema: {
      tags: ['Admin', 'Grandfathering'],
      summary: 'Remove grandfathering from subscription',
      description: 'Remove grandfathering status from a specific subscription',
      params: {
        type: 'object',
        properties: {
          subscriptionId: { type: 'string' }
        },
        required: ['subscriptionId']
      },
      body: {
        type: 'object',
        properties: {
          reason: { type: 'string' }
        },
        required: ['reason']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      // Admin access is already enforced by requireAdmin middleware
      const user = (request as any).user;
      const { subscriptionId } = request.params as { subscriptionId: string };
      const { reason } = request.body as { reason: string };
      
      fastify.log.info({ 
        adminEmail: user.email,
        action: 'grandfathering.remove.attempt',
        subscriptionId,
        reason
      }, 'Admin attempting to remove grandfathering');

      // Verify subscription exists
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId }
      });

      if (!subscription) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Subscription not found'
        });
      }

      await GrandfatheringService.removeGrandfathering(subscriptionId, reason);

      return reply.send({
        success: true,
        message: 'Grandfathering removed successfully'
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to remove grandfathering');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to remove grandfathering'
      });
    }
  });

  // Get grandfathering statistics
  fastify.get('/stats', {
    preHandler: UnifiedAuthMiddleware.requireFeature('admin:access'),
    schema: {
      tags: ['Admin', 'Grandfathering'],
      summary: 'Get grandfathering statistics',
      description: 'Retrieve statistics about grandfathered subscriptions',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            stats: {
              type: 'object',
              properties: {
                totalGrandfathered: { type: 'number' },
                totalSubscriptions: { type: 'number' },
                grandfatheringPercentage: { type: 'number' },
                byReason: {
                  type: 'object',
                  additionalProperties: { type: 'number' }
                },
                byPlan: {
                  type: 'object',
                  additionalProperties: { type: 'number' }
                },
                totalSavings: { type: 'number' }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      // Get grandfathering statistics
      const totalSubscriptions = await prisma.subscription.count({
        where: { status: 'ACTIVE' }
      });

      const grandfatheredSubscriptions = await prisma.subscription.findMany({
        where: {
          status: 'ACTIVE',
          isGrandfathered: true
        },
        select: {
          grandfatheringReason: true,
          planType: true,
          grandfatheredPrice: true,
          amount: true
        }
      });

      const totalGrandfathered = grandfatheredSubscriptions.length;
      const grandfatheringPercentage = totalSubscriptions > 0 
        ? Math.round((totalGrandfathered / totalSubscriptions) * 100) 
        : 0;

      // Group by reason
      const byReason: Record<string, number> = {};
      grandfatheredSubscriptions.forEach(sub => {
        const reason = sub.grandfatheringReason || 'unknown';
        byReason[reason] = (byReason[reason] || 0) + 1;
      });

      // Group by plan
      const byPlan: Record<string, number> = {};
      grandfatheredSubscriptions.forEach(sub => {
        byPlan[sub.planType] = (byPlan[sub.planType] || 0) + 1;
      });

      // Calculate total monthly savings
      const totalSavings = grandfatheredSubscriptions.reduce((total, sub) => {
        if (sub.grandfatheredPrice && sub.amount) {
          const savings = Number(sub.amount) - Number(sub.grandfatheredPrice);
          return total + Math.max(0, savings);
        }
        return total;
      }, 0);

      return reply.send({
        success: true,
        stats: {
          totalGrandfathered,
          totalSubscriptions,
          grandfatheringPercentage,
          byReason,
          byPlan,
          totalSavings: Math.round(totalSavings * 100) / 100 // Round to 2 decimal places
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get grandfathering stats');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve grandfathering statistics'
      });
    }
  });
};

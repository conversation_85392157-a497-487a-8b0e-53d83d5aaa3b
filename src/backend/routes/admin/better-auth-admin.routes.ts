import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { auth } from '../../lib/better-auth.js';
// Legacy AdminPermissions removed - rely on BetterAuth role
import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

export const betterAuthAdminRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Middleware to check BetterAuth admin permissions
  const requireBetterAuthAdmin = async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Get session from BetterAuth
      const session = await auth.api.getSession({
        headers: request.headers as any,
      });

      if (!session || !session.user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Authentication required'
        });
      }

      // Check if user has admin permissions
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { email: true, planType: true }
      });

      if (!user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      // Check admin permissions using existing system
      // Check admin role only
      const isAdmin = (await prisma.user.findUnique({ where: { id: session.user.id }, select: { role: true } }))?.role === 'admin';
      if (!isAdmin) {
        return reply.status(403).send({
          statusCode: 403,
          error: 'Forbidden',
          message: 'Admin access required'
        });
      }

      // Attach user info to request
      (request as any).user = {
        id: session.user.id,
        email: user.email,
        planType: user.planType
      };

    } catch (error: any) {
      logger.error({ error: error.message }, 'Error in BetterAuth admin permission check');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to verify admin permissions'
      });
    }
  };

  // Start impersonation via BetterAuth API
  fastify.post('/impersonate/:userId', {
    preHandler: requireBetterAuthAdmin,
    schema: {
      tags: ['Admin', 'BetterAuth'],
      summary: 'Start impersonating user via BetterAuth',
      description: 'Start impersonating a user using BetterAuth admin API',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' }
              }
            }
          }
        },
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      const currentUser = (request as any).user;

      // Verify target user exists
      const targetUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true
        }
      });

      if (!targetUser) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        });
      }

      // Use BetterAuth's admin functionality - for now, we'll integrate with existing JWT system
      // TODO: Implement proper BetterAuth admin impersonation when API is stable

      logger.info({
        impersonatorEmail: currentUser.email,
        targetUserId: userId,
        targetUserEmail: targetUser.email,
        action: 'better_auth_impersonation_started'
      }, 'Admin started impersonating user via BetterAuth');

      return reply.send({
        success: true,
        message: `Now impersonating ${targetUser.email}`,
        user: {
          id: targetUser.id,
          email: targetUser.email,
          name: targetUser.name
        }
      });

    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to start BetterAuth impersonation');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to start impersonation: ' + error.message
      });
    }
  });

  // Stop impersonation via BetterAuth API
  fastify.post('/stop-impersonation', {
    preHandler: requireBetterAuthAdmin,
    schema: {
      tags: ['Admin', 'BetterAuth'],
      summary: 'Stop BetterAuth impersonation',
      description: 'Stop current BetterAuth impersonation session',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      // TODO: Use BetterAuth's API to stop impersonation when available
      // For now, acknowledge the request

      logger.info({
        action: 'better_auth_impersonation_stopped'
      }, 'Admin stopped BetterAuth impersonation');

      return reply.send({
        success: true,
        message: 'Stopped impersonation via BetterAuth'
      });

    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to stop BetterAuth impersonation');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to stop impersonation: ' + error.message
      });
    }
  });

  // Get admin status with BetterAuth integration
  fastify.get('/status', {
    preHandler: requireBetterAuthAdmin,
    schema: {
      tags: ['Admin', 'BetterAuth'],
      summary: 'Get BetterAuth admin status',
      description: 'Check admin permissions and impersonation status via BetterAuth',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            isAdmin: { type: 'boolean' },
            canImpersonate: { type: 'boolean' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                planType: { type: 'string' }
              }
            }
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    const currentUser = (request as any).user;

    return reply.send({
      success: true,
      isAdmin: true,
      canImpersonate: true,
      user: {
        id: currentUser.id,
        email: currentUser.email,
        planType: currentUser.planType
      }
    });
  });
};
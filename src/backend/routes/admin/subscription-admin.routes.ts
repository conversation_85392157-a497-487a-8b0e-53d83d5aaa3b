import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../../middleware/unified-auth.middleware.js';
import { schedulerService } from '../../services/scheduler.service.js';

export const adminSubscriptionsRoutes: FastifyPluginAsync = async (fastify) => {
  // Admin-only trigger for subscription lifecycle worker
  fastify.post('/run-lifecycle', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Admin'],
      summary: 'Run subscription lifecycle now',
      description: 'Admin-only endpoint to trigger end-of-period downgrades processing immediately',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (_request, reply) => {
    try {
      await schedulerService.triggerSubscriptionLifecycle();
      return reply.send({ success: true, message: 'Subscription lifecycle processed' });
    } catch (error: any) {
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: error.message });
    }
  });

  // Admin-only trigger for renewal notifications
  fastify.post('/run-renewal-notifications', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Admin'],
      summary: 'Run renewal notifications now',
      description: 'Admin-only endpoint to trigger subscription renewal email notifications immediately',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (_request, reply) => {
    try {
      await schedulerService.triggerRenewalNotifications();
      return reply.send({ success: true, message: 'Renewal notifications processed' });
    } catch (error: any) {
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: error.message });
    }
  });
};


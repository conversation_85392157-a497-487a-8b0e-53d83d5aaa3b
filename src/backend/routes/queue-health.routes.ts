import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { queueMonitoringService } from '../services/queue-monitoring.service.js';
import { enhancedQueueService } from '../services/enhanced-queue.service.js';
import { redisRateLimiter } from '../services/redis-rate-limiter.service.js';
import { logger } from '../utils/logger.js';

export const queueHealthRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  /**
   * Get overall queue system health
   * Requires admin access
   */
  fastify.get('/queue/health', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Queue Management'],
      summary: 'Get queue system health status',
      description: 'Returns comprehensive health information about the queue system including Redis, queues, and monitoring data.',
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
            timestamp: { type: 'string', format: 'date-time' },
            redis: {
              type: 'object',
              properties: {
                connected: { type: 'boolean' },
                sentinel: { type: 'boolean' },
                rateLimiter: { type: 'boolean' },
              }
            },
            queues: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  waiting: { type: 'number' },
                  active: { type: 'number' },
                  completed: { type: 'number' },
                  failed: { type: 'number' },
                  delayed: { type: 'number' },
                  paused: { type: 'boolean' },
                  health: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
                  lastProcessedAt: { type: 'string', format: 'date-time', nullable: true },
                  avgProcessingTime: { type: 'number', nullable: true },
                }
              }
            },
            alerts: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string' },
                  severity: { type: 'string', enum: ['warning', 'critical'] },
                  message: { type: 'string' },
                  queueName: { type: 'string', nullable: true },
                  timestamp: { type: 'string', format: 'date-time' },
                }
              }
            },
            deadLetterCount: { type: 'number' },
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema,
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Get queue health from enhanced queue service
      const queueHealth = await enhancedQueueService.getQueueHealth();
      
      // Get metrics from monitoring service
      const queueMetrics = queueMonitoringService.getMetrics();
      
      // Get recent alerts
      const recentAlerts = queueMonitoringService.getRecentAlerts(60); // Last hour
      
      // Get rate limiter health
      const rateLimiterHealth = await redisRateLimiter.getHealthStatus();

      // Determine overall status
      let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (!queueHealth.redis || !rateLimiterHealth.redisConnected) {
        overallStatus = 'critical';
      } else if (queueMetrics.some(q => q.health === 'critical') || 
                 recentAlerts.some(a => a.severity === 'critical')) {
        overallStatus = 'critical';
      } else if (queueMetrics.some(q => q.health === 'warning') || 
                 recentAlerts.some(a => a.severity === 'warning')) {
        overallStatus = 'warning';
      }

      const response = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        redis: {
          connected: queueHealth.redis,
          sentinel: process.env.NODE_ENV === 'production' || process.env.USE_REDIS_SENTINEL === 'true',
          rateLimiter: rateLimiterHealth.redisConnected,
        },
        queues: queueMetrics,
        alerts: recentAlerts.map(alert => ({
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
          queueName: alert.queueName,
          timestamp: alert.timestamp.toISOString(),
        })),
        deadLetterCount: queueHealth.deadLetterCount,
      };

      reply.send(response);
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get queue health');
      reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve queue health information'
      });
    }
  });

  /**
   * Get detailed status for a specific queue
   */
  fastify.get<{ Params: { queueName: string } }>('/queue/:queueName/status', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Queue Management'],
      summary: 'Get detailed status for a specific queue',
      params: {
        type: 'object',
        properties: {
          queueName: { type: 'string' }
        },
        required: ['queueName']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            counts: {
              type: 'object',
              properties: {
                waiting: { type: 'number' },
                active: { type: 'number' },
                completed: { type: 'number' },
                failed: { type: 'number' },
                delayed: { type: 'number' },
              }
            },
            recentJobs: {
              type: 'object',
              properties: {
                waiting: { type: 'array' },
                active: { type: 'array' },
                failed: { type: 'array' },
                completed: { type: 'array' },
              }
            },
            metrics: { type: 'object' },
          }
        },
        404: errorResponseSchema,
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema,
      }
    }
  }, async (request: FastifyRequest<{ Params: { queueName: string } }>, reply: FastifyReply) => {
    try {
      const { queueName } = request.params;
      
      const detailedStatus = await queueMonitoringService.getDetailedQueueStatus(queueName);
      
      reply.send(detailedStatus);
    } catch (error) {
      if (error.message.includes('not found')) {
        reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: `Queue '${request.params.queueName}' not found`
        });
      } else {
        logger.error({ 
          error: error.message, 
          queueName: request.params.queueName 
        }, 'Failed to get detailed queue status');
        
        reply.code(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Failed to retrieve queue status'
        });
      }
    }
  });

  /**
   * Get queue alerts
   */
  fastify.get('/queue/alerts', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Queue Management'],
      summary: 'Get queue system alerts',
      querystring: {
        type: 'object',
        properties: {
          severity: { type: 'string', enum: ['warning', 'critical'] },
          minutes: { type: 'number', minimum: 1, maximum: 1440, default: 60 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            alerts: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string' },
                  severity: { type: 'string', enum: ['warning', 'critical'] },
                  message: { type: 'string' },
                  queueName: { type: 'string', nullable: true },
                  timestamp: { type: 'string', format: 'date-time' },
                  metrics: { type: 'object', nullable: true },
                }
              }
            },
            summary: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                critical: { type: 'number' },
                warning: { type: 'number' },
              }
            }
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema,
      }
    }
  }, async (request: FastifyRequest<{ 
    Querystring: { severity?: 'warning' | 'critical'; minutes?: number } 
  }>, reply: FastifyReply) => {
    try {
      const { severity, minutes = 60 } = request.query;
      
      let alerts = queueMonitoringService.getRecentAlerts(minutes);
      
      if (severity) {
        alerts = alerts.filter(alert => alert.severity === severity);
      }

      const summary = {
        total: alerts.length,
        critical: alerts.filter(a => a.severity === 'critical').length,
        warning: alerts.filter(a => a.severity === 'warning').length,
      };

      reply.send({
        alerts: alerts.map(alert => ({
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
          queueName: alert.queueName,
          timestamp: alert.timestamp.toISOString(),
          metrics: alert.metrics,
        })),
        summary,
      });
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get queue alerts');
      reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve queue alerts'
      });
    }
  });

  /**
   * Manual retry of dead letter queue jobs
   */
  fastify.post<{ Body: { jobIds?: string[]; queueName?: string } }>('/queue/retry-dead-letter', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access')],
    schema: {
      tags: ['Queue Management'],
      summary: 'Retry jobs from dead letter queue',
      body: {
        type: 'object',
        properties: {
          jobIds: { 
            type: 'array', 
            items: { type: 'string' },
            description: 'Specific job IDs to retry (optional)'
          },
          queueName: { 
            type: 'string',
            description: 'Only retry jobs from specific queue (optional)'
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            retriedCount: { type: 'number' },
            message: { type: 'string' },
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        500: errorResponseSchema,
      }
    }
  }, async (request: FastifyRequest<{ 
    Body: { jobIds?: string[]; queueName?: string } 
  }>, reply: FastifyReply) => {
    try {
      const { jobIds, queueName } = request.body;
      
      // This would implement dead letter queue retry logic
      // For now, return a placeholder response
      logger.info({ 
        jobIds, 
        queueName,
        adminUser: request.user?.id 
      }, 'Dead letter queue retry requested');

      reply.send({
        success: true,
        retriedCount: 0,
        message: 'Dead letter queue retry functionality will be implemented',
      });
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to retry dead letter queue jobs');
      reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retry dead letter queue jobs'
      });
    }
  });
};

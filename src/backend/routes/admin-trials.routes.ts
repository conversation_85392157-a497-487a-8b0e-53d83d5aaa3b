import { FastifyPluginAsync } from 'fastify';
import { UnifiedAuthMiddleware } from '../middleware/unified-auth.middleware.js';
import { TrialService } from '../services/billing/trial.service.js';
import { logger } from '../utils/logger.js';

export const adminTrialsRoutes: FastifyPluginAsync = async (fastify) => {
  // Middleware to check if user is admin
  const requireAdmin = async (request: any, reply: any) => {
    if (!request.user || request.user.role !== 'admin') {
      return reply.code(403).send({
        statusCode: 403,
        error: 'Forbidden',
        message: 'Admin access required'
      });
    }
  };

  // Start trial for a user
  fastify.post('/admin/trials/start', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin'],
      summary: 'Start a trial for a user',
      body: {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          days: { type: 'number', minimum: 1, maximum: 365, default: 7 }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            trial: {
              type: 'object',
              properties: {
                started: { type: 'string', format: 'date-time' },
                ends: { type: 'string', format: 'date-time' },
                daysRemaining: { type: 'number' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId, days = 7 } = request.body as { userId: string; days?: number };
      const adminEmail = (request as any).user.email;

      await TrialService.startTrial(userId, adminEmail, days);
      const trialInfo = await TrialService.getTrialInfo(userId);

      logger.info({ userId, adminEmail }, 'Admin started trial for user');

      return reply.send({
        success: true,
        message: 'Trial started successfully',
        trial: trialInfo
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to start trial');
      return reply.code(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: error.message
      });
    }
  });

  // Reset trial for a user (allows them to start a new trial)
  fastify.post('/admin/trials/reset', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin'],
      summary: 'Reset a user\'s trial eligibility',
      body: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.body as { userId: string };
      const adminEmail = (request as any).user.email;

      await TrialService.resetTrial(userId, adminEmail);

      logger.info({ userId, adminEmail }, 'Admin reset trial for user');

      return reply.send({
        success: true,
        message: 'Trial eligibility reset successfully'
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to reset trial');
      return reply.code(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: error.message
      });
    }
  });

  // End trial for a user
  fastify.post('/admin/trials/end', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin'],
      summary: 'End a trial for a user',
      body: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.body as { userId: string };
      const adminEmail = (request as any).user.email;

      await TrialService.endTrial(userId);

      logger.info({ userId, adminEmail }, 'Admin ended trial for user');

      return reply.send({
        success: true,
        message: 'Trial ended successfully'
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to end trial');
      return reply.code(400).send({
        statusCode: 400,
        error: 'Bad Request',
        message: error.message
      });
    }
  });

  // Get trial status for a user
  fastify.get('/admin/trials/status/:userId', {
    preHandler: [UnifiedAuthMiddleware.requireFeature('admin:access'), requireAdmin],
    schema: {
      tags: ['Admin'],
      summary: 'Get trial status for a user',
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            hasUsedTrial: { type: 'boolean' },
            isActive: { type: 'boolean' },
            trial: {
              type: 'object',
              nullable: true,
              properties: {
                started: { type: 'string', format: 'date-time' },
                ends: { type: 'string', format: 'date-time' },
                activatedBy: { type: 'string', nullable: true },
                daysRemaining: { type: 'number' }
              }
            }
          }
        },
        500: {
          type: 'object',
          properties: {
            statusCode: { type: 'number' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as { userId: string };
      
      const trialInfo = await TrialService.getTrialInfo(userId);
      const isOnTrial = await TrialService.isOnTrial(userId);

      return reply.send({
        hasUsedTrial: !!trialInfo,
        isActive: isOnTrial,
        trial: trialInfo
      });
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get trial status');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to get trial status'
      });
    }
  });
};
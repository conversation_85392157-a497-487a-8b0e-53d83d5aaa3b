import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';

interface ScalewayEmailWebhookPayload {
  event_type: 'blocklist_created' | 'email_blocklisted' | 'email_dropped' | 'email_spam' | 'email_mailbox_not_found';
  email_to: string;
  email_from: string;
  email_subject?: string;
  message_id?: string;
  project_id: string;
  timestamp: string;
  // Additional fields based on event type
  reason?: string;
  bounce_type?: string;
  smtp_code?: number;
  smtp_message?: string;
}

export const scalewayEmailWebhooksRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Scaleway webhook endpoint - handles both verification and events
  // Use promise-based parser to avoid callback-style issues with some plugins
  fastify.addContentTypeParser('text/plain', { parseAs: 'string' }, async (_req, body: string) => {
    return body;
  });

  fastify.post('/email-reports', {
    schema: {
      tags: ['Webhooks', 'Email'],
      summary: 'Scaleway webhook endpoint',
      description: 'Handles verification requests and email events from Scaleway',
      response: {
        200: {
          description: 'Success response'
        },
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const contentType = request.headers['content-type'];

      // If it's plain text (verification code), echo it back
      if (contentType && contentType.includes('text/plain')) {
        const textBody = request.body?.toString().trim();
        
        logger.info({
          verificationCode: textBody
        }, 'Scaleway verification code received');
        
        // Echo back the verification code
        return reply
          .header('Content-Type', 'text/plain')
          .send(textBody);
      }

      // Handle JSON webhook events
      let payload: ScalewayEmailWebhookPayload;
      if (typeof request.body === 'string') {
        try {
          payload = JSON.parse(request.body);
        } catch (e) {
          // If it can't be parsed as JSON and it's not explicitly text/plain,
          // it might be a verification code sent without proper content-type
          logger.debug({
            body: request.body,
            contentType
          }, 'Non-JSON body received, echoing back (possible verification)');
          
          // Echo it back in case it's a verification
          return reply.send(request.body);
        }
      } else if (typeof request.body === 'object' && request.body !== null) {
        payload = request.body as ScalewayEmailWebhookPayload;
      } else {
        logger.debug({ 
          bodyType: typeof request.body
        }, 'Unexpected body type');
        
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request', 
          message: 'Invalid request body'
        });
      }

      const { event_type, email_to, email_from, email_subject, message_id, timestamp, reason, bounce_type, smtp_code, smtp_message } = payload;

      logger.info({
        event_type,
        email_to,
        email_from,
        message_id,
        reason,
        bounce_type,
        smtp_code
      }, `Scaleway email event received: ${event_type}`);

      // Find the user by email address
      const user = await prisma.user.findUnique({
        where: { email: email_to },
        select: { id: true, email: true, emailVerified: true, name: true }
      });

      if (!user) {
        logger.warn({ email_to, event_type }, 'Scaleway email event for unknown user');
        return reply.send({
          success: true,
          message: 'Email address not found in user database'
        });
      }

      // Check if user is already marked as unverified
      if (!user.emailVerified) {
        logger.debug({ email_to, event_type }, 'User already marked as unverified');
        return reply.send({
          success: true,
          message: 'User already marked as unverified'
        });
      }

      // Events that should mark user as unverified
      const unverifyingEvents = ['blocklist_created', 'email_blocklisted', 'email_dropped', 'email_spam', 'email_mailbox_not_found'];
      
      if (unverifyingEvents.includes(event_type)) {
        // Mark user as unverified
        await prisma.user.update({
          where: { id: user.id },
          data: { emailVerified: false }
        });

        // Create audit log entry for GDPR compliance
        await prisma.auditLog.create({
          data: {
            action: 'email.verification.disabled',
            resourceType: 'user',
            resourceId: user.id,
            metadata: {
              reason: `Scaleway email event: ${event_type}`,
              email_to,
              email_from,
              email_subject,
              message_id,
              timestamp,
              bounce_reason: reason,
              bounce_type,
              smtp_code,
              smtp_message,
              previous_verified_status: true,
              new_verified_status: false
            },
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] || 'Scaleway-Webhook',
            expiresAt: new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000) // 7 years retention
          }
        });

        logger.warn({
          userId: user.id,
          userEmail: user.email,
          userName: user.name,
          event_type,
          reason,
          bounce_type,
          smtp_code,
          smtp_message
        }, `User marked as unverified due to ${event_type} event`);

        return reply.send({
          success: true,
          message: `User ${email_to} marked as unverified due to ${event_type}`
        });
      }

      // For other event types, just log them
      logger.info({
        userId: user.id,
        userEmail: user.email,
        event_type,
        message: 'Email event received but no action taken'
      }, `Scaleway email event logged: ${event_type}`);

      return reply.send({
        success: true,
        message: `Email event ${event_type} logged successfully`
      });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack
      }, 'Failed to process Scaleway webhook');

      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  });


  // Health check endpoint for Scaleway webhook setup
  fastify.get('/email-reports/health', {
    schema: {
      tags: ['Webhooks', 'Email'],
      summary: 'Email webhook health check',
      description: 'Health check endpoint for Scaleway email webhook configuration',
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            service: { type: 'string' }
          }
        }
      }
    }
  }, async (_request: FastifyRequest, reply: FastifyReply) => {
    return reply.send({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'scaleway-email-webhooks'
    });
  });
};
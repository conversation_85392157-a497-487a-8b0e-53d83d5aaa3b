import { FastifyRequest, FastifyReply } from 'fastify';
import { paymentWorkflowService } from '../../services/payment/payment-workflow.service.js';
import { mollieService } from '../../services/payment/mollie.service.js';
import { logger } from '../../utils/logger.js';

interface MollieWebhookBody {
  id: string;
}

interface MollieNextGenWebhookBody {
  resource: 'event';
  id: string; // event ID
  type: string; // e.g. 'payment.paid', 'subscription.created'
  entityId: string; // actual payment/subscription ID
  createdAt: string;
  _embedded?: Record<string, any>;
  _links?: Record<string, any>;
}

export class MollieWebhookController {
  /**
   * Unified webhook handler that determines resource type and routes accordingly
   * Supports both classic webhooks and next-gen webhooks
   */
  async handleUnifiedWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body as MollieWebhookBody | MollieNextGenWebhookBody;
      const signature = request.headers['x-mollie-signature'] as string;

      // Verify webhook signature if configured
      if (signature) {
        const rawBody = JSON.stringify(request.body);
        const isValid = mollieService.verifyWebhookSignature(rawBody, signature);

        if (!isValid) {
          logger.warn({
            signature,
            resourceId: body.id
          }, 'Invalid Mollie webhook signature');

          return reply.code(401).send({
            statusCode: 401,
            error: 'Unauthorized',
            message: 'Invalid webhook signature'
          });
        }
      }

      // Determine webhook type and extract resource ID
      let resourceId: string;
      let webhookType: 'classic' | 'nextgen';

      if ('resource' in body && body.resource === 'event') {
        // Next-gen webhook
        webhookType = 'nextgen';
        const nextGenBody = body as MollieNextGenWebhookBody;

        if (!nextGenBody.entityId) {
          return reply.code(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'Missing entityId in next-gen webhook payload'
          });
        }

        resourceId = nextGenBody.entityId;
        logger.info({
          eventId: nextGenBody.id,
          eventType: nextGenBody.type,
          entityId: resourceId
        }, 'Processing next-gen webhook');

      } else {
        // Classic webhook
        webhookType = 'classic';
        const classicBody = body as MollieWebhookBody;

        if (!classicBody.id) {
          return reply.code(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'Missing id in classic webhook payload'
          });
        }

        resourceId = classicBody.id;
        logger.info({ resourceId }, 'Processing classic webhook');
      }

      // Route based on resource ID prefix
      if (resourceId.startsWith('tr_')) {
        // Payment webhook
        logger.info({ paymentId: resourceId, webhookType }, 'Processing payment webhook via unified endpoint');
        await paymentWorkflowService.processPaymentWebhook(resourceId);
      } else if (resourceId.startsWith('sub_')) {
        // Subscription webhook
        logger.info({ subscriptionId: resourceId, webhookType }, 'Processing subscription webhook via unified endpoint');
        await paymentWorkflowService.processSubscriptionWebhook(resourceId);
      } else {
        logger.warn({ resourceId, webhookType }, 'Unknown Mollie resource type in webhook');
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Unknown resource type'
        });
      }

      logger.info({
        resourceId,
        webhookType
      }, 'Mollie webhook processed successfully via unified endpoint');

      return reply.code(200).send({ success: true });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        body: request.body
      }, 'Failed to process Mollie webhook via unified endpoint');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  }

  /**
   * Handle Mollie payment webhooks (legacy endpoint)
   */
  async handlePaymentWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body as MollieWebhookBody;
      const signature = request.headers['x-mollie-signature'] as string;

      // Verify webhook signature if configured
      if (signature) {
        const rawBody = JSON.stringify(request.body);
        const isValid = mollieService.verifyWebhookSignature(rawBody, signature);
        
        if (!isValid) {
          logger.warn({ 
            signature,
            paymentId: body.id 
          }, 'Invalid Mollie webhook signature');
          
          return reply.code(401).send({
            statusCode: 401,
            error: 'Unauthorized',
            message: 'Invalid webhook signature'
          });
        }
      }

      // Validate webhook payload
      if (!body.id) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Missing payment ID in webhook payload'
        });
      }

      // Process the payment webhook
      await paymentWorkflowService.processPaymentWebhook(body.id);

      logger.info({ 
        paymentId: body.id 
      }, 'Mollie payment webhook processed successfully');

      return reply.code(200).send({ success: true });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        body: request.body
      }, 'Failed to process Mollie payment webhook');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  }

  /**
   * Handle Mollie subscription webhooks
   */
  async handleSubscriptionWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = request.body as MollieWebhookBody;
      const signature = request.headers['x-mollie-signature'] as string;

      // Verify webhook signature if configured
      if (signature) {
        const rawBody = JSON.stringify(request.body);
        const isValid = mollieService.verifyWebhookSignature(rawBody, signature);
        
        if (!isValid) {
          logger.warn({ 
            signature,
            subscriptionId: body.id 
          }, 'Invalid Mollie webhook signature');
          
          return reply.code(401).send({
            statusCode: 401,
            error: 'Unauthorized',
            message: 'Invalid webhook signature'
          });
        }
      }

      // Validate webhook payload
      if (!body.id) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Missing subscription ID in webhook payload'
        });
      }

      // Process the subscription webhook
      await paymentWorkflowService.processSubscriptionWebhook(body.id);

      logger.info({ 
        subscriptionId: body.id 
      }, 'Mollie subscription webhook processed successfully');

      return reply.code(200).send({ success: true });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        body: request.body
      }, 'Failed to process Mollie subscription webhook');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to process webhook'
      });
    }
  }
}

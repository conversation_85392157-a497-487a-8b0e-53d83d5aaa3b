import { FastifyRequest, FastifyReply } from 'fastify';
import { UserSettingsService, UserSettingsData } from '../../services/user/user-settings.service.js';
import { logger } from '../../utils/logger.js';

const userSettingsService = new UserSettingsService();

export class UserSettingsController {
  /**
   * Get current user's settings
   */
  async getSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const settings = await userSettingsService.getUserSettings(user.id);
      
      return reply.send({
        success: true,
        settings
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to get user settings');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve settings'
      });
    }
  }

  /**
   * Update user settings
   */
  async updateSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const data = request.body as UserSettingsData;

      // Validate Pro features if needed
      const validation = await userSettingsService.validateProFeatures(user.id, data);
      if (!validation.allowed) {
        return reply.status(402).send({
          statusCode: 402,
          error: 'Payment Required',
          message: validation.reason
        });
      }

      const settings = await userSettingsService.upsertUserSettings(user.id, data);
      
      return reply.send({
        success: true,
        message: 'Settings updated successfully',
        settings
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to update user settings');
      
      // Handle validation errors
      if (error.message.includes('must be') || error.message.includes('requires')) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }
      
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update settings'
      });
    }
  }

  /**
   * Reset user settings to defaults
   */
  async resetSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const result = await userSettingsService.deleteUserSettings(user.id);
      
      return reply.send({
        success: true,
        message: result.message
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to reset user settings');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to reset settings'
      });
    }
  }

  /**
   * Test S3 connection
   */
  async testS3Connection(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { region, bucket, accessKey, secretKey, endpoint } = request.body as any;
      
      // Import S3StorageService
      const { S3StorageService } = await import('../../services/storage/s3-storage.service.js');
      
      // Create S3 service with provided config
      const s3Config = {
        accessKeyId: accessKey,
        secretAccessKey: secretKey,
        region,
        bucket,
        endpoint
      };
      
      const s3Service = new S3StorageService(s3Config);
      
      // Test the connection
      const testResult = await s3Service.testConnection();
      
      if (testResult) {
        return reply.send({
          success: true,
          message: 'S3 connection successful',
          details: {
            canWrite: true,
            canRead: true,
            canDelete: true,
            bucketExists: true
          }
        });
      } else {
        return reply.send({
          success: false,
          message: 'S3 connection failed - check your permissions',
          details: {
            canWrite: false,
            canRead: false,
            canDelete: false,
            bucketExists: false
          }
        });
      }
    } catch (error: any) {
      logger.error({ error: error.message }, 'S3 connection test failed');
      
      // Parse error message for more specific feedback
      let message = 'S3 connection failed';
      if (error.message.includes('NoSuchBucket')) {
        message = 'Bucket does not exist';
      } else if (error.message.includes('InvalidAccessKeyId')) {
        message = 'Invalid access key ID';
      } else if (error.message.includes('SignatureDoesNotMatch')) {
        message = 'Invalid secret access key';
      } else if (error.message.includes('AccessDenied')) {
        message = 'Access denied - check bucket permissions';
      }
      
      return reply.send({
        success: false,
        message,
        details: {
          canWrite: false,
          canRead: false,
          canDelete: false,
          bucketExists: false
        }
      });
    }
  }
}

import { FastifyRequest, FastifyReply } from 'fastify';
import { paymentWorkflowService } from '../../services/payment/payment-workflow.service.js';
import { logger } from '../../utils/logger.js';

export class PaymentController {
  /**
   * Create a one-off payment for manual billing
   */
  async createOneOffPayment(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { planType, interval, amount, currency, description, successUrl, cancelUrl, metadata } = request.body as {
        planType: 'pro' | 'enterprise';
        interval: 'one-time';
        amount: number;
        currency: string;
        description: string;
        successUrl: string;
        cancelUrl: string;
        metadata?: Record<string, any>;
      };

      // Validate input
      if (!planType || !interval || !amount || !currency || !description || !successUrl || !cancelUrl) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Missing required fields: planType, interval, amount, currency, description, successUrl, cancelUrl'
        });
      }

      if (!['pro', 'enterprise'].includes(planType)) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid plan type'
        });
      }

      if (interval !== 'one-time') {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Only one-time interval is supported for manual payments'
        });
      }

      if (currency !== 'EUR') {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Only EUR currency is supported'
        });
      }

      if (amount <= 0) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Amount must be positive'
        });
      }

      // Create one-off payment using the payment workflow service
      const payment = await paymentWorkflowService.createPayment({
        userId: user.id,
        planType: planType as 'pro' | 'enterprise',
        interval: 'one-time',
        amount,
        currency,
        description,
        successUrl,
        cancelUrl,
        metadata: {
          type: 'manual_subscription',
          virtualSubscription: true,
          interval: metadata?.interval || 'monthly', // Make sure interval is available for virtual subscription
          ...metadata
        }
      });

      return reply.send({
        success: true,
        checkoutUrl: payment.checkoutUrl,
        paymentId: payment.paymentId,
        mollieId: payment.mollieId
      });

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        userId: (request as any).user?.id
      }, 'Failed to create one-off payment');

      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to create one-off payment'
      });
    }
  }
}
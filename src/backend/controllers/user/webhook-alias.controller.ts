import { FastifyRequest, FastifyReply } from 'fastify';
import { WebhookAliasService } from '../../services/user/webhook-alias.service.js';
import { logger } from '../../utils/logger.js';

interface CreateWebhookAliasBody {
  domainId: string;
  webhookUrl: string;
  webhookName: string;
  webhookDescription?: string;
  aliasType: 'catchall' | 'specific';
  localPart?: string;
  syncWithDomain?: boolean;
  autoVerify?: boolean;
  firstOrCreate?: boolean;
  updateWebhookData?: boolean;
}

export class WebhookAliasController {
  private webhookAliasService: WebhookAliasService;

  constructor() {
    this.webhookAliasService = new WebhookAliasService();
  }

  async createWebhookAlias(
    request: FastifyRequest<{ Body: CreateWebhookAliasBody }>,
    reply: FastifyReply
  ) {
    try {
      // Get user from auth middleware
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          error: 'Authentication required'
        });
      }

      // Validate request body
      const validation = this.webhookAliasService.validateCreateRequest(request.body);
      if (!validation.valid) {
        return reply.status(400).send({
          success: false,
          error: validation.error,
          details: {
            field: 'body',
            code: 'VALIDATION_ERROR',
            message: validation.error
          }
        });
      }

      // Create webhook and alias
      const result = await this.webhookAliasService.createWebhookAlias({
        ...request.body,
        userId: user.id
      });

      if (!result.success) {
        // Determine appropriate status code based on error
        let statusCode = 500;
        if (result.error?.includes('not found') || result.error?.includes('access denied')) {
          statusCode = 404;
        } else if (result.error?.includes('already exists')) {
          statusCode = 409;
        } else if (result.error?.includes('required') || result.error?.includes('invalid')) {
          statusCode = 400;
        }

        return reply.status(statusCode).send({
          success: false,
          error: result.error
        });
      }

      // Return success response
      return reply.status(201).send(result);

    } catch (error) {
      logger.error({ data: error }, 'Error in createWebhookAlias controller:');
      return reply.status(500).send({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

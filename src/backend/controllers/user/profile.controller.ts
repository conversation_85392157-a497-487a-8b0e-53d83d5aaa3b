import { FastifyRequest, FastifyReply } from 'fastify'
import { prisma } from '../../lib/prisma.js'
import bcrypt from 'bcrypt'
import { logger } from '../../utils/logger.js'

interface UpdateProfileRequest {
  name?: string
}

interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

interface ChangeEmailRequest {
  newEmail: string
  password: string
}

interface UpdateOrganizationRequest {
  name?: string
  registrationNumber?: string
  addressStreet?: string
  addressApartment?: string
  addressZipCode?: string
  addressCity?: string
  addressCountry?: string
}

export class ProfileController {
  /**
   * Get current user profile
   */
  async getProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      
      const userProfile = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          emailVerified: true,
          currentMonthEmails: true,
          password: true, // Include to check if user has password set
          createdAt: true,
          updatedAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              registrationNumber: true,
              addressStreet: true,
              addressApartment: true,
              addressZipCode: true,
              addressCity: true,
              addressCountry: true
            }
          }
        }
      })

      if (!userProfile) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User profile not found'
        })
      }

      // Return profile without password but include hasPassword flag
      const { password, ...profileWithoutPassword } = userProfile

      return reply.send({
        success: true,
        user: {
          ...profileWithoutPassword,
          hasPassword: !!password // Boolean flag indicating if user has password set
        }
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error getting user profile')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve user profile'
      })
    }
  }

  /**
   * Update user profile (name only for now)
   */
  async updateProfile(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { name } = request.body as UpdateProfileRequest

      // Validate input
      if (name !== undefined && (typeof name !== 'string' || name.length > 100)) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Name must be a string with maximum 100 characters'
        })
      }

      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: {
          name: name || null
        },
        select: {
          id: true,
          email: true,
          name: true,
          planType: true,
          emailVerified: true,
          updatedAt: true
        }
      })

      return reply.send({
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error updating user profile')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update user profile'
      })
    }
  }

  /**
   * Change user password
   */
  async changePassword(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { currentPassword, newPassword } = request.body as ChangePasswordRequest

      // Validate input
      if (!newPassword) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'New password is required'
        })
      }

      if (newPassword.length < 6) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'New password must be at least 6 characters long'
        })
      }

      // Get current user with password and email
      const currentUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { 
          password: true,
          email: true 
        }
      })

      if (!currentUser) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        })
      }

      // Handle users without existing password (magic link users)
      if (!currentUser.password) {
        // User doesn't have a password yet - skip current password verification
        logger.info({ userId: user.id }, 'Setting initial password for user without existing password')
        
        // Create a credential account for BetterAuth when setting password for first time
        // This allows the user to login with email/password through BetterAuth
        const existingAccount = await prisma.account.findFirst({
          where: {
            userId: user.id,
            providerId: 'credential'
          }
        })
        
        if (!existingAccount) {
          await prisma.account.create({
            data: {
              userId: user.id,
              providerId: 'credential',
              accountId: currentUser.email, // Use email as accountId for credential provider
              createdAt: new Date(),
              updatedAt: new Date()
            }
          })
          logger.info({ userId: user.id }, 'Created credential account for BetterAuth')
        }
      } else {
        // User has existing password - current password is required
        if (!currentPassword) {
          return reply.status(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'Current password is required to change existing password'
          })
        }
        
        // Verify current password for users who have one
        const passwordMatch = await bcrypt.compare(currentPassword, currentUser.password)
        if (!passwordMatch) {
          return reply.status(400).send({
            statusCode: 400,
            error: 'Bad Request',
            message: 'Current password is incorrect'
          })
        }
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 10)

      // Update password in both User table (for legacy compatibility) and Account table (for BetterAuth)
      await prisma.$transaction([
        // Update User table password (legacy)
        prisma.user.update({
          where: { id: user.id },
          data: { password: hashedNewPassword }
        }),
        // Update or create credential account for BetterAuth with password
        prisma.account.upsert({
          where: {
            providerId_accountId: {
              providerId: 'credential',
              accountId: currentUser.email
            }
          },
          update: {
            password: hashedNewPassword, // Store password in Account table for BetterAuth
            updatedAt: new Date()
          },
          create: {
            userId: user.id,
            providerId: 'credential',
            accountId: currentUser.email,
            password: hashedNewPassword, // Store password in Account table for BetterAuth
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      ])

      return reply.send({
        success: true,
        message: 'Password changed successfully'
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error changing user password')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to change password'
      })
    }
  }

  /**
   * Change user email (requires password confirmation)
   */
  async changeEmail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const { newEmail, password } = request.body as ChangeEmailRequest

      // Validate input
      if (!newEmail || !password) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'New email and password are required'
        })
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(newEmail)) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Invalid email format'
        })
      }

      // Check if email is already taken
      const existingUser = await prisma.user.findUnique({
        where: { email: newEmail }
      })

      if (existingUser && existingUser.id !== user.id) {
        return reply.status(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: 'Email address is already in use'
        })
      }

      // Get current user with password
      const currentUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { password: true, email: true }
      })

      if (!currentUser) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        })
      }

      // Verify password
      const passwordMatch = await bcrypt.compare(password, currentUser.password)
      if (!passwordMatch) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Password is incorrect'
        })
      }

      // Update email
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { 
          email: newEmail,
          emailVerified: false // Reset verification status when email changes
        },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true
        }
      })

      return reply.send({
        success: true,
        message: 'Email address changed successfully',
        user: updatedUser
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error changing user email')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to change email address'
      })
    }
  }

  /**
   * Update organization details for invoicing
   */
  async updateOrganization(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user
      const {
        name,
        registrationNumber,
        addressStreet,
        addressApartment,
        addressZipCode,
        addressCity,
        addressCountry
      } = request.body as UpdateOrganizationRequest

      // Validate field lengths
      if (name && name.length > 200) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Organization name must be maximum 200 characters'
        })
      }

      if (registrationNumber && registrationNumber.length > 100) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Registration number must be maximum 100 characters'
        })
      }

      if (addressStreet && addressStreet.length > 200) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Street address must be maximum 200 characters'
        })
      }

      if (addressApartment && addressApartment.length > 100) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Apartment/suite must be maximum 100 characters'
        })
      }

      if (addressZipCode && addressZipCode.length > 20) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Postal code must be maximum 20 characters'
        })
      }

      if (addressCity && addressCity.length > 100) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'City must be maximum 100 characters'
        })
      }

      if (addressCountry && addressCountry.length > 100) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Country must be maximum 100 characters'
        })
      }

      // Get current user with organization
      const currentUser = await prisma.user.findUnique({
        where: { id: user.id },
        include: { organization: true }
      })

      if (!currentUser) {
        return reply.status(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'User not found'
        })
      }

      let organization
      
      if (currentUser.organization) {
        // Update existing organization
        organization = await prisma.organization.update({
          where: { id: currentUser.organization.id },
          data: {
            name: name || currentUser.organization.name,
            registrationNumber: registrationNumber !== undefined ? registrationNumber : currentUser.organization.registrationNumber,
            addressStreet: addressStreet !== undefined ? addressStreet : currentUser.organization.addressStreet,
            addressApartment: addressApartment !== undefined ? addressApartment : currentUser.organization.addressApartment,
            addressZipCode: addressZipCode !== undefined ? addressZipCode : currentUser.organization.addressZipCode,
            addressCity: addressCity !== undefined ? addressCity : currentUser.organization.addressCity,
            addressCountry: addressCountry !== undefined ? addressCountry : currentUser.organization.addressCountry
          }
        })
      } else {
        // Create new organization and link to user
        organization = await prisma.organization.create({
          data: {
            name: name || `${currentUser.name || currentUser.email}'s Organization`,
            registrationNumber: registrationNumber || null,
            addressStreet: addressStreet || null,
            addressApartment: addressApartment || null,
            addressZipCode: addressZipCode || null,
            addressCity: addressCity || null,
            addressCountry: addressCountry || null
          }
        })

        // Link user to organization
        await prisma.user.update({
          where: { id: user.id },
          data: { organizationId: organization.id }
        })
      }

      // Return updated user with organization
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          updatedAt: true,
          organization: {
            select: {
              id: true,
              name: true,
              registrationNumber: true,
              addressStreet: true,
              addressApartment: true,
              addressZipCode: true,
              addressCity: true,
              addressCountry: true
            }
          }
        }
      })

      return reply.send({
        success: true,
        message: 'Organization details updated successfully',
        user: updatedUser
      })
    } catch (error: any) {
      logger.error({ err: error }, 'Error updating organization details')
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update organization details'
      })
    }
  }
}

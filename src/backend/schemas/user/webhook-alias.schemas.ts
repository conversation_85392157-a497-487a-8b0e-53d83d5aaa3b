export const webhookAliasSchemas = {
  // Request schema for creating webhook + alias in one operation
  CreateWebhookAliasRequest: {
    type: 'object',
    required: ['domainId', 'webhookUrl', 'webhookName', 'aliasType'],
    properties: {
      domainId: { 
        type: 'string', 
        description: 'ID of the domain to create the alias for' 
      },
      webhookUrl: { 
        type: 'string', 
        format: 'uri',
        description: 'URL for the webhook endpoint' 
      },
      webhookName: { 
        type: 'string', 
        minLength: 1,
        maxLength: 100,
        description: 'Name for the webhook' 
      },
      webhookDescription: { 
        type: 'string', 
        maxLength: 500,
        description: 'Optional description for the webhook' 
      },
      aliasType: {
        type: 'string',
        enum: ['catchall', 'specific'],
        description: 'Type of alias to create - catchall (*@domain.com) or specific (<EMAIL>)'
      },
      localPart: {
        type: 'string',
        minLength: 1,
        maxLength: 64,
        description: 'Local part for specific aliases (required when aliasType is "specific")'
      },
      syncWithDomain: {
        type: 'boolean',
        default: true,
        description: 'For catch-all aliases, whether to sync webhook with domain webhook (default: true)'
      },
      autoVerify: {
        type: 'boolean',
        default: false,
        description: 'Whether to auto-verify webhook using last 5 characters method'
      },
      firstOrCreate: {
        type: 'boolean',
        default: false,
        description: 'Enable smart create/update logic - update existing alias if found, create new if not found (default: false)'
      },
      updateWebhookData: {
        type: 'boolean',
        default: true,
        description: 'When updating existing alias, whether to update webhook name/description (default: true)'
      }
    },
    // Conditional validation: localPart is required when aliasType is 'specific'
    if: {
      properties: { aliasType: { const: 'specific' } }
    },
    then: {
      required: ['localPart']
    }
  },

  // Response schema for successful webhook + alias creation
  CreateWebhookAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          verificationToken: { 
            type: 'string', 
            nullable: true,
            description: 'Token for manual verification if auto-verify is false'
          },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      action: {
        type: 'string',
        enum: ['created', 'updated', 'webhook_added', 'cross_domain_update'],
        description: 'Indicates what action was performed on the alias'
      },
      domain: {
        type: 'object',
        nullable: true,
        description: 'Domain information if webhook was synced with domain',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          webhookUpdated: { type: 'boolean' }
        }
      },
      warning: {
        type: 'string',
        nullable: true,
        description: 'Warning message for edge cases (e.g., cross-domain updates)'
      },
      message: { 
        type: 'string',
        description: 'Success message with details about the operation'
      }
    }
  },

  // Error response schema
  WebhookAliasErrorResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean', enum: [false] },
      error: { type: 'string' },
      details: {
        type: 'object',
        nullable: true,
        properties: {
          field: { type: 'string' },
          code: { type: 'string' },
          message: { type: 'string' }
        }
      }
    }
  }
};

// OpenAPI path definition
export const webhookAliasPath = {
  '/api/webhooks/alias': {
    post: {
      tags: ['Webhook-Alias'],
      summary: 'Create webhook and alias in one atomic operation',
      description: `
        Creates a webhook and alias together in a single atomic operation.
        Supports both catch-all (*@domain.com) and specific (<EMAIL>) aliases.
        For catch-all aliases, optionally syncs the webhook with the domain's webhook.
        Provides auto-verification option using the last 5 characters method.
      `,
      security: [{ cookieAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/CreateWebhookAliasRequest' },
            examples: {
              catchall: {
                summary: 'Create catch-all alias with webhook',
                value: {
                  domainId: 'domain-123',
                  webhookUrl: 'https://hooks.n8n.cloud/webhook/abc123',
                  webhookName: 'N8N Catch-All Handler',
                  webhookDescription: 'Handles all emails for domain via n8n',
                  aliasType: 'catchall',
                  syncWithDomain: true,
                  autoVerify: true
                }
              },
              specific: {
                summary: 'Create specific alias with webhook',
                value: {
                  domainId: 'domain-123',
                  webhookUrl: 'https://hooks.n8n.cloud/webhook/def456',
                  webhookName: 'Support Email Handler',
                  webhookDescription: 'Handles support emails via n8n',
                  aliasType: 'specific',
                  localPart: 'support',
                  autoVerify: false
                }
              },
              firstOrCreate: {
                summary: 'Smart create/update with firstOrCreate',
                value: {
                  domainId: 'domain-123',
                  webhookUrl: 'https://hooks.n8n.cloud/webhook/updated456',
                  webhookName: 'Updated Support Handler',
                  webhookDescription: 'Updated support handler via n8n',
                  aliasType: 'specific',
                  localPart: 'support',
                  firstOrCreate: true,
                  updateWebhookData: true,
                  autoVerify: true
                }
              }
            }
          }
        }
      },
      responses: {
        201: {
          description: 'Webhook and alias created successfully',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateWebhookAliasResponse' }
            }
          }
        },
        400: {
          description: 'Invalid request data',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
            }
          }
        },
        401: {
          description: 'Authentication required',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
            }
          }
        },
        404: {
          description: 'Domain not found or not accessible',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
            }
          }
        },
        409: {
          description: 'Alias already exists',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
            }
          }
        },
        500: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
            }
          }
        }
      }
    }
  }
};

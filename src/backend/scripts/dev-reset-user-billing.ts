#!/usr/bin/env tsx

/**
 * Development script to completely reset a user's billing state
 * This is for testing subscription flows in development only
 */

import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';

interface ResetOptions {
  userId?: string;
  email?: string;
  resetMollie?: boolean;
  resetTrials?: boolean;
  resetSubscriptions?: boolean;
  resetPayments?: boolean;
  resetPaymentMethods?: boolean;
  resetGrandfathering?: boolean;
}

export class DevBillingReset {
  /**
   * Complete reset of user's billing state for development testing
   */
  static async resetUserBilling(options: ResetOptions): Promise<void> {
    const {
      userId,
      email,
      resetMollie = true,
      resetTrials = true,
      resetSubscriptions = true,
      resetPayments = true,
      resetPaymentMethods = true,
      resetGrandfathering = true
    } = options;

    if (!userId && !email) {
      throw new Error('Either userId or email must be provided');
    }

    try {
      // Find user
      const user = await prisma.user.findFirst({
        where: userId ? { id: userId } : { email: email },
        select: {
          id: true,
          email: true,
          planType: true,
          mollieCustomerId: true,
          trialStartedAt: true,
          trialEndsAt: true
        }
      });

      if (!user) {
        throw new Error(`User not found: ${userId || email}`);
      }

      logger.info({ userId: user.id, email: user.email }, 'Starting complete billing reset');

      // 1. Reset subscriptions
      if (resetSubscriptions) {
        const deletedSubs = await prisma.subscription.deleteMany({
          where: { userId: user.id }
        });
        logger.info({ count: deletedSubs.count }, 'Deleted subscriptions');
      }

      // 2. Reset payments
      if (resetPayments) {
        const deletedPayments = await prisma.payment.deleteMany({
          where: { userId: user.id }
        });
        logger.info({ count: deletedPayments.count }, 'Deleted payments');
      }

      // 3. Reset payment methods
      if (resetPaymentMethods) {
        const deletedMethods = await prisma.paymentMethod.deleteMany({
          where: { userId: user.id }
        });
        logger.info({ count: deletedMethods.count }, 'Deleted payment methods');
      }

      // 4. Reset user billing fields
      const updateData: any = {
        planType: 'free'
      };

      if (resetMollie) {
        updateData.mollieCustomerId = null;
      }

      if (resetTrials) {
        updateData.trialStartedAt = null;
        updateData.trialEndsAt = null;
      }

      await prisma.user.update({
        where: { id: user.id },
        data: updateData
      });

      logger.info({ 
        userId: user.id, 
        resetFields: Object.keys(updateData) 
      }, 'Reset user billing fields');

      // 5. Clear any cached subscription status (if using Redis/cache)
      // This would be where you'd clear any cached plan status

      logger.info({ userId: user.id }, 'Complete billing reset successful');

    } catch (error: any) {
      logger.error({ error: error.message, userId, email }, 'Failed to reset user billing');
      throw error;
    }
  }

  /**
   * Quick reset for current user (development convenience)
   */
  static async resetCurrentUser(email: string): Promise<void> {
    await this.resetUserBilling({
      email,
      resetMollie: true,
      resetTrials: true,
      resetSubscriptions: true,
      resetPayments: true,
      resetPaymentMethods: true,
      resetGrandfathering: true
    });
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const email = process.argv[2];
  
  if (!email) {
    logger.error('Usage: tsx dev-reset-user-billing.ts <email>');
    process.exit(1);
  }

  DevBillingReset.resetCurrentUser(email)
    .then(() => {
      logger.info(`✅ Successfully reset billing for ${email}`);
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ Failed to reset billing: ${error.message}`);
      process.exit(1);
    });
}

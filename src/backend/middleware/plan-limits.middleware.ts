import { FastifyRequest, FastifyReply } from 'fastify';
import { PlanConfigService } from '../services/billing/plan-config.service.js';
import { NotificationService } from '../services/notifications/notification.service.js';
import { logger } from '../utils/logger.js';
import { prisma } from '../lib/prisma.js';

export interface PlanLimitValidationResult {
  allowed: boolean;
  reason?: string;
  currentUsage?: number;
  limit?: number;
  resourceType?: string;
  upgradeRequired?: boolean;
  notificationCreated?: boolean;
}

/**
 * Middleware to enforce plan limits for resource creation
 */
export class PlanLimitMiddleware {
  
  /**
   * Create middleware for validating specific resource limits
   */
  static validateResourceLimit(resourceType: 'domains' | 'aliases' | 'webhooks') {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user;
      
      if (!user) {
        return reply.code(401).send({
          success: false,
          error: 'Authentication required'
        });
      }

      try {
        const validation = await this.checkResourceLimit(user.id, resourceType, user.planType);
        
        if (!validation.allowed) {
          // Create notification for limit reached
          try {
            await NotificationService.createPlanLimitNotification(
              user.id,
              resourceType,
              validation.currentUsage!,
              validation.limit!
            );
          } catch (notificationError) {
            // Log the notification error but don't fail the request
            logger.error({
              userId: user.id,
              resourceType,
              notificationError: notificationError instanceof Error ? notificationError.message : notificationError
            }, 'Failed to create plan limit notification');
          }
          
          return reply.code(403).send({
            statusCode: 403,
            error: 'Forbidden',
            message: validation.reason,
            success: false,
            details: {
              resourceType,
              currentUsage: validation.currentUsage,
              limit: validation.limit,
              upgradeRequired: validation.upgradeRequired
            },
            upgradeUrl: '/settings#billing'
          });
        }

        // Check if approaching limit (80% or 90%) and create warning notifications
        await this.checkAndCreateWarningNotifications(user.id, resourceType, validation);
        
        // Continue to route handler
      } catch (error) {
        logger.error({ 
          userId: user.id, 
          resourceType, 
          error: error instanceof Error ? error.message : error 
        }, 'Plan limit validation failed');
        
        return reply.code(500).send({
          success: false,
          error: 'Failed to validate plan limits'
        });
      }
    };
  }

  /**
   * Check if user can create a new resource of the specified type
   */
  private static async checkResourceLimit(
    userId: string, 
    resourceType: 'domains' | 'aliases' | 'webhooks',
    planType: string
  ): Promise<PlanLimitValidationResult> {
    // Get current usage
    const currentUsage = await this.getCurrentUsage(userId);
    
    // Get plan limits - account for domain-based limits for Pro plan
    const limits = PlanConfigService.getPlanLimits(planType, currentUsage.domains);
    
    const currentCount = currentUsage[resourceType];
    const limit = limits[resourceType];
    
    // Check if adding one more would exceed the limit
    const wouldExceedLimit = (currentCount + 1) > limit;
    
    return {
      allowed: !wouldExceedLimit,
      reason: wouldExceedLimit ? 
        `Cannot create ${resourceType.slice(0, -1)}. Plan limit of ${limit} ${resourceType} reached.` : 
        undefined,
      currentUsage: currentCount,
      limit,
      resourceType,
      upgradeRequired: wouldExceedLimit
    };
  }

  /**
   * Get current resource usage for a user
   */
  private static async getCurrentUsage(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            domains: true,
            webhooks: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get alias count: include current user's system-domain aliases even if they don't own the system domain
    const userIdSuffix = userId.slice(-8);
    const aliasCount = await prisma.alias.count({
      where: {
        OR: [
          {
            domain: { userId },
            NOT: {
              AND: [
                { domain: { domain: 'user.emailconnect.eu' } },
                { email: { not: { startsWith: `${userIdSuffix}+` } } }
              ]
            }
          },
          {
            domain: { domain: 'user.emailconnect.eu' },
            email: { startsWith: `${userIdSuffix}+` }
          }
        ]
      }
    });

    return {
      domains: user._count.domains,
      webhooks: user._count.webhooks,
      aliases: aliasCount
    };
  }

  /**
   * Create a notification when a limit is reached
   */
  private static async createLimitNotification(
    userId: string, 
    resourceType: string, 
    validation: PlanLimitValidationResult
  ): Promise<void> {
    try {
      // Note: This assumes we have a notifications table
      // We'll create this in the next task
      const notificationData = {
        userId,
        type: 'plan_limit_reached',
        title: `${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} limit reached`,
        message: `You've reached your plan limit of ${validation.limit} ${resourceType}. Upgrade to add more.`,
        category: 'billing',
        priority: 'high',
        data: {
          resourceType,
          currentUsage: validation.currentUsage,
          limit: validation.limit,
          upgradeUrl: '/settings#billing'
        },
        actionUrl: '/settings#billing',
        actionText: 'Upgrade Plan'
      };

      
      logger.info({ 
        userId, 
        resourceType, 
        limit: validation.limit 
      }, 'Plan limit notification created');
      
    } catch (error) {
      logger.error({ 
        userId, 
        resourceType, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to create limit notification');
    }
  }

  /**
   * Check if user is approaching limits and create warning notifications
   */
  private static async checkAndCreateWarningNotifications(
    userId: string,
    resourceType: string,
    validation: PlanLimitValidationResult
  ): Promise<void> {
    if (!validation.currentUsage || !validation.limit) return;

    const usagePercentage = (validation.currentUsage / validation.limit) * 100;
    
    // Create warning at 80% and 90% usage
    if (usagePercentage >= 80 && usagePercentage < 90) {
      await this.createWarningNotification(userId, resourceType, validation, '80%');
    } else if (usagePercentage >= 90) {
      await this.createWarningNotification(userId, resourceType, validation, '90%');
    }
  }

  /**
   * Create a warning notification for approaching limits
   */
  private static async createWarningNotification(
    userId: string,
    resourceType: string,
    validation: PlanLimitValidationResult,
    threshold: string
  ): Promise<void> {
    try {
      // Check if we already sent this warning recently (within 24 hours)
      const recentWarning = await this.hasRecentWarning(userId, resourceType, threshold);
      if (recentWarning) return;

      await NotificationService.createPlanWarningNotification(
        userId,
        resourceType,
        validation.currentUsage!,
        validation.limit!,
        threshold
      );
      
    } catch (error) {
      logger.error({ 
        userId, 
        resourceType, 
        threshold,
        error: error instanceof Error ? error.message : error 
      }, 'Failed to create warning notification');
    }
  }

  /**
   * Check if we've sent a warning notification recently
   */
  private static async hasRecentWarning(
    userId: string, 
    resourceType: string, 
    threshold: string
  ): Promise<boolean> {
    try {
      const recentWarning = await prisma.notification.findFirst({
        where: {
          userId,
          type: 'PLAN_LIMIT_WARNING',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
          },
          data: {
            path: ['resourceType'],
            equals: resourceType
          }
        }
      });
      
      // Additional check for threshold since Prisma JSON filtering is complex
      if (recentWarning && recentWarning.data) {
        const data = recentWarning.data as any;
        return data.threshold === threshold;
      }
      
      return false;
    } catch (error) {
      logger.error({ userId, resourceType, threshold, error }, 'Failed to check recent warnings');
      return false;
    }
  }

  /**
   * Validate email quota before processing
   */
  static async validateEmailQuota(userId: string): Promise<PlanLimitValidationResult> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          creditBatches: {
            where: {
              isExpired: false,
              expiresAt: {
                gt: new Date()
              },
              remainingAmount: {
                gt: 0
              }
            }
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Calculate available emails (monthly allowance + credits) using plan-based limits
      const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
      const monthlyAllowance = planConfig.monthlyEmailLimit;
      const totalCredits = user.creditBatches.reduce((sum, batch) => sum + batch.remainingAmount, 0);
      const monthlyRemaining = Math.max(0, monthlyAllowance - user.currentMonthEmails);
      const totalAvailable = monthlyRemaining + totalCredits;

      const hasQuota = totalAvailable > 0;

      if (!hasQuota) {
        // Create quota exhausted notification
        await NotificationService.createEmailQuotaNotification(userId);
      }

      return {
        allowed: hasQuota,
        reason: hasQuota ? undefined : 'Email quota exhausted. Purchase credits or upgrade your plan.',
        currentUsage: user.currentMonthEmails,
        limit: monthlyAllowance,
        resourceType: 'emails',
        upgradeRequired: !hasQuota
      };
    } catch (error) {
      logger.error({ userId, error }, 'Email quota validation failed');
      throw error;
    }
  }
}

/**
 * Convenience middleware functions for specific resources
 */
export const validateDomainLimit = PlanLimitMiddleware.validateResourceLimit('domains');
export const validateAliasLimit = PlanLimitMiddleware.validateResourceLimit('aliases'); 
export const validateWebhookLimit = PlanLimitMiddleware.validateResourceLimit('webhooks');

import rateLimit from '@fastify/rate-limit';
import { FastifyInstance, FastifyRequest } from 'fastify';
import { auditLogService } from '../services/audit-log.service.js';
import { logger } from '../utils/logger.js';

/**
 * Rate limiting configuration for different endpoint types
 */
export const rateLimitConfigs = {
  // Strict limits for authentication endpoints
  auth: {
    max: 5,
    timeWindow: '15 minutes',
    ban: 3, // Ban after 3 violations
    continueExceeding: false,
    skipSuccessfulRequests: false,
    keyGenerator: (req: FastifyRequest) => {
      return req.ip; // Rate limit by IP for auth
    }
  },

  // Moderate limits for API endpoints
  api: {
    max: 100,
    timeWindow: '1 minute',
    continueExceeding: true,
    skipSuccessfulRequests: false,
    keyGenerator: (req: FastifyRequest) => {
      // Rate limit by user ID if authenticated, otherwise by IP
      const userId = (req as any).user?.id;
      return userId || req.ip;
    }
  },

  // Relaxed limits for webhooks (they need high throughput)
  webhook: {
    max: 1000,
    timeWindow: '1 minute',
    continueExceeding: true,
    skipSuccessfulRequests: true,
    keyGenerator: (req: FastifyRequest) => {
      const userId = (req as any).user?.id;
      return userId || req.ip;
    }
  },

  // Strict limits for payment endpoints
  payment: {
    max: 10,
    timeWindow: '5 minutes',
    ban: 5,
    continueExceeding: false,
    skipSuccessfulRequests: false,
    keyGenerator: (req: FastifyRequest) => {
      const userId = (req as any).user?.id;
      return userId || req.ip;
    }
  },

  // Very strict for password reset and sensitive operations
  sensitive: {
    max: 3,
    timeWindow: '30 minutes',
    ban: 2,
    continueExceeding: false,
    skipSuccessfulRequests: false,
    keyGenerator: (req: FastifyRequest) => {
      return req.ip;
    }
  },

  // Public endpoints (docs, health checks)
  public: {
    max: 30,
    timeWindow: '1 minute',
    continueExceeding: true,
    skipSuccessfulRequests: true,
    keyGenerator: (req: FastifyRequest) => {
      return req.ip;
    }
  }
};

/**
 * Build error response and log when rate limit is exceeded
 */
function buildRateLimitErrorResponse(
  req: FastifyRequest,
  context: any
) {
  // Log the rate limit violation for security monitoring
  auditLogService.logSecurityEvent('rate_limit_exceeded', req, {
    limit: context.max,
    current: context.current,
    timeWindow: context.timeWindow,
    ban: context.ban,
    endpoint: req.url
  }).catch(() => {});

  // Log for monitoring
  logger.warn({
    ip: req.ip,
    userId: (req as any).user?.id,
    endpoint: req.url,
    limit: context.max,
    current: context.current
  }, 'Rate limit exceeded');

  return {
    error: 'Too Many Requests',
    message: 'You have exceeded the rate limit. Please try again later.',
    retryAfter: context.timeWindow,
    limit: context.max
  };
}

/**
 * Register rate limiting on the Fastify instance
 */
export async function registerRateLimiting(fastify: FastifyInstance) {
  // Global rate limiter with generous defaults
  await fastify.register(rateLimit, {
    global: true,
    max: 200,
    timeWindow: '1 minute',
    cache: 10000,
    errorResponseBuilder: buildRateLimitErrorResponse,
    addHeadersOnExceeding: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true
    },
    addHeaders: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true,
      'retry-after': true
    }
  });

  // Apply specific rate limits to different route groups
  fastify.addHook('onRoute', (routeOptions) => {
    const path = routeOptions.path;

    // Authentication endpoints
    if (path.includes('/auth/login') || path.includes('/auth/register')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.auth
      };
    }
    // Password reset and sensitive operations
    else if (path.includes('/auth/reset') || path.includes('/auth/forgot')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.sensitive
      };
    }
    // Payment endpoints
    else if (path.includes('/billing') || path.includes('/payment') || path.includes('/mollie')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.payment
      };
    }
    // Webhook endpoints need high throughput
    else if (path.includes('/webhook') || path.includes('/email/receive')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.webhook
      };
    }
    // Public endpoints
    else if (path.includes('/public') || path.includes('/health')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.public
      };
    }
    // Default API endpoints
    else if (path.startsWith('/api/')) {
      routeOptions.config = {
        ...routeOptions.config,
        rateLimit: rateLimitConfigs.api
      };
    }
  });
}

/**
 * Get rate limit status for a specific key
 */
export async function getRateLimitStatus(
  fastify: FastifyInstance,
  key: string
): Promise<{ remaining: number; limit: number; ttl: number }> {
  const rateLimiter = (fastify as any).rateLimit;
  if (!rateLimiter) {
    throw new Error('Rate limiter not initialized');
  }

  const status = await rateLimiter.getKey(key);
  return {
    remaining: status.remaining,
    limit: status.limit,
    ttl: status.ttl
  };
}

/**
 * Reset rate limit for a specific key (admin function)
 */
export async function resetRateLimit(
  fastify: FastifyInstance,
  key: string
): Promise<void> {
  const rateLimiter = (fastify as any).rateLimit;
  if (!rateLimiter) {
    throw new Error('Rate limiter not initialized');
  }

  await rateLimiter.resetKey(key);
  
  logger.info({ key }, 'Rate limit reset for key');
}

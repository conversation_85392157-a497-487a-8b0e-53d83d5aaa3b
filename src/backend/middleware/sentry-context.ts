import { FastifyRequest, FastifyReply } from 'fastify';
import { Sentry } from '../lib/sentry.js';

/**
 * Middleware to set Sentry user context for authenticated requests
 */
export async function setSentryContext(request: FastifyRequest, reply: FastifyReply) {
  // Set user context if authenticated
  if (request.user) {
    Sentry.setUser({
      id: request.user.id,
      email: request.user.email,
      username: request.user.email,
    });
    
    // Add additional context
    Sentry.setContext('request', {
      url: request.url,
      method: request.method,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
    });
  } else {
    // Clear user context for non-authenticated requests
    Sentry.setUser(null);
  }
}
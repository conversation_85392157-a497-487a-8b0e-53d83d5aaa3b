// Import Fastify and plugins
import Fastify from 'fastify';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import cors from '@fastify/cors';
import cookie from '@fastify/cookie';
import formbody from '@fastify/formbody';
import fastifySwagger from '@fastify/swagger';
import fastifyStatic from '@fastify/static';
import fastifySwaggerUI from '@fastify/swagger-ui';
import compress from '@fastify/compress';

// Initialize Sentry error tracking
import * as Sentry from '@sentry/node';
import { initializeSentry, captureException } from './lib/sentry.js';
import { setSentryContext } from './middleware/sentry-context.js';
initializeSentry();

// Import route plugins
import { coreEmailPlugin } from './plugins/core-email.plugin.js';
import { userManagementPlugin } from './plugins/user-management.plugin.js';
import { adminPlugin } from './plugins/admin.plugin.js';
import { billingPlugin } from './plugins/billing.plugin.js';
import { authPlugin } from './plugins/auth.plugin.js';
import { utilitiesPlugin } from './plugins/utilities.plugin.js';
import { testingPlugin } from './plugins/testing.plugin.js';

// Import services and utilities
import { initializeQueue } from './services/queue.js';
import { enhancedQueueService } from './services/enhanced-queue.service.js';

// Initialize scheduler service to manage all background workers
import { schedulerService } from './services/scheduler.service.js';
import { logger } from './utils/logger.js';

import { redisRateLimiter } from './services/redis-rate-limiter.service.js';
import { connectDatabase, disconnectDatabase, checkDatabaseHealth } from './lib/prisma.js';
import { webSocketService } from './services/websocket.service.js';
import { env } from './config/env.js'; // This already handles dotenv.config()
import { openApiSpecification } from './openapi-spec.js';
import { registerCommonSchemas } from './schemas/common.js';
import { filterPublicRoutes } from './lib/swagger-utils.js';
import { allSchemas } from './schemas/openapi-schemas.js';
import { SitemapService } from './services/sitemap.service.js';
import { registerRateLimiting } from './middleware/rate-limit.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Handlebars helpers removed - pure Vue SPA now

// Initialize Fastify with the shared logger instance
export const fastify = Fastify({
  logger: {
    level: env.NODE_ENV === 'development' ? 'debug' : 'info',
    transport: env.NODE_ENV === 'development' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        ignore: 'pid,hostname',
      },
    } : undefined,
    redact: ['email.from.value', 'email.to.value', 'email.subject'],
  },
  ajv: {
    customOptions: {
      strict: false,
      keywords: ['example'], // allows "example" keyword
    }
  },
  // Body size limit for email attachments - reduced for memory optimization
  bodyLimit: 10 * 1024 * 1024, // 10MB limit for email processing (reduced from 50MB)
});
async function start() {
  try {
    // Connect to database
    await connectDatabase();

    // Register CORS with proper credentials support
    await fastify.register(cors, {
      origin: (origin, callback) => {
        // Allow requests from same origin (SPA) and development
        if (!origin ||
            origin === 'https://emailconnect.eu' ||
            origin === 'http://localhost:3000' ||
            origin.includes('localhost') ||
            // Allow ngrok URLs for development/testing
            (process.env.NODE_ENV === 'development' && origin.includes('ngrok'))) {
          callback(null, true);
        } else {
          // Log the rejected origin for debugging
          fastify.log.warn(`CORS: Rejected origin: ${origin}`);

          // Send to Bugsink as a breadcrumb for tracking
          Sentry.addBreadcrumb({
            category: 'cors',
            message: `Rejected origin: ${origin}`,
            level: 'warning',
            data: {
              origin,
              timestamp: new Date().toISOString()
            }
          });

          // Properly reject without throwing an error
          callback(null, false);
        }
      },
      credentials: true, // Enable credentials (cookies) support
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    });

// Register rate limiting BEFORE other plugins (disabled in tests)
    if (env.NODE_ENV !== 'test') {
      await registerRateLimiting(fastify);
      fastify.log.info('✅ Rate limiting configured');
    } else {
      fastify.log.info('⏭️ Skipping rate limiting in test environment');
    }

    // Register compression plugin with global hook (must be before @fastify/static)
    await fastify.register(compress, {
      global: true,
      encodings: ['gzip', 'deflate', 'br'],
      threshold: 1024 // Only compress responses larger than 1KB
    });

    // Determine if we're in development or production
    const isDev = __dirname.includes('src/backend');
    const rootDir = isDev ? path.join(__dirname, '../..') : path.join(__dirname, '../..');

    // Serve Vue frontend assets with proper MIME types
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'dist/frontend/assets'),
      prefix: '/assets/',
      decorateReply: false
    });

    // Serve Vue SPA chunks (new chunked build files)
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'dist/frontend/chunks'),
      prefix: '/chunks/',
      decorateReply: false
    });

    // Serve Vue SPA files (for sendFile functionality)
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'dist/frontend'),
      prefix: '/spa/',
      decorateReply: true,  // This enables reply.sendFile()
      serve: false  // Don't serve directory, just enable sendFile
    });

    // Serve public files (including custom CSS)
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'public'),
      prefix: '/',
      decorateReply: false
    });

    // Explicit SPA routes to avoid static '/' plugin 404 for app paths like /login
    try {
      const { spaRoutes } = await import('./config/spa-routes.js');
      for (const spaPath of spaRoutes as string[]) {
        if (spaPath === '/') continue; // already handled below
        fastify.get(spaPath, async (_, reply) => {
          reply.type('text/html');
          return reply.sendFile('index.html');
        });
      }
    } catch (err) {
      fastify.log.warn({ err }, 'Failed to register explicit SPA routes');
    }

    // Add all schemas (including admin) to Fastify for $ref resolution
    // This ensures all routes can find their referenced schemas
    for (const [id, schema] of Object.entries(allSchemas)) {
      fastify.addSchema({ ...schema, $id: id });
    }
    fastify.log.info(`Registered ${Object.keys(allSchemas).length} schemas from OpenAPI spec`);

    // Register common schemas
    await registerCommonSchemas(fastify);
    fastify.log.info('Common schemas registered via registerCommonSchemas.');

    // Register form body parser
    await fastify.register(formbody);

    // Register cookie plugin
    await fastify.register(cookie, {
      secret: env.BETTER_AUTH_SECRET, // use BetterAuth secret for cookie signing
      parseOptions: {} // options for parsing cookies
    });

    // Register Swagger in static mode to avoid auto-generation from routes
    await fastify.register(fastifySwagger, {
      mode: 'static',
      specification: {
        document: openApiSpecification,
      },
    });

    // Register Swagger UI
    await fastify.register(fastifySwaggerUI, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        validatorUrl: null, // Disable online validator
      },
      staticCSP: true,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject) => {
        // Apply filtering to show only public routes
        return filterPublicRoutes(swaggerObject);
      }
    });

    // Initialize Redis queue systems
    await initializeQueue(); // Keep original for backward compatibility

    // Initialize enhanced queue system with high availability
    try {
      await enhancedQueueService.initialize();

      // Initialize Redis-based rate limiter
      const { getRedisClient } = await import('./services/queue.js');
      const redisClient = getRedisClient();
      if (redisClient) {
        await redisRateLimiter.initialize(redisClient);
      }

      fastify.log.info('✅ Enhanced queue system initialized with Redis Sentinel support');
    } catch (error) {
      fastify.log.warn({ error: error.message }, '⚠️ Enhanced queue system failed to initialize, using fallback');
    }

    // Initialize WebSocket service
    webSocketService.initialize(fastify);
    fastify.log.info('✅ WebSocket service initialized');

    // Add Sentry context hook for authenticated requests
    fastify.addHook('preHandler', setSentryContext);

    // Add HTTP to HTTPS redirect and subdomain handling for production
    fastify.addHook('onRequest', async (request, reply) => {
      if (env.NODE_ENV === 'production') {
        const host = request.headers.host || 'emailconnect.eu';
        
        // Skip redirect for internal requests from localhost
        // These are internal health checks and email processing that should not be redirected
        const isLocalhost = host === 'localhost:3000' || host === '127.0.0.1:3000' || host.startsWith('127.0.0.1:');
        const isInternalEndpoint = request.url.startsWith('/api/email/process') || 
                                  request.url.startsWith('/health') ||
                                  request.url === '/health';
        
        if (isLocalhost && isInternalEndpoint) {
          return; // Allow internal requests without redirect
        }
        
        // Also skip for Postfix email processing specifically
        if (request.url.startsWith('/api/email/process') && request.headers['x-email-source'] === 'postfix') {
          return; // Allow email processing from Postfix
        }
        
        // Redirect mx1.emailconnect.eu to main domain
        if (host.startsWith('mx1.') || host.startsWith('mx2.')) {
          const url = `https://emailconnect.eu${request.url}`;
          reply.code(301).redirect(url);
          return;
        }
        
        // Check if request is over HTTP (not HTTPS)
        const proto = request.headers['x-forwarded-proto'] || request.protocol;
        if (proto === 'http') {
          const url = `https://${host}${request.url}`;
          reply.code(301).redirect(url);
        }
      }
    });

    // Add security headers hook
    fastify.addHook('onSend', async (request, reply, payload) => {
      // Skip security headers for sitemap.xml and robots.txt
      if (request.url === '/sitemap.xml' || request.url === '/robots.txt') {
        return payload;
      }

      // Core security headers
      reply.header('X-Content-Type-Options', 'nosniff');
      reply.header('X-Frame-Options', 'DENY');
      reply.header('X-XSS-Protection', '0'); // Disabled in modern browsers, CSP is preferred
      reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
      reply.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()');
      
      // Cache control for sensitive pages
      if (request.url.startsWith('/api/') || request.url.startsWith('/dashboard')) {
        reply.header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        reply.header('Pragma', 'no-cache');
        reply.header('Expires', '0');
      }

      // Add HSTS header for HTTPS in production
      if (env.NODE_ENV === 'production') {
        reply.header('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
      }

      // Enhanced CSP header for SOC 2 compliance
      const cspDirectives = [
        "default-src 'self'",
        "script-src 'self' https://plausible.io 'sha256-/6SBPqW+GW+//4nlXX6Y1nR9dWlh0gsQJ6KK71djH6A='",
        "style-src 'self' 'unsafe-inline'", // Required for Vue.js
        "img-src 'self' data: https: blob:",
        "font-src 'self' data:",
        "connect-src 'self' ws: wss: https://plausible.io https://trace.axtg.nl",
        "worker-src 'self' blob:",
        "frame-ancestors 'none'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "upgrade-insecure-requests",
        "block-all-mixed-content"
      ];

      // Add CSP report-uri in production for monitoring violations
      if (env.NODE_ENV === 'production' && env.CSP_REPORT_URI) {
        cspDirectives.push(`report-uri ${env.CSP_REPORT_URI}`);
      }

      reply.header('Content-Security-Policy', cspDirectives.join('; '));

      return payload;
    });

    // Add global scope validation hook (runs before schema validation)
    fastify.addHook('onRequest', async (request, reply) => {
      // Only check API routes
      if (!request.url.startsWith('/api/')) {
        return;
      }

      // Only validate API key requests (not cookie-based)
      const apiKey = request.headers['x-api-key'] as string;
      if (!apiKey) {
        return; // Let normal auth middleware handle this
      }

      try {
        const { ApiKeyService } = await import('./services/auth/api-key.service.js');
        const { ScopeValidatorService } = await import('./services/auth/scope-validator.service.js');
        const apiKeyService = new ApiKeyService();

        const verifyResult = await apiKeyService.verifyApiKey(apiKey);
        if (!verifyResult.success) {
          return reply.status(401).send({ error: 'Unauthorized: Invalid API key' });
        }

        // Validate scopes for this request
        const scopeValidation = ScopeValidatorService.validateRequest(verifyResult.scopes!, request);
        if (!scopeValidation.allowed) {
          return reply.status(403).send({
            error: 'Forbidden: Insufficient permissions',
            message: scopeValidation.reason
          });
        }

        // Attach user info to request for later middleware
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (request as any).user = {
          id: verifyResult.user!.id,
          email: verifyResult.user!.email,
          planType: verifyResult.user!.planType
        };
      } catch (error) {
        request.log.error({ err: error }, 'Global scope validation failed');
        return reply.status(500).send({ error: 'Internal server error' });
      }
    });

    // Register robots.txt route
    fastify.get('/robots.txt', async (request, reply) => {
      reply.type('text/plain');

      // Derive absolute URL from request (respecting proxies)
      const protoHeader = (request.headers['x-forwarded-proto'] as string) || '';
      const hostHeader = (request.headers['x-forwarded-host'] as string) || (request.headers['host'] as string) || '';
      const protocol = protoHeader.split(',')[0]?.trim() || (request.protocol || 'https');
      const host = hostHeader.split(',')[0]?.trim();
      const origin = host ? `${protocol}://${host}` : env.URL;

      return `User-agent: *
Allow: /
Allow: /docs
Allow: /help
Allow: /help/*
Allow: /changelog
Allow: /pricing
Allow: /terms-of-service
Allow: /privacy-policy
Allow: /hidden-risks-email-automation
Allow: /why-businesses-automate-email-first
Allow: /selective-email-access-vs-full-mailbox
Allow: /how-email-automation-works
Allow: /built-for-european-privacy
Disallow: /dashboard
Disallow: /domains
Disallow: /aliases
Disallow: /webhooks  
Disallow: /logs
Disallow: /settings
Disallow: /api/
Disallow: /auth/
Disallow: /assets/
Disallow: /chunks/
Disallow: /404
Disallow: /admin

# Crawl delay to be respectful
Crawl-delay: 1

Sitemap: ${origin}/sitemap.xml`;
    });

    // Register route plugins
    // Core email processing (email ingestion, webhooks, domains, attachments)
    await fastify.register(coreEmailPlugin);

    // User management (dashboard, webhooks, aliases, settings, profile)
    await fastify.register(userManagementPlugin);

    // Admin functionality (user management, grandfathering, trials, audit logs)
    await fastify.register(adminPlugin);

    // Billing and payments (subscriptions, invoices, payments, Mollie webhooks)
    await fastify.register(billingPlugin);

    // Authentication (BetterAuth, account linking)
    await fastify.register(authPlugin);

    // Utilities (templates, logs, API keys, notifications, public routes, CSP)
    await fastify.register(utilitiesPlugin);

    // Testing and development (webhook testing, OAuth, error testing)
    await fastify.register(testingPlugin);

    // Enhanced health check endpoint
    fastify.get('/health', async () => {
      const dbHealthy = await checkDatabaseHealth();
      const schedulerHealthy = await schedulerService.isHealthy();
      const heartbeatStatus = schedulerService.getHeartbeatStatus();
      const allHealthy = dbHealthy && schedulerHealthy;
      
      return {
        status: allHealthy ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        database: dbHealthy ? 'connected' : 'disconnected',
        scheduler: schedulerHealthy ? 'healthy' : 'unhealthy',
        heartbeat: heartbeatStatus
      };
    });

    // Scheduler health and monitoring endpoint for Uptime Kuma
    fastify.get('/health/scheduler', async (_, reply) => {
      try {
        const isHealthy = await schedulerService.isHealthy();
        const jobStatuses = await schedulerService.getJobStatuses();
        
        if (!isHealthy) {
          reply.status(503); // Service Unavailable
        }
        
        return {
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          ...jobStatuses
        };
      } catch (error) {
        reply.status(503);
        return {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          error: error.message
        };
      }
    });

    // Manual heartbeat trigger endpoint
    fastify.post('/health/heartbeat', async () => {
      try {
        await schedulerService.triggerHeartbeat();
        return {
          status: 'sent',
          timestamp: new Date().toISOString(),
          message: 'Heartbeat sent successfully'
        };
      } catch (error) {
        return {
          status: 'failed',
          timestamp: new Date().toISOString(),
          error: error.message
        };
      }
    });

    // Comprehensive mail pipeline health check endpoint (duplicate from public routes for direct access)
    fastify.get('/health/mail', async (_, reply) => {
      const checks = {
        database: false,
        redis: false,
        postfix: false
      };

      try {
        // Check main database
        checks.database = await checkDatabaseHealth();

        // Check Redis (queue system)
        const { getRedisClient } = await import('./services/queue.js');
        const redisClient = getRedisClient();
        if (redisClient) {
          try {
            const pingResult = await redisClient.ping();
            checks.redis = pingResult === 'PONG';
          } catch {
            checks.redis = false;
          }
        }

        // Check Postfix tables via PostfixSyncService
        try {
          const { PostfixSyncService } = await import('./services/postfix-sync.service.js');
          const postfixSync = new PostfixSyncService();
          const postfixHealth = await postfixSync.healthCheck();
          checks.postfix = postfixHealth.healthy;
        } catch {
          checks.postfix = false;
        }


        const allHealthy = Object.values(checks).every(Boolean);
        const healthyCount = Object.values(checks).filter(Boolean).length;
        const totalCount = Object.keys(checks).length;

        // Return appropriate HTTP status code for Upptime
        if (!allHealthy) {
          reply.status(503); // Service Unavailable
        }

        return {
          status: allHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          checks,
          summary: `${healthyCount}/${totalCount} services healthy`,
          details: {
            database: checks.database ? 'Connected' : 'Disconnected',
            redis: checks.redis ? 'Connected' : 'Disconnected',
            postfix: checks.postfix ? 'Tables accessible' : 'Tables inaccessible'
          }
        };

      } catch (error: unknown) {
        reply.status(500);
        return {
          status: 'error',
          timestamp: new Date().toISOString(),
          checks,
          error: error instanceof Error ? error.message : 'Unknown error',
          summary: 'Health check failed'
        };
      }
    });

    // Sitemap.xml endpoint for SEO with in-memory caching and graceful fallback
    type SitemapCache = { xml: string; generatedAt: number } | null;
    let sitemapCache: SitemapCache = null;
    const SITEMAP_TTL_MS = 15 * 60 * 1000; // 15 minutes

    fastify.get('/sitemap.xml', async (_, reply) => {
      const setCommonHeaders = () => {
        reply
          .type('application/xml; charset=utf-8')
          .header('Cache-Control', 'public, max-age=3600') // Cache for 1 hour
          .header('Last-Modified', new Date().toUTCString());
      };

      const minimalSitemap = () => {
        const today = new Date().toISOString().split('T')[0];
        const base = 'https://emailconnect.eu';
        return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${base}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;
      };

      try {
        const now = Date.now();
        if (sitemapCache && (now - sitemapCache.generatedAt) < SITEMAP_TTL_MS) {
          setCommonHeaders();
          return sitemapCache.xml;
        }

        const sitemapService = new SitemapService();
        const sitemap = await sitemapService.generateFullSitemap();

        // Cache the fresh sitemap
        sitemapCache = { xml: sitemap, generatedAt: now };

        setCommonHeaders();
        return sitemap;
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        fastify.log.error({ error: errorMessage }, 'Failed to generate sitemap');

        // Serve last known good sitemap if available, otherwise a minimal valid sitemap
        setCommonHeaders();
        if (sitemapCache) {
          return sitemapCache.xml;
        }

        return minimalSitemap();
      }
    });

    // Serve Vue SPA for all non-API routes (must be registered last)
    fastify.get('/', async (_, reply) => {
      return reply.sendFile('index.html');
    });


    // Set up error handler for Sentry
    fastify.setErrorHandler((error, request, reply) => {
      // Log the error locally
      fastify.log.error(error);

      // Capture exception in Sentry/Bugsink
      captureException(error, {
        url: request.url,
        method: request.method,
        headers: request.headers,
        query: request.query,
        params: request.params,
        userId: request.user?.id,
        userEmail: request.user?.email,
        requestId: request.id,
      });

      // Send appropriate error response
      const statusCode = error.statusCode || 500;
      reply.status(statusCode).send({
        statusCode,
        error: statusCode >= 500 ? 'Internal Server Error' : error.name || 'Error',
        message: statusCode >= 500 ? 'An unexpected error occurred' : error.message,
      });
    });

    // Catch-all route for Vue Router (SPA) - MUST BE LAST
    fastify.setNotFoundHandler(async (request, reply) => {
      const { isSpaRoute } = await import('./config/spa-routes.js');

      // Inspect the request URL to determine how to handle the 404
      const requestUrl = request.url;

      // Check if the URL matches any of the SPA route patterns
      if (isSpaRoute(requestUrl)) {
        // Check if this is a catch-all dynamic route that might be a 404
        // Random strings like 'mhiyj', '404-539-4205' should return 404
        const pathSegments = requestUrl.split('/').filter(Boolean);
        
        // If it's a single-segment path that looks like gibberish or contains '404', return 404
        if (pathSegments.length === 1) {
          const segment = pathSegments[0];
          // Check for patterns that indicate this is not a valid page
          const isLikelyInvalid = 
            segment.includes('404') || // Contains '404'
            /^[a-z]{5,}$/.test(segment) || // Random lowercase letters
            /^\d{3}-\d{3}-\d{4}$/.test(segment) || // Phone number pattern
            segment.length > 30; // Unusually long slug
          
          if (isLikelyInvalid) {
            return reply.status(404).send({
              statusCode: 404,
              error: 'Not Found',
              message: 'Page not found'
            });
          }
        }
        
        // Serve the Vue SPA for valid SPA routes
        // Set HTML content type to ensure proper rendering
        reply.type('text/html');
        return reply.sendFile('index.html');
      }

      // For all other paths, return a proper 404 JSON response
      // This prevents search engines from indexing unknown/invalid paths
      // and provides a clear API response for non-existent endpoints
      return reply.status(404).send({
        statusCode: 404,
        error: 'Not Found',
        message: 'Route not found'
      });
    });

    // Start server
    const address = await fastify.listen({
      port: env.PORT,
      host: env.HOST,
    });

    fastify.log.info(`🚀 EU Email Webhook Service running at ${address}`);

    // Start centralized scheduler service to manage all background workers
    await schedulerService.start();
    fastify.log.info('✅ All scheduled background workers started via Bull queues');

  } catch (error) {
    fastify.log.error(error);
    captureException(error as Error, { context: 'Server startup failed' });
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error({ data: error }, 'Uncaught Exception:');
  captureException(error, { context: 'Uncaught exception' });
  // Give Sentry time to send the error before exiting
  setTimeout(() => {
    process.exit(1);
  }, 2000);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error({ reason, promise }, 'Unhandled Promise Rejection');
  captureException(new Error('Unhandled Promise Rejection'), {
    context: 'Unhandled rejection',
    reason: reason
  });
});

// Graceful shutdown with database cleanup and worker stop
process.on('SIGTERM', async () => {
  fastify.log.info('Received SIGTERM, shutting down gracefully');
  await schedulerService.stop();
  await fastify.close();
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGINT', async () => {
  fastify.log.info('Received SIGINT, shutting down gracefully');
  await schedulerService.stop();
  await fastify.close();
  await disconnectDatabase();
  process.exit(0);
});

start();
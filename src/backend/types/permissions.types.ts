/**
 * Unified Permission System Types
 * Defines the types for the new unified permission validation system
 */

import { PlanPermission, PlanType } from '../services/billing/plan-config.service.js';

// Core unified permission types

export interface ResourceLimitCheck {
  resourceType: 'domains' | 'aliases' | 'webhooks' | 'emails';
  operation: 'create' | 'read' | 'update' | 'delete';
  quantity?: number; // For bulk operations
}

export interface UnifiedPermissionResult {
  allowed: boolean;
  reason?: string;
  details: {
    // API Scope validation
    hasApiScope: boolean;
    scopeReason?: string;
    
    // Plan permission validation
    hasPlanPermission: boolean;
    planPermissionReason?: string;
    
    // Resource limit validation
    withinLimits: boolean;
    limitReason?: string;
    
    // Current state
    currentPlan: PlanType;
    currentUsage?: ResourceUsage;
    limits?: ResourceLimits;
    
    // Upgrade information
    upgradeRequired?: boolean;
    requiredPlan?: PlanType;
    upgradeUrl?: string;
  };
}

export interface ResourceUsage {
  domains: number;
  aliases: number;
  webhooks: number;
  emails: number;
  monthlyEmails: number;
}

export interface ResourceLimits {
  domains: number;
  aliases: number;
  webhooks: number;
  emails: number;
  monthlyEmails: number;
}

// Scope to Plan Permission mapping
export interface ScopePlanMapping {
  scope: string;
  requiredPlanPermissions: PlanPermission[];
  minimumPlan?: PlanType;
  description: string;
}

// Feature-based permission check
export interface FeaturePermissionRequest {
  userId: string;
  apiKey?: string;
  featureName: string;
}

export interface FeatureConfig {
  requiredScope?: string; // Optional - only required for API key authentication
  requiredPlanPermissions: PlanPermission[];
  resourceLimits?: ResourceLimitCheck;
  minimumPlan?: PlanType;
  description: string;
}

// Effective permissions (what user can actually do)
export interface EffectivePermissions {
  scopes: string[];
  planPermissions: PlanPermission[];
  planType: PlanType;
  resourceLimits: ResourceLimits;
  currentUsage: ResourceUsage;
  availableFeatures: string[];
  restrictions: string[];
}

// Enhanced error responses
export interface PermissionError {
  type: 'scope_missing' | 'plan_permission_missing' | 'limit_exceeded' | 'feature_not_available';
  message: string;
  requiredScope?: string;
  requiredPlanPermission?: PlanPermission;
  requiredPlan?: PlanType;
  currentUsage?: number;
  limit?: number;
  upgradeUrl?: string;
}

// Permission matrix definition
export interface PermissionMatrix {
  [key: string]: {
    scope: string;
    planPermissions: PlanPermission[];
    minimumPlan?: PlanType;
    resourceType?: 'domains' | 'aliases' | 'webhooks' | 'emails';
  };
}

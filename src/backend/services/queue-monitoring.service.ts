import Bull from 'bull';
import { logger } from '../utils/logger.js';
import { captureQueueError } from '../lib/sentry-helpers.js';

export interface QueueMetrics {
  name: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
  health: 'healthy' | 'warning' | 'critical';
  lastProcessedAt?: Date;
  avgProcessingTime?: number;
}

export interface QueueAlert {
  type: 'high_queue_depth' | 'high_failure_rate' | 'stalled_jobs' | 'redis_connection_error';
  severity: 'warning' | 'critical';
  message: string;
  queueName?: string;
  metrics?: any;
  timestamp: Date;
}

/**
 * Queue monitoring service for health checks and alerting
 */
export class QueueMonitoringService {
  private queues: Map<string, Bull.Queue> = new Map();
  private metrics: Map<string, QueueMetrics> = new Map();
  private alerts: QueueAlert[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private initialized = false;

  // Thresholds for alerting
  private readonly thresholds = {
    highQueueDepth: 1000,
    highFailureRate: 0.1, // 10%
    stalledJobTimeout: 300000, // 5 minutes
    avgProcessingTimeWarning: 30000, // 30 seconds
  };

  async initialize(queues: Bull.Queue[]) {
    if (this.initialized) {
      logger.warn('Queue monitoring service already initialized');
      return;
    }

    // Register queues for monitoring
    for (const queue of queues) {
      this.registerQueue(queue);
    }

    // Start monitoring loop
    this.startMonitoring();
    
    this.initialized = true;
    logger.info({ 
      queueCount: this.queues.size 
    }, 'Queue monitoring service initialized');
  }

  registerQueue(queue: Bull.Queue) {
    const queueName = queue.name;
    this.queues.set(queueName, queue);

    // Set up queue event listeners
    queue.on('completed', (job) => {
      this.updateMetrics(queueName, 'completed', job);
    });

    queue.on('failed', (job, err) => {
      this.updateMetrics(queueName, 'failed', job);
      this.handleJobFailure(queueName, job, err);
    });

    queue.on('stalled', (job) => {
      this.handleStalledJob(queueName, job);
    });

    queue.on('error', (error) => {
      this.handleQueueError(queueName, error);
    });

    logger.info({ queueName }, 'Queue registered for monitoring');
  }

  private startMonitoring() {
    // Monitor every 30 seconds
    this.monitoringInterval = setInterval(async () => {
      await this.collectMetrics();
      await this.checkAlerts();
    }, 30000);

    logger.info('Queue monitoring started');
  }

  private async collectMetrics() {
    for (const [queueName, queue] of this.queues) {
      try {
        const counts = await queue.getJobCounts();
        const jobs = await queue.getJobs(['completed', 'failed'], 0, 99);
        
        // Calculate average processing time from recent jobs
        let avgProcessingTime = 0;
        let processedJobs = 0;
        let lastProcessedAt: Date | undefined;

        for (const job of jobs) {
          if (job.finishedOn && job.processedOn) {
            avgProcessingTime += (job.finishedOn - job.processedOn);
            processedJobs++;
            
            if (!lastProcessedAt || job.finishedOn > lastProcessedAt.getTime()) {
              lastProcessedAt = new Date(job.finishedOn);
            }
          }
        }

        if (processedJobs > 0) {
          avgProcessingTime = avgProcessingTime / processedJobs;
        }

        // Determine health status
        let health: 'healthy' | 'warning' | 'critical' = 'healthy';
        
        const totalJobs = counts.waiting + counts.active + counts.delayed;
        const failureRate = counts.failed / Math.max(counts.completed + counts.failed, 1);

        if (totalJobs > this.thresholds.highQueueDepth || 
            failureRate > this.thresholds.highFailureRate ||
            avgProcessingTime > this.thresholds.avgProcessingTimeWarning) {
          health = 'warning';
        }

        if (totalJobs > this.thresholds.highQueueDepth * 2 || 
            failureRate > this.thresholds.highFailureRate * 2) {
          health = 'critical';
        }

        const metrics: QueueMetrics = {
          name: queueName,
          waiting: counts.waiting,
          active: counts.active,
          completed: counts.completed,
          failed: counts.failed,
          delayed: counts.delayed,
          paused: await queue.isPaused(),
          health,
          lastProcessedAt,
          avgProcessingTime: avgProcessingTime || undefined,
        };

        this.metrics.set(queueName, metrics);

      } catch (error) {
        logger.error({ 
          queueName, 
          error: error.message 
        }, 'Failed to collect queue metrics');
        
        this.handleQueueError(queueName, error);
      }
    }
  }

  private async checkAlerts() {
    for (const [queueName, metrics] of this.metrics) {
      // Check for high queue depth
      const totalJobs = metrics.waiting + metrics.active + metrics.delayed;
      if (totalJobs > this.thresholds.highQueueDepth) {
        this.createAlert({
          type: 'high_queue_depth',
          severity: totalJobs > this.thresholds.highQueueDepth * 2 ? 'critical' : 'warning',
          message: `Queue ${queueName} has ${totalJobs} pending jobs`,
          queueName,
          metrics: { totalJobs, waiting: metrics.waiting, active: metrics.active },
          timestamp: new Date(),
        });
      }

      // Check for high failure rate
      const totalProcessed = metrics.completed + metrics.failed;
      if (totalProcessed > 10) { // Only check if we have enough data
        const failureRate = metrics.failed / totalProcessed;
        if (failureRate > this.thresholds.highFailureRate) {
          this.createAlert({
            type: 'high_failure_rate',
            severity: failureRate > this.thresholds.highFailureRate * 2 ? 'critical' : 'warning',
            message: `Queue ${queueName} has high failure rate: ${(failureRate * 100).toFixed(1)}%`,
            queueName,
            metrics: { failureRate, failed: metrics.failed, completed: metrics.completed },
            timestamp: new Date(),
          });
        }
      }

      // Check for stalled processing
      if (metrics.lastProcessedAt) {
        const timeSinceLastProcessed = Date.now() - metrics.lastProcessedAt.getTime();
        if (timeSinceLastProcessed > this.thresholds.stalledJobTimeout && 
            (metrics.waiting > 0 || metrics.active > 0)) {
          this.createAlert({
            type: 'stalled_jobs',
            severity: 'warning',
            message: `Queue ${queueName} hasn't processed jobs for ${Math.round(timeSinceLastProcessed / 1000)}s`,
            queueName,
            metrics: { timeSinceLastProcessed, waiting: metrics.waiting, active: metrics.active },
            timestamp: new Date(),
          });
        }
      }
    }

    // Clean up old alerts (keep last 100)
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }

  private createAlert(alert: QueueAlert) {
    // Avoid duplicate alerts within 5 minutes
    const recentAlerts = this.alerts.filter(a => 
      a.type === alert.type && 
      a.queueName === alert.queueName &&
      Date.now() - a.timestamp.getTime() < 300000
    );

    if (recentAlerts.length === 0) {
      this.alerts.push(alert);
      
      logger.warn({
        type: alert.type,
        severity: alert.severity,
        queueName: alert.queueName,
        message: alert.message,
        metrics: alert.metrics,
      }, 'Queue alert triggered');

      // Send to error tracking
      captureQueueError(new Error(alert.message), {
        feature: alert.type,
        queueName: alert.queueName || 'unknown',
        severity: alert.severity,
        metrics: alert.metrics,
      });
    }
  }

  private updateMetrics(queueName: string, event: 'completed' | 'failed', job: Bull.Job) {
    // This is called from queue events to update real-time metrics
    // The main metrics collection happens in collectMetrics()
    logger.debug({ 
      queueName, 
      event, 
      jobId: job.id,
      processingTime: job.finishedOn && job.processedOn ? job.finishedOn - job.processedOn : undefined
    }, 'Queue job event');
  }

  private handleJobFailure(queueName: string, job: Bull.Job, error: Error) {
    logger.error({
      queueName,
      jobId: job.id,
      attempts: job.attemptsMade,
      maxAttempts: job.opts.attempts,
      error: error.message,
      jobData: job.data,
    }, 'Queue job failed');

    // If this is the final failure, it might go to dead letter queue
    if (job.attemptsMade >= (job.opts.attempts || 1)) {
      logger.warn({
        queueName,
        jobId: job.id,
      }, 'Job permanently failed - should be sent to dead letter queue');
    }
  }

  private handleStalledJob(queueName: string, job: Bull.Job) {
    this.createAlert({
      type: 'stalled_jobs',
      severity: 'warning',
      message: `Job ${job.id} in queue ${queueName} has stalled`,
      queueName,
      metrics: { jobId: job.id, stalledAt: new Date() },
      timestamp: new Date(),
    });
  }

  private handleQueueError(queueName: string, error: Error) {
    this.createAlert({
      type: 'redis_connection_error',
      severity: 'critical',
      message: `Queue ${queueName} error: ${error.message}`,
      queueName,
      timestamp: new Date(),
    });
  }

  // Public API methods
  getMetrics(): QueueMetrics[] {
    return Array.from(this.metrics.values());
  }

  getQueueMetrics(queueName: string): QueueMetrics | undefined {
    return this.metrics.get(queueName);
  }

  getAlerts(severity?: 'warning' | 'critical'): QueueAlert[] {
    if (severity) {
      return this.alerts.filter(alert => alert.severity === severity);
    }
    return [...this.alerts];
  }

  getRecentAlerts(minutes = 60): QueueAlert[] {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp.getTime() > cutoff);
  }

  async getDetailedQueueStatus(queueName: string): Promise<any> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const [counts, waiting, active, failed, completed] = await Promise.all([
      queue.getJobCounts(),
      queue.getJobs(['waiting'], 0, 9),
      queue.getJobs(['active'], 0, 9),
      queue.getJobs(['failed'], 0, 9),
      queue.getJobs(['completed'], 0, 9),
    ]);

    return {
      name: queueName,
      counts,
      recentJobs: {
        waiting: waiting.map(job => ({ id: job.id, data: job.data, createdAt: job.timestamp })),
        active: active.map(job => ({ id: job.id, data: job.data, processedOn: job.processedOn })),
        failed: failed.map(job => ({ id: job.id, failedReason: job.failedReason, finishedOn: job.finishedOn })),
        completed: completed.map(job => ({ id: job.id, finishedOn: job.finishedOn, returnvalue: job.returnvalue })),
      },
      metrics: this.metrics.get(queueName),
    };
  }

  async shutdown() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.initialized = false;
    logger.info('Queue monitoring service shut down');
  }
}

// Export singleton instance
export const queueMonitoringService = new QueueMonitoringService();

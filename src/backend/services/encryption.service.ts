import crypto from 'crypto';
import { logger } from '../utils/logger.js';
import { env } from '../config/env.js';

/**
 * Service for encrypting/decrypting sensitive data
 * Uses AES-256-GCM for authenticated encryption
 */
export class EncryptionService {
  private algorithm = 'aes-256-gcm';
  private keyDerivationSalt: Buffer;
  private encryptionKey: Buffer;

  constructor() {
    // Derive encryption key from environment variable
const masterKey = env.ENCRYPTION_MASTER_KEY;
    
    if (!masterKey || masterKey.length < 32) {
      throw new Error('ENCRYPTION_MASTER_KEY must be at least 32 characters');
    }

    // Use a fixed salt for key derivation (store this securely)
    this.keyDerivationSalt = Buffer.from('emailconnect-encryption-v1', 'utf8');
    
    // Derive a 256-bit key using PBKDF2
    this.encryptionKey = crypto.pbkdf2Sync(
      masterKey,
      this.keyDerivationSalt,
      100000, // iterations
      32, // key length in bytes
      'sha256'
    );
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(plaintext: string): { encrypted: string; iv: string; authTag: string } {
    try {
      // Generate random initialization vector
      const iv = crypto.randomBytes(16);
      
      // Create cipher
      const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
      
      // Encrypt the plaintext
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get the authentication tag
      const authTag = (cipher as any).getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error: any) {
      logger.error({ error: error.message }, 'Encryption failed');
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData: { encrypted: string; iv: string; authTag: string }): string {
    try {
      const { encrypted, iv, authTag } = encryptedData;
      
      // Create decipher
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        this.encryptionKey,
        Buffer.from(iv, 'hex')
      );
      
      // Set the authentication tag
      (decipher as any).setAuthTag(Buffer.from(authTag, 'hex'));
      
      // Decrypt
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error: any) {
      logger.error({ error: error.message }, 'Decryption failed');
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Encrypt S3 configuration
   */
  encryptS3Config(s3Config: any): any {
    if (!s3Config) return null;
    
    const encrypted = { ...s3Config };
    
    // Encrypt sensitive fields
    if (s3Config.accessKey) {
      const encryptedAccessKey = this.encrypt(s3Config.accessKey);
      encrypted.accessKey = encryptedAccessKey.encrypted;
      encrypted.accessKeyIv = encryptedAccessKey.iv;
      encrypted.accessKeyTag = encryptedAccessKey.authTag;
    }
    
    if (s3Config.secretKey) {
      const encryptedSecretKey = this.encrypt(s3Config.secretKey);
      encrypted.secretKey = encryptedSecretKey.encrypted;
      encrypted.secretKeyIv = encryptedSecretKey.iv;
      encrypted.secretKeyTag = encryptedSecretKey.authTag;
    }
    
    // Mark as encrypted
    encrypted._encrypted = true;
    encrypted._encryptionVersion = 1;
    
    return encrypted;
  }

  /**
   * Decrypt S3 configuration
   */
  decryptS3Config(encryptedConfig: any): any {
    if (!encryptedConfig || !encryptedConfig._encrypted) {
      return encryptedConfig;
    }
    
    const decrypted = { ...encryptedConfig };
    
    // Decrypt access key
    if (encryptedConfig.accessKey && encryptedConfig.accessKeyIv && encryptedConfig.accessKeyTag) {
      decrypted.accessKey = this.decrypt({
        encrypted: encryptedConfig.accessKey,
        iv: encryptedConfig.accessKeyIv,
        authTag: encryptedConfig.accessKeyTag
      });
      delete decrypted.accessKeyIv;
      delete decrypted.accessKeyTag;
    }
    
    // Decrypt secret key
    if (encryptedConfig.secretKey && encryptedConfig.secretKeyIv && encryptedConfig.secretKeyTag) {
      decrypted.secretKey = this.decrypt({
        encrypted: encryptedConfig.secretKey,
        iv: encryptedConfig.secretKeyIv,
        authTag: encryptedConfig.secretKeyTag
      });
      delete decrypted.secretKeyIv;
      delete decrypted.secretKeyTag;
    }
    
    // Remove encryption metadata
    delete decrypted._encrypted;
    delete decrypted._encryptionVersion;
    
    return decrypted;
  }

  /**
   * Hash API keys for storage (one-way)
   */
  hashApiKey(apiKey: string): string {
    return crypto
      .createHash('sha256')
      .update(apiKey)
      .digest('hex');
  }

  /**
   * Generate secure random tokens
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}

// Export singleton instance
export const encryptionService = new EncryptionService();

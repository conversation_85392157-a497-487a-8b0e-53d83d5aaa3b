import crypto from 'crypto';
import { env } from '../../config/env.js';
import { getRedisClient } from '../queue.js';

interface SignedPayloadBase {
  exp: number; // unix seconds
  purpose: string;
  jti?: string; // nonce for replay prevention
}

const ALGO = 'sha256';

export function createSignedToken<T extends Record<string, any>>(
  payload: T,
  options: { purpose: string; ttlSeconds: number; useReplayProtection?: boolean }
): string {
  const secret = env.SIGNED_LINK_SECRET || env.BETTER_AUTH_SECRET;
  const exp = Math.floor(Date.now() / 1000) + Math.max(1, options.ttlSeconds);
  const fullPayload: SignedPayloadBase & T = {
    ...payload,
    purpose: options.purpose,
    exp,
  } as any;

  if (options.useReplayProtection) {
    fullPayload.jti = crypto.randomUUID();
    try {
      const redis = getRedisClient();
      if (redis) {
        // store nonce with TTL
        void redis.set(`signedlink:jti:${fullPayload.jti}`, '1', 'EX', options.ttlSeconds);
      }
    } catch {}
  }

  const body = Buffer.from(JSON.stringify(fullPayload)).toString('base64url');
  const sig = crypto.createHmac(ALGO, secret).update(body).digest('base64url');
  return `${body}.${sig}`;
}

export async function verifySignedToken<T = any>(
  token: string,
  expectedPurpose: string,
  options?: { useReplayProtection?: boolean }
): Promise<(SignedPayloadBase & T) | null> {
  try {
    const secret = env.SIGNED_LINK_SECRET || env.BETTER_AUTH_SECRET;
    const [body, sig] = token.split('.');
    if (!body || !sig) return null;
    const expectedSig = crypto.createHmac(ALGO, secret).update(body).digest('base64url');
    if (!crypto.timingSafeEqual(Buffer.from(sig), Buffer.from(expectedSig))) return null;

    const payload = JSON.parse(Buffer.from(body, 'base64url').toString('utf8')) as SignedPayloadBase & T;
    if (!payload?.exp || payload.exp * 1000 < Date.now()) return null;
    if (payload.purpose !== expectedPurpose) return null;

    if (options?.useReplayProtection && payload.jti) {
      try {
        const redis = getRedisClient();
        if (redis) {
          const key = `signedlink:jti:${payload.jti}`;
          const val = await redis.get(key);
          if (!val) return null; // already consumed or never set
          await redis.del(key); // consume
        }
      } catch {}
    }

    return payload;
  } catch {
    return null;
  }
}

export function buildSignedUrl(basePath: string, token: string): string {
  const root = env.URL?.replace(/\/$/, '') || '';
  return `${root}${basePath}?token=${encodeURIComponent(token)}`;
}

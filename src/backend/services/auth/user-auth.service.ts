// Legacy JWT-based UserAuthService has been retired. BetterAuth-only.
// This stub remains for backward compatibility to avoid breaking imports.
import { UserAccountService } from '../user-account.service.js';

export interface UserRegisterData { email: string; password: string; name?: string }
export interface UserLoginData { email: string; password: string }
export interface UserProfile { id: string; email: string; name: string | null; planType: string; currentMonthEmails: number; emailVerified: boolean }

export class UserAuthService {
  async registerUser(_userData: UserRegisterData): Promise<{ success: boolean; user?: UserProfile; token?: string; error?: string }> {
    return { success: false, error: 'Legacy JWT auth removed. Use BetterAuth for registration.' };
  }
  async loginUser(_credentials: UserLoginData): Promise<{ success: boolean; user?: UserProfile; token?: string; error?: string }> {
    return { success: false, error: 'Legacy JWT auth removed. Use BetterAuth for login.' };
  }
  // Test-compatibility: accept optional payload and return a dummy token string
  generateToken(_payload?: { userId: string; email: string }): { success: boolean; token?: string; error?: string } {
    // Return a deterministic dummy token for tests; not used in production auth
    const userPart = _payload ? `${_payload.userId}:${_payload.email}` : 'anonymous';
    const base = Buffer.from(userPart).toString('base64url');
    return { success: true, token: `test.${base}.token` };
  }
  verifyToken(): { success: boolean; payload?: any; error?: string } {
    return { success: false, error: 'JWT verification disabled. Use BetterAuth sessions.' };
  }
  generateImpersonationToken(): { success: boolean; token?: string; error?: string } {
    return { success: false, error: 'Impersonation via JWT disabled. Use BetterAuth admin tooling.' };
  }
  getCookieConfig() {
    return { path: '/', httpOnly: true, secure: true, sameSite: 'lax' as const, maxAge: 7 * 24 * 60 * 60 * 1000 };
  }
  async checkUserUsageLimit(userId: string): Promise<boolean> {
    return UserAccountService.checkUserUsageLimit(userId);
  }
  async incrementUserEmailUsage(userId: string): Promise<void> {
    return UserAccountService.incrementUserEmailUsage(userId);
  }
  async updateUserPlan(_userId: string, _newPlanType: string): Promise<{ success: boolean; error?: string; user?: UserProfile }> {
    return { success: false, error: 'Use UserAccountService.updateUserPlan for plan changes.' };
  }
  async getUserPlanInfo(_userId: string): Promise<{ planConfig: any; usage: any; limits: any } | null> {
    return null;
  }
}

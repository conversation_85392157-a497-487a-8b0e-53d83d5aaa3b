/**
 * UPDATED: Feature Configuration - Single Source of Truth
 * 
 * This is where ALL feature requirements are maintained in one place.
 * Easy to check, easy to maintain, clear mapping of requirements.
 */

import { FeatureConfig } from '../../../types/permissions.types.js';

/**
 * FEATURE REQUIREMENTS MATRIX
 * 
 * This is the single source of truth for what each feature requires:
 * - requiredScope: What API scope is needed
 * - requiredPlanPermissions: What plan permissions are needed  
 * - resourceLimits: What resource limits apply
 * - minimumPlan: Minimum plan required (if any)
 * 
 * Adding a new feature? Just add it here!
 */
export const FEATURE_CONFIGS: Record<string, FeatureConfig> = {
  // ============================================================================
  // WEBHOOK FEATURES
  // ============================================================================
  'webhook:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read webhook information - available on all plans, no scope required for session auth'
  },

  'webhook:create': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'webhooks', operation: 'create' },
    description: 'Create basic webhook - available on all plans (subject to quantity limits), no scope required for session auth'
  },

  'webhook:create_advanced': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: ['custom_headers'],
    resourceLimits: { resourceType: 'webhooks', operation: 'create' },
    minimumPlan: 'pro',
    description: 'Create advanced webhook with custom headers - requires Pro+ plan, no scope required for session auth'
  },
  
  'webhook:write': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Write webhook operations - available on all plans, no scope required for session auth'
  },
  
  'webhook:delete': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Delete webhook - available on all plans, no scope required for session auth'
  },
  
  'webhook:test': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Test webhook - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // DOMAIN FEATURES  
  // ============================================================================
  'domain:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read domain information - available on all plans, no scope required for session auth'
  },
  
  'domain:create': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'domains', operation: 'create' },
    description: 'Create domain - available on all plans (subject to quantity limits), no scope required for session auth'
  },

  'domain:write': {
    // Generic write operations for domains - no scope required for session auth
    requiredPlanPermissions: [],
    description: 'Write domain operations - available on all plans, no scope required for session auth'
  },
  
  'domain:configure': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Basic domain configuration - available on all plans, no scope required for session auth'
  },

  'domain:configure_advanced': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: ['custom_headers'],
    minimumPlan: 'pro',
    description: 'Advanced domain configuration features - requires Pro+ plan, no scope required for session auth'
  },
  
  'domain:delete': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Delete domain - available on all plans, no scope required for session auth'
  },

  'domain:verify': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Verify domain - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // ALIAS FEATURES
  // ============================================================================
  'alias:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read alias information - available on all plans, no scope required for session auth'
  },

  'alias:create': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    resourceLimits: { resourceType: 'aliases', operation: 'create' },
    description: 'Create alias - available on all plans (subject to quantity limits), no scope required for session auth'
  },

  'alias:write': {
    // Generic write operations for aliases - no scope required for session auth
    requiredPlanPermissions: [],
    description: 'Write alias operations - available on all plans, no scope required for session auth'
  },

  'alias:update': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Update alias - available on all plans, no scope required for session auth'
  },

  'alias:delete': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Delete alias - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // ANALYTICS FEATURES (Enterprise only)
  // ============================================================================
  'analytics:read': {
    requiredScope: 'analytics:read',
    requiredPlanPermissions: ['email_analytics'],
    minimumPlan: 'enterprise',
    description: 'View email analytics dashboard - requires Enterprise plan'
  },

  // ============================================================================
  // PROFILE/ACCOUNT FEATURES
  // ============================================================================
  'profile:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read own profile information - available on all plans, no scope required for session auth'
  },

  'profile:update': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Update own profile information - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // API KEY FEATURES  
  // ============================================================================
  'apikey:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read own API keys - available on all plans, no scope required for session auth'
  },

  'apikey:create': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Create API keys - available on all plans, no scope required for session auth'
  },

  'apikey:delete': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Delete own API keys - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // BILLING FEATURES
  // ============================================================================
  'billing:read': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Read billing information - available on all plans, no scope required for session auth'
  },

  'billing:update': {
    // No required scope for session-based requests, only for API keys
    requiredPlanPermissions: [],
    description: 'Update billing/plan information - available on all plans, no scope required for session auth'
  },

  // ============================================================================
  // BASIC AUTH (no specific permissions)
  // ============================================================================
  'auth:basic': {
    // No required scope - basic authentication only
    requiredPlanPermissions: [],
    description: 'Basic authentication - no specific permissions required'
  },

  // ============================================================================
  // ADMIN FEATURES
  // ============================================================================
  'admin:access': {
    requiredScope: 'admin:*',
    requiredPlanPermissions: [],
    description: 'Admin panel access - admin authentication required'
  }
};

/**
 * Helper function to get feature config
 */
export function getFeatureConfig(featureName: string): FeatureConfig | null {
  return FEATURE_CONFIGS[featureName] || null;
}

/**
 * Helper function to check if a feature exists
 */
export function isValidFeature(featureName: string): boolean {
  return featureName in FEATURE_CONFIGS;
}

/**
 * Get all available features for documentation/UI
 */
export function getAllFeatures(): Record<string, FeatureConfig> {
  return FEATURE_CONFIGS;
}

/**
 * Get features by category for organization
 */
export function getFeaturesByCategory(): Record<string, Record<string, FeatureConfig>> {
  const categories: Record<string, Record<string, FeatureConfig>> = {
    webhooks: {},
    domains: {},
    aliases: {},
    analytics: {},
    profile: {},
    apikeys: {},
    billing: {},
    admin: {}
  };

  Object.entries(FEATURE_CONFIGS).forEach(([featureName, config]) => {
    const category = featureName.split(':')[0];
    if (categories[category]) {
      categories[category][featureName] = config;
    }
  });

  return categories;
}

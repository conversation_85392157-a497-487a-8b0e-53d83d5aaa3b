import { env } from '../config/env.js';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger.js';

export interface WebhookTestEndpoint {
    success: boolean;
    webhookEndpoint: string;
    testId: string;
    verificationCode: string;
    expiresAt?: string;
    user?: {
        id: string;
        email: string;
        isLinked: boolean;
        isPermanent: boolean;
        planType: string;
    };
    endpoint?: {
        id: string;
        friendlyUrl: string;
        createdAt: string;
    };
}

export interface WebhookTestResult {
    success: boolean;
    webhookEndpoint: string;
    testId: string;
    results?: {
        status: number;
        responseTime: number;
        headers: object;
        body: string;
        timestamp: string;
    };
    verificationCode: string;
    error?: string;
}

export class WebhookTestIntegrationService {
    /**
     * Create a WebhookTest endpoint for EmailConnect integration
     * ENHANCED: Now creates permanent user accounts instead of temporary ones
     */
    static async createWebhookTestEndpoint(
      userId: string,
      userEmail: string,
      userName?: string
    ): Promise<WebhookTestEndpoint> {
      if (!env.WEBHOOKTEST_API_URL || !env.WEBHOOKTEST_JWT_SECRET) {
        throw new Error('WebhookTest integration not configured');
      }

      try {
        const token = this.generateWebhookTestToken(userId, userEmail, 'create_endpoint_permanent');

        // Use the new create-endpoint endpoint for permanent user creation
        const response = await fetch(`${env.WEBHOOKTEST_API_URL}/api/integration/create-endpoint`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Origin': env.URL || 'http://localhost:3000'
          },
          body: JSON.stringify({
            token,
            createPermanentUser: true
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({})) as any;
          if (response.status === 404 || response.status === 405) {
            throw new Error('WebhookTest integration not configured');
          }
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json() as any;
        
        return {
          success: result.success,
          webhookEndpoint: result.webhookEndpoint,
          testId: result.testId,
          verificationCode: result.verificationCode,
          user: result.user,
          endpoint: result.endpoint,
          // No longer expires since users are permanent
          expiresAt: undefined
        };
      } catch (error) {
        logger.error({ data: error }, 'WebhookTest integration error:');
        throw error;
      }
    }


    /**
     * Test a webhook URL and get immediate results
     */
    static async testWebhook(
      userId: string,
      userEmail: string,
      webhookUrl: string,
      testPayload?: object,
      timeout?: number
    ): Promise<WebhookTestResult> {
      if (!env.WEBHOOKTEST_API_URL || !env.WEBHOOKTEST_JWT_SECRET) {
        throw new Error('WebhookTest integration not configured');
      }

      try {
        const token = this.generateWebhookTestToken(userId, userEmail, 'webhook_test', webhookUrl);

        const response = await fetch(`${env.WEBHOOKTEST_API_URL}/api/integration/test-webhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': env.URL || 'http://localhost:3000'
          },
          body: JSON.stringify({
            token,
            webhookUrl,
            testPayload,
            timeout
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({})) as any;
          if (response.status === 404 || response.status === 405) {
            throw new Error('WebhookTest integration not configured');
          }
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json() as WebhookTestResult;
      } catch (error) {
        logger.error({ data: error }, 'WebhookTest test error:');
        throw error;
      }
    }

    /**
     * Get test results for a WebhookTest endpoint
     */
    static async getTestResults(verificationCode: string): Promise<any> {
      if (!env.WEBHOOKTEST_API_URL) {
        throw new Error('WebhookTest integration not configured');
      }

      try {
        const response = await fetch(
          `${env.WEBHOOKTEST_API_URL}/api/integration/results/${verificationCode}`,
          {
            headers: {
              'Origin': env.URL || 'http://localhost:3000'
            }
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        logger.error({ data: error }, 'Failed to get test results:');
        throw error;
      }
    }

    /**
     * Get received payloads for a WebhookTest endpoint (legacy method)
     */
    static async getReceivedPayloads(verificationCode: string): Promise<any[]> {
      const results = await this.getTestResults(verificationCode);
      return results.payloads || [];
    }

    /**
     * Get EmailConnect user info and statistics from WebhookTest
     * NEW: For permanent user dashboard integration
     */
    static async getUserInfo(userId: string, userEmail: string): Promise<any> {
      if (!env.WEBHOOKTEST_API_URL || !env.WEBHOOKTEST_JWT_SECRET) {
        throw new Error('WebhookTest integration not configured');
      }

      try {
        const token = this.generateWebhookTestToken(userId, userEmail, 'get_user_info');

        const response = await fetch(`${env.WEBHOOKTEST_API_URL}/api/integration/user-info`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': env.URL || 'http://localhost:3000'
          },
          body: JSON.stringify({ token })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({})) as any;
          if (response.status === 404 || response.status === 405) {
            throw new Error('WebhookTest integration not configured');
          }
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        logger.error({ data: error }, 'Failed to get WebhookTest user info:');
        throw error;
      }
    }

    /**
     * Generate SSO URL for WebhookTest login
     * NEW: For seamless login between services
     */
    static generateWebhookTestSSOUrl(): string {
      const clientId = 'webhooktest';
      const redirectUri = `${env.WEBHOOKTEST_API_URL}/auth/callback`;
      const scope = 'profile';
      const state = Math.random().toString(36).substring(2, 15);

      const params = new URLSearchParams({
        client_id: clientId,
        redirect_uri: redirectUri,
        response_type: 'code',
        scope,
        state
      });

      return `${env.URL}/oauth/authorize?${params.toString()}`;
    }

    /**
     * Generate direct SSO login token for WebhookTest
     * Alternative to OAuth flow for API-based integration
     */
    static generateDirectSSOToken(userId: string, userEmail: string, userName?: string): string {
      return this.generateWebhookTestToken(userId, userEmail, 'sso_login', undefined, userName);
    }

    /**
     * Delete WebhookTest endpoint created by EmailConnect integration
     */
    static async deleteWebhookTestEndpoint(
      userId: string,
      userEmail: string,
      webhookUrl: string
    ): Promise<{ success: boolean; message: string; deletedEndpoint?: any }> {
      if (!env.WEBHOOKTEST_API_URL || !env.WEBHOOKTEST_JWT_SECRET) {
        throw new Error('WebhookTest integration not configured');
      }

      try {
        const token = this.generateWebhookTestToken(userId, userEmail, 'delete_endpoint', webhookUrl);

        const response = await fetch(`${env.WEBHOOKTEST_API_URL}/api/integration/delete-endpoint`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Origin': env.URL || 'http://localhost:3000'
          },
          body: JSON.stringify({
            token,
            webhookUrl
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({})) as any;
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json() as any;
        
        return {
          success: result.success,
          message: result.message,
          deletedEndpoint: result.deletedEndpoint
        };
      } catch (error) {
        logger.error({ data: error }, 'WebhookTest endpoint deletion error:');
        throw error;
      }
    }

    /**
     * Check if a URL is a WebhookTest endpoint
     */
    static isWebhookTestUrl(url: string): boolean {
      if (!env.WEBHOOKTEST_API_URL) {
        return false;
      }
      
      try {
        const webhookTestDomain = new URL(env.WEBHOOKTEST_API_URL).hostname;
        const urlDomain = new URL(url).hostname;
        return urlDomain === webhookTestDomain || urlDomain === 'webhooktest.eu' || urlDomain === 'www.webhooktest.eu';
      } catch {
        return false;
      }
    }

    /**
     * Extract endpoint ID from WebhookTest URL
     */
    static extractEndpointId(webhookTestUrl: string): string | null {
      try {
        const url = new URL(webhookTestUrl);
        const pathSegments = url.pathname.split('/').filter(segment => segment.length > 0);
        
        // WebhookTest URLs are in format: https://webhooktest.eu/{endpointId}
        if (pathSegments.length >= 1) {
          return pathSegments[0];
        }
        
        return null;
      } catch {
        return null;
      }
    }

    /**
     * Get request data from WebhookTest for a specific webhook delivery
     */
    static async getWebhookTestRequestData(
      userId: string,
      userEmail: string,
      webhookUrl: string,
      timestampStart?: Date,
      timestampEnd?: Date
    ): Promise<any> {
      if (!env.WEBHOOKTEST_API_URL || !env.WEBHOOKTEST_JWT_SECRET) {
        throw new Error('WebhookTest integration not configured');
      }

      if (!this.isWebhookTestUrl(webhookUrl)) {
        throw new Error('URL is not a WebhookTest endpoint');
      }

      const endpointId = this.extractEndpointId(webhookUrl);
      if (!endpointId) {
        throw new Error('Could not extract endpoint ID from WebhookTest URL');
      }

      try {
        const token = this.generateWebhookTestToken(userId, userEmail, 'get_request_data', webhookUrl);

        const params = new URLSearchParams({
          endpointId,
          limit: '50'
        });

        if (timestampStart) {
          params.append('after', timestampStart.toISOString());
        }
        if (timestampEnd) {
          params.append('before', timestampEnd.toISOString());
        }

        const response = await fetch(`${env.WEBHOOKTEST_API_URL}/api/integration/request-data?${params}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Origin': env.URL || 'http://localhost:3000'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({})) as any;
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        logger.error({ data: error }, 'Failed to get WebhookTest request data:');
        throw error;
      }
    }

    private static generateWebhookTestToken(
      userId: string, 
      email: string, 
      action: string = 'webhook_test',
      webhookUrl?: string,
      name?: string
    ): string {
      const payload = {
        userId,
        email,
        name,
        action,
        webhookUrl,
        timestamp: Date.now(),
        nonce: Math.random().toString(36).substring(2, 11),
        iss: 'emailconnect',
        aud: 'webhooktest'
      };

      // Use WebhookTest secret and set 5 minute expiration
      return jwt.sign(payload as any, env.WEBHOOKTEST_JWT_SECRET as string, { expiresIn: '5m' });
    }
}
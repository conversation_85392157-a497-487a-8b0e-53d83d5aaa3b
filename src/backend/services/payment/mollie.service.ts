import { createMollieClient, MollieApiError, PaymentMethod } from '@mollie/api-client';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';
import crypto from 'crypto';
import { captureBillingError } from '../../lib/sentry-helpers.js';

export class MollieService {
  private client;

  constructor() {
    // Use process.env for the check so tests can manipulate it
    if (!process.env.MOLLIE_API_KEY) {
      logger.warn('MOLLIE_API_KEY not configured - payment processing will be disabled');
      return;
    }

    // Use env for the actual value (validated and typed)
    this.client = createMollieClient({
      apiKey: env.MOLLIE_API_KEY,
    });

    logger.info({ 
      testMode: env.MOLLIE_TEST_MODE 
    }, 'Mollie service initialized');
  }

  /**
   * Check if <PERSON><PERSON> is properly configured
   * Uses process.env directly to allow test manipulation
   */
  isConfigured(): boolean {
    return !!this.client && !!process.env.MOLLIE_API_KEY;
  }

  /**
   * Create a new payment
   */
  async createPayment(params: {
    amount: { value: string; currency: string };
    description: string;
    redirectUrl: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    method?: string;
    methods?: string[];
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const paymentData: any = {
        amount: params.amount,
        description: params.description,
        redirectUrl: params.redirectUrl,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
      };

      // Only set method if explicitly specified
      if (params.method) {
        paymentData.method = params.method;
      }
      // If no method is specified, Mollie will show all available payment methods

      const payment = await this.client.payments.create(paymentData);

      logger.info({ 
        paymentId: payment.id,
        amount: params.amount,
        description: params.description 
      }, 'Payment created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating payment');
        captureBillingError(error, {
          feature: 'payment',
          mollieField: error.field,
          paymentAmount: params.amount.value,
        });
      } else {
        logger.error({ error }, 'Unexpected error creating payment');
        captureBillingError(error as Error, {
          feature: 'payment',
          paymentAmount: params.amount.value,
        });
      }
      throw error;
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const payment = await this.client.payments.get(paymentId);
      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          paymentId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting payment');
      } else {
        logger.error({ paymentId, error }, 'Unexpected error getting payment');
      }
      throw error;
    }
  }

  /**
   * Create a customer for recurring payments
   */
  async createCustomer(params: {
    name: string;
    email: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.create({
        name: params.name,
        email: params.email,
        metadata: params.metadata,
      });

      logger.info({ 
        customerId: customer.id,
        email: params.email 
      }, 'Customer created successfully');

      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating customer');
      } else {
        logger.error({ error }, 'Unexpected error creating customer');
      }
      throw error;
    }
  }

  /**
   * Create a subscription with stored payment method
   */
  async createSubscriptionWithStoredMethod(params: {
    customerId: string;
    amount: { value: string; currency: string };
    interval: string;
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    startDate?: string; // ISO date to start charging from (schedules first charge)
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      // Ensure subscription webhooks go to the subscription endpoint
      const defaultSubWebhook = (() => {
        if (env.MOLLIE_WEBHOOK_URL?.includes('/payment')) {
          return env.MOLLIE_WEBHOOK_URL.replace('/payment', '/subscription')
        }
        if (env.MOLLIE_WEBHOOK_URL?.includes('/subscription')) {
          return env.MOLLIE_WEBHOOK_URL
        }
        return `${env.URL}/api/webhooks/mollie/subscription`
      })()

      const subscription = await this.client.customerSubscriptions.create({
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval,
        description: params.description,
        webhookUrl: params.webhookUrl || defaultSubWebhook,
        metadata: params.metadata,
        ...(params.startDate ? { startDate: params.startDate } : {})
      });

      logger.info({ 
        subscriptionId: subscription.id,
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval 
      }, 'Subscription created successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating subscription');
      } else {
        logger.error({ error }, 'Unexpected error creating subscription');
      }
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(customerId: string, subscriptionId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.cancel(subscriptionId, {
        customerId,
      });

      logger.info({ 
        subscriptionId,
        customerId 
      }, 'Subscription cancelled successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error cancelling subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error cancelling subscription');
      }
      throw error;
    }
  }

  /**
   * Get customer details
   */
  async getCustomer(customerId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.get(customerId);
      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting customer');
      } else {
        logger.error({ customerId, error }, 'Unexpected error getting customer');
      }
      throw error;
    }
  }

  /**
   * Update customer details
   */
  async updateCustomer(customerId: string, params: {
    name?: string;
    email?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.update(customerId, params);

      logger.info({ 
        customerId,
        email: params.email 
      }, 'Customer updated successfully');

      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error updating customer');
      } else {
        logger.error({ customerId, error }, 'Unexpected error updating customer');
      }
      throw error;
    }
  }

  /**
   * Create a payment with stored payment method
   */
  async createPaymentWithStoredMethod(params: {
    customerId: string;
    amount: { value: string; currency: string };
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    sequenceType?: 'oneoff' | 'recurring';
    mandateId?: string;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const paymentData: any = {
        customerId: params.customerId,
        amount: params.amount,
        description: params.description,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
        sequenceType: params.sequenceType || 'recurring'
      };

      // Add mandate ID if provided for oneoff payments
      if (params.mandateId && params.sequenceType === 'oneoff') {
        paymentData.mandateId = params.mandateId;
      }

      const payment = await this.client.payments.create(paymentData);

      logger.info({ 
        paymentId: payment.id,
        customerId: params.customerId,
        amount: params.amount,
        description: params.description,
        sequenceType: params.sequenceType || 'recurring',
        mandateId: params.mandateId
      }, 'Payment with stored method created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId: params.customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error creating payment with stored method');
      } else {
        logger.error({ customerId: params.customerId, error }, 'Unexpected error creating payment with stored method');
      }
      throw error;
    }
  }

  /**
   * Create a one-time payment to establish a customer mandate
   */
  async createMandatePayment(params: {
    customerId: string;
    amount: { value: string; currency: string };
    description: string;
    redirectUrl: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    method?: string; // Optional: force a specific method like 'creditcard' or 'directdebit'
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      logger.info({ 
        customerId: params.customerId,
        amount: params.amount,
        method: params.method,
        description: params.description 
      }, 'Creating mandate payment with method');

      const payment = await this.client.payments.create({
        customerId: params.customerId,
        amount: params.amount,
        description: params.description,
        redirectUrl: params.redirectUrl,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
        sequenceType: 'first',
        ...(params.method ? { method: params.method } : {})
      });

      logger.info({ 
        paymentId: payment.id,
        customerId: params.customerId,
        amount: params.amount,
        description: params.description 
      }, 'Mandate payment created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId: params.customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error creating mandate payment');
      } else {
        logger.error({ customerId: params.customerId, error }, 'Unexpected error creating mandate payment');
      }
      throw error;
    }
  }

  /**
   * Create a subscription with first payment (Mollie best practice)
   */
  async createSubscription(params: {
    customerId: string;
    amount: { value: string; currency: string };
    interval: 'monthly' | 'yearly';
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    firstPaymentMethod?: string;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      logger.info({ 
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval,
        method: params.firstPaymentMethod,
        description: params.description 
      }, 'Creating subscription with first payment');

      // For subscriptions with first payment, we need to create a subscription
      // but Mollie will automatically create the first payment with checkout URL
      const subscriptionData: any = {
        customerId: params.customerId,
        amount: params.amount,
        interval: `${params.interval === 'yearly' ? '12' : '1'} month${params.interval === 'yearly' ? 's' : ''}`,
        description: params.description,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata
      };

      // Add first payment method if specified
      if (params.firstPaymentMethod) {
        subscriptionData.method = params.firstPaymentMethod;
      }

      const subscription = await this.client.customerSubscriptions.create(subscriptionData);

      logger.info({ 
        subscriptionId: subscription.id,
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval
      }, 'Subscription created successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId: params.customerId,
          error: error.message,
          mollieError: error.field,
          amount: params.amount.value,
        }, 'Mollie API error creating subscription');
        captureBillingError(error, {
          feature: 'subscription',
          mollieCustomerId: params.customerId,
          mollieField: error.field,
          subscriptionAmount: params.amount.value,
        });
      } else {
        logger.error({ error, customerId: params.customerId }, 'Unexpected error creating subscription');
        captureBillingError(error as Error, {
          feature: 'subscription',
          mollieCustomerId: params.customerId,
          subscriptionAmount: params.amount.value,
        });
      }
      throw error;
    }
  }

  /**
   * Get available payment methods
   */
  async getAvailablePaymentMethods() {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const methods = await this.client.methods.list();
      logger.info({ 
        methods: methods.map(m => ({ id: m.id, description: m.description }))
      }, 'Available payment methods retrieved');
      return methods;
    } catch (error) {
      logger.error({ error }, 'Failed to get available payment methods');
      throw error;
    }
  }

  /**
   * Get customer's payment methods
   */
  async getCustomerPaymentMethods(customerId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      logger.info({ 
        customerId,
        hasClient: !!this.client,
        hasCustomerPaymentMethods: !!this.client?.customerPaymentMethods
      }, 'Getting customer payment methods');

      if (!this.client.customerPaymentMethods) {
        logger.warn({ customerId }, 'Mollie client customerPaymentMethods not available - returning empty array');
        return [];
      }

      const paymentMethods = await this.client.customerPaymentMethods.list({
        customerId
      });

      logger.info({ 
        customerId,
        count: paymentMethods.length
      }, 'Customer payment methods retrieved successfully');

      return paymentMethods;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting customer payment methods');
      } else {
        logger.error({ customerId, error }, 'Unexpected error getting customer payment methods');
      }
      throw error;
    }
  }

  /**
   * Delete a customer's payment method (by revoking the mandate)
   */
  async deleteCustomerPaymentMethod(customerId: string, mandateId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      // Revoke the mandate (which effectively removes the payment method)
      await this.client.customerMandates.delete(mandateId, {
        customerId
      });

      logger.info({ 
        customerId,
        mandateId
      }, 'Customer mandate revoked successfully');

      return true;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          mandateId,
          error: error.message,
          field: error.field
        }, 'Mollie API error revoking customer mandate');
      } else {
        logger.error({ customerId, mandateId, error }, 'Unexpected error revoking customer mandate');
      }
      throw error;
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(customerId: string, subscriptionId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.get(subscriptionId, {
        customerId
      });
      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error getting subscription');
      }
      throw error;
    }
  }

  /**
   * Update subscription details
   */
  async updateSubscription(customerId: string, subscriptionId: string, params: {
    amount?: { value: string; currency: string };
    description?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.update(subscriptionId, {
        customerId,
        ...params
      });

      logger.info({ 
        subscriptionId,
        customerId,
        amount: params.amount
      }, 'Subscription updated successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error updating subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error updating subscription');
      }
      throw error;
    }
  }

  /**
   * Get customer mandates from Mollie
   */
  async getCustomerMandates(customerId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      logger.info({ customerId }, 'Getting customer mandates from Mollie');
      
      const mandates = await this.client.customerMandates.page({ customerId });
      
      logger.info({ 
        customerId,
        mandateCount: mandates.length
      }, 'Retrieved customer mandates from Mollie');
      
      return mandates;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting customer mandates');
      } else {
        logger.error({ customerId, error }, 'Unexpected error getting customer mandates');
      }
      throw error;
    }
  }

  /**
   * Verify webhook signature (if webhook secret is configured)
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    // Use process.env for the check to allow test manipulation
    if (!process.env.MOLLIE_WEBHOOK_SECRET) {
      logger.warn('MOLLIE_WEBHOOK_SECRET not configured - webhook signature verification disabled');
      return true; // Allow webhook if no secret is configured
    }

    // Use env for the actual value (validated and typed)
    const expectedSignature = crypto
      .createHmac('sha256', env.MOLLIE_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    try {
      const isValid = crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
      
      if (!isValid) {
        logger.warn({ signature, expectedSignature }, 'Invalid webhook signature');
      }
      
      return isValid;
    } catch (error) {
      logger.warn({ error: (error as Error).message, signature }, 'Invalid webhook signature format');
      return false;
    }
  }
}

// Export singleton instance
export const mollieService = new MollieService();

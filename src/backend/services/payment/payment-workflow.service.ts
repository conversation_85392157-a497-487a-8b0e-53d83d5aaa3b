import { prisma } from '../../lib/prisma.js';
import { mollieService } from './mollie.service.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService } from '../billing/plan-config.service.js';
import { CreditService } from '../billing/credit.service.js';
import { GrandfatheringService } from '../billing/grandfathering.service.js';
import { OneOffSubscriptionService } from '../billing/one-off-subscription.service.js';
import { notify } from '../notifications/index.js';
import { env } from '../../config/env.js';

export interface CreatePaymentRequest {
  userId: string;
  planType: 'pro' | 'enterprise' | 'credits';
  interval: 'monthly' | 'yearly' | 'one-time';
  successUrl: string;
  cancelUrl: string;
  // Optional overrides for credit purchases
  amount?: number;
  currency?: string;
  description?: string;
  metadata?: Record<string, any>;
  methods?: string[]; // Optional: restrict available payment methods
}

export interface CreateSubscriptionRequest {
  userId: string;
  planType: 'pro' | 'enterprise';
  interval: 'monthly' | 'yearly';
  mollieCustomerId: string;
}

export class PaymentWorkflowService {
  /**
   * Create or get Mollie customer for user
   */
  async createOrGetCustomer(userId: string) {
    try {
      // Check if user already has a Mollie customer
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true,
          email: true, 
          name: true,
          mollieCustomerId: true
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // If user has existing Mollie customer, return it
      if (user.mollieCustomerId) {
        try {
          // Verify customer still exists in Mollie
          await mollieService.getCustomer(user.mollieCustomerId);
          return { customerId: user.mollieCustomerId, isNew: false };
        } catch (error) {
          // Customer doesn't exist in Mollie anymore, create new one
          logger.warn({ 
            userId, 
            customerId: user.mollieCustomerId 
          }, 'Mollie customer not found, creating new one');
        }
      }

      // Create new Mollie customer
      const mollieCustomer = await mollieService.createCustomer({
        name: user.name || user.email,
        email: user.email,
        metadata: {
          userId: userId
        }
      });

      // Store customer ID on user record
      await prisma.user.update({
        where: { id: userId },
        data: { mollieCustomerId: mollieCustomer.id }
      });

      logger.info({
        userId,
        customerId: mollieCustomer.id,
        email: user.email
      }, 'Mollie customer created successfully');

      return { customerId: mollieCustomer.id, isNew: true };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to create or get Mollie customer');
      throw error;
    }
  }

  /**
   * Create a mandate payment to establish payment method
   */
  async createMandatePayment(request: {
    userId: string;
    amount: { value: string; currency: string };
    description: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, any>;
    method?: 'creditcard' | 'directdebit';
  }) {
    try {
      // Get or create Mollie customer
      const { customerId } = await this.createOrGetCustomer(request.userId);

      // Create mandate payment
      const molliePayment = await mollieService.createMandatePayment({
        customerId,
        amount: request.amount,
        description: request.description,
        redirectUrl: request.successUrl,
        metadata: {
          userId: request.userId,
          type: 'mandate',
          ...request.metadata
        },
        // Prefer selected method if provided; default to credit card
        method: request.method || 'creditcard'
      });

      // Store payment in database
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: 'PENDING',
          amount: parseFloat(request.amount.value),
          currency: request.amount.currency,
          description: request.description,
          userId: request.userId,
          mollieCustomerId: customerId,
          mollieWebhookData: JSON.parse(JSON.stringify(molliePayment))
        }
      });

      logger.info({
        paymentId: payment.id,
        mollieId: molliePayment.id,
        userId: request.userId,
        customerId
      }, 'Mandate payment created successfully');

      return {
        paymentId: payment.id,
        mollieId: molliePayment.id,
        checkoutUrl: molliePayment.getCheckoutUrl(),
        customerId
      };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId
      }, 'Failed to create mandate payment');
      throw error;
    }
  }

  /**
   * Create a one-time payment for plan upgrade or credit purchase
   */
  async createPayment(request: CreatePaymentRequest) {
    try {
      let amount: number;
      let currency: string;
      let description: string;

      // Handle credit purchases vs plan upgrades
      if (request.planType === 'credits') {
        if (!request.amount || !request.currency || !request.description) {
          throw new Error('Credit purchases require amount, currency, and description');
        }
        amount = request.amount;
        currency = request.currency;
        description = request.description;
      } else {
        // Plan upgrade - get configuration
        const planConfig = PlanConfigService.getPlanConfig(request.planType);
        if (!planConfig.price) {
          throw new Error(`Plan ${request.planType} does not have pricing configured`);
        }

        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
        currency = planConfig.price.currency;
        description = `${planConfig.name} Plan - ${request.interval}`;
      }

      // Get user for metadata
      const user = await prisma.user.findUnique({
        where: { id: request.userId },
        select: { email: true, name: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // For credit purchases, try to use existing mandate for one-off payment
      let molliePayment;
      if (request.planType === 'credits') {
        // Try to get customer and their mandates
        try {
          const { customerId } = await this.createOrGetCustomer(request.userId);
          const mandates = await mollieService.getCustomerMandates(customerId);
          const validMandate = mandates.find(m => m.status === 'valid');
          
          if (validMandate) {
            // Use mandate for one-off payment
            logger.info({ userId: request.userId, mandateId: validMandate.id }, 'Using existing mandate for credit purchase');
            
            molliePayment = await mollieService.createPaymentWithStoredMethod({
              customerId,
              mandateId: validMandate.id,
              amount: {
                value: amount.toFixed(2),
                currency
              },
              description,
              sequenceType: 'oneoff',
              webhookUrl: env.MOLLIE_WEBHOOK_URL,
              metadata: {
                userId: request.userId,
                planType: request.planType,
                interval: request.interval,
                userEmail: user.email,
                ...request.metadata
              }
            });
          }
        } catch (error: any) {
          logger.warn({ userId: request.userId, error: error.message }, 'Could not use mandate for credit purchase, falling back to regular payment');
        }
      }
      
      // If no mandate payment was created, use regular payment
      if (!molliePayment) {
        molliePayment = await mollieService.createPayment({
          amount: {
            value: amount.toFixed(2),
            currency
          },
          description,
          redirectUrl: request.successUrl,
          webhookUrl: env.MOLLIE_WEBHOOK_URL,
          metadata: {
            userId: request.userId,
            planType: request.planType,
            interval: request.interval,
            userEmail: user.email,
            ...request.metadata
          }
        });
      }

      // Store payment in database
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: 'PENDING',
          amount: amount,
          currency,
          description,
          userId: request.userId,
          mollieWebhookData: JSON.parse(JSON.stringify(molliePayment))
        }
      });

      logger.info({
        paymentId: payment.id,
        mollieId: molliePayment.id,
        userId: request.userId,
        planType: request.planType,
        amount: amount
      }, 'Payment created successfully');

      return {
        paymentId: payment.id,
        mollieId: molliePayment.id,
        checkoutUrl: molliePayment.getCheckoutUrl(),
        amount: {
          value: amount.toFixed(2),
          currency
        }
      };
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create payment');
      throw error;
    }
  }

  /**
   * Unified subscription creation following Mollie's correct flow:
   * 1. Check for existing valid mandate
   * 2a. If no mandate: Create first payment with sequenceType='first' to establish mandate
   * 2b. If mandate exists: Create subscription immediately with startDate=today
   */
  async createSubscriptionWithFirstPayment(request: {
    userId: string;
    planType: 'pro' | 'enterprise';
    interval: 'monthly' | 'yearly';
    amount: { value: string; currency: string };
    description: string;
    successUrl: string;
    cancelUrl: string;
    method?: string;
  }) {
    try {
      // Get or create customer
      const { customerId } = await this.createOrGetCustomer(request.userId);

      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Check grandfathering
      const grandfatheringStatus = await GrandfatheringService.getGrandfatheringStatus(request.userId);
      const { isGrandfathered, grandfatheredPrice } = grandfatheringStatus || { 
        isGrandfathered: false, 
        grandfatheredPrice: null 
      };
      const grandfatheringReason = null;

      // Check for existing valid mandate
      const mandates = await mollieService.getCustomerMandates(customerId);
      const validMandate = mandates.find(m => m.status === 'valid');
      
      logger.info({
        userId: request.userId,
        customerId,
        mandatesFound: mandates.length,
        hasValidMandate: !!validMandate,
        mandateStatuses: mandates.map(m => ({ id: m.id, status: m.status, method: m.method }))
      }, 'Checking mandates for subscription creation');

      if (!validMandate) {
        // No valid mandate - create first payment with sequenceType='first' to establish mandate
        logger.info({ userId: request.userId }, 'No valid mandate found, creating first payment with sequenceType=first');
        
        const firstPayment = await mollieService.createMandatePayment({
          customerId,
          amount: request.amount,
          description: `${request.description} (First payment)`,
          redirectUrl: request.successUrl,
          webhookUrl: env.MOLLIE_WEBHOOK_URL || `${env.URL}/api/webhooks/mollie/payment`,
          method: request.method,
          metadata: {
            userId: request.userId,
            planType: request.planType,
            interval: request.interval,
            type: 'subscription_first_payment',
            customerId
          }
        });

        // Store subscription in database (will be activated when first payment succeeds)
        const subscription = await prisma.subscription.create({
        data: {
          mollieId: `pending_${firstPayment.id}`, // Temporary ID until subscription is created
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: parseFloat(request.amount.value),
          currency: request.amount.currency,
          description: request.description,
          mollieCustomerId: customerId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: null, // Will be set when subscription is created
          isGrandfathered,
          grandfatheredPrice,
          grandfatheredAt: isGrandfathered ? new Date() : null,
          grandfatheringReason
        }
      });

      // Create payment record linked to subscription
      await prisma.payment.create({
        data: {
          mollieId: firstPayment.id,
          status: 'PENDING',
          amount: parseFloat(request.amount.value),
          currency: request.amount.currency,
          description: `${request.description} (First payment)`,
          mollieCustomerId: customerId,
          userId: request.userId,
          subscriptionId: subscription.id
        }
      });

        // Get checkout URL from first payment
        const checkoutUrl = firstPayment._links?.checkout?.href;
        if (!checkoutUrl) {
          throw new Error('No checkout URL returned from first payment');
        }

        logger.info({
          subscriptionId: subscription.id,
          paymentId: firstPayment.id,
          userId: request.userId,
          planType: request.planType,
          interval: request.interval
        }, 'First payment created - subscription will be activated after payment succeeds');

        return {
          subscriptionId: subscription.id,
          checkoutUrl,
          customerId,
          requiresAction: true // User needs to complete payment
        };
        
      } else {
        // Valid mandate exists - create subscription immediately with startDate=today
        logger.info({ 
          userId: request.userId,
          mandateId: validMandate.id,
          mandateMethod: validMandate.method
        }, 'Valid mandate found, creating subscription immediately');
        
        // Calculate start date (today for immediate charging)
        const startDate = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
        
        // Create Mollie subscription
        const mollieSubscription = await mollieService.createSubscriptionWithStoredMethod({
          customerId,
          amount: request.amount,
          interval: request.interval === 'monthly' ? '1 month' : '1 year', // Convert to Mollie format
          description: request.description,
          startDate, // Start immediately since mandate exists
          webhookUrl: env.MOLLIE_WEBHOOK_URL || `${env.URL}/api/webhooks/mollie/subscription`,
          metadata: {
            userId: request.userId,
            planType: request.planType
          }
        });

        // Store subscription in database
        const subscription = await prisma.subscription.create({
          data: {
            mollieId: mollieSubscription.id,
            status: mollieSubscription.status === 'active' ? 'ACTIVE' : 'PENDING',
            planType: request.planType,
            interval: request.interval,
            amount: parseFloat(request.amount.value),
            currency: request.amount.currency,
            description: request.description,
            mollieCustomerId: customerId,
            molliePaymentMethod: validMandate.method,
            userId: request.userId,
            startDate: new Date(),
            nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null,
            isGrandfathered,
            grandfatheredPrice,
            grandfatheredAt: isGrandfathered ? new Date() : null,
            grandfatheringReason
          }
        });

        // Upgrade user immediately since subscription is active
        await this.upgradeUserPlan(request.userId, request.planType, request.interval);

        // Audit: subscription started
        try {
          await prisma.auditLog.create({
            data: {
              action: 'subscription.started',
              resourceType: 'subscription',
              resourceId: subscription.id,
              metadata: {
                userId: request.userId,
                planType: request.planType,
                interval: request.interval,
                mollieSubscriptionId: mollieSubscription.id
              },
              expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
            }
          });
        } catch (e:any) {
          logger.warn({ error: e.message, subscriptionId: subscription.id }, 'Failed to log subscription.started');
        }

        logger.info({
          userId: request.userId,
          customerId,
          subscriptionId: subscription.id,
          mollieSubscriptionId: mollieSubscription.id,
          status: mollieSubscription.status
        }, 'Subscription created immediately with existing mandate');

        return {
          subscriptionId: subscription.id,
          checkoutUrl: null, // No checkout needed
          customerId,
          requiresAction: false // Subscription is active
        };
      }

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription with first payment');
      throw error;
    }
  }

  /**
   * DEPRECATED - Use createSubscriptionWithFirstPayment instead
   * This method is kept for backwards compatibility but should not be used
   * @deprecated
   */
  async createSubscriptionWithStoredMethod(request: {
    userId: string;
    planType: 'pro' | 'enterprise';
    interval: 'monthly' | 'yearly';
    paymentMethodId?: string; // Optional - uses default if not provided
  }) {
    try {
      // Get or create customer
      const { customerId } = await this.createOrGetCustomer(request.userId);

      // Get payment method to use
      let paymentMethod;
      if (request.paymentMethodId) {
        paymentMethod = await prisma.paymentMethod.findFirst({
          where: { id: request.paymentMethodId, userId: request.userId }
        });
        if (!paymentMethod) {
          throw new Error('Payment method not found');
        }
      } else {
        // Use default payment method
        paymentMethod = await prisma.paymentMethod.findFirst({
          where: { userId: request.userId, isDefault: true }
        });
        
        if (!paymentMethod) {
          // Try to sync payment methods first, then retry
          logger.info({ userId: request.userId }, 'No default payment method found, attempting to sync from Mollie');
          
          try {
            await this.syncPaymentMethods(request.userId);
            
            // Retry finding default payment method after sync
            paymentMethod = await prisma.paymentMethod.findFirst({
              where: { userId: request.userId, isDefault: true }
            });
          } catch (syncError: any) {
            logger.error({ userId: request.userId, error: syncError.message }, 'Failed to sync payment methods');
          }
          
          // If still no default, try any payment method for this user
          if (!paymentMethod) {
            paymentMethod = await prisma.paymentMethod.findFirst({
              where: { userId: request.userId }
            });
          }
          
          if (!paymentMethod) {
            throw new Error('No payment methods found. Please add a payment method first.');
          }
        }
      }

      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Check if user has existing grandfathering that should be preserved
      const grandfatheringStatus = await GrandfatheringService.getGrandfatheringStatus(request.userId);
      const shouldKeepGrandfathering = grandfatheringStatus?.isGrandfathered &&
        await GrandfatheringService.validateGrandfathering(request.userId, request.planType, request.interval);

      // Determine effective price (grandfathered or current)
      let amount: number;
      let isGrandfathered = false;
      let grandfatheredPrice: number | null = null;
      let grandfatheringReason: string | null = null;

      if (shouldKeepGrandfathering && grandfatheringStatus) {
        amount = grandfatheringStatus.grandfatheredPrice!;
        isGrandfathered = true;
        grandfatheredPrice = grandfatheringStatus.grandfatheredPrice!;
        grandfatheringReason = grandfatheringStatus.reason || null;

        logger.info({
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          grandfatheredPrice: amount,
          currentPrice: grandfatheringStatus.currentPrice,
          savings: grandfatheringStatus.savings
        }, 'User keeps grandfathered pricing');
      } else {
        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
      }

      // Create subscription with Mollie
      const mollieSubscription = await mollieService.createSubscription({
        customerId,
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        interval: request.interval,
        description: `${planConfig.name} Plan`,
        metadata: {
          userId: request.userId,
          planType: request.planType,
          paymentMethodId: paymentMethod.id
        }
      });

      // Store subscription in database
      const subscription = await prisma.subscription.create({
        data: {
          mollieId: mollieSubscription.id,
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan`,
          mollieCustomerId: customerId,
          molliePaymentMethod: paymentMethod.mollieId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null,
          // Grandfathering fields
          isGrandfathered,
          grandfatheredPrice,
          grandfatheredAt: isGrandfathered ? new Date() : null,
          grandfatheringReason
        }
      });

      // Immediately upgrade user's plan since subscription is created with stored payment method
      await this.upgradeUserPlan(request.userId, request.planType, request.interval);

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscription.id,
        userId: request.userId,
        planType: request.planType,
        paymentMethodId: paymentMethod.id
      }, 'Subscription created with stored payment method');

      return subscription;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription with stored payment method');
      throw error;
    }
  }

  /**
   * Create a recurring subscription
   */
  async createSubscription(request: CreateSubscriptionRequest) {
    try {
      // Get plan configuration
      const planConfig = PlanConfigService.getPlanConfig(request.planType);
      if (!planConfig.price) {
        throw new Error(`Plan ${request.planType} does not have pricing configured`);
      }

      // Check if user has existing grandfathering that should be preserved
      const grandfatheringStatus = await GrandfatheringService.getGrandfatheringStatus(request.userId);
      const shouldKeepGrandfathering = grandfatheringStatus?.isGrandfathered &&
        await GrandfatheringService.validateGrandfathering(request.userId, request.planType, request.interval);

      // Determine effective price (grandfathered or current)
      let amount: number;
      let isGrandfathered = false;
      let grandfatheredPrice: number | null = null;
      let grandfatheringReason: string | null = null;

      if (shouldKeepGrandfathering && grandfatheringStatus) {
        amount = grandfatheringStatus.grandfatheredPrice!;
        isGrandfathered = true;
        grandfatheredPrice = grandfatheringStatus.grandfatheredPrice!;
        grandfatheringReason = grandfatheringStatus.reason || null;

        logger.info({
          userId: request.userId,
          planType: request.planType,
          interval: request.interval,
          grandfatheredPrice: amount,
          currentPrice: grandfatheringStatus.currentPrice,
          savings: grandfatheringStatus.savings
        }, 'User keeps grandfathered pricing');
      } else {
        // Calculate amount based on interval
        amount = request.interval === 'yearly'
          ? planConfig.price.yearly
          : planConfig.price.monthly;
      }

      // Create subscription with Mollie
      const mollieSubscription = await mollieService.createSubscription({
        customerId: request.mollieCustomerId,
        amount: {
          value: amount.toFixed(2),
          currency: planConfig.price.currency
        },
        interval: request.interval,
        description: `${planConfig.name} Plan`,
        metadata: {
          userId: request.userId,
          planType: request.planType
        }
      });

      // Store subscription in database
      const subscription = await prisma.subscription.create({
        data: {
          mollieId: mollieSubscription.id,
          status: 'PENDING',
          planType: request.planType,
          interval: request.interval,
          amount: amount,
          currency: planConfig.price.currency,
          description: `${planConfig.name} Plan`,
          mollieCustomerId: request.mollieCustomerId,
          userId: request.userId,
          startDate: new Date(),
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null,
          // Grandfathering fields
          isGrandfathered,
          grandfatheredPrice,
          grandfatheredAt: isGrandfathered ? new Date() : null,
          grandfatheringReason
        }
      });

      // Immediately upgrade user's plan since subscription is created with stored customer
      await this.upgradeUserPlan(request.userId, request.planType, request.interval);

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscription.id,
        userId: request.userId,
        planType: request.planType
      }, 'Subscription created successfully');

      return subscription;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId: request.userId,
        planType: request.planType
      }, 'Failed to create subscription');
      throw error;
    }
  }

  /**
   * Sync payment methods from Mollie mandates to local database
   */
  async syncPaymentMethods(userId: string) {
    try {
      // Get or create customer
      const { customerId } = await this.createOrGetCustomer(userId);

      logger.info({ userId, customerId }, 'Starting payment method sync from mandates');

      // Get mandates from Mollie
      const mollieMandates = await mollieService.getCustomerMandates(customerId);

      logger.info({ 
        userId, 
        customerId, 
        mandateCount: mollieMandates.length 
      }, 'Retrieved mandates from Mollie');

      // Filter for valid mandates only
      const validMandates = mollieMandates.filter(mandate => mandate.status === 'valid' || mandate.status === 'pending');

      // If no valid mandates from Mollie, don't clear existing ones
      if (validMandates.length === 0) {
        logger.warn({ userId, customerId }, 'No valid mandates found in Mollie - keeping existing local methods');
        return await prisma.paymentMethod.findMany({
          where: { userId }
        });
      }

      // Clear existing payment methods for this user
      await prisma.paymentMethod.deleteMany({
        where: { userId }
      });

      // Store new payment methods from mandates
      const paymentMethods = [];
      for (const mandate of validMandates) {
        // Build description based on mandate details
        let description = mandate.method;
        let displayType = mandate.method;
        
        if (mandate.details) {
          // For credit cards, show card type and last 4 digits
          if (mandate.method === 'creditcard' && mandate.details.cardNumber) {
            // Card number is masked like "**** **** **** 1234"
            const last4 = mandate.details.cardNumber.slice(-4);
            const cardLabel = mandate.details.cardLabel || 'Card'; // Visa, Mastercard, Amex, etc.
            description = `${cardLabel} ••••${last4}`;
            displayType = cardLabel;
          } 
          // For SEPA Direct Debit
          else if (mandate.method === 'directdebit' && mandate.details.consumerAccount) {
            // IBAN is masked
            const maskedIBAN = mandate.details.consumerAccount;
            const last4 = maskedIBAN.replace(/[^0-9]/g, '').slice(-4);
            description = `SEPA ••••${last4}`;
            if (mandate.details.consumerName) {
              description += ` - ${mandate.details.consumerName}`;
            }
          }
          // For PayPal
          else if (mandate.method === 'paypal' && mandate.details.consumerAccount) {
            description = `PayPal - ${mandate.details.consumerAccount}`;
          }
          // Fallback to existing logic
          else if (mandate.details.cardHolder) {
            description = `${mandate.method} - ${mandate.details.cardHolder}`;
          } else if (mandate.details.consumerName) {
            description = `${mandate.method} - ${mandate.details.consumerName}`;
          } else if (mandate.details.consumerAccount) {
            description = `${mandate.method} - ${mandate.details.consumerAccount}`;
          }
        }

        const paymentMethod = await prisma.paymentMethod.create({
          data: {
            mandateId: mandate.id,
            type: displayType || mandate.method || 'unknown',
            description: description || 'Payment method',
            isDefault: false, // We'll set default manually
            userId
          }
        });
        paymentMethods.push(paymentMethod);
      }

      // Set first payment method as default if none exists
      if (paymentMethods.length > 0) {
        await prisma.paymentMethod.update({
          where: { id: paymentMethods[0].id },
          data: { isDefault: true }
        });
      }

      logger.info({
        userId,
        customerId,
        paymentMethodCount: paymentMethods.length
      }, 'Payment methods synced successfully');

      return paymentMethods;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to sync payment methods');
      throw error;
    }
  }

  /**
   * Get user's stored payment methods
   */
  async getUserPaymentMethods(userId: string) {
    try {
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return paymentMethods.map(method => ({
        id: method.id,
        mandateId: method.mandateId,
        type: method.type,
        description: method.description,
        isDefault: method.isDefault,
        createdAt: method.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user payment methods');
      throw error;
    }
  }

  /**
   * Set default payment method for user
   */
  async setDefaultPaymentMethod(userId: string, paymentMethodId: string) {
    try {
      // Verify payment method belongs to user
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Remove default from all other payment methods
      await prisma.paymentMethod.updateMany({
        where: { userId },
        data: { isDefault: false }
      });

      // Set new default
      await prisma.paymentMethod.update({
        where: { id: paymentMethodId },
        data: { isDefault: true }
      });

      logger.info({
        userId,
        paymentMethodId,
        mandateId: paymentMethod.mandateId
      }, 'Default payment method updated');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        paymentMethodId
      }, 'Failed to set default payment method');
      throw error;
    }
  }

  /**
   * Delete payment method
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string) {
    try {
      // Get payment method and verify ownership
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId }
      });

      if (!paymentMethod) {
        throw new Error('Payment method not found');
      }

      // Get customer ID for Mollie deletion
      const { customerId } = await this.createOrGetCustomer(userId);

      // Delete from Mollie
      await mollieService.deleteCustomerPaymentMethod(customerId, paymentMethod.mandateId);

      // Delete from database
      await prisma.paymentMethod.delete({
        where: { id: paymentMethodId }
      });

      // If this was the default payment method, set another as default
      if (paymentMethod.isDefault) {
        const nextPaymentMethod = await prisma.paymentMethod.findFirst({
          where: { userId },
          orderBy: { createdAt: 'desc' }
        });

        if (nextPaymentMethod) {
          await prisma.paymentMethod.update({
            where: { id: nextPaymentMethod.id },
            data: { isDefault: true }
          });
        }
      }

      logger.info({
        userId,
        paymentMethodId,
        mandateId: paymentMethod.mandateId
      }, 'Payment method deleted successfully');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        paymentMethodId
      }, 'Failed to delete payment method');
      throw error;
    }
  }

  /**
   * Handle recurring subscription payments that aren't pre-created in our database
   */
  private async handleMissingSubscriptionPayment(molliePayment: any): Promise<any | null> {
    try {
      // Check if this payment has a subscriptionId in Mollie
      if (!molliePayment.subscriptionId) {
        return null; // Not a subscription payment
      }

      // Find the subscription in our database
      const subscription = await prisma.subscription.findFirst({
        where: { mollieId: molliePayment.subscriptionId },
        include: { user: true }
      });

      if (!subscription) {
        logger.warn({ 
          molliePaymentId: molliePayment.id,
          mollieSubscriptionId: molliePayment.subscriptionId 
        }, 'Subscription payment received but subscription not found in database');
        return null;
      }

      // Create payment record for the recurring subscription payment
      const payment = await prisma.payment.create({
        data: {
          mollieId: molliePayment.id,
          status: this.mapMollieStatusToPaymentStatus(molliePayment.status) as any,
          amount: parseFloat(molliePayment.amount.value),
          currency: molliePayment.amount.currency,
          description: molliePayment.description || `${subscription.planType} plan subscription`,
          userId: subscription.userId,
          subscriptionId: subscription.id,
          mollieCustomerId: molliePayment.customerId,
          molliePaymentMethod: molliePayment.mandateId,
          mollieWebhookData: JSON.parse(JSON.stringify(molliePayment)),
          method: molliePayment.method
        }
      });

      logger.info({
        paymentId: payment.id,
        subscriptionId: subscription.id,
        userId: subscription.userId,
        molliePaymentId: molliePayment.id
      }, 'Created payment record for recurring subscription payment');

      return payment;

    } catch (error: any) {
      logger.error({
        error: error.message,
        molliePaymentId: molliePayment.id,
        mollieSubscriptionId: molliePayment.subscriptionId
      }, 'Failed to handle missing subscription payment');
      return null;
    }
  }

  /**
   * Process payment webhook from Mollie
   */
  async processPaymentWebhook(molliePaymentId: string) {
    try {
      // Get payment details from Mollie
      const molliePayment = await mollieService.getPayment(molliePaymentId);

      // Find payment in database
      let payment = await prisma.payment.findUnique({
        where: { mollieId: molliePaymentId },
        include: { user: true }
      });

      if (!payment) {
        // Check if this is a subscription payment (first payment or recurring)
        const subscriptionPayment = await this.handleMissingSubscriptionPayment(molliePayment);
        if (!subscriptionPayment) {
          logger.warn({ molliePaymentId }, 'Payment not found in database and not a subscription payment');
          return;
        }
        // Continue processing with the newly created payment
        const paymentWithUser = await prisma.payment.findUnique({
          where: { id: subscriptionPayment.id },
          include: { user: true }
        });
        if (!paymentWithUser) {
          logger.error({ molliePaymentId }, 'Created subscription payment but could not retrieve with user');
          return;
        }
        // Use the created payment for further processing
        payment = paymentWithUser;
      }

      // Update payment status
      const updateData: any = {
        status: this.mapMollieStatusToPaymentStatus(molliePayment.status),
        mollieWebhookData: JSON.parse(JSON.stringify(molliePayment)),
        method: molliePayment.method || null
      };

      // Set timestamps based on status
      if (molliePayment.status === 'paid' && molliePayment.paidAt) {
        updateData.paidAt = new Date(molliePayment.paidAt);
      } else if (molliePayment.status === 'cancelled' && molliePayment.cancelledAt) {
        updateData.cancelledAt = new Date(molliePayment.cancelledAt);
      } else if (molliePayment.status === 'expired' && molliePayment.expiredAt) {
        updateData.expiredAt = new Date(molliePayment.expiredAt);
      } else if (molliePayment.status === 'failed' && molliePayment.failedAt) {
        updateData.failedAt = new Date(molliePayment.failedAt);
        updateData.failureReason = molliePayment.details?.failureReason;
      }

      await prisma.payment.update({
        where: { id: payment.id },
        data: updateData
      });

      // If payment is successful, process based on type
      if (molliePayment.status === 'paid') {
        const metadata = molliePayment.metadata || {};
        const planType = metadata.planType as string;
        const paymentType = metadata.type as string;

        // Handle metadata-based payments (first payments, credits, mandates)
        logger.info({
          paymentId: payment.id,
          metadata,
          paymentType,
          planType,
          hasMetadata: !!metadata,
          metadataKeys: Object.keys(metadata || {}),
          subscriptionId: payment.subscriptionId
        }, 'Processing payment webhook - metadata analysis');

        if (metadata && Object.keys(metadata).length > 0) {
          if (paymentType === 'mandate') {
            // Mandate payment successful - sync payment methods
            try {
              await this.syncPaymentMethods(payment.userId);
              logger.info({ paymentId: payment.id }, 'Payment methods synced after mandate payment');
            } catch (syncError: any) {
              logger.error({ 
                paymentId: payment.id, 
                error: syncError.message 
              }, 'Failed to sync payment methods after mandate payment - continuing without sync');
              // Don't fail the entire webhook if payment method sync fails
            }
          } else if (paymentType === 'subscription_first_payment') {
            // First payment successful - create the actual subscription now
            logger.info({
              paymentId: payment.id,
              subscriptionId: payment.subscriptionId,
              hasSubscriptionId: !!payment.subscriptionId
            }, 'Processing subscription_first_payment');
            
            if (payment.subscriptionId) {
              try {
                logger.info({ paymentId: payment.id }, 'Calling createSubscriptionAfterFirstPayment');
                await this.createSubscriptionAfterFirstPayment(payment, molliePayment.metadata);
                logger.info({ 
                  paymentId: payment.id, 
                  subscriptionId: payment.subscriptionId 
                }, 'Subscription created after first payment success');
              } catch (subscriptionError: any) {
                logger.error({ 
                  paymentId: payment.id, 
                  subscriptionId: payment.subscriptionId,
                  error: subscriptionError.message,
                  stack: subscriptionError.stack
                }, 'Failed to create subscription after first payment');
              }
            } else {
              logger.warn({
                paymentId: payment.id
              }, 'subscription_first_payment but no subscriptionId found on payment');
            }
          } else if (paymentType === 'subscription_renewal') {
            // Process renewal payment for virtual subscriptions
            await OneOffSubscriptionService.processRenewalPayment(molliePayment.id);
            logger.info({
              paymentId: payment.id,
              molliePaymentId: molliePayment.id,
              subscriptionId: metadata.subscriptionId
            }, 'Processed subscription renewal payment');
          } else if (planType === 'credits') {
            // Process credit purchase
            await this.processCreditPurchase(payment.id, molliePayment.metadata);
          } else if (metadata.virtualSubscription && planType) {
            // Process one-off payment - create virtual subscription
            const billingPeriod = metadata.interval === 'yearly' ? 'yearly' : 'monthly';
            await OneOffSubscriptionService.createVirtualSubscription({
              userId: payment.userId,
              planType,
              amount: Number(payment.amount),
              currency: payment.currency,
              billingPeriod,
              paymentId: payment.id,
              description: `${planType} plan - ${metadata.interval} (One-off payment)`
            });
            
            // Upgrade user plan
            await this.upgradeUserPlan(payment.userId, planType, metadata.interval as string);
            
            // Send payment success notification
            await notify.payment.success(payment.userId, {
              amount: Number(payment.amount),
              currency: payment.currency,
              planType,
              method: 'one-off'
            });
            
            // Send subscription created notification
            await notify.subscription.created(payment.userId, {
              planType,
              interval: metadata.interval as string,
              amount: Number(payment.amount)
            });
            
            logger.info({
              paymentId: payment.id,
              userId: payment.userId,
              planType,
              billingPeriod
            }, 'Created virtual subscription for one-off payment');
          } else if (planType) {
            // Process plan upgrade
            await this.upgradeUserPlan(
              payment.userId,
              planType,
              metadata.interval as string
            );
          }
        }

        // For subscription payments without metadata, ensure user stays on correct plan
        if (payment.subscriptionId) {
          const subscription = await prisma.subscription.findUnique({
            where: { id: payment.subscriptionId }
          });
          
          if (subscription && subscription.status === 'ACTIVE') {
            await this.upgradeUserPlan(payment.userId, subscription.planType, subscription.interval);
            logger.info({
              paymentId: payment.id,
              subscriptionId: payment.subscriptionId,
              planType: subscription.planType
            }, 'User plan refreshed after subscription payment');
          }
        }

        // Generate invoice for all payments that create subscriptions or purchase credits
        // Include mandate payments for subscriptions (when user resubscribes with existing payment method)
        // Include virtual subscriptions (manual one-off payments for subscriptions)
        const shouldGenerateInvoice = planType === 'credits' || 
                                     paymentType === 'subscription_first_payment' || 
                                     paymentType === 'mandate' || 
                                     payment.subscriptionId ||
                                     metadata.virtualSubscription;
        
        if (shouldGenerateInvoice) {
          try {
            const { InvoiceGenerationService } = await import('../billing/invoice-generation.service.js');
            await InvoiceGenerationService.createInvoice(payment.id);
            
            if (payment.subscriptionId) {
              logger.info({ paymentId: payment.id, subscriptionId: payment.subscriptionId }, 'Invoice generated for subscription payment');
            } else {
              logger.info({ paymentId: payment.id }, 'Invoice generated for credit purchase');
            }
          } catch (invoiceError: any) {
            logger.error({
              paymentId: payment.id,
              subscriptionId: payment.subscriptionId,
              error: invoiceError.message
            }, 'Failed to generate invoice');
            // Don't fail the webhook if invoice generation fails
          }
        }
      }

      logger.info({
        paymentId: payment.id,
        mollieId: molliePaymentId,
        status: molliePayment.status,
        userId: payment.userId
      }, 'Payment webhook processed');

    } catch (error: any) {
      logger.error({
        error: error.message,
        molliePaymentId
      }, 'Failed to process payment webhook');
      throw error;
    }
  }

  /**
   * Upgrade user's plan after successful payment
   */
  private async upgradeUserPlan(userId: string, planType: string, interval: string) {
    try {
      const planConfig = PlanConfigService.getPlanConfig(planType);

      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: planType,

        }
      });

      // Sync plan changes to Postfix PostgreSQL for all user domains
      try {
        const { PostfixManager } = await import('../postfix-manager.js');
        const postfixManager = new PostfixManager();

        await postfixManager.updateUserDomainsPlan(userId, planType);
      } catch (error: any) {
        // Log error but don't fail the plan upgrade
        logger.error({
          error: error.message,
          userId,
          planType
        }, 'Failed to sync plan upgrade to Postfix PostgreSQL');
      }

      logger.info({
        userId,
        planType,
        interval,
        emailLimit: planConfig.monthlyEmailLimit
      }, 'User plan upgraded successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        planType
      }, 'Failed to upgrade user plan');
      throw error;
    }
  }

  /**
   * Process credit purchase after successful payment
   */
  private async processCreditPurchase(paymentId: string, metadata: any) {
    try {
      const creditAmount = parseInt(metadata.creditAmount);
      const userId = metadata.userId;

      if (!creditAmount || !userId) {
        throw new Error('Invalid credit purchase metadata');
      }

      // Purchase credits using the credit service
      const result = await CreditService.purchaseCredits(userId, creditAmount, paymentId);

      if (!result.success) {
        throw new Error(result.error || 'Failed to purchase credits');
      }

      logger.info({
        userId,
        creditAmount,
        paymentId,
        batchId: result.batchId,
        newBalance: result.newBalance
      }, 'Credit purchase processed successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        paymentId,
        metadata
      }, 'Failed to process credit purchase');
      throw error;
    }
  }

  /**
   * Map Mollie payment status to our payment status enum
   */
  private mapMollieStatusToPaymentStatus(mollieStatus: string): string {
    const statusMap: Record<string, string> = {
      'open': 'PENDING',
      'pending': 'PENDING',
      'paid': 'PAID',
      'cancelled': 'CANCELLED',
      'expired': 'EXPIRED',
      'failed': 'FAILED',
      'authorized': 'AUTHORIZED'
    };

    return statusMap[mollieStatus] || 'PENDING';
  }

  /**
   * Clean up expired pending payments (older than 15 minutes)
   */
  async cleanupExpiredPendingPayments() {
    try {
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
      
      const expiredPayments = await prisma.payment.findMany({
        where: {
          status: 'PENDING',
          createdAt: { lt: fifteenMinutesAgo }
        }
      });

      if (expiredPayments.length > 0) {
        await prisma.payment.updateMany({
          where: {
            status: 'PENDING',
            createdAt: { lt: fifteenMinutesAgo }
          },
          data: {
            status: 'EXPIRED',
            expiredAt: new Date()
          }
        });

        logger.info({ 
          expiredCount: expiredPayments.length 
        }, 'Cleaned up expired pending payments');
      }
    } catch (error: any) {
      logger.error({
        error: error.message
      }, 'Failed to cleanup expired pending payments');
    }
  }

  /**
   * Process subscription webhook from Mollie
   */
  async processSubscriptionWebhook(mollieSubscriptionId: string) {
    try {
      // Find subscription in database
      const subscription = await prisma.subscription.findUnique({
        where: { mollieId: mollieSubscriptionId },
        include: { user: true }
      });

      if (!subscription) {
        logger.warn({ mollieSubscriptionId }, 'Subscription not found in database');
        return;
      }

      // Get subscription details from Mollie
      const mollieSubscription = await mollieService.getSubscription(
        subscription.mollieCustomerId!,
        mollieSubscriptionId
      );

      // Update subscription status
      const previousNextPayment = subscription.nextPaymentDate;
      const updateData: any = {
        status: this.mapMollieStatusToSubscriptionStatus(mollieSubscription.status)
      };

      // For active subscriptions, refresh nextPaymentDate
      if (mollieSubscription.status === 'active') {
        updateData.nextPaymentDate = mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null;
      }

      // For cancelled subscriptions, preserve previously stored nextPaymentDate so we can allow access until end of period
      if (mollieSubscription.status === 'cancelled' && mollieSubscription.canceledAt) {
        updateData.cancelledAt = new Date(mollieSubscription.canceledAt);
        updateData.cancelReason = 'User cancelled';
        // Do NOT overwrite nextPaymentDate here
      }

      const updated = await prisma.subscription.update({
        where: { id: subscription.id },
        data: updateData
      });

      // If subscription is active, update user's plan
      if (mollieSubscription.status === 'active') {
        await this.upgradeUserPlan(subscription.userId, subscription.planType, subscription.interval);
        // If nextPaymentDate moved forward, log prolonged
        try {
          const prev = previousNextPayment?.getTime() || 0;
          const curr = updated.nextPaymentDate?.getTime() || 0;
          if (curr && curr > prev) {
            await prisma.auditLog.create({
              data: {
                action: 'subscription.prolonged',
                resourceType: 'subscription',
                resourceId: updated.id,
                metadata: {
                  userId: subscription.userId,
                  planType: subscription.planType,
                  previousNextPaymentDate: previousNextPayment?.toISOString() || null,
                  nextPaymentDate: updated.nextPaymentDate?.toISOString() || null
                },
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
              }
            });
          }
        } catch (e:any) {
          logger.warn({ error: e.message, subscriptionId: subscription.id }, 'Failed to log subscription.prolonged');
        }
      }

      // If subscription is cancelled, DO NOT downgrade immediately; worker will downgrade at end of current period

      logger.info({
        subscriptionId: subscription.id,
        mollieId: mollieSubscriptionId,
        status: mollieSubscription.status,
        userId: subscription.userId
      }, 'Subscription webhook processed');

    } catch (error: any) {
      logger.error({
        error: error.message,
        mollieSubscriptionId
      }, 'Failed to process subscription webhook');
      throw error;
    }
  }

  /**
   * Handle subscription cancellation
   */
  private async handleSubscriptionCancellation(userId: string, planType: string) {
    try {
      // For now, we'll downgrade to free plan
      // In future, we might implement grace periods
      await prisma.user.update({
        where: { id: userId },
        data: { planType: 'free' }
      });

      // Sync plan changes to Postfix PostgreSQL
      try {
        const { PostfixManager } = await import('../postfix-manager.js');
        const postfixManager = new PostfixManager();
        await postfixManager.updateUserDomainsPlan(userId, 'free');
      } catch (error: any) {
        logger.error({
          error: error.message,
          userId
        }, 'Failed to sync plan downgrade to Postfix PostgreSQL');
      }

      logger.info({
        userId,
        previousPlan: planType,
        newPlan: 'free'
      }, 'User plan downgraded after subscription cancellation');

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        planType
      }, 'Failed to handle subscription cancellation');
      throw error;
    }
  }

  /**
   * Cancel user's subscription
   */
  async cancelSubscription(userId: string, subscriptionId: string) {
    try {
      // Verify subscription belongs to user
      const subscription = await prisma.subscription.findFirst({
        where: { id: subscriptionId, userId }
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      if (subscription.status === 'CANCELLED') {
        throw new Error('Subscription already cancelled');
      }

      // Handle cancellation based on subscription type
      if (subscription.mollieId) {
        // Regular Mollie subscription - cancel with Mollie
        await mollieService.cancelSubscription(
          subscription.mollieCustomerId!,
          subscription.mollieId
        );
        logger.info({
          subscriptionId,
          mollieId: subscription.mollieId,
          userId
        }, 'Mollie subscription cancelled');
      } else {
        // Virtual subscription (manual payments) - only cancel locally
        logger.info({
          subscriptionId,
          userId
        }, 'Virtual subscription cancelled (no Mollie subscription to cancel)');
      }

      // Update subscription in database
      await prisma.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: 'CANCELLED',
          cancelledAt: new Date(),
          cancelReason: 'User cancelled'
        }
      });

      // Audit: subscription cancelled
      try {
        await prisma.auditLog.create({
          data: {
            action: 'subscription.cancelled',
            resourceType: 'subscription',
            resourceId: subscription.id,
            metadata: {
              userId,
              planType: subscription.planType,
              endDate: subscription.nextPaymentDate?.toISOString() || null
            },
            expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        });
      } catch (e:any) {
        logger.warn({ error: e.message, subscriptionId }, 'Failed to log subscription.cancelled');
      }

      // Send cancellation notification
      await notify.subscription.cancelled(userId, {
        planType: subscription.planType,
        endDate: subscription.nextPaymentDate?.toISOString()
      });

      logger.info({
        subscriptionId,
        mollieId: subscription.mollieId || 'virtual',
        userId
      }, 'Subscription cancelled successfully');

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        subscriptionId
      }, 'Failed to cancel subscription');
      throw error;
    }
  }

  /**
   * Get user's active subscriptions
   */
  async getUserSubscriptions(userId: string) {
    try {
      const subscriptions = await prisma.subscription.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      });

      return subscriptions.map(sub => ({
        id: sub.id,
        mollieId: sub.mollieId,
        status: sub.status.toLowerCase(),
        planType: sub.planType,
        interval: sub.interval,
        amount: {
          value: sub.amount.toString(),
          currency: sub.currency
        },
        description: sub.description,
        startDate: sub.startDate?.toISOString(),
        nextPaymentDate: sub.nextPaymentDate?.toISOString(),
        cancelledAt: sub.cancelledAt?.toISOString(),
        cancelReason: sub.cancelReason,
        isGrandfathered: sub.isGrandfathered,
        grandfatheredPrice: sub.grandfatheredPrice?.toString(),
        grandfatheringReason: sub.grandfatheringReason,
        createdAt: sub.createdAt.toISOString()
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user subscriptions');
      throw error;
    }
  }

  /**
   * Map Mollie subscription status to our subscription status enum
   */
  private mapMollieStatusToSubscriptionStatus(mollieStatus: string): string {
    const statusMap: Record<string, string> = {
      'pending': 'PENDING',
      'active': 'ACTIVE',
      'cancelled': 'CANCELLED',
      'suspended': 'SUSPENDED',
      'completed': 'COMPLETED'
    };

    return statusMap[mollieStatus] || 'PENDING';
  }

  /**
   * Get user's payment history
   */
  async getUserPayments(userId: string, limit: number = 10) {
    try {
      const payments = await prisma.payment.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          mollieId: true,
          status: true,
          amount: true,
          currency: true,
          description: true,
          method: true,
          paidAt: true,
          createdAt: true,
          invoices: { select: { id: true } }
        }
      });

      return payments.map(payment => ({
        id: payment.id,
        amount: {
          value: payment.amount.toString(),
          currency: payment.currency
        },
        description: payment.description || '',
        status: payment.status.toLowerCase(),
        method: payment.method,
        paidAt: payment.paidAt?.toISOString(),
        createdAt: payment.createdAt.toISOString(),
        invoiceId: payment.invoices?.[0]?.id || null
      }));
    } catch (error: any) {
      logger.error({
        error: error.message,
        userId
      }, 'Failed to get user payments');
      throw error;
    }
  }

  /**
   * Create actual Mollie subscription after first payment succeeds
   * This creates ONLY the subscription, no additional payments
   */
  private async createSubscriptionAfterFirstPayment(payment: any, metadata: any) {
    try {
      const { userId, planType, interval, customerId } = metadata;
      
      logger.info({
        paymentId: payment.id,
        userId,
        planType,
        interval,
        customerId
      }, 'Starting createSubscriptionAfterFirstPayment');
      
      // Get subscription record
      const subscription = await prisma.subscription.findUnique({
        where: { id: payment.subscriptionId }
      });

      if (!subscription) {
        throw new Error('Subscription record not found');
      }

      // After a first payment, a mandate should be created automatically
      // Wait a moment and check for mandates
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      
      logger.info({ customerId }, 'Checking for mandates after first payment');
      
      // Get mandates to ensure one was created
      const mandates = await mollieService.getCustomerMandates(customerId);
      const validMandate = mandates.find(m => m.status === 'valid' || m.status === 'pending');
      
      if (!validMandate) {
        logger.warn({
          customerId,
          paymentId: payment.id,
          mandatesFound: mandates.length,
          mandateStatuses: mandates.map(m => m.status)
        }, 'No valid mandate found after first payment - proceeding anyway');
      }

      // Calculate start date: today + 1 period (since first payment already charged today)
      const today = new Date();
      let startDate: string;
      if (interval === 'monthly') {
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
        startDate = nextMonth.toISOString().split('T')[0];
      } else { // yearly
        const nextYear = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());
        startDate = nextYear.toISOString().split('T')[0];
      }
      
      logger.info({
        customerId,
        interval,
        calculatedStartDate: startDate
      }, 'Creating subscription with startDate = today + 1 period');

      // Create the actual Mollie subscription with startDate = today + 1 period
      // This prevents double charging since first payment already processed
      const mollieSubscription = await mollieService.createSubscriptionWithStoredMethod({
        customerId,
        amount: {
          value: payment.amount.toString(),
          currency: payment.currency
        },
        interval: interval === 'monthly' ? '1 month' : '1 year', // Convert to Mollie format
        description: subscription.description,
        startDate, // Start next period to avoid double charge
        webhookUrl: env.MOLLIE_WEBHOOK_URL || `${env.URL}/api/webhooks/mollie/subscription`,
        metadata: {
          userId,
          planType,
          subscriptionId: subscription.id
        }
      });

      // Update subscription record with actual Mollie subscription ID
      const updated = await prisma.subscription.update({
        where: { id: payment.subscriptionId },
        data: {
          mollieId: mollieSubscription.id,
          status: 'ACTIVE',
          nextPaymentDate: mollieSubscription.nextPaymentDate ? new Date(mollieSubscription.nextPaymentDate) : null
        }
      });

      // Audit: subscription started after first payment
      try {
        await prisma.auditLog.create({
          data: {
            action: 'subscription.started',
            resourceType: 'subscription',
            resourceId: updated.id,
            metadata: {
              userId,
              planType,
              interval,
              mollieSubscriptionId: mollieSubscription.id
            },
            expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        });
      } catch (e:any) {
        logger.warn({ error: e.message, subscriptionId: updated.id }, 'Failed to log subscription.started');
      }

      // Update user's plan
      await this.upgradeUserPlan(userId, planType, interval);

      logger.info({
        subscriptionId: payment.subscriptionId,
        mollieId: mollieSubscription.id,
        paymentId: payment.id,
        userId,
        planType,
        nextPaymentDate: mollieSubscription.nextPaymentDate
      }, 'Subscription created successfully after first payment - NO DUPLICATE CHARGES');

    } catch (error: any) {
      logger.error({
        error: error.message,
        stack: error.stack,
        paymentId: payment.id,
        metadata
      }, 'Failed to create subscription after first payment');
      throw error;
    }
  }

}

export const paymentWorkflowService = new PaymentWorkflowService();

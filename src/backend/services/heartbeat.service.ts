import { logger } from '../utils/logger.js';
import { env } from '../config/env.js';
import { schedulerService } from './scheduler.service.js';

interface HeartbeatPayload {
  status: 'ok' | 'degraded' | 'error';
  timestamp: string;
  service: string;
  version?: string;
  database: 'connected' | 'disconnected';
  scheduler: 'healthy' | 'unhealthy';
  uptime: number;
}

/**
 * Service for sending heartbeat signals to external monitoring services
 */
export class HeartbeatService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly HEARTBEAT_INTERVAL_MS: number;
  private readonly HEARTBEAT_URL: string | undefined;
  private readonly SERVICE_NAME = 'emailconnect-backend';
  private readonly startTime = Date.now();

  constructor() {
    this.HEARTBEAT_INTERVAL_MS = env.HEARTBEAT_INTERVAL_MINUTES * 60 * 1000;
    this.HEARTBEAT_URL = env.HEARTBEAT_URL;
  }

  /**
   * Start sending heartbeat signals
   */
  start(): void {
    if (!this.HEARTBEAT_URL) {
      logger.info('Heartbeat service disabled - no HEARTBEAT_URL configured');
      return;
    }

    if (this.intervalId) {
      logger.warn('Heartbeat service already running');
      return;
    }

    logger.info({
      heartbeatUrl: this.HEARTBEAT_URL,
      intervalMinutes: env.HEARTBEAT_INTERVAL_MINUTES
    }, 'Starting heartbeat service');

    // Send initial heartbeat
    this.sendHeartbeat();

    // Schedule recurring heartbeats
    this.intervalId = setInterval(() => {
      this.sendHeartbeat();
    }, this.HEARTBEAT_INTERVAL_MS);
  }

  /**
   * Stop sending heartbeat signals
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Heartbeat service stopped');
    }
  }

  /**
   * Send a single heartbeat signal
   */
  async sendHeartbeat(): Promise<void> {
    if (!this.HEARTBEAT_URL) {
      return;
    }

    try {
      const payload = await this.buildHeartbeatPayload();
      
      logger.debug({
        heartbeatUrl: this.HEARTBEAT_URL,
        payload
      }, 'Sending heartbeat');

      const response = await fetch(this.HEARTBEAT_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `${this.SERVICE_NAME}/1.0`
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      logger.debug({
        heartbeatUrl: this.HEARTBEAT_URL,
        status: payload.status,
        responseStatus: response.status
      }, 'Heartbeat sent successfully');

    } catch (error: any) {
      logger.error({
        error: error.message,
        heartbeatUrl: this.HEARTBEAT_URL
      }, 'Failed to send heartbeat');
    }
  }

  /**
   * Build heartbeat payload with current system status
   */
  private async buildHeartbeatPayload(): Promise<HeartbeatPayload> {
    try {
      // Check database health
      const dbHealthy = await this.checkDatabaseHealth();
      
      // Check scheduler health
      const schedulerHealthy = await schedulerService.isHealthy();
      
      // Determine overall status
      const allHealthy = dbHealthy && schedulerHealthy;
      const status = allHealthy ? 'ok' : 'degraded';

      // Calculate uptime in seconds
      const uptime = Math.floor((Date.now() - this.startTime) / 1000);

      return {
        status,
        timestamp: new Date().toISOString(),
        service: this.SERVICE_NAME,
        version: process.env.npm_package_version || '1.0.0',
        database: dbHealthy ? 'connected' : 'disconnected',
        scheduler: schedulerHealthy ? 'healthy' : 'unhealthy',
        uptime
      };
    } catch (error: any) {
      logger.error({
        error: error.message
      }, 'Failed to build heartbeat payload');

      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: this.SERVICE_NAME,
        database: 'disconnected',
        scheduler: 'unhealthy',
        uptime: Math.floor((Date.now() - this.startTime) / 1000)
      };
    }
  }

  /**
   * Check database connectivity
   */
  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      const { prisma } = await import('../lib/prisma.js');
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error: any) {
      logger.debug({
        error: error.message
      }, 'Database health check failed');
      return false;
    }
  }

  /**
   * Get current heartbeat service status
   */
  getStatus(): {
    enabled: boolean;
    running: boolean;
    heartbeatUrl: string | undefined;
    intervalMinutes: number;
    uptime: number;
  } {
    return {
      enabled: !!this.HEARTBEAT_URL,
      running: !!this.intervalId,
      heartbeatUrl: this.HEARTBEAT_URL,
      intervalMinutes: env.HEARTBEAT_INTERVAL_MINUTES,
      uptime: Math.floor((Date.now() - this.startTime) / 1000)
    };
  }

  /**
   * Manual trigger for testing
   */
  async triggerManually(): Promise<void> {
    logger.info('Manually triggering heartbeat');
    await this.sendHeartbeat();
  }
}

// Export singleton instance
export const heartbeatService = new HeartbeatService();
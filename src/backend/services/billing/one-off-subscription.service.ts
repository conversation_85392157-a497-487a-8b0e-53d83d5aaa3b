import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { mollieService } from '../payment/mollie.service.js';
import { notify } from '../notifications/index.js';

/**
 * Service for managing "virtual" subscriptions for one-off payment users
 * 
 * These users pay manually via bank transfer, iDEAL, or other non-recurring methods.
 * We create subscription records without mollieId to track their renewal dates
 * and send renewal notifications before expiry.
 */
export class OneOffSubscriptionService {
  
  /**
   * Create a virtual subscription for a one-off payment user
   */
  static async createVirtualSubscription(params: {
    userId: string;
    planType: string;
    amount: number;
    currency: string;
    billingPeriod: 'monthly' | 'yearly';
    paymentId: string; // Reference to the initial payment
    description?: string;
  }) {
    try {
      const { userId, planType, amount, currency, billingPeriod, paymentId, description } = params;
      
      // Calculate next payment date
      const now = new Date();
      const nextPaymentDate = new Date(now);
      if (billingPeriod === 'monthly') {
        nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);
      } else {
        nextPaymentDate.setFullYear(nextPaymentDate.getFullYear() + 1);
      }

      // Create virtual subscription (virtual mollieId = virtual subscription)
      const subscription = await prisma.subscription.create({
        data: {
          userId,
          // Virtual mollieId to mark as a virtual subscription
          mollieId: `virtual_${userId}_${Date.now()}`,
          status: 'ACTIVE',
          planType,
          interval: billingPeriod,
          amount,
          currency,
          description: description || `${planType.charAt(0).toUpperCase() + planType.slice(1)} plan - Manual renewal`,
          startDate: now,
          nextPaymentDate
        },
        include: {
          user: true
        }
      });

      // Connect the initial payment to the subscription
      await prisma.payment.update({
        where: { id: paymentId },
        data: { subscriptionId: subscription.id }
      });

      logger.info({
        userId,
        subscriptionId: subscription.id,
        planType,
        amount,
        billingPeriod,
        nextPaymentDate: nextPaymentDate.toISOString()
      }, 'Created virtual subscription for one-off payment user');

      return subscription;
    } catch (error) {
      logger.error({ error, userId: params.userId }, 'Failed to create virtual subscription');
      throw error;
    }
  }

  /**
   * Create renewal payment for virtual subscription users
   * Uses Mollie Payments API to create a one-off payment with checkout URL
   */
  static async createRenewalPayment(subscriptionId: string, options?: {
    notificationDays?: number; // Days before expiry (default: 3)
    preferredMethods?: string[]; // Preferred payment methods
  }) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId },
        include: {
          user: true
        }
      });

      if (!subscription) {
        throw new Error(`Subscription not found: ${subscriptionId}`);
      }

      if (subscription.mollieId) {
        throw new Error('Cannot create renewal payment for Mollie-managed subscription');
      }

      if (!subscription.nextPaymentDate) {
        throw new Error('Subscription has no next payment date');
      }

      const notificationDays = options?.notificationDays || 3;
      const now = new Date();
      const notificationDate = new Date(subscription.nextPaymentDate);
      notificationDate.setDate(notificationDate.getDate() - notificationDays);

      // Check if it's time to send renewal notification
      if (now < notificationDate) {
        logger.debug({
          subscriptionId,
          nextPaymentDate: subscription.nextPaymentDate.toISOString(),
          notificationDate: notificationDate.toISOString(),
          currentDate: now.toISOString()
        }, 'Not yet time for renewal notification');
        return null;
      }

      // Create renewal payment with Mollie
      const payment = await mollieService.createPayment({
        amount: {
          value: subscription.amount.toFixed(2),
          currency: subscription.currency
        },
        description: `Renewal: ${subscription.description}`,
        redirectUrl: `${process.env.URL}/settings/billing?renewal=success`,
        webhookUrl: `${process.env.URL}/api/webhooks/mollie/payment`,
        metadata: {
          type: 'subscription_renewal',
          subscriptionId: subscription.id,
          userId: subscription.userId,
          planType: subscription.planType,
          billingPeriod: subscription.interval
        },
        // Allow multiple payment methods for flexibility
        methods: options?.preferredMethods
      });

      // Create payment record
      const paymentRecord = await prisma.payment.create({
        data: {
          mollieId: payment.id,
          userId: subscription.userId,
          subscriptionId: subscription.id,
          status: 'PENDING',
          amount: subscription.amount,
          currency: subscription.currency,
          description: `Renewal: ${subscription.description}`,
          method: null // Will be updated when payment is completed
        }
      });

      // Send renewal notification via the new notification system
      await notify.payment.reminder(subscription.userId, {
        amount: Number(subscription.amount),
        currency: subscription.currency,
        planType: subscription.planType,
        expiryDate: subscription.nextPaymentDate.toISOString(),
        checkoutUrl: payment.getCheckoutUrl()
      });

      logger.info({
        subscriptionId,
        paymentId: payment.id,
        userId: subscription.userId,
        amount: subscription.amount,
        checkoutUrl: payment.getCheckoutUrl(),
        expiryDate: subscription.nextPaymentDate.toISOString()
      }, 'Created renewal payment and sent notification');

      return {
        payment: paymentRecord,
        molliePayment: payment,
        checkoutUrl: payment.getCheckoutUrl()
      };
    } catch (error) {
      logger.error({ error, subscriptionId }, 'Failed to create renewal payment');
      throw error;
    }
  }

  /**
   * Process completed renewal payment
   * Extends the subscription for another period
   */
  static async processRenewalPayment(paymentId: string) {
    try {
      const payment = await prisma.payment.findUnique({
        where: { mollieId: paymentId },
        include: {
          subscription: true,
          user: true
        }
      });

      if (!payment) {
        throw new Error(`Payment not found: ${paymentId}`);
      }

      if (!payment.subscription) {
        logger.warn({ paymentId }, 'Renewal payment has no associated subscription');
        return;
      }

      const subscription = payment.subscription;

      // Verify this is a virtual subscription
      if (subscription.mollieId) {
        logger.warn({ paymentId, subscriptionId: subscription.id }, 'Attempted to process renewal for Mollie-managed subscription');
        return;
      }

      // Extend subscription period
      const currentNextPayment = subscription.nextPaymentDate || new Date();
      const newNextPayment = new Date(currentNextPayment);
      
      if (subscription.interval === 'monthly') {
        newNextPayment.setMonth(newNextPayment.getMonth() + 1);
      } else if (subscription.interval === 'yearly') {
        newNextPayment.setFullYear(newNextPayment.getFullYear() + 1);
      }

      // Update subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          nextPaymentDate: newNextPayment,
          updatedAt: new Date()
        }
      });

      // Ensure user plan type is synced
      await prisma.user.update({
        where: { id: subscription.userId },
        data: { planType: subscription.planType }
      });

      // Send renewal success notification
      await notify.subscription.renewed(subscription.userId, {
        planType: subscription.planType,
        nextPaymentDate: newNextPayment.toISOString(),
        amount: Number(payment.amount)
      });

      logger.info({
        paymentId,
        subscriptionId: subscription.id,
        userId: subscription.userId,
        planType: subscription.planType,
        oldExpiryDate: currentNextPayment.toISOString(),
        newExpiryDate: newNextPayment.toISOString()
      }, 'Successfully processed renewal payment and extended subscription');

      return updatedSubscription;
    } catch (error) {
      logger.error({ error, paymentId }, 'Failed to process renewal payment');
      throw error;
    }
  }

  /**
   * Find virtual subscriptions that need renewal notifications
   * Called by scheduled job
   */
  static async findSubscriptionsNeedingRenewal(notificationDays: number = 3) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() + notificationDays);

      const subscriptions = await prisma.subscription.findMany({
        where: {
          mollieId: null, // Virtual subscriptions only
          status: 'ACTIVE',
          nextPaymentDate: {
            lte: cutoffDate,
            gte: new Date() // Not already expired
          }
        },
        include: {
          user: true,
          payments: {
            where: {
              status: 'PENDING',
              description: {
                startsWith: 'Renewal:'
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      // Filter out subscriptions that already have pending renewal payments
      const needingRenewal = subscriptions.filter(sub => 
        sub.payments.length === 0
      );

      logger.info({
        totalVirtualSubscriptions: subscriptions.length,
        needingRenewal: needingRenewal.length,
        cutoffDate: cutoffDate.toISOString()
      }, 'Found virtual subscriptions needing renewal');

      return needingRenewal;
    } catch (error) {
      logger.error({ error }, 'Failed to find subscriptions needing renewal');
      throw error;
    }
  }

  /**
   * Convert existing one-off payment user to virtual subscription
   * For migrating existing customers
   */
  static async migrateExistingCustomer(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscriptions: true,
          payments: {
            where: { status: 'PAID' },
            orderBy: { paidAt: 'desc' },
            take: 1
          }
        }
      });

      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Skip if user already has subscriptions
      if (user.subscriptions.length > 0) {
        logger.info({ userId }, 'User already has subscriptions - skipping migration');
        return null;
      }

      // Skip if user is on free plan
      if (user.planType === 'free') {
        logger.info({ userId }, 'User is on free plan - skipping migration');
        return null;
      }

      // Check if user has a completed payment
      const lastPayment = user.payments[0];
      if (!lastPayment) {
        logger.warn({ userId }, 'User has no completed payments - cannot migrate');
        return null;
      }

      // Determine billing period from payment amount using plan config
      const { PlanConfigService } = await import('./plan-config.service.js');
      const planConfig = PlanConfigService.getPlanConfig(user.planType);
      const amount = Number(lastPayment.amount);
      let billingPeriod: 'monthly' | 'yearly' = 'monthly';
      
      if (planConfig.price) {
        // If amount is closer to yearly price, assume yearly billing
        const monthlyDiff = Math.abs(amount - planConfig.price.monthly);
        const yearlyDiff = Math.abs(amount - planConfig.price.yearly);
        
        if (yearlyDiff < monthlyDiff) {
          billingPeriod = 'yearly';
        }
      }

      // Create virtual subscription starting from last payment date
      const subscription = await this.createVirtualSubscription({
        userId,
        planType: user.planType,
        amount,
        currency: lastPayment.currency,
        billingPeriod,
        paymentId: lastPayment.id,
        description: `Migrated ${user.planType} plan - Manual renewal`
      });

      logger.info({
        userId,
        subscriptionId: subscription.id,
        planType: user.planType,
        amount,
        billingPeriod,
        basePaymentId: lastPayment.id
      }, 'Successfully migrated existing customer to virtual subscription');

      return subscription;
    } catch (error) {
      logger.error({ error, userId }, 'Failed to migrate existing customer');
      throw error;
    }
  }
}
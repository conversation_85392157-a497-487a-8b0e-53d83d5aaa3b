import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService, PlanType } from './plan-config.service.js';

/**
 * Service for determining a user's effective plan and subscription status
 * This replaces direct checks against user.planType with proper subscription status checks
 */
export class SubscriptionStatusService {
  /**
   * Get the user's effective plan type based on active subscription or trial
   * This should be used instead of directly checking user.planType
   */
  static async getEffectivePlanType(userId: string): Promise<PlanType> {
    try {
      // Check for active subscription first
      const activeSubscription = await prisma.subscription.findFirst({
        where: {
          userId: userId,
          status: 'ACTIVE'
        },
        orderBy: { createdAt: 'desc' }
      });

      if (activeSubscription) {
        return activeSubscription.planType as PlanType;
      }

      // No active subscription - check for active trial
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          trialStartedAt: true,
          trialEndsAt: true
        }
      });

      if (user) {
        // Check if user has an active trial
        const now = new Date();
        const hasActiveTrial = user.trialStartedAt && 
                              user.trialEndsAt && 
                              user.trialEndsAt > now && 
                              user.planType === 'pro';

        if (hasActiveTrial) {
          logger.debug({ userId, trialEndsAt: user.trialEndsAt, planType: user.planType }, 'User has active trial - returning pro plan');
          return 'pro'; // Active trial = pro plan
        } else {
          logger.debug({ userId, trialEndsAt: user.trialEndsAt, planType: user.planType, now }, 'User does not have active trial');
        }

        // Return the user's current plan type (should be 'free' if no active subscription/trial)
        return (user.planType as PlanType) || 'free';
      }

      return 'free';
    } catch (error) {
      logger.error({ userId, error }, 'Failed to determine effective plan type');
      // Fall back to user.planType for safety
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });
      return (user?.planType as PlanType) || 'free';
    }
  }

  /**
   * Check if user has an active Pro or Enterprise subscription
   */
  static async hasActiveProSubscription(userId: string): Promise<boolean> {
    const effectivePlan = await this.getEffectivePlanType(userId);
    return effectivePlan === 'pro' || effectivePlan === 'enterprise';
  }

  /**
   * Check if user has access to a specific feature based on their active subscription
   * Use PlanConfigService.userHasPermission() directly with the effectivePlan if you need specific permission checks
   */

  /**
   * Get the user's current subscription status for billing UI
   */
  static async getSubscriptionStatus(userId: string): Promise<{
    effectivePlan: PlanType;
    hasActiveSubscription: boolean;
    subscription?: any;
    hasActiveTrial?: boolean;
    trialEndsAt?: Date;
  }> {
    try {
      const activeSubscription = await prisma.subscription.findFirst({
        where: {
          userId: userId,
          status: 'ACTIVE'
        },
        orderBy: { createdAt: 'desc' }
      });

      // If there's an active subscription, use that
      if (activeSubscription) {
        return {
          effectivePlan: activeSubscription.planType as PlanType,
          hasActiveSubscription: true,
          subscription: activeSubscription,
          hasActiveTrial: false
        };
      }

      // No active subscription - check for trial
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          trialStartedAt: true,
          trialEndsAt: true
        }
      });

      if (user) {
        const now = new Date();
        const hasActiveTrial = user.trialStartedAt && 
                              user.trialEndsAt && 
                              user.trialEndsAt > now && 
                              user.planType === 'pro';

        return {
          effectivePlan: hasActiveTrial ? 'pro' : ((user.planType as PlanType) || 'free'),
          hasActiveSubscription: false,
          hasActiveTrial: !!hasActiveTrial,
          trialEndsAt: hasActiveTrial ? user.trialEndsAt : undefined
        };
      }

      return {
        effectivePlan: 'free',
        hasActiveSubscription: false,
        hasActiveTrial: false
      };
    } catch (error) {
      logger.error({ userId, error }, 'Failed to get subscription status');
      return {
        effectivePlan: 'free',
        hasActiveSubscription: false,
        hasActiveTrial: false
      };
    }
  }

  /**
   * Update user.planType to match their effective plan
   * This should be called after subscription changes to keep the fields in sync
   */
  static async syncUserPlanType(userId: string): Promise<void> {
    try {
      const effectivePlan = await this.getEffectivePlanType(userId);
      
      await prisma.user.update({
        where: { id: userId },
        data: { planType: effectivePlan }
      });

      logger.info({ userId, effectivePlan }, 'Synced user.planType with effective plan');
    } catch (error) {
      logger.error({ userId, error }, 'Failed to sync user.planType with effective plan');
    }
  }
}
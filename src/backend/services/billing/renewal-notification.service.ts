import { logger } from '../../utils/logger.js';
import { prisma } from '../../lib/prisma.js';
import { notify } from '../notifications/index.js';
import { createSignedToken, buildSignedUrl } from '../security/signed-link.util.js';

/**
 * Scheduled service that sends renewal notifications for virtual subscriptions
 * Runs on a daily cadence. Sends at T-3 and T-1 days only (max two emails).
 * Dedupe via notifications table (Notification.type = PAYMENT_REMINDER) per subscription.
 */
export class RenewalNotificationService {
  private intervalId: NodeJS.Timeout | null = null;
  // Interval unused when run via global scheduler; kept for backward compatibility
  private readonly CHECK_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours

  start(): void {
    if (this.intervalId) {
      logger.warn('Renewal notification service already running');
      return;
    }

    logger.info('Starting renewal notification service (daily cadence)');

    // Run immediately, then on daily interval
    this.checkAndNotifyRenewals();
    this.intervalId = setInterval(() => this.checkAndNotifyRenewals(), this.CHECK_INTERVAL_MS);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Renewal notification service stopped');
    }
  }

  /**
   * Find eligible virtual subscriptions and send at most two reminders (T-3 and T-1).
   * Uses notifications table for dedupe in the last 30 hours.
   */
  async checkAndNotifyRenewals(): Promise<void> {
    try {
      logger.debug('Running renewal notification daily check');

      const now = new Date();
      const inOneDay = new Date(now);
      inOneDay.setDate(inOneDay.getDate() + 1);
      const inThreeDays = new Date(now);
      inThreeDays.setDate(inThreeDays.getDate() + 3);

      // Get active virtual subscriptions that expire within 1..3 days (inclusive)
      const subs = await prisma.subscription.findMany({
        where: {
          mollieId: null,
          status: 'ACTIVE',
          nextPaymentDate: {
            gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
            lte: inThreeDays
          }
        },
        select: {
          id: true,
          userId: true,
          planType: true,
          amount: true,
          currency: true,
          interval: true,
          nextPaymentDate: true
        }
      });

      if (subs.length === 0) {
        logger.debug('No virtual subscriptions in renewal window');
        return;
      }

      let sentCount = 0;

      for (const sub of subs) {
        if (!sub.nextPaymentDate) continue;
        const daysUntil = Math.ceil((sub.nextPaymentDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
        if (daysUntil !== 3 && daysUntil !== 1) continue; // Only T-3 and T-1

        // Dedupe: check notifications in last 30 hours for this subscription
        const thirtyHoursAgo = new Date(Date.now() - 30 * 60 * 60 * 1000);
        const existing = await prisma.notification.findFirst({
          where: {
            userId: sub.userId,
            type: 'PAYMENT_REMINDER',
            createdAt: { gte: thirtyHoursAgo },
            OR: [
              { data: { path: ['subscriptionId'], equals: sub.id } },
              { AND: [{ userId: sub.userId }] }
            ]
          },
          select: { id: true }
        });
        if (existing) {
          logger.debug({ subscriptionId: sub.id, daysUntil }, 'Skipping reminder - recent one already exists');
          continue;
        }

        // Build first-party renewal link with signed token (public endpoint)
        const ttlSec = Math.max(60, Math.floor((sub.nextPaymentDate.getTime() - Date.now()) / 1000));
        const token = createSignedToken({ userId: sub.userId, subscriptionId: sub.id }, { purpose: 'renewal', ttlSeconds: ttlSec, useReplayProtection: true });
        const renewalUrl = buildSignedUrl('/api/payments/renew-by-token', token);

        // Send reminder via unified notification system
        await notify.payment.reminder(sub.userId, {
          amount: Number(sub.amount),
          currency: sub.currency,
          planType: sub.planType,
          expiryDate: sub.nextPaymentDate.toISOString(),
          checkoutUrl: renewalUrl,
          subscriptionId: sub.id,
        });

        // Also store subscriptionId in the notification data for exact dedupe later
        try {
          await prisma.notification.updateMany({
            where: { userId: sub.userId, type: 'PAYMENT_REMINDER' },
            data: { data: { set: { subscriptionId: sub.id } } }
          });
        } catch (e) {
          // best effort; ignore
        }

        sentCount += 1;
        logger.info({ subscriptionId: sub.id, userId: sub.userId, daysUntil }, 'Sent renewal reminder');
      }

      logger.info({ total: subs.length, sent: sentCount }, 'Renewal reminder job completed');

    } catch (error) {
      logger.error({ error }, 'Renewal notification service check failed');
    }
  }

  async triggerManually(): Promise<void> {
    logger.info('Manually triggering renewal notification check');
    await this.checkAndNotifyRenewals();
  }

  getStatus(): {
    running: boolean;
    checkInterval: number;
    notificationDays?: number;
  } {
    return {
      running: !!this.intervalId,
      checkInterval: this.CHECK_INTERVAL_MS,
      notificationDays: undefined
    };
  }
}

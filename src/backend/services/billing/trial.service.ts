import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { notify } from '../notifications/index.js';

export class TrialService {
  static readonly TRIAL_DURATION_DAYS = 7;
  static readonly TRIAL_PLAN = 'pro';

  /**
   * Start a trial for a user
   */
  static async startTrial(userId: string, adminEmail?: string, days: number = this.TRIAL_DURATION_DAYS): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          planType: true,
          trialStartedAt: true,
          trialEndsAt: true,
          subscriptions: {
            where: {
              status: 'ACTIVE'
            }
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if user already has an active subscription
      if (user.subscriptions.length > 0) {
        throw new Error('User already has an active subscription');
      }

      // Check if user already used their trial
      if (user.trialStartedAt) {
        throw new Error('User has already used their trial');
      }

      const now = new Date();
      const trialEndsAt = new Date(now);
      trialEndsAt.setDate(trialEndsAt.getDate() + days);

      // Start the trial
      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: this.TRIAL_PLAN,
          trialStartedAt: now,
          trialEndsAt,
          trialActivatedBy: adminEmail || null
        }
      });

      logger.info({ userId, adminEmail, trialEndsAt, days }, 'Trial started for user');

      // Send trial start notification
      await notify.trial.started(userId, {
        days,
        activatedBy: adminEmail || 'user',
        expiryDate: trialEndsAt.toISOString()
      });

      return true;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to start trial');
      throw error;
    }
  }

  /**
   * Reset a user's trial eligibility (allows them to start a new trial)
   */
  static async resetTrial(userId: string, adminEmail?: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          trialStartedAt: true,
          planType: true,
          subscriptions: {
            where: {
              status: 'ACTIVE'
            }
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Reset trial fields to allow a new trial
      await prisma.user.update({
        where: { id: userId },
        data: {
          trialStartedAt: null,
          trialEndsAt: null,
          trialActivatedBy: null,
          // Reset to free plan if they don't have an active subscription
          planType: user.subscriptions.length > 0 ? 'pro' : 'free'
        }
      });

      logger.info({ userId, adminEmail }, 'Trial reset for user');

      return true;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to reset trial');
      throw error;
    }
  }

  /**
   * End a trial for a user
   */
  static async endTrial(userId: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          trialStartedAt: true,
          trialEndsAt: true,
          subscriptions: {
            where: {
              status: 'ACTIVE'
            }
          }
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // If user has an active subscription, keep their plan
      const newPlanType = user.subscriptions.length > 0 ? 'pro' : 'free';

      // End the trial
      await prisma.user.update({
        where: { id: userId },
        data: {
          planType: newPlanType,
          trialEndsAt: new Date() // Set to now to indicate trial has ended
        }
      });

      logger.info({ userId, newPlanType }, 'Trial ended for user');
      return true;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to end trial');
      throw error;
    }
  }

  /**
   * Check if a user is on trial
   */
  static async isOnTrial(userId: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          trialStartedAt: true,
          trialEndsAt: true,
          planType: true
        }
      });

      if (!user || !user.trialStartedAt || !user.trialEndsAt) {
        return false;
      }

      const now = new Date();
      return user.planType === this.TRIAL_PLAN && 
             user.trialEndsAt > now;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to check trial status');
      return false;
    }
  }

  /**
   * Get trial information for a user
   */
  static async getTrialInfo(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          trialStartedAt: true,
          trialEndsAt: true,
          trialActivatedBy: true,
          planType: true
        }
      });

      if (!user || !user.trialStartedAt) {
        return null;
      }

      const now = new Date();
      const isActive = user.trialEndsAt ? user.trialEndsAt > now : false;
      const daysRemaining = user.trialEndsAt 
        ? Math.max(0, Math.ceil((user.trialEndsAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
        : 0;

      return {
        started: user.trialStartedAt,
        ends: user.trialEndsAt,
        activatedBy: user.trialActivatedBy,
        isActive,
        daysRemaining,
        hasUsedTrial: true
      };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get trial info');
      return null;
    }
  }

  /**
   * Send trial ending notifications (3 days, 1 day before expiry)
   */
  static async notifyTrialEndings(): Promise<number> {
    try {
      const now = new Date();
      const threeDaysFromNow = new Date(now);
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
      const oneDayFromNow = new Date(now);
      oneDayFromNow.setDate(oneDayFromNow.getDate() + 1);

      // Find users with trials ending in 3 days or 1 day
      const [ending3Days, ending1Day] = await Promise.all([
        // Trials ending in 3 days (with some tolerance for scheduling)
        prisma.user.findMany({
          where: {
            planType: this.TRIAL_PLAN,
            trialEndsAt: {
              gte: new Date(threeDaysFromNow.getTime() - 12 * 60 * 60 * 1000), // 12 hours before
              lte: new Date(threeDaysFromNow.getTime() + 12 * 60 * 60 * 1000)  // 12 hours after
            }
          },
          select: {
            id: true,
            email: true,
            trialEndsAt: true
          }
        }),
        // Trials ending in 1 day (with some tolerance for scheduling)
        prisma.user.findMany({
          where: {
            planType: this.TRIAL_PLAN,
            trialEndsAt: {
              gte: new Date(oneDayFromNow.getTime() - 12 * 60 * 60 * 1000), // 12 hours before
              lte: new Date(oneDayFromNow.getTime() + 12 * 60 * 60 * 1000)  // 12 hours after
            }
          },
          select: {
            id: true,
            email: true,
            trialEndsAt: true
          }
        })
      ]);

      let notificationCount = 0;

      // Send 3-day warnings
      for (const user of ending3Days) {
        if (!user.trialEndsAt) continue;
        
        await notify.trial.ending(user.id, {
          daysLeft: 3,
          expiryDate: user.trialEndsAt.toISOString(),
          upgradeUrl: '/settings/billing'
        });
        
        logger.info({ userId: user.id, email: user.email }, 'Sent 3-day trial ending notification');
        notificationCount++;
      }

      // Send 1-day warnings
      for (const user of ending1Day) {
        if (!user.trialEndsAt) continue;
        
        await notify.trial.ending(user.id, {
          daysLeft: 1,
          expiryDate: user.trialEndsAt.toISOString(),
          upgradeUrl: '/settings/billing'
        });
        
        logger.info({ userId: user.id, email: user.email }, 'Sent 1-day trial ending notification');
        notificationCount++;
      }

      if (notificationCount > 0) {
        logger.info({ notificationCount }, 'Sent trial ending notifications');
      }

      return notificationCount;
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to send trial ending notifications');
      return 0;
    }
  }

  /**
   * Check and expire trials that have ended
   */
  static async expireTrials(): Promise<number> {
    try {
      const now = new Date();
      
      // Find users with expired trials
      const expiredTrials = await prisma.user.findMany({
        where: {
          planType: this.TRIAL_PLAN,
          trialEndsAt: {
            lte: now
          }
        },
        select: {
          id: true,
          email: true,
          subscriptions: {
            where: {
              status: 'ACTIVE'
            }
          }
        }
      });

      let expiredCount = 0;
      for (const user of expiredTrials) {
        // If user has an active subscription, keep them on pro
        const newPlanType = user.subscriptions.length > 0 ? 'pro' : 'free';
        
        // Get trial start date for notification
        const userWithTrialData = await prisma.user.findUnique({
          where: { id: user.id },
          select: { 
            trialStartedAt: true, 
            trialEndsAt: true 
          }
        });

        await prisma.user.update({
          where: { id: user.id },
          data: {
            planType: newPlanType
          }
        });

        logger.info({ userId: user.id, email: user.email, newPlanType }, 'Trial expired for user');

        // Send trial end notification (only if user doesn't have active subscription)
        if (newPlanType === 'free' && userWithTrialData?.trialStartedAt) {
          const startDate = userWithTrialData.trialStartedAt;
          const endDate = userWithTrialData.trialEndsAt || now;
          const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

          await notify.trial.ended(user.id, {
            totalDays,
            startDate: startDate.toISOString()
          });
        }

        expiredCount++;
      }

      return expiredCount;
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to expire trials');
      return 0;
    }
  }
}
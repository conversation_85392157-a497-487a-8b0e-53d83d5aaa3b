import { prisma } from '../../lib/prisma.js';
import { PlanConfigService } from './plan-config.service.js';
import { CreditService } from './credit.service.js';
import { AtomicUsageTrackingService } from '../atomic-usage-tracking.service.js';
import { logger } from '../../utils/logger.js';

export interface UsageInfo {
  // Current usage
  currentUsage: {
    emails: number;
    domains: number;
    aliases: number;
    webhooks: number;
  };
  
  // Limits
  limits: {
    emails: number;
    domains: number;
    aliases: number;
    webhooks: number;
  };
  
  // Email breakdown
  emailBreakdown: {
    monthlyAllowance: number;
    monthlyUsed: number;
    monthlyRemaining: number;
    purchasedCredits: number;
    totalAvailable: number;
  };
  
  // Credit info
  creditInfo: {
    balance: number;
    expiringCredits: number;
    nextExpirationDate: Date | null;
  };
}

export interface EmailUsageResult {
  success: boolean;
  source: 'monthly' | 'credits' | 'failed';
  remainingMonthly: number;
  remainingCredits: number;
  error?: string;
}

export class UsageCalculationService {
  /**
   * Get comprehensive usage information for a user
   */
  static async getUserUsageInfo(userId: string): Promise<UsageInfo> {
    // Get user with plan info
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        planType: true,
        currentMonthEmails: true,
        domains: { select: { id: true } },
        webhooks: { select: { id: true } },
        _count: {
          select: {
            domains: true,
            webhooks: true
          }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get alias count: include current user's system-domain aliases even if they don't own the system domain
    const userIdSuffix = userId.slice(-8);

    const aliasCount = await prisma.alias.count({
      where: {
        OR: [
          // Aliases under domains owned by the user (excluding other users' system-domain aliases)
          {
            domain: { userId },
            NOT: {
              AND: [
                { domain: { domain: 'user.emailconnect.eu' } },
                { email: { not: { startsWith: `${userIdSuffix}+` } } }
              ]
            }
          },
          // System-domain aliases belonging to this user (when they don't own the system domain)
          {
            domain: { domain: 'user.emailconnect.eu' },
            email: { startsWith: `${userIdSuffix}+` }
          }
        ]
      }
    });

    // Get plan limits (domain-based for Pro)
    const limits = PlanConfigService.getPlanLimits(user.planType, user._count.domains);

    // Get credit balance
    const creditBalance = await CreditService.getCreditBalance(userId);

    // Calculate email breakdown using plan-based monthly limit instead of stored value
    const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
    const monthlyAllowance = planConfig.monthlyEmailLimit;
    const monthlyUsed = user.currentMonthEmails;
    const monthlyRemaining = Math.max(0, monthlyAllowance - monthlyUsed);
    const totalAvailable = monthlyRemaining + creditBalance.totalCredits;

    return {
      currentUsage: {
        emails: monthlyUsed,
        domains: user._count.domains,
        aliases: aliasCount,
        webhooks: user._count.webhooks
      },
      limits,
      emailBreakdown: {
        monthlyAllowance: monthlyAllowance,
        monthlyUsed,
        monthlyRemaining,
        purchasedCredits: creditBalance.totalCredits,
        totalAvailable
      },
      creditInfo: {
        balance: creditBalance.totalCredits,
        expiringCredits: creditBalance.expiringCredits,
        nextExpirationDate: creditBalance.nextExpirationDate
      }
    };
  }

  /**
   * Process email usage - use monthly allowance first, then credits
   * Uses atomic usage tracking to prevent race conditions
   * @deprecated Use AtomicUsageTrackingService.incrementEmailUsage directly for new code
   */
  static async processEmailUsage(userId: string, emailCount: number = 1): Promise<EmailUsageResult> {
    logger.warn({
      userId,
      emailCount,
      caller: 'UsageCalculationService.processEmailUsage'
    }, 'Using deprecated processEmailUsage method - consider migrating to AtomicUsageTrackingService');

    // Delegate to the atomic usage tracking service
    const result = await AtomicUsageTrackingService.incrementEmailUsage(userId, emailCount);

    return {
      success: result.success,
      source: result.source,
      remainingMonthly: result.remainingMonthly,
      remainingCredits: result.remainingCredits,
      error: result.error,
    };
  }

  /**
   * Check if user can process emails (has monthly allowance or credits)
   */
  static async canProcessEmails(userId: string, emailCount: number = 1): Promise<boolean> {
    const usageInfo = await this.getUserUsageInfo(userId);
    return usageInfo.emailBreakdown.totalAvailable >= emailCount;
  }

  /**
   * Get usage summary for billing display
   */
  static async getBillingUsageSummary(userId: string) {
    const usageInfo = await this.getUserUsageInfo(userId);
    
    return {
      usage: usageInfo.currentUsage,
      limits: usageInfo.limits,
      emailBreakdown: usageInfo.emailBreakdown,
      creditInfo: usageInfo.creditInfo
    };
  }

  /**
   * Get simple usage for MetricsPill display
   */
  static async getSimpleUsage(userId: string) {
    const usageInfo = await this.getUserUsageInfo(userId);
    
    return {
      emails: usageInfo.currentUsage.emails,
      domains: usageInfo.currentUsage.domains,
      aliases: usageInfo.currentUsage.aliases,
      webhooks: usageInfo.currentUsage.webhooks,
      emailsAvailable: usageInfo.emailBreakdown.totalAvailable,
      emailsLimit: usageInfo.limits.emails
    };
  }
}

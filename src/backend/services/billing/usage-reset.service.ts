import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

export class UsageResetService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 60 * 60 * 1000; // Check every hour

  /**
   * Start the usage reset worker
   */
  start(): void {
    if (this.intervalId) {
      logger.warn('Usage reset worker already running');
      return;
    }

    logger.info('Starting usage reset worker');
    
    // Run immediately, then on interval
    this.checkAndResetUsage();
    this.intervalId = setInterval(() => {
      this.checkAndResetUsage();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stop the usage reset worker
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Usage reset worker stopped');
    }
  }

  /**
   * Check for users that need monthly usage reset
   */
  async checkAndResetUsage(): Promise<void> {
    try {
      logger.debug('Running usage reset check');

      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      // Find users whose lastUsageReset is from a previous month
      const startOfCurrentMonth = new Date(currentYear, currentMonth, 1);

      const usersToReset = await prisma.user.findMany({
        where: {
          currentMonthEmails: {
            gt: 0 // Only reset users who have usage to reset
          },
          lastUsageReset: {
            lt: startOfCurrentMonth // Before start of current month
          }
        },
        select: {
          id: true,
          email: true,
          currentMonthEmails: true,
          lastUsageReset: true
        }
      });

      if (usersToReset.length === 0) {
        logger.debug('No users require usage reset at this time');
        return;
      }

      logger.info({ userCount: usersToReset.length }, 'Resetting monthly usage for users');

      // Reset usage for all users in a single transaction
      const resetResult = await prisma.user.updateMany({
        where: {
          id: {
            in: usersToReset.map(user => user.id)
          }
        },
        data: {
          currentMonthEmails: 0,
          lastUsageReset: now
        }
      });

      logger.info({ 
        usersReset: resetResult.count,
        month: currentMonth + 1,
        year: currentYear 
      }, 'Monthly usage reset completed');

      // Log individual resets for audit purposes
      for (const user of usersToReset) {
        logger.info({
          userId: user.id,
          email: user.email,
          previousUsage: user.currentMonthEmails,
          lastReset: user.lastUsageReset
        }, 'User usage reset');
      }

    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to run usage reset check');
    }
  }

  /**
   * Manually reset usage for a specific user
   */
  async resetUserUsage(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          currentMonthEmails: true
        }
      });

      if (!user) {
        return { success: false, error: 'User not found' };
      }

      await prisma.user.update({
        where: { id: userId },
        data: {
          currentMonthEmails: 0,
          lastUsageReset: new Date()
        }
      });

      logger.info({
        userId,
        email: user.email,
        previousUsage: user.currentMonthEmails
      }, 'Manual user usage reset');

      return { success: true };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to reset user usage');
      return { success: false, error: error.message };
    }
  }

  /**
   * Get usage reset statistics
   */
  async getResetStats(): Promise<{
    totalUsers: number;
    usersWithUsage: number;
    usersNeedingReset: number;
    lastResetCheck: Date;
  }> {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    const [totalUsers, usersWithUsage, usersNeedingReset] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          currentMonthEmails: { gt: 0 }
        }
      }),
      prisma.user.count({
        where: {
          currentMonthEmails: { gt: 0 },
          lastUsageReset: {
            lt: new Date(currentYear, currentMonth, 1)
          }
        }
      })
    ]);

    return {
      totalUsers,
      usersWithUsage,
      usersNeedingReset,
      lastResetCheck: now
    };
  }
}

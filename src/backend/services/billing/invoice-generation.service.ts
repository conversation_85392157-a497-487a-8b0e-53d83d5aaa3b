import PDFDocument from 'pdfkit';
import { prisma } from '../../lib/prisma.js';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';
import { S3StorageService } from '../storage/s3-storage.service.js';
import { PutObjectCommand } from '@aws-sdk/client-s3';

export interface InvoiceData {
  invoiceNumber: string;
  paymentId: string;
  userId: string;
  amount: number;
  currency: string;
  description: string;
  billingPeriod?: string;
  paidAt: Date;
  userEmail: string;
  userName?: string;
  // Organization details
  organizationName?: string;
  organizationReg?: string;
  addressStreet?: string;
  addressApartment?: string;
  addressZipCode?: string;
  addressCity?: string;
  addressCountry?: string;
}

export class InvoiceGenerationService {
  /**
   * Generate a unique invoice number
   */
  private static async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    
    // Get the latest invoice number for this year
    const latestInvoice = await prisma.invoice.findFirst({
      where: {
        invoiceNumber: {
          startsWith: `INV-${year}-`
        }
      },
      orderBy: {
        invoiceNumber: 'desc'
      }
    });

    let nextNumber = 2984;
    if (latestInvoice) {
      const match = latestInvoice.invoiceNumber.match(/INV-\d{4}-(\d+)/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `INV-${year}-${nextNumber.toString().padStart(3, '0')}`;
  }

  /**
   * Generate PDF invoice and upload to S3
   */
  private static async generateInvoicePDFToS3(invoiceData: InvoiceData, s3Key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        logger.info({ s3Key }, 'Starting PDF generation to S3');
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        // Collect PDF data in memory
        doc.on('data', (chunk: Buffer) => {
          chunks.push(chunk);
        });

        doc.on('end', async () => {
          try {
            const pdfBuffer = Buffer.concat(chunks);
            
            // Upload to S3
            const s3Service = new S3StorageService();
            const putCommand = new PutObjectCommand({
              Bucket: env.S3_BUCKET,
              Key: s3Key,
              Body: pdfBuffer,
              ContentType: 'application/pdf',
              ContentDisposition: `attachment; filename="${invoiceData.invoiceNumber}.pdf"`
            });
            
            await s3Service['client'].send(putCommand);
            logger.info({ s3Key }, 'PDF invoice uploaded to S3 successfully');
            resolve();
          } catch (uploadError) {
            logger.error({ error: uploadError, s3Key }, 'Failed to upload PDF to S3');
            reject(uploadError);
          }
        });

        doc.on('error', (error) => {
          logger.error({ error: error.message, s3Key }, 'PDFKit error during PDF generation');
          reject(error);
        });

        this.generatePDFContent(doc, invoiceData);
        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }


  /**
   * Generate PDF content for invoices
   */
  private static generatePDFContent(doc: PDFKit.PDFDocument, invoiceData: InvoiceData): void {
    // Company Header
    doc.fontSize(20)
       .text('EmailConnect.eu', 50, 50, { align: 'left' })
       .fontSize(10)
       .text('EmailConnect by XADI', 50, 75)
       .text('https://emailconnect.eu', 50, 90)
       .text('VAT: NL004926305B88', 50, 105);

    // Invoice Title
    doc.fontSize(24)
       .text('INVOICE', 400, 50, { align: 'right' });

    // Invoice Details (Top Right)
    doc.fontSize(10)
       .text(`Invoice #: ${invoiceData.invoiceNumber}`, 400, 80, { align: 'right' })
       .text(`Date: ${invoiceData.paidAt.toLocaleDateString('en-GB')}`, 400, 95, { align: 'right' })
       .text('Status: PAID', 400, 110, { align: 'right' });

    // Bill To Section
    doc.fontSize(12)
       .text('BILL TO:', 50, 160);
        
    
    let yPosition = 180;
    doc.fontSize(10);
        
    // Organization name or user name
    if (invoiceData.organizationName) {
      doc.text(invoiceData.organizationName, 50, yPosition);
      yPosition += 15;
    } else if (invoiceData.userName) {
      doc.text(invoiceData.userName, 50, yPosition);
      yPosition += 15;
    }
            
    // Address if available
    if (invoiceData.addressStreet) {
      doc.text(invoiceData.addressStreet, 50, yPosition);
      yPosition += 15;
      
      if (invoiceData.addressApartment) {
        doc.text(invoiceData.addressApartment, 50, yPosition);
        yPosition += 15;
      }
      
      // City, postal code, country on one line if available
      let addressLine = '';
      if (invoiceData.addressZipCode) {
        addressLine += invoiceData.addressZipCode;
      }
      if (invoiceData.addressCity) {
        if (addressLine) addressLine += ' ';
        addressLine += invoiceData.addressCity;
      }
      if (invoiceData.addressCountry) {
        if (addressLine) addressLine += ', ';
        addressLine += invoiceData.addressCountry;
      }
      
      if (addressLine) {
        doc.text(addressLine, 50, yPosition);
        yPosition += 15;
      }

      // Registration number if available
      if (invoiceData.organizationReg) {
        doc.text(invoiceData.organizationReg, 50, yPosition);
        yPosition += 15;
      }

      // Email address
      doc.text(invoiceData.userEmail, 50, yPosition);
      yPosition += 15;
    }
    
    // Ensure there's enough space before the line separator
    const lineY = Math.max(yPosition + 10, 230);

    // Line separator
    doc.moveTo(50, lineY)
       .lineTo(550, lineY)
       .stroke();

    // Table Header
    const tableHeaderY = lineY + 20;
    doc.fontSize(10)
       .text('DESCRIPTION', 50, tableHeaderY, { width: 300 })
       .text('AMOUNT', 450, tableHeaderY, { width: 100, align: 'right' });

    // Table line
    const tableLineY = tableHeaderY + 20;
    doc.moveTo(50, tableLineY)
       .lineTo(550, tableLineY)
       .stroke();

    // Invoice Line Item
    const itemY = tableLineY + 15;
    const description = `${invoiceData.description}${invoiceData.billingPeriod ? ` - ${invoiceData.billingPeriod}` : ''}`;
    doc.text(description, 50, itemY, { width: 300 })
       .text(`${invoiceData.currency.toUpperCase()} ${invoiceData.amount.toFixed(2)}`, 450, itemY, { width: 100, align: 'right' });

    // Total Section
    const totalSectionY = itemY + 35;
    doc.moveTo(350, totalSectionY)
       .lineTo(550, totalSectionY)
       .stroke();

    // Calculate VAT amounts (21% VAT is included in the total amount)
    const vatRate = 0.21;
    const totalWithVat = invoiceData.amount;
    const subtotalExclVat = totalWithVat / (1 + vatRate);
    const vatAmount = totalWithVat - subtotalExclVat;

    const subtotalY = totalSectionY + 20;
    const vatY = subtotalY + 15;
    const totalY = vatY + 20;

    doc.fontSize(10)
       .text('SUBTOTAL (excl. VAT):', 350, subtotalY, { width: 140, align: 'left' })
       .text(`${invoiceData.currency.toUpperCase()} ${subtotalExclVat.toFixed(2)}`, 450, subtotalY, { width: 100, align: 'right' });

    doc.text('VAT (21%):', 350, vatY, { width: 140, align: 'left' })
       .text(`${invoiceData.currency.toUpperCase()} ${vatAmount.toFixed(2)}`, 450, vatY, { width: 100, align: 'right' });

    doc.fontSize(12)
       .text('TOTAL (incl. VAT):', 350, totalY, { width: 140, align: 'left' })
       .text(`${invoiceData.currency.toUpperCase()} ${totalWithVat.toFixed(2)}`, 450, totalY, { width: 100, align: 'right' });

    // Payment Information
    const paymentInfoY = totalY + 45;
    doc.fontSize(10)
       .text('PAYMENT INFORMATION', 50, paymentInfoY)
       .fontSize(8)
       .text(`Payment method: via Mollie`, 50, paymentInfoY + 20)
       .text(`Payment date: ${invoiceData.paidAt.toLocaleDateString('en-GB')}`, 50, paymentInfoY + 35)
       .text(`Payment ID: ${invoiceData.paymentId}`, 50, paymentInfoY + 50)
       .text('Status: PAID IN FULL', 50, paymentInfoY + 65);

    // Footer
    const footerY = paymentInfoY + 130;
    doc.fontSize(8)
       .text('Thank you for your business!', 50, footerY)
       .text('This invoice was generated automatically by EmailConnect.eu', 50, footerY + 15)
       .text(`Generated on: ${new Date().toLocaleString('en-GB')}`, 50, footerY + 30);

    // Terms & Conditions
    doc.fontSize(7)
       .text('Terms: Payment due upon receipt. For questions about this invoice, contact <EMAIL>', 50, 720, { align: 'center' });
  }

  /**
   * Create complete invoice with PDF
   */
  static async createInvoice(paymentId: string): Promise<string> {
    try {
      logger.info({ paymentId }, 'Starting invoice generation');

      // Get payment details
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: {
          user: {
            select: {
              email: true,
              name: true,
              organization: {
                select: {
                  name: true,
                  registrationNumber: true,
                  addressStreet: true,
                  addressApartment: true,
                  addressZipCode: true,
                  addressCity: true,
                  addressCountry: true
                }
              }
            }
          }
        }
      }) as any;

      if (!payment) {
        throw new Error(`Payment ${paymentId} not found`);
      }

      if (!payment.paidAt) {
        throw new Error(`Payment ${paymentId} is not marked as paid`);
      }

      // Check if invoice already exists
      const existingInvoice = await prisma.invoice.findFirst({
        where: { paymentId }
      });

      if (existingInvoice) {
        logger.info({ invoiceId: existingInvoice.id }, 'Invoice already exists');
        return existingInvoice.id;
      }

      // Generate invoice number
      const invoiceNumber = await this.generateInvoiceNumber();

      // Prepare invoice data
      const invoiceData: InvoiceData = {
        invoiceNumber,
        paymentId: payment.id,
        userId: payment.userId,
        amount: Number(payment.amount),
        currency: payment.currency,
        description: payment.description || 'EmailConnect.eu Service',
        billingPeriod: this.getBillingPeriod(payment.description),
        paidAt: payment.paidAt,
        userEmail: payment.user.email,
        userName: payment.user.name || undefined,
        // Organization details
        organizationName: payment.user.organization?.name || undefined,
        organizationReg: payment.user.organization?.registrationNumber || undefined,
        addressStreet: payment.user.organization?.addressStreet || undefined,
        addressApartment: payment.user.organization?.addressApartment || undefined,
        addressZipCode: payment.user.organization?.addressZipCode || undefined,
        addressCity: payment.user.organization?.addressCity || undefined,
        addressCountry: payment.user.organization?.addressCountry || undefined
      };

      // Generate S3 key for invoice
      const s3Key = `invoices/${invoiceNumber}.pdf`;

      // Generate PDF and upload to S3
      await this.generateInvoicePDFToS3(invoiceData, s3Key);
      
      // Save invoice record to database
      const invoice = await prisma.invoice.create({
        data: {
          invoiceNumber: invoiceData.invoiceNumber,
          paymentId: invoiceData.paymentId,
          userId: invoiceData.userId,
          amount: invoiceData.amount,
          currency: invoiceData.currency,
          description: invoiceData.description,
          billingPeriod: invoiceData.billingPeriod,
          generatedAt: new Date(),
          s3Key: s3Key
        }
      });

      logger.info({ 
        invoiceId: invoice.id, 
        invoiceNumber, 
        userEmail: payment.user.email,
        amount: invoiceData.amount,
        currency: invoiceData.currency
      }, 'Invoice generated successfully');

      return invoice.id;

    } catch (error: any) {
      logger.error({ 
        error: error.message, 
        paymentId 
      }, 'Failed to generate invoice');
      throw error;
    }
  }

  /**
   * Extract billing period from payment description
   */
  private static getBillingPeriod(description: string | null): string | undefined {
    if (!description) return undefined;
    
    const desc = description.toLowerCase();
    if (desc.includes('monthly')) return 'Monthly';
    if (desc.includes('yearly') || desc.includes('annual')) return 'Yearly';
    if (desc.includes('one-time') || desc.includes('credit')) return 'One-time';
    
    return undefined;
  }

  /**
   * Get invoice by ID with user access control
   */
  static async getInvoice(invoiceId: string, userId: string) {
    return prisma.invoice.findFirst({
      where: {
        id: invoiceId,
        userId: userId
      }
    });
  }

  /**
   * Get all invoices for a user
   */
  static async getUserInvoices(userId: string) {
    return prisma.invoice.findMany({
      where: { userId },
      include: {
        payment: {
          select: { paidAt: true }
        }
      },
      orderBy: { generatedAt: 'desc' }
    });
  }
}
import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

/**
 * Periodic worker that enforces end-of-period downgrades for cancelled subscriptions.
 *
 * Policy:
 * - When a subscription is cancelled at Mollie, we keep the user on the paid plan
 *   until the subscription.nextPaymentDate (end of current period).
 * - This worker downgrades such users to the free plan when now >= nextPaymentDate.
 */
export class SubscriptionLifecycleService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 60 * 60 * 1000; // hourly

  start(): void {
    if (this.intervalId) {
      logger.warn('Subscription lifecycle worker already running');
      return;
    }
    logger.info('Starting subscription lifecycle worker');
    // Run immediately, then on interval
    this.processEndOfPeriodDowngrades();
    this.intervalId = setInterval(() => this.processEndOfPeriodDowngrades(), this.CHECK_INTERVAL_MS);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Subscription lifecycle worker stopped');
    }
  }

  async processEndOfPeriodDowngrades(): Promise<void> {
    try {
      const now = new Date();

      // Find cancelled subscriptions that have reached their end-of-period
      const dueSubs = await prisma.subscription.findMany({
        where: {
          status: 'CANCELLED',
          nextPaymentDate: { lte: now },
        },
        select: {
          id: true,
          userId: true,
          planType: true,
          nextPaymentDate: true,
          user: {
            select: { id: true, planType: true, email: true }
          }
        }
      });

      if (dueSubs.length === 0) {
        logger.debug('No subscriptions require end-of-period downgrade at this time');
        return;
      }

      logger.info({ count: dueSubs.length }, 'Processing end-of-period subscription downgrades');

      for (const sub of dueSubs) {
        try {
          // If already free, skip
          if (sub.user.planType === 'free') {
            continue;
          }

          // Downgrade to free plan
          await prisma.user.update({
            where: { id: sub.userId },
            data: { planType: 'free' }
          });

          // Audit: subscription expired (end of period)
          try {
            await prisma.auditLog.create({
              data: {
                action: 'subscription.expired',
                resourceType: 'subscription',
                resourceId: sub.id,
                metadata: {
                  userId: sub.userId,
                  planType: sub.planType,
                  expiredAt: now.toISOString(),
                  previousNextPaymentDate: sub.nextPaymentDate?.toISOString() || null
                },
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
              }
            });
          } catch (e:any) {
            logger.warn({ error: e.message, subscriptionId: sub.id }, 'Failed to log subscription.expired');
          }

          // Attempt to sync to Postfix/PostgreSQL
          try {
            const { PostfixManager } = await import('../postfix-manager.js');
            const postfixManager = new PostfixManager();
            await postfixManager.updateUserDomainsPlan(sub.userId, 'free');
          } catch (e: any) {
            logger.error({ error: e.message, userId: sub.userId }, 'Failed to sync plan downgrade to Postfix PostgreSQL');
          }

          logger.info({
            userId: sub.userId,
            subscriptionId: sub.id,
            nextPaymentDate: sub.nextPaymentDate?.toISOString()
          }, 'User plan downgraded to free at end of period');
        } catch (e: any) {
          logger.error({ error: e.message, subscriptionId: sub.id, userId: sub.userId }, 'Failed to downgrade user at end of period');
        }
      }
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Subscription lifecycle worker run failed');
    }
  }
}


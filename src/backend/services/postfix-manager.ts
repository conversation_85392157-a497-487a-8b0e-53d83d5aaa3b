import { logger } from '../utils/logger.js';
import { PostfixSyncService } from './postfix-sync.service.js';
import { prisma } from '../lib/prisma.js';

/**
 * PostfixManager - Updated to use PostgreSQL directly instead of Go service
 *
 * This class maintains the same interface as before but now uses the
 * PostfixSyncService to write directly to PostgreSQL postfix tables
 * instead of calling the external Go postfix-manager service.
 */
export class PostfixManager {
  private readonly postfixSync = new PostfixSyncService();

  /**
   * Add a domain to Postfix configuration via PostgreSQL
   */
  async addDomain(domain: string, planType: string = 'free'): Promise<void> {
    try {
      logger.info({ domain, planType }, 'Adding domain via PostgreSQL');

      // Get user ID from domain (assuming domain is being added in context of a user)
      // This method needs to be called with proper user context
      const domainRecord = await prisma.domain.findUnique({
        where: { domain },
        select: { userId: true }
      });

      if (!domainRecord) {
        throw new Error(`Domain ${domain} not found in main database`);
      }

      // Sync domain to Postfix PostgreSQL tables
      await this.postfixSync.syncDomainToPostfix(domain, domainRecord.userId, true);

      logger.info({ domain, planType }, 'Domain added successfully via PostgreSQL');
    } catch (error) {
      logger.error({
        domain,
        planType,
        error: error instanceof Error ? error.message : error
      }, 'Failed to add domain via PostgreSQL');
      throw error;
    }
  }

  /**
   * Add a domain to Postfix configuration with explicit user ID
   * This version is used when we already know the user ID (e.g., during domain creation)
   */
  async addDomainWithUserId(domain: string, userId: string, planType: string = 'free'): Promise<void> {
    try {
      logger.info({ domain, userId, planType }, 'Adding domain with user ID via PostgreSQL');

      // Sync domain to Postfix PostgreSQL tables
      await this.postfixSync.syncDomainToPostfix(domain, userId, true);

      logger.info({ domain, userId, planType }, 'Domain added successfully via PostgreSQL');
    } catch (error) {
      logger.error({
        domain,
        userId,
        planType,
        error: error instanceof Error ? error.message : error
      }, 'Failed to add domain with user ID via PostgreSQL');
      throw error;
    }
  }

  /**
   * Update domain plan type (for plan upgrades/downgrades)
   */
  async updateDomainPlan(domain: string, planType: string): Promise<void> {
    try {
      logger.info({ domain, planType }, 'Updating domain plan via PostgreSQL');

      // Get user ID from domain
      const domainRecord = await prisma.domain.findUnique({
        where: { domain },
        select: { userId: true }
      });

      if (!domainRecord) {
        throw new Error(`Domain ${domain} not found in main database`);
      }

      // Re-sync domain to update spam filtering based on new plan
      await this.postfixSync.syncDomainToPostfix(domain, domainRecord.userId, true);

      logger.info({ domain, planType }, 'Domain plan updated successfully via PostgreSQL');
    } catch (error) {
      logger.error({
        domain,
        planType,
        error: error instanceof Error ? error.message : error
      }, 'Failed to update domain plan via PostgreSQL');
      throw error;
    }
  }

  /**
   * Update plan type for all domains belonging to a user (batch operation)
   */
  async updateUserDomainsPlan(userId: string, planType: string): Promise<void> {
    try {
      logger.info({ userId, planType }, 'Updating all user domains plan via PostgreSQL');

      // Get all active domains for this user
      const userDomains = await prisma.domain.findMany({
        where: { userId, active: true },
        select: { id: true, domain: true, configuration: true }
      });

      if (userDomains.length === 0) {
        logger.info({ userId, planType }, 'No active domains found for user - skipping postfix update');
        return;
      }

      logger.info({ userId, planType, domainCount: userDomains.length }, 'Found domains to update');

      // Update each domain individually
      let successCount = 0;
      let failureCount = 0;
      const failures: string[] = [];

      for (const domain of userDomains) {
        try {
          // Update Postfix PostgreSQL configuration (this will determine spam filtering based on plan)
          await this.postfixSync.syncDomainToPostfix(domain.domain, userId, true);

          // Update main database domain configuration
          const currentConfig = domain.configuration as any || {};
          const spamFilteringEnabled = planType === 'pro' || planType === 'enterprise';
          const updatedConfig = {
            ...currentConfig,
            spamFiltering: {
              ...currentConfig.spamFiltering,
              enabled: spamFilteringEnabled
            }
          };

          await prisma.domain.update({
            where: { id: domain.id },
            data: {
              configuration: updatedConfig,
              updatedAt: new Date()
            }
          });

          successCount++;
          logger.debug({
            domain: domain.domain,
            planType,
            spamFilteringEnabled
          }, 'Domain plan and configuration updated successfully');
        } catch (error) {
          failureCount++;
          failures.push(domain.domain);
          logger.error({
            domain: domain.domain,
            planType,
            error: error instanceof Error ? error.message : error
          }, 'Failed to update individual domain plan');
          // Continue with other domains rather than failing completely
        }
      }

      logger.info({
        userId,
        planType,
        successCount,
        failureCount,
        totalDomains: userDomains.length,
        failures: failures.length > 0 ? failures : undefined
      }, 'Batch domain plan update completed');

      // If some domains failed, log warning but don't throw error
      if (failureCount > 0) {
        logger.warn({
          userId,
          planType,
          failureCount,
          failures
        }, 'Some domains failed to update - plan change may be partially applied');
      }

    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error,
        userId,
        planType
      }, 'Failed to update user domains plan');
      throw new Error(`Failed to update user domains plan: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Remove a domain from Postfix configuration via PostgreSQL
   */
  async removeDomain(domain: string): Promise<void> {
    try {
      logger.info({ domain }, 'Removing domain via PostgreSQL');

      await this.postfixSync.removeDomainFromPostfix(domain);

      logger.info({ domain }, 'Domain removed successfully via PostgreSQL');
    } catch (error) {
      logger.error({
        domain,
        error: error instanceof Error ? error.message : error
      }, 'Failed to remove domain via PostgreSQL');
      throw error;
    }
  }

  /**
   * Get list of currently configured domains via PostgreSQL
   */
  async getConfiguredDomains(): Promise<string[]> {
    try {
      return await this.postfixSync.getConfiguredDomains();
    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'Failed to get configured domains from PostgreSQL');
      return [];
    }
  }

  /**
   * Test Postfix configuration via PostgreSQL health check
   */
  async testConfiguration(): Promise<boolean> {
    try {
      const healthCheck = await this.postfixSync.healthCheck();
      return healthCheck.healthy;
    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'Failed to test configuration via PostgreSQL');
      return false;
    }
  }

  /**
   * Setup initial Postfix configuration (no longer needed with PostgreSQL)
   * This method is kept for compatibility but does nothing
   */
  async setupInitialConfig(): Promise<void> {
    logger.info('Setup initial configuration - no action needed with PostgreSQL integration');
    // PostgreSQL tables are created via Prisma migrations
    // No additional setup required
  }

  /**
   * Get service status via PostgreSQL health check
   */
  async getServiceStatus(): Promise<any> {
    try {
      const healthCheck = await this.postfixSync.healthCheck();
      return {
        status: healthCheck.healthy ? 'ok' : 'error',
        details: healthCheck.details,
        service: 'postgresql',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'Failed to get service status');
      throw new Error(`Service unavailable: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Update domain spam filtering configuration via PostgreSQL
   */
  async updateDomainSpamFiltering(domain: string, enabled: boolean): Promise<void> {
    try {
      logger.info({ domain, enabled }, 'Updating domain spam filtering via PostgreSQL');

      await this.postfixSync.updateDomainSpamFiltering(domain, enabled);

      logger.info({ domain, enabled }, 'Domain spam filtering updated successfully');
    } catch (error) {
      logger.error({
        domain,
        enabled,
        error: error instanceof Error ? error.message : error
      }, 'Failed to update spam filtering via PostgreSQL');
      throw error;
    }
  }

  /**
   * Health check for PostgreSQL integration
   */
  async healthCheck(): Promise<boolean> {
    try {
      const healthCheck = await this.postfixSync.healthCheck();
      return healthCheck.healthy;
    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'PostgreSQL health check failed');
      return false;
    }
  }
}

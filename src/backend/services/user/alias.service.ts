import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { captureAliasError } from '../../lib/sentry-helpers.js';
import { PlanConfigService } from '../billing/plan-config.service.js';

// Helper function to validate email format or catch-all pattern
export function isValidEmailOrCatchAll(email: string): boolean {
  // Allow catch-all format (exactly *@domain.com)
  if (email.match(/^\*@[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)) {
    return true;
  }
  // Reject patterns that start with * but aren't exactly *@domain.com
  if (email.startsWith('*') && !email.match(/^\*@[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)) {
    return false;
  }
  // Standard email validation (no asterisks allowed)
  const emailRegex = /^[^\s@*]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Helper function to extract domain from email
function extractDomainFromEmail(email: string): string | null {
  const parts = email.split('@');
  return parts.length === 2 ? parts[1].toLowerCase() : null;
}

// Reserved email local parts that should not be allowed
const RESERVED_LOCAL_PARTS = [
  'admin', 'administrator', 'root', 'postmaster', 'webmaster',
  'hostmaster', 'abuse', 'security', 'noreply', 'no-reply',
  'mailer-daemon', 'daemon', 'system', 'api', 'www'
];

// Helper function to check if local part is reserved
function isReservedLocalPart(localPart: string): boolean {
  return RESERVED_LOCAL_PARTS.includes(localPart.toLowerCase());
}

export interface CreateAliasData {
  email: string;
  domainId: string;
  webhookId: string;
  active?: boolean;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
  attachmentHandling?: 'inline' | 'storage';
  s3Folder?: string;
  userId: string;
}

export interface UpdateAliasData {
  email?: string;
  webhookId?: string;
  active?: boolean;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
  attachmentHandling?: 'inline' | 'storage';
  s3Folder?: string;
}

export class AliasService {
  /**
   * Get all aliases for a user
   */
  async getUserAliases(userId: string) {
    const userIdSuffix = userId.slice(-8);
    
    // Get regular aliases (domains owned by the user, but exclude system aliases for other users)
    const aliases = await prisma.alias.findMany({
      where: {
        domain: {
          userId
        },
        // If this is the system domain, exclude aliases that don't belong to this user
        NOT: {
          AND: [
            {
              domain: {
                domain: 'user.emailconnect.eu'
              }
            },
            {
              email: {
                not: {
                  startsWith: `${userIdSuffix}+`
                }
              }
            }
          ]
        }
      },
      include: {
        domain: {
          select: {
            id: true,
            domain: true,
            verified: true
          }
        },
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Also get system aliases for this user if they don't already own the system domain
    let systemAliases: any[] = [];
    const systemDomain = await prisma.domain.findFirst({
      where: {
        domain: 'user.emailconnect.eu',
        userId
      }
    });

    // Only fetch system aliases separately if this user doesn't own the system domain
    if (!systemDomain) {
      systemAliases = await prisma.alias.findMany({
        where: {
          domain: {
            domain: 'user.emailconnect.eu'
          },
          email: {
            startsWith: `${userIdSuffix}+`
          }
        },
        include: {
          domain: {
            select: {
              id: true,
              domain: true,
              verified: true
            }
          },
          webhook: {
            select: {
              id: true,
              name: true,
              url: true,
              verified: true,
              webhookSecret: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });
    }

    // Combine both regular and system aliases
    const allAliases = [...aliases, ...systemAliases];

    const result = allAliases.map(alias => ({
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      configuration: alias.configuration,
      domain: alias.domain,
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    }));

    return {
      aliases: result,
      total: result.length
    };
  }

  /**
   * Get a specific alias by ID for a user
   */
  async getAliasById(aliasId: string, userId: string) {
    let alias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      },
      include: {
        domain: {
          select: {
            id: true,
            domain: true,
            verified: true
          }
        },
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      }
    });

    // If not found in regular domains, check for system aliases
    if (!alias) {
      const userIdSuffix = userId.slice(-8);
      alias = await prisma.alias.findFirst({
        where: {
          id: aliasId,
          domain: {
            domain: 'user.emailconnect.eu'
          },
          email: {
            startsWith: `${userIdSuffix}+`
          }
        },
        include: {
          domain: {
            select: {
              id: true,
              domain: true,
              verified: true
            }
          },
          webhook: {
            select: {
              id: true,
              name: true,
              url: true,
              verified: true,
              webhookSecret: true
            }
          }
        }
      });
    }

    if (!alias) {
      return null;
    }

    return {
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      configuration: alias.configuration,
      domain: alias.domain,
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    };
  }

  /**
   * Create a new alias
   */
  async createAlias(data: CreateAliasData) {
    // Validate email format (supports catch-all patterns)
    if (!isValidEmailOrCatchAll(data.email)) {
      throw new Error('Invalid email format');
    }

    const emailDomain = extractDomainFromEmail(data.email);
    if (!emailDomain) {
      throw new Error('Could not extract domain from email');
    }

    const localPart = data.email.split('@')[0];

    // Check for reserved local parts (skip for catch-all aliases)
    if (localPart !== '*' && isReservedLocalPart(localPart)) {
      throw new Error(`The local part '${localPart}' is reserved and cannot be used for aliases`);
    }

    // Handle system domain aliases (user.emailconnect.eu) differently
    let domain = null;
    if (emailDomain === 'user.emailconnect.eu') {
      // System domain - create or find system domain record
      if (data.domainId) {
        throw new Error('System aliases should not have a domainId');
      }
      // Validate that the local part matches the expected user pattern (userID suffix + single tag)
      const userId = data.userId;
      const userIdSuffix = userId.slice(-8);
      
      // Check if it starts with the correct user ID suffix
      if (!localPart.startsWith(userIdSuffix + '+')) {
        throw new Error(`System alias must start with user ID suffix: ${userIdSuffix}+`);
      }
      
      // Extract the tag part and validate it
      const tagPart = localPart.substring((userIdSuffix + '+').length);
      
      // Tag must not be empty and must not contain additional plus signs
      if (!tagPart || tagPart.includes('+')) {
        throw new Error(`System alias must have exactly one tag after ${userIdSuffix}+. Multiple plus signs are not allowed.`);
      }
      
      // Tag must match the allowed pattern
      if (!/^[a-zA-Z0-9\-_]+$/.test(tagPart)) {
        throw new Error('System alias tag can only contain letters, numbers, hyphens, and underscores.');
      }
      
      // Find or create the system domain record (use the first admin user or any user)
      domain = await prisma.domain.findFirst({
        where: {
          domain: 'user.emailconnect.eu'
        }
      });
      
      if (!domain) {
        // Create system domain record using current user (it doesn't matter which user owns it)
        domain = await prisma.domain.create({
          data: {
            domain: 'user.emailconnect.eu',
            userId: data.userId, // Use current user ID for foreign key constraint
            verified: true,
            verificationStatus: 'VERIFIED',
            active: true
          }
        });
      }
      
      // Override domainId for system aliases
      data.domainId = domain.id;
    } else {
      // Regular domain - check if domain exists and belongs to user
      const domainRecord = await prisma.domain.findFirst({
        where: {
          id: data.domainId,
          userId: data.userId
        }
      });

      if (!domainRecord) {
        throw new Error('Domain not found');
      }

      // Verify email domain matches the domain record
      if (emailDomain !== domainRecord.domain) {
        throw new Error(`Email domain '${emailDomain}' does not match domain '${domainRecord.domain}'`);
      }
      
      domain = domainRecord;
    }

    // Critical: Domain must be verified to create aliases (system domain is always "verified")
    if (domain && !domain.verified) {
      const error = new Error('Cannot create aliases for unverified domains.');
      error.name = 'DOMAIN_NOT_VERIFIED';
      throw error;
    }

    // Check if webhook exists and belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: data.webhookId,
        userId: data.userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Check if alias already exists
    const existingAlias = await prisma.alias.findFirst({
      where: {
        email: data.email,
        domainId: data.domainId
      }
    });

    if (existingAlias) {
      throw new Error('Alias with this email already exists for this domain');
    }

    // Create the alias - set active=false if webhook is unverified
    const aliasActive = webhook.verified ? (data.active ?? true) : false

    // Get user's plan type for permission validation
    const user = await prisma.user.findUnique({
      where: { id: data.userId },
      select: { planType: true }
    });
    
    if (!user) {
      throw new Error('User not found');
    }

    // Prepare configuration object
    const configuration: any = {};
    if (data.allowAttachments !== undefined) {
      configuration.allowAttachments = data.allowAttachments;
    }
    if (data.includeEnvelope !== undefined) {
      configuration.includeEnvelope = data.includeEnvelope;
    }
    if (data.attachmentHandling !== undefined) {
      // Validate S3 storage permission for custom domains only
      if (data.attachmentHandling === 'storage') {
        // Check if user has S3 storage permission
        if (!PlanConfigService.userHasPermission(user.planType, 's3_storage')) {
          throw new Error('S3 storage requires Pro plan or higher');
        }
        
        // System domains cannot use S3 storage
        if (emailDomain === 'user.emailconnect.eu') {
          throw new Error('S3 storage is not available for system domain aliases');
        }
      }
      configuration.attachmentHandling = data.attachmentHandling;
    }
    if (data.s3Folder !== undefined) {
      configuration.s3Folder = data.s3Folder;
    }

    const alias = await prisma.alias.create({
      data: {
        email: data.email,
        domainId: data.domainId, // This should now always be set (either user domain or system domain)
        webhookId: data.webhookId,
        active: aliasActive,
        ...(Object.keys(configuration).length > 0 && { configuration })
      }
    });

    return {
      success: true,
      alias: {
        id: alias.id,
        email: alias.email,
        active: alias.active,
        createdAt: alias.createdAt.toISOString(),
        updatedAt: alias.updatedAt.toISOString(),
        domainId: alias.domainId,
        webhookId: alias.webhookId
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url,
        verified: webhook.verified
      },
      webhookNeedsVerification: !webhook.verified,
      aliasSetInactive: !webhook.verified // Indicates if alias was set inactive due to unverified webhook
    };
  }

  /**
   * Update an existing alias
   */
  async updateAlias(aliasId: string, userId: string, updates: UpdateAliasData) {
    // Check if alias exists and belongs to user
    let existingAlias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      }
    });

    // If not found in regular domains, check for system aliases
    if (!existingAlias) {
      const userIdSuffix = userId.slice(-8);
      existingAlias = await prisma.alias.findFirst({
        where: {
          id: aliasId,
          domain: {
            domain: 'user.emailconnect.eu'
          },
          email: {
            startsWith: `${userIdSuffix}+`
          }
        }
      });
    }

    if (!existingAlias) {
      throw new Error('Alias not found');
    }

    // If webhook is being updated, verify it exists and belongs to user
    if (updates.webhookId) {
      const webhook = await prisma.webhook.findFirst({
        where: {
          id: updates.webhookId,
          userId
        }
      });

      if (!webhook) {
        throw new Error('Webhook not found');
      }
    }

    // Validate email format if being updated
    if (updates.email !== undefined) {
      // If this is a system domain alias, disallow changing the email (tag is immutable)
      const aliasWithDomain = await prisma.alias.findUnique({
        where: { id: aliasId },
        include: { domain: true }
      });
      if (aliasWithDomain?.domain.domain === 'user.emailconnect.eu' && updates.email !== existingAlias.email) {
        throw new Error('System alias tag is immutable and cannot be changed');
      }
      if (!isValidEmailOrCatchAll(updates.email)) {
        throw new Error('Invalid email format');
      }
    }

    // Prepare update data
    const updateData: any = { updatedAt: new Date() };

    if (updates.email !== undefined) updateData.email = updates.email;
    if (updates.webhookId !== undefined) updateData.webhookId = updates.webhookId;
    if (updates.active !== undefined) updateData.active = updates.active;

    // Handle configuration updates
    if (updates.allowAttachments !== undefined ||
        updates.includeEnvelope !== undefined ||
        updates.attachmentHandling !== undefined ||
        updates.s3Folder !== undefined) {
      
      // Validate S3 storage permission if being updated
      if (updates.attachmentHandling === 'storage') {
        // Get user's plan type for permission validation
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { planType: true }
        });
        
        if (!user) {
          throw new Error('User not found');
        }

        // Check if user has S3 storage permission
        if (!PlanConfigService.userHasPermission(user.planType, 's3_storage')) {
          throw new Error('S3 storage requires Pro plan or higher');
        }
        
        // Get domain information to check if it's a system domain
        const aliasWithDomain = await prisma.alias.findUnique({
          where: { id: aliasId },
          include: { domain: true }
        });
        
        if (aliasWithDomain?.domain.domain === 'user.emailconnect.eu') {
          throw new Error('S3 storage is not available for system domain aliases');
        }
      }
      
      const currentConfig = existingAlias.configuration as any || {};
      const newConfig = {
        ...currentConfig,
        ...(updates.allowAttachments !== undefined && { allowAttachments: updates.allowAttachments }),
        ...(updates.includeEnvelope !== undefined && { includeEnvelope: updates.includeEnvelope }),
        ...(updates.attachmentHandling !== undefined && { attachmentHandling: updates.attachmentHandling }),
        ...(updates.s3Folder !== undefined && { s3Folder: updates.s3Folder })
      };
      updateData.configuration = newConfig;
    }

    const updatedAlias = await prisma.alias.update({
      where: { id: aliasId },
      data: updateData
    });

    return {
      success: true,
      alias: {
        id: updatedAlias.id,
        email: updatedAlias.email,
        active: updatedAlias.active,
        createdAt: updatedAlias.createdAt.toISOString(),
        updatedAt: updatedAlias.updatedAt.toISOString(),
        domainId: updatedAlias.domainId,
        webhookId: updatedAlias.webhookId
      }
    };
  }

  /**
   * Delete an alias
   */
  async deleteAlias(aliasId: string, userId: string) {
    // Check if alias exists and belongs to user
    let alias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      },
      include: {
        domain: true
      }
    });

    // If not found in regular domains, check for system aliases
    if (!alias) {
      const userIdSuffix = userId.slice(-8);
      alias = await prisma.alias.findFirst({
        where: {
          id: aliasId,
          domain: {
            domain: 'user.emailconnect.eu'
          },
          email: {
            startsWith: `${userIdSuffix}+`
          }
        },
        include: {
          domain: true
        }
      });
    }

    if (!alias) {
      throw new Error('Alias not found');
    }

    // Get domain info to determine restrictions
    const domain = await prisma.domain.findUnique({
      where: { id: alias.domainId! }
    });

    const isSystemDomain = domain?.domain === 'user.emailconnect.eu';

    // Prevent deletion of catch-all aliases for custom domains (system domain doesn't use catch-all)
    if (alias.email.startsWith('*@') && !isSystemDomain) {
      throw new Error('Cannot delete catch-all aliases. They are required for domain webhook functionality.');
    }

    // Check if this is the only alias for custom domains
    if (!isSystemDomain) {
      const aliasCount = await prisma.alias.count({
        where: {
          domainId: alias.domainId
        }
      });

      // Prevent deletion if this is the only alias for a custom domain
      if (aliasCount === 1) {
        throw new Error('Cannot delete the last alias for a domain. Each domain must have at least one alias.');
      }
    }

    await prisma.alias.delete({
      where: { id: aliasId }
    });

    return {
      success: true,
      message: 'Alias deleted successfully'
    };
  }

  /**
   * Update webhook for an alias (dedicated method)
   */
  async updateAliasWebhook(aliasId: string, userId: string, webhookId: string) {
    // Check if alias exists and belongs to user
    let existingAlias = await prisma.alias.findFirst({
      where: {
        id: aliasId,
        domain: {
          userId
        }
      }
    });

    // If not found in regular domains, check for system aliases
    if (!existingAlias) {
      const userIdSuffix = userId.slice(-8);
      existingAlias = await prisma.alias.findFirst({
        where: {
          id: aliasId,
          domain: {
            domain: 'user.emailconnect.eu'
          },
          email: {
            startsWith: `${userIdSuffix}+`
          }
        }
      });
    }

    if (!existingAlias) {
      throw new Error('Alias not found');
    }

    // Verify webhook exists and belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Update the alias webhook
    const updatedAlias = await prisma.alias.update({
      where: { id: aliasId },
      data: {
        webhookId: webhookId,
        updatedAt: new Date()
      }
    });

    // Note: No sync needed - catch-all alias webhook is the single source of truth

    return {
      success: true,
      alias: {
        id: updatedAlias.id,
        email: updatedAlias.email,
        webhookId: updatedAlias.webhookId,
        updatedAt: updatedAlias.updatedAt.toISOString()
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url
      },
      message: 'Alias webhook updated successfully'
    };
  }

  /**
   * Get aliases for a specific domain
   */
  async getDomainAliases(domainId: string, userId: string) {
    // Check if domain exists and belongs to user
    const domain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Get aliases for this domain
    const aliases = await prisma.alias.findMany({
      where: {
        domainId: domainId
      },
      include: {
        webhook: {
          select: {
            id: true,
            name: true,
            url: true,
            verified: true,
            webhookSecret: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const result = aliases.map(alias => ({
      id: alias.id,
      email: alias.email,
      active: alias.active,
      createdAt: alias.createdAt.toISOString(),
      updatedAt: alias.updatedAt.toISOString(),
      configuration: alias.configuration,
      webhook: {
        id: alias.webhook.id,
        name: alias.webhook.name,
        url: alias.webhook.url,
        verified: alias.webhook.verified,
        hasSecret: !!alias.webhook.webhookSecret
      }
    }));

    return {
      aliases: result,
      domain: {
        id: domain.id,
        domain: domain.domain,
        verified: domain.verified
      },
      total: result.length
    };
  }
}

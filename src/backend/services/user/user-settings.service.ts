import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { SubscriptionStatusService } from '../billing/subscription-status.service.js';
import { encryptionService } from '../encryption.service.js';

export interface FileTypeSettings {
  text: boolean;
  images: boolean;
  documents: boolean;
  archives: boolean;
  media: boolean;
}

export interface UserSettingsData {
  maxInlineSize?: number;
  fileTypeSettings?: FileTypeSettings;
  storageProvider?: string;
  dataRetentionHours?: number | null;
  s3Config?: {
    region?: string;
    bucket?: string;
    accessKey?: string;
    secretKey?: string;
    endpoint?: string;
  };
  allowFallbackStorage?: boolean;
  fallbackNotification?: boolean;
  useGravatar?: boolean;
}

export interface CreateUserSettingsData extends UserSettingsData {
  userId: string;
}

// Default file type settings based on plan
const DEFAULT_FILE_TYPE_SETTINGS: Record<string, FileTypeSettings> = {
  free: {
    text: false,     // User configurable
    images: false,   // Pro only
    documents: false, // User configurable
    archives: false, // Pro only
    media: false     // Pro only
  },
  pro: {
    text: false,     // User configurable (default off)
    images: false,   // User configurable (default off)
    documents: false, // User configurable (default off)
    archives: false, // User configurable (default off)
    media: false     // User configurable (default off)
  },
  enterprise: {
    text: false,     // User configurable (default off)
    images: false,   // User configurable (default off)
    documents: false, // User configurable (default off)
    archives: false, // User configurable (default off)
    media: false     // User configurable (default off)
  }
};

export class UserSettingsService {
  /**
   * Get default file type settings based on user plan
   */
  private static getDefaultFileTypeSettings(planType: string): FileTypeSettings {
    return DEFAULT_FILE_TYPE_SETTINGS[planType] || DEFAULT_FILE_TYPE_SETTINGS.free;
  }

  /**
   * Get user settings by user ID
   */
  async getUserSettings(userId: string) {
    try {
      const settings = await prisma.userSettings.findUnique({
        where: { userId },
        select: {
          id: true,
          maxInlineSize: true,
          fileTypeSettings: true,
          storageProvider: true,
          dataRetentionHours: true,
          s3Config: true,
          allowFallbackStorage: true,
          fallbackNotification: true,
          useGravatar: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // Return default settings if none exist
      if (!settings) {
        // Get user plan to determine default file type settings
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { planType: true }
        });
        
        const fileTypeSettings = UserSettingsService.getDefaultFileTypeSettings(user?.planType || 'free');
        
        return {
          maxInlineSize: 1.0,
          fileTypeSettings,
          storageProvider: 'default',
          dataRetentionHours: null, // null means use plan default
          s3Config: null,
          allowFallbackStorage: true,
          fallbackNotification: true,
          useGravatar: false,
          createdAt: null,
          updatedAt: null
        };
      }

      // Decrypt S3 config if present
      if (settings.s3Config) {
        settings.s3Config = encryptionService.decryptS3Config(settings.s3Config);
      }

      return settings;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get user settings');
      throw new Error('Failed to retrieve user settings');
    }
  }

  /**
   * Create or update user settings
   */
  async upsertUserSettings(userId: string, data: UserSettingsData) {
    try {
      // Validate input data
      if (data.maxInlineSize !== undefined) {
        if (typeof data.maxInlineSize !== 'number' || data.maxInlineSize < 0.1 || data.maxInlineSize > 10) {
          throw new Error('maxInlineSize must be a number between 0.1 and 10');
        }
      }

      if (data.storageProvider !== undefined) {
        if (!['default', 's3-compatible'].includes(data.storageProvider)) {
          throw new Error('storageProvider must be either "default" or "s3-compatible"');
        }
      }

      if (data.dataRetentionHours !== undefined && data.dataRetentionHours !== null) {
        if (typeof data.dataRetentionHours !== 'number' || data.dataRetentionHours < 1 || data.dataRetentionHours > 8760) {
          throw new Error('dataRetentionHours must be a number between 1 and 8760 (1 year)');
        }
      }

      // Validate S3 config if provided
      if (data.s3Config !== undefined && data.storageProvider === 's3-compatible') {
        const { region, bucket, accessKey, secretKey, endpoint } = data.s3Config;
        
        if (!region || !bucket || !accessKey || !secretKey) {
          throw new Error('S3 configuration requires region, bucket, accessKey, and secretKey');
        }

        // Basic validation for S3 fields
        if (typeof region !== 'string' || region.length < 2) {
          throw new Error('S3 region must be a valid string');
        }
        if (typeof bucket !== 'string' || bucket.length < 3) {
          throw new Error('S3 bucket name must be at least 3 characters');
        }
        if (typeof accessKey !== 'string' || accessKey.length < 10) {
          throw new Error('S3 access key must be at least 10 characters');
        }
        if (typeof secretKey !== 'string' || secretKey.length < 10) {
          throw new Error('S3 secret key must be at least 10 characters');
        }
        if (endpoint && (typeof endpoint !== 'string' || !endpoint.startsWith('http'))) {
          throw new Error('S3 endpoint must be a valid URL');
        }
      }

      // Prepare update data
      const updateData: any = {};
      if (data.maxInlineSize !== undefined) updateData.maxInlineSize = data.maxInlineSize;
      if (data.fileTypeSettings !== undefined) updateData.fileTypeSettings = data.fileTypeSettings;
      if (data.storageProvider !== undefined) updateData.storageProvider = data.storageProvider;
      if (data.dataRetentionHours !== undefined) updateData.dataRetentionHours = data.dataRetentionHours;
      
      // Encrypt S3 config before storing
      if (data.s3Config !== undefined) {
        updateData.s3Config = data.s3Config ? encryptionService.encryptS3Config(data.s3Config) : null;
      }
      
      if (data.allowFallbackStorage !== undefined) updateData.allowFallbackStorage = data.allowFallbackStorage;
      if (data.fallbackNotification !== undefined) updateData.fallbackNotification = data.fallbackNotification;
      if (data.useGravatar !== undefined) updateData.useGravatar = data.useGravatar;

      const settings = await prisma.userSettings.upsert({
        where: { userId },
        update: updateData,
        create: {
          userId,
          maxInlineSize: data.maxInlineSize ?? 1.0,
          fileTypeSettings: data.fileTypeSettings as any ?? null,
          storageProvider: data.storageProvider ?? 'default',
          dataRetentionHours: data.dataRetentionHours ?? null,
          s3Config: data.s3Config ? encryptionService.encryptS3Config(data.s3Config) : null,
          allowFallbackStorage: data.allowFallbackStorage ?? true,
          fallbackNotification: data.fallbackNotification ?? true,
          useGravatar: data.useGravatar ?? false
        },
        select: {
          id: true,
          maxInlineSize: true,
          fileTypeSettings: true,
          storageProvider: true,
          dataRetentionHours: true,
          s3Config: true,
          allowFallbackStorage: true,
          fallbackNotification: true,
          useGravatar: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // Decrypt S3 config before returning
      if (settings.s3Config) {
        settings.s3Config = encryptionService.decryptS3Config(settings.s3Config);
      }

      logger.info({ userId, settingsId: settings.id }, 'User settings updated successfully');
      return settings;
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to update user settings');
      throw error;
    }
  }

  /**
   * Delete user settings (reset to defaults)
   */
  async deleteUserSettings(userId: string) {
    try {
      const deleted = await prisma.userSettings.delete({
        where: { userId }
      });

      logger.info({ userId, settingsId: deleted.id }, 'User settings deleted successfully');
      return { success: true, message: 'Settings reset to defaults' };
    } catch (error: any) {
      if (error.code === 'P2025') {
        // Record not found - this is fine, settings already don't exist
        return { success: true, message: 'Settings already at defaults' };
      }
      
      logger.error({ error: error.message, userId }, 'Failed to delete user settings');
      throw new Error('Failed to reset user settings');
    }
  }

  /**
   * Check if user has Pro plan for advanced settings
   */
  async validateProFeatures(userId: string, data: UserSettingsData): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });

      if (!user) {
        return { allowed: false, reason: 'User not found' };
      }

      // Check if user has an active subscription instead of just planType
      const isProUser = await SubscriptionStatusService.hasActiveProSubscription(userId);

      // Check Pro-only features
      if (!isProUser) {
        if (data.maxInlineSize && data.maxInlineSize > 1.0) {
          return { allowed: false, reason: 'Attachment size above 1MB requires Pro plan' };
        }
        // Check Pro-only file types
        if (data.fileTypeSettings) {
          if (data.fileTypeSettings.images === true) {
            return { allowed: false, reason: 'Image file processing requires Pro plan' };
          }
          if (data.fileTypeSettings.archives === true) {
            return { allowed: false, reason: 'Archive file processing requires Pro plan' };
          }
          if (data.fileTypeSettings.media === true) {
            return { allowed: false, reason: 'Media file processing requires Pro plan' };
          }
        }
        if (data.storageProvider === 's3-compatible') {
          return { allowed: false, reason: 'Custom storage providers require Pro plan' };
        }
        if (data.dataRetentionHours !== undefined && data.dataRetentionHours !== null) {
          return { allowed: false, reason: 'Custom data retention requires Pro plan' };
        }
      }

      return { allowed: true };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to validate Pro features');
      return { allowed: false, reason: 'Failed to validate plan permissions' };
    }
  }
}

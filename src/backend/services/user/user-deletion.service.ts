import type { FastifyRequest } from 'fastify';
import { auth } from '../../lib/better-auth.js';
import { prisma } from '../../lib/prisma.js';
import { auditLogService } from '../audit-log.service.js';
import { EmailTemplateService } from '../notifications/email-template.service.js';
import { scalewayEmailService } from '../notifications/scaleway-email.service.js';
import type { EmailRecipient } from '../notifications/scaleway-email.service.js';
import { logger } from '../../utils/logger.js';
import { notificationEvents } from '../notifications/notification-events.service.js';

interface DeleteContextBase {
  reason?: string | null;
}

interface SelfDeleteContext extends DeleteContextBase {
  request: FastifyRequest;
  currentUser: { id: string; email: string; name?: string | null };
}

interface AdminDeleteContext extends DeleteContextBase {
  request: FastifyRequest;
  targetUserId: string;
  adminUser: { id: string; email: string; name?: string | null };
  notify?: boolean; // send confirmation email to the user
  bcc?: EmailRecipient[]; // optional BCCs for admin-triggered email
}

export class UserDeletionService {
  static async deleteSelf(ctx: SelfDeleteContext): Promise<void> {
    const { request, currentUser, reason } = ctx;

    // 1) BetterAuth delete (while session/cookies are still valid)
    await this.tryBetterAuthDeleteUser(currentUser.id, request);

    // 2) BetterAuth revoke sessions (best-effort)
    await this.tryBetterAuthRevokeSessions(currentUser.id, request);

    // 3) Prisma cleanup of sessions (defensive) and audit
    await prisma.session.deleteMany({ where: { userId: currentUser.id } });
    await auditLogService.log({
      action: 'user.sessions_revoked',
      resourceType: 'user',
      resourceId: currentUser.id,
      userId: currentUser.id,
      metadata: { deletedBy: 'self' }
    });

    // 4) Prisma delete the user row
    await prisma.user.delete({ where: { id: currentUser.id } });

    await auditLogService.log({
      action: 'user.deleted',
      resourceType: 'user',
      resourceId: currentUser.id,
      userId: currentUser.id,
      metadata: {
        deletedBy: 'self',
        reason: reason || undefined,
        retention: { backupsDays: 7, financeYears: 7 }
      }
    });

    // 5) Send confirmation email (best-effort)
    try {
      const tpl = EmailTemplateService.accountDeletedNotice({ fullName: currentUser.name || undefined, email: currentUser.email });
      await scalewayEmailService.sendEmail({
        to: [{ email: currentUser.email, name: currentUser.name || currentUser.email }],
        subject: tpl.subject,
        html: tpl.html,
        text: tpl.text
      });
    } catch (err) {
      logger.warn({ err }, 'Failed to send account deletion confirmation email (self)');
    }

    // Admin-only Telegram notification (no PII, not stored)
    try {
      await notificationEvents.notifyChannel('telegram', 'user.deleted', {
        type: 'user.deleted',
        userId: currentUser.id,
        title: 'User deleted',
        message: 'A user account was deleted',
        category: 'SYSTEM',
        priority: 'MEDIUM'
      });
    } catch (e) {
      logger.warn({ err: e instanceof Error ? e.message : e }, 'Failed to emit Telegram admin notification for user.deleted (self)');
    }
  }

  static async deleteAsAdmin(ctx: AdminDeleteContext): Promise<void> {
    const { request, targetUserId, adminUser, reason, notify = true, bcc } = ctx;

    // Resolve target user before deletion for email purposes
    const target = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: { id: true, email: true, name: true }
    });

    // 1) BetterAuth delete for the target user using admin credentials/cookies
    await this.tryBetterAuthDeleteUser(targetUserId, request);

    // 2) BetterAuth revoke sessions (best-effort)
    await this.tryBetterAuthRevokeSessions(targetUserId, request);

    // 3) Prisma cleanup: remove sessions then log
    await prisma.session.deleteMany({ where: { userId: targetUserId } });
    await auditLogService.log({
      action: 'user.sessions_revoked',
      resourceType: 'user',
      resourceId: targetUserId,
      userId: adminUser.id,
      metadata: { deletedBy: 'admin' }
    });

    // 4) Delete user row
    await prisma.user.delete({ where: { id: targetUserId } });

    await auditLogService.log({
      action: 'user.deleted',
      resourceType: 'user',
      resourceId: targetUserId,
      userId: adminUser.id,
      metadata: { deletedBy: 'admin', reason: reason || undefined, retention: { backupsDays: 7, financeYears: 7 } }
    });

    // 5) Optional email to the target user
    if (notify && target?.email) {
      try {
        const tpl = EmailTemplateService.accountDeletedNotice({ fullName: target.name || undefined, email: target.email });
        await scalewayEmailService.sendEmail({
          to: [{ email: target.email, name: target.name || target.email }],
          subject: tpl.subject,
          html: tpl.html,
          text: tpl.text,
          ...(bcc && bcc.length > 0 ? { bcc } : {})
        });
      } catch (err) {
        logger.warn({ err }, 'Failed to send account deletion confirmation email (admin)');
      }
    }

    // Admin-only Telegram notification (no PII, not stored)
    try {
      await notificationEvents.notifyChannel('telegram', 'user.deleted', {
        type: 'user.deleted',
        userId: adminUser.id,
        title: 'User deleted',
        message: 'A user account was deleted by admin',
        category: 'SYSTEM',
        priority: 'MEDIUM',
        data: { targetUserId }
      });
    } catch (e) {
      logger.warn({ err: e instanceof Error ? e.message : e, targetUserId }, 'Failed to emit Telegram admin notification for user.deleted (admin)');
    }
  }

  private static async tryBetterAuthDeleteUser(userId: string, request: FastifyRequest): Promise<void> {
    const headers = request.headers as any;
    const api: any = auth.api as any;

    const candidates: ((body: any, headers: any) => Promise<any>)[] = [];

    // Push potential method shapes in order of preference
    if (api?.deleteUser) candidates.push((body, headers) => api.deleteUser({ body, headers }));
    if (api?.removeUser) candidates.push((body, headers) => api.removeUser({ body, headers }));
    if (api?.users?.delete) candidates.push((body, headers) => api.users.delete({ body, headers }));
    if (api?.admin?.deleteUser) candidates.push((body, headers) => api.admin.deleteUser({ body, headers }));

    const body = { userId };

    for (const call of candidates) {
      try {
        await call(body, headers);
        logger.debug({ userId }, 'BetterAuth deleteUser call succeeded');
        return;
      } catch (err) {
        logger.debug({ err }, 'BetterAuth deleteUser variant failed, trying next');
      }
    }

    logger.warn({ userId }, 'No BetterAuth deleteUser method succeeded or available; proceeding with Prisma-only deletion');
  }

  private static async tryBetterAuthRevokeSessions(userId: string, request: FastifyRequest): Promise<void> {
    const headers = request.headers as any;
    const api: any = auth.api as any;

    const candidates: ((body: any, headers: any) => Promise<any>)[] = [];
    if (api?.revokeSessions) candidates.push((body, headers) => api.revokeSessions({ body, headers }));
    if (api?.revokeUserSessions) candidates.push((body, headers) => api.revokeUserSessions({ body, headers }));
    if (api?.sessions?.revoke) candidates.push((body, headers) => api.sessions.revoke({ body, headers }));

    const body = { userId };

    for (const call of candidates) {
      try {
        await call(body, headers);
        logger.debug({ userId }, 'BetterAuth revoke sessions call succeeded');
        return;
      } catch (err) {
        logger.debug({ err }, 'BetterAuth revoke sessions variant failed, trying next');
      }
    }

    // Final fallback: signOut only affects current session, but try it in case that's useful
    try {
      if (api?.signOut) {
        await api.signOut({ headers });
        logger.debug({ userId }, 'BetterAuth signOut called as a fallback');
      }
    } catch (err) {
      logger.debug({ err }, 'BetterAuth signOut fallback failed');
    }
  }
}


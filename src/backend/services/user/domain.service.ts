import { prisma } from '../../lib/prisma.js';
import { PostfixManager } from '../postfix-manager.js';
import { DNSVerifier } from '../dns-verifier.js';
import { logger } from '../../utils/logger.js';
import { webSocketService } from '../websocket.service.js';
import { NotificationService } from '../notifications/notification.service.js';

const postfixManager = new PostfixManager();
const dnsVerifier = new DNSVerifier();

export interface CreateDomainData {
  domain: string;
  webhookUrl?: string;
  webhookId?: string;
  active?: boolean;
  createCatchAll?: boolean;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
  configuration?: any; // Domain configuration object
  userId: string;
}

export interface UpdateDomainData {
  active?: boolean;
  webhookId?: string;
  allowAttachments?: boolean;
  includeEnvelope?: boolean;
  spamFiltering?: boolean;
  configuration?: any; // Domain configuration object
}

export class DomainService {
  /**
   * Get catch-all alias for a domain (used for webhook info)
   */
  private async getCatchAllAlias(domainId: string) {
    return await prisma.alias.findFirst({
      where: {
        domainId: domainId,
        email: { startsWith: '*@' }
      },
      include: { webhook: true }
    });
  }

  /**
   * Get all domains for a user
   */
  async getUserDomains(userId: string) {
    const dbDomains = await prisma.domain.findMany({
      where: { userId },
      include: {
        aliases: {
          include: { webhook: true },
          where: { active: true }
        }
      },
      orderBy: { createdAt: 'desc' },
    });

    // Make postfix call resilient to failures
    let postfixDomainsList: string[] = [];
    try {
      postfixDomainsList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    const domains = await Promise.all(dbDomains.map(async (d) => {
      // Get catch-all alias for webhook info
      const catchAllAlias = await this.getCatchAllAlias(d.id);

      // Extract spam filtering setting from configuration
      const config = d.configuration as any || {};
      const spamFiltering = config.spamFiltering?.enabled || false;

      const mappedDomain = {
        id: d.id,
        domain: d.domain,
        webhook: catchAllAlias?.webhook ? {
          id: catchAllAlias.webhook.id,
          name: catchAllAlias.webhook.name,
          url: catchAllAlias.webhook.url,
          verified: catchAllAlias.webhook.verified
        } : null,
        active: d.active,
        isVerified: d.verified,
        verificationStatus: d.verificationStatus,
        configuration: d.configuration,
        spamFiltering: spamFiltering, // Include spam filtering status
        postfix_configured: postfixDomainsList.includes(d.domain),
        createdAt: d.createdAt.toISOString(),
        updatedAt: d.updatedAt.toISOString(),
        aliases: d.aliases.map(alias => ({
          id: alias.id,
          email: alias.email,
          webhookName: alias.webhook?.name,
          active: alias.active,
        })),
        lastVerificationAttempt: d.lastVerificationAttempt?.toISOString(),
        verificationFailureCount: d.verificationFailureCount,
        nextVerificationCheck: d.nextVerificationCheck?.toISOString(),
        expectedTxtRecord: d.domain ? DNSVerifier.getExpectedTXTRecord(d.domain) : null,
      };

      logger.info({
        domainId: mappedDomain.id,
        mappedConfiguration: mappedDomain.configuration,
        originalConfiguration: d.configuration
      }, 'Mapped domain configuration debug');

      return mappedDomain;
    }));

    return {
      domains,
      total: domains.length,
      postfix_status: 'active',
      verified_count: domains.filter(d => d.isVerified).length,
      pending_verification: domains.filter(d => d.verificationStatus === 'PENDING').length,
    };
  }

  /**
   * Get domain by ID
   */
  async getDomain(domainId: string, userId: string) {
    const domainConfig = await prisma.domain.findFirst({ 
      where: { 
        id: domainId,
        userId  
      }, 
      include: { 
        aliases: { include: { webhook: true } }
      } 
    });

    if (!domainConfig) {
      return null;
    }

    // Get catch-all alias for webhook info
    const catchAllAlias = await this.getCatchAllAlias(domainId);
    
    // Make postfix call resilient to failures
    let postfixList: string[] = [];
    try {
      postfixList = await postfixManager.getConfiguredDomains();
    } catch (error: any) {
      logger.warn({ error: error.message }, 'Failed to get postfix domains, continuing without');
    }

    return {
      id: domainConfig.id,
      domain: domainConfig.domain,
      webhookUrl: catchAllAlias?.webhook?.url,
      webhookName: catchAllAlias?.webhook?.name,
      active: domainConfig.active,
      isVerified: domainConfig.verified,
      verificationStatus: domainConfig.verificationStatus,
      configuration: domainConfig.configuration,
      postfix_configured: postfixList.includes(domainConfig.domain),
      createdAt: domainConfig.createdAt.toISOString(),
      updatedAt: domainConfig.updatedAt.toISOString(),
      aliases: domainConfig.aliases.map((a: any) => ({
        id: a.id,
        email: a.email,
        webhookName: a.webhook?.name,
        active: a.active
      })),
      lastVerificationAttempt: domainConfig.lastVerificationAttempt?.toISOString(),
      verificationFailureCount: domainConfig.verificationFailureCount,
      nextVerificationCheck: domainConfig.nextVerificationCheck?.toISOString(),
      expectedTxtRecord: DNSVerifier.getExpectedTXTRecord(domainConfig.domain),
    };
  }

  /**
   * Create a new domain
   */
  async createDomain(data: CreateDomainData) {
    // Validate domain format
    if (!DNSVerifier.isValidDomain(data.domain)) {
      throw new Error('Invalid domain format');
    }

    if (!data.webhookUrl && !data.webhookId) {
      throw new Error('Either webhookUrl or webhookId is required');
    }

    const result = await prisma.$transaction(async (tx) => {
      // Get user
      const currentUser = await tx.user.findUnique({
        where: { id: data.userId }
      });
      
      if (!currentUser) {
        throw new Error('User not found');
      }
      
      // Get webhook for catch-all alias
      let webhook;

      if (data.webhookId) {
        // Use existing webhook by ID
        webhook = await tx.webhook.findFirst({
          where: { id: data.webhookId, userId: currentUser.id }
        });

        if (!webhook) {
          throw new Error('Webhook not found or does not belong to user');
        }
      } else if (data.webhookUrl) {
        // Legacy support: create or find webhook by URL
        webhook = await tx.webhook.findFirst({
          where: { url: data.webhookUrl, userId: currentUser.id }
        });

        if (!webhook) {
          webhook = await tx.webhook.create({
            data: {
              url: data.webhookUrl,
              name: `Default webhook for ${data.domain}`,
              description: `Auto-created webhook for ${data.domain}`,
              userId: currentUser.id
            }
          });
        }
      }

      // Prepare domain configuration
      const configuration: any = {};
      if (data.allowAttachments !== undefined) {
        configuration.allowAttachments = data.allowAttachments;
      }
      if (data.includeEnvelope !== undefined) {
        configuration.includeEnvelope = data.includeEnvelope;
      }

      // Include any additional configuration from the request
      if (data.configuration) {
        Object.assign(configuration, data.configuration);
      }

      // Create domain (without webhook reference)
      const newDomain = await tx.domain.create({
        data: {
          domain: data.domain,
          userId: currentUser.id,
          active: data.active ?? true,
          verified: false,
          verificationStatus: 'PENDING',
          nextVerificationCheck: new Date(Date.now() + 15 * 60 * 1000),
          configuration: Object.keys(configuration).length > 0 ? configuration : undefined,
        },
      });

      // Always create catch-all alias with the webhook
      const catchAllAlias = await tx.alias.create({
        data: {
          domainId: newDomain.id,
          email: `*@${newDomain.domain}`,
          webhookId: webhook!.id,
          active: true,
          configuration: Object.keys(configuration).length > 0 ? configuration : undefined,
        },
      });

      return { domain: newDomain, webhook, catchAllAlias, currentUser };
    });

    // Sync domain to Postfix AFTER transaction completes successfully
    try {
      await postfixManager.addDomainWithUserId(data.domain, result.currentUser.id, result.currentUser.planType);
    } catch (postfixError) {
      // Log error but don't fail the domain creation since DB transaction already succeeded
      logger.error({
        domain: data.domain,
        userId: data.userId,
        error: postfixError instanceof Error ? postfixError.message : postfixError
      }, 'Failed to sync domain to Postfix after successful database creation');

      // Could implement compensation logic here if needed
      // For now, domain exists in DB but not in Postfix - admin can manually sync
    }

    const instructions = {
      mx_record: {
        type: 'MX',
        name: '@',
        value: process.env.MAIL_HOSTNAME || 'your-mail-server.example.com',
        priority: 10
      },
      txt_record: {
        type: 'TXT',
        name: '@',
        value: DNSVerifier.getExpectedTXTRecord(result.domain.domain)
      },
      next_steps: [
        'Add the MX record to your DNS settings',
        'Add the TXT record for domain verification',
        'Wait for DNS propagation (up to 24 hours)',
        'Use the verify endpoint to check verification status'
      ]
    };

    const responseDomain = {
      id: result.domain.id,
      domain: result.domain.domain,
      apiKey: null,
      dkimPublicKey: null,
      dkimSelector: null,
      isVerified: result.domain.verified,
      verificationToken: null,
      createdAt: result.domain.createdAt.toISOString(),
      updatedAt: result.domain.updatedAt.toISOString(),
    };

    const webhookInfo = {
      id: result.webhook.id,
      url: result.webhook.url,
      name: result.webhook.name,
      verified: result.webhook.verified
    };

    // Admin-only Telegram notification for domain created (no PII, not stored)
    try {
      const { notificationEvents } = await import('../notifications/notification-events.service.js');
      await notificationEvents.notifyChannel('telegram', 'domain.created', {
        type: 'domain.created',
        userId: data.userId,
        title: 'Domain created',
        message: 'A new domain was created',
        category: 'DOMAIN',
        priority: 'MEDIUM'
      });
    } catch (e) {
      logger.warn({ err: e instanceof Error ? e.message : e, userId: data.userId }, 'Failed to emit Telegram admin notification for domain.created');
    }

    return {
      success: true,
      domain: responseDomain,
      webhook: webhookInfo,
      instructions,
      postfix_status: 'configured'
    };
  }

  /**
   * Update a domain by ID
   */
  async updateDomainById(domainId: string, userId: string, updates: UpdateDomainData) {
    const existing = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    const dataToUpdate: any = { updatedAt: new Date() };
    if (updates.active !== undefined) dataToUpdate.active = updates.active;

    // Handle configuration updates
    if (updates.configuration !== undefined) {
      dataToUpdate.configuration = updates.configuration;
    }

    // Handle webhook updates by updating the catch-all alias
    if (updates.webhookId !== undefined) {
      // Verify webhook belongs to user
      const webhook = await prisma.webhook.findFirst({
        where: { id: updates.webhookId, userId }
      });
      if (!webhook) {
        throw new Error('Webhook not found');
      }

      // Update catch-all alias webhook instead of domain webhook
      const catchAllAlias = await this.getCatchAllAlias(domainId);
      if (catchAllAlias) {
        await prisma.alias.update({
          where: { id: catchAllAlias.id },
          data: { webhookId: updates.webhookId }
        });
      }
    }

    // Handle configuration updates (including spam filtering)
    if (updates.allowAttachments !== undefined || updates.includeEnvelope !== undefined || updates.spamFiltering !== undefined) {
      const currentConfig = existing.configuration as any || {};
      const newConfig = {
        ...currentConfig,
        ...(updates.allowAttachments !== undefined && { allowAttachments: updates.allowAttachments }),
        ...(updates.includeEnvelope !== undefined && { includeEnvelope: updates.includeEnvelope }),
        ...(updates.spamFiltering !== undefined && {
          spamFiltering: {
            ...currentConfig.spamFiltering,
            enabled: updates.spamFiltering
          }
        })
      };
      dataToUpdate.configuration = newConfig;
    }

    // Handle spam filtering postfix updates (only for legacy spamFiltering boolean field)
    if (updates.spamFiltering !== undefined) {
      try {
        await postfixManager.updateDomainSpamFiltering(existing.domain, updates.spamFiltering);
        logger.info({
          domain: existing.domain,
          enabled: updates.spamFiltering,
          userId
        }, 'Updated domain spam filtering via updateDomainById');
      } catch (error: any) {
        logger.error({
          error: error.message,
          domain: existing.domain,
          enabled: updates.spamFiltering,
          userId
        }, 'Failed to update postfix spam filtering');
        throw new Error('Failed to update spam filtering configuration');
      }
    }

    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: dataToUpdate
    });

    const responseDomain = {
      id: updated.id,
      domain: updated.domain,
      apiKey: null,
      dkimPublicKey: null,
      dkimSelector: null,
      isVerified: updated.verified,
      verificationToken: null,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString()
    };

    return {
      success: true,
      domain: responseDomain,
      message: 'Domain updated successfully'
    };
  }

  /**
   * Update domain status by ID
   */
  async updateDomainStatus(domainId: string, userId: string, active: boolean) {
    // Verify domain belongs to user
    const existing = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existing) {
      throw new Error('Domain not found');
    }

    // Update the domain status
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        active: active,
        updatedAt: new Date()
      }
    });

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        active: updated.active,
        updatedAt: updated.updatedAt.toISOString()
      },
      message: `Domain ${updated.domain} ${active ? 'activated' : 'deactivated'} successfully`
    };
  }

  /**
   * Update domain webhook by ID (actually updates catch-all alias webhook)
   */
  async updateDomainWebhook(domainId: string, userId: string, webhookId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Verify webhook belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Update the catch-all alias webhook instead of domain webhook
    const catchAllAlias = await this.getCatchAllAlias(domainId);
    if (!catchAllAlias) {
      throw new Error('No catch-all alias found for domain');
    }

    await prisma.alias.update({
      where: { id: catchAllAlias.id },
      data: { webhookId: webhookId }
    });

    return {
      success: true,
      domain: {
        id: existingDomain.id,
        domain: existingDomain.domain,
        webhookId: webhookId,
        updatedAt: new Date().toISOString()
      },
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url
      },
      message: `Domain webhook updated to "${webhook.name}"`
    };
  }

  /**
   * Delete a domain by ID
   */
  async deleteDomain(domainId: string, userId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Delete the domain (aliases will be cascade deleted)
    await prisma.domain.delete({
      where: { id: domainId }
    });

    // Remove from postfix configuration
    try {
      await postfixManager.removeDomain(existingDomain.domain);
    } catch (error) {
      logger.warn({ data: error }, `Failed to remove domain ${existingDomain.domain} from postfix:`);
      // Don't fail the operation if postfix removal fails
    }

    return {
      success: true,
      message: `Domain ${existingDomain.domain} deleted successfully`
    };
  }

  /**
   * Verify a domain by ID
   */
  async verifyDomain(domainId: string, userId: string) {
    // Verify domain belongs to user
    const existingDomain = await prisma.domain.findFirst({
      where: {
        id: domainId,
        userId
      }
    });

    if (!existingDomain) {
      throw new Error('Domain not found');
    }

    // Clear DNS cache for this domain to ensure fresh verification attempt
    dnsVerifier.clearCache(existingDomain.domain);
    logger.info({ domain: existingDomain.domain }, 'Manual verification requested - DNS cache cleared');

    // Perform DNS verification
    const verificationResult = await dnsVerifier.verifyDomainOwnership(existingDomain.domain);

    // Calculate verification status using same logic as verification worker
    const now = new Date();
    let nextCheck: Date | null;
    let newStatus: 'PENDING' | 'VERIFIED' | 'FAILED';
    const newFailureCount = verificationResult.verified ? 0 : existingDomain.verificationFailureCount + 1;

    // Determine if we should auto-disable (stop checks) after too many failures
    const shouldDisable = !verificationResult.verified && newFailureCount > 10;

    if (verificationResult.verified) {
      // Success - schedule periodic re-verification in 24 hours
      nextCheck = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      newStatus = 'VERIFIED';
    } else {
      // Failed - retry based on failure count (but disable if too many failures)
      if (shouldDisable) {
        // Disable and stop further checks
        nextCheck = null;
        newStatus = 'FAILED';
      } else if (newFailureCount >= 10) {
        // Too many failures (threshold reached) - mark as failed and check daily
        nextCheck = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        newStatus = 'FAILED';
      } else {
        // Keep as pending and retry in 15 minutes
        nextCheck = new Date(now.getTime() + 15 * 60 * 1000);
        newStatus = 'PENDING';
      }
    }

    const previousStatus = existingDomain.verificationStatus;
    const previousActive = existingDomain.active;

    // Update domain verification status
    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: {
        verified: verificationResult.verified,
        verificationStatus: newStatus,
        lastVerificationAttempt: now,
        verificationFailureCount: newFailureCount,
        nextVerificationCheck: nextCheck,
        active: shouldDisable ? false : existingDomain.active,
        updatedAt: now
      }
    });

    // Emit WebSocket event for real-time updates
    webSocketService.emitDomainVerificationUpdated(userId, {
      domainId: updated.id,
      domain: updated.domain,
      verified: updated.verified,
      verificationStatus: updated.verificationStatus,
      verificationFailureCount: newFailureCount,
      nextVerificationCheck: nextCheck?.toISOString(),
      error: verificationResult.error,
      timestamp: new Date().toISOString(),
    });

    // Notifications: send only on status changes or when auto-disabled
    try {
      if (verificationResult.verified && !existingDomain.verified) {
        await NotificationService.createDomainVerifiedNotification(userId, updated.domain);
        // Admin-only Telegram notification for domain verified (no PII, not stored)
        try {
          const { notificationEvents } = await import('../notifications/notification-events.service.js');
          await notificationEvents.notifyChannel('telegram', 'domain.verified', {
            type: 'domain.verified',
            userId,
            title: 'Domain verified',
            message: 'A domain was verified',
            category: 'DOMAIN',
            priority: 'HIGH'
          });
        } catch (e) {
          logger.warn({ err: e instanceof Error ? e.message : e, userId }, 'Failed to emit Telegram admin notification for domain.verified');
        }
      } else if (previousStatus !== newStatus && newStatus === 'FAILED') {
        await NotificationService.createDomainVerificationFailedNotification(userId, updated.domain, newFailureCount);
      }

      if (shouldDisable && previousActive) {
        await NotificationService.createDomainAutoDisabledNotification(userId, updated.domain);
      }
    } catch (error) {
      logger.error({ userId, domain: updated.domain, error }, 'Failed to create domain status change notification');
    }

    // Audit log for manual verification attempt (to match auto path visibility)
    try {
      await prisma.auditLog.create({
        data: {
          action: 'domain.verification.manual',
          resourceId: updated.id,
          resourceType: 'domain',
          metadata: {
            domain: updated.domain,
            verified: verificationResult.verified,
            automatic: false,
            previousStatus,
            newStatus,
            failureCount: newFailureCount,
            disabled: shouldDisable,
            nextCheck: nextCheck ? nextCheck.toISOString() : null,
            error: verificationResult.error,
          },
          expiresAt: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
      });
    } catch (error) {
      logger.error({ domain: existingDomain.domain, error }, 'Failed to create manual verification audit log');
    }

    return {
      success: true,
      domain: {
        id: updated.id,
        domain: updated.domain,
        verified: updated.verified,
        verificationStatus: updated.verificationStatus,
        updatedAt: updated.updatedAt.toISOString()
      },
      verification: verificationResult,
      message: verificationResult.verified ? 'Domain verified successfully' : (shouldDisable ? 'Domain verification failed and domain was disabled' : 'Domain verification failed')
    };
  }

  /**
   * Get spam filtering settings for a domain
   */
  async getSpamFilterSettings(domainId: string, userId: string) {
    const domain = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Get current settings from domain configuration
    const config = domain.configuration as any || {};
    const spamConfig = config.spamFiltering || {};

    return {
      enabled: spamConfig.enabled || false,
      thresholds: {
        green: spamConfig.thresholds?.green || 2.0,
        red: spamConfig.thresholds?.red || 10.0
      }
    };
  }

  /**
   * Update spam filtering settings for a domain
   */
  async updateSpamFilterSettings(
    domainId: string,
    userId: string,
    settings: { enabled: boolean; thresholds: { green: number; red: number } }
  ) {
    const domain = await prisma.domain.findFirst({
      where: { id: domainId, userId }
    });

    if (!domain) {
      throw new Error('Domain not found');
    }

    // Get current spam filtering state to check if enabled/disabled changed
    const currentConfig = domain.configuration as any || {};
    const currentSpamConfig = currentConfig.spamFiltering || {};
    const previousEnabled = currentSpamConfig.enabled || false;
    const enabledStateChanged = previousEnabled !== settings.enabled;

    // Update domain configuration in database
    const updatedConfig = {
      ...currentConfig,
      spamFiltering: {
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }
    };

    const updated = await prisma.domain.update({
      where: { id: domainId },
      data: { configuration: updatedConfig }
    });

    // Only update Postfix configuration if enabled/disabled state changed
    // Threshold changes don't require postfix updates (read from postgres by advanced-process-email.js)
    if (enabledStateChanged) {
      try {
        await postfixManager.updateDomainSpamFiltering(domain.domain, settings.enabled);
        logger.info({
          domain: domain.domain,
          enabled: settings.enabled,
          previousEnabled,
          thresholds: settings.thresholds
        }, 'Updated domain spam filtering routing in postfix');
      } catch (error) {
        logger.error({
          domain: domain.domain,
          error: error instanceof Error ? error.message : error
        }, 'Failed to update Postfix spam filtering configuration');
        // Don't throw here - the database update succeeded
      }
    } else {
      logger.info({
        domain: domain.domain,
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }, 'Updated spam filtering thresholds (no postfix update needed)');
    }

    return {
      success: true,
      message: 'Spam filtering settings updated successfully',
      settings: {
        enabled: settings.enabled,
        thresholds: settings.thresholds
      }
    };
  }
}

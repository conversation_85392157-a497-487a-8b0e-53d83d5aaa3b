import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

interface LogsFilter {
  domainId?: string;
  aliasId?: string;
  status?: 'PENDING' | 'DELIVERED' | 'FAILED' | 'RETRYING' | 'EXPIRED';
  limit: number;
  offset: number;
  testWebhooksOnly?: boolean;
}

export class LogsService {
  /**
   * Get email logs for a user with filtering and pagination
   */
  async getUserLogs(userId: string, filter: LogsFilter) {
    try {
      // Build the where clause for filtering
      let whereClause: any = {};
      
      // Handle testWebhooksOnly filter
      if (filter.testWebhooksOnly) {
        // Show only test webhook calls for this user
        whereClause = {
          isTestWebhook: true,
          OR: [
            // Test webhooks directly associated with the user
            { userId },
            // Test webhooks via domain ownership
            { domain: { userId } },
            // Legacy test webhooks with user ID pattern
            {
              toAddresses: {
                has: `${userId.slice(-8)}@user.emailconnect.eu`
              }
            }
          ]
        };
      } else {
        // Regular filtering - include all emails (both normal and test) for the user's data
        whereClause = {
          OR: [
            { domain: { userId } }, // Regular domain-based emails
            { userId } // Test webhooks stored with userId and no domain
          ]
        };
      }

      // Filter by domain if specified (only if not filtering for test webhooks only)
      if (filter.domainId && !filter.testWebhooksOnly) {
        // Only show emails from the specified domain
        whereClause = {
          domain: { userId },
          domainId: filter.domainId
        };
      }

      // Filter by delivery status if specified
      if (filter.status) {
        whereClause.deliveryStatus = filter.status;
      }

      // Filter by alias if specified
      // This is more complex because we need to match the email address to an alias
      if (filter.aliasId) {
        // First, get the alias to find its email address
        const alias = await prisma.alias.findFirst({
          where: {
            id: filter.aliasId,
            domain: { userId }
          }
        });

        if (alias) {
          // Filter emails that were sent to this specific alias email
          whereClause.toAddresses = {
            has: alias.email
          };
        } else {
          // If alias not found, return empty result
          return {
            logs: [],
            total: 0,
            hasMore: false
          };
        }
      }

      // Get total count for pagination
      const total = await prisma.email.count({
        where: whereClause
      });

      // Get the logs with pagination
      const logs = await prisma.email.findMany({
        where: whereClause,
        include: {
          domain: {
            select: {
              id: true,
              domain: true
            }
          },
          alias: {
            select: {
              id: true,
              email: true,
              webhook: {
                select: {
                  id: true,
                  name: true,
                  url: true
                }
              }
            }
          },
          webhook: {
            select: {
              id: true,
              name: true,
              url: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: filter.limit,
        skip: filter.offset
      });

      // Format the response
      const formattedLogs = logs.map(log => ({
        id: log.id,
        messageId: log.messageId,
        fromAddress: log.fromAddress,
        toAddresses: log.toAddresses,
        subject: log.subject,
        deliveryStatus: log.deliveryStatus,
        deliveryAttempts: log.deliveryAttempts,
        lastAttemptAt: log.lastAttemptAt?.toISOString() || null,
        deliveredAt: log.deliveredAt?.toISOString() || null,
        errorMessage: log.errorMessage,
        createdAt: log.createdAt.toISOString(),
        expiresAt: log.expiresAt.toISOString(),
        isTestWebhook: log.isTestWebhook || false,
        webhookPayload: log.webhookPayload,
        httpStatus: log.httpStatus || null,
        domain: log.domain ? {
          id: log.domain.id,
          domain: log.domain.domain
        } : null,
        alias: log.alias ? {
          id: log.alias.id,
          email: log.alias.email,
          webhook: log.alias.webhook
        } : null,
        webhook: log.webhook ? {
          id: log.webhook.id,
          name: log.webhook.name,
          url: log.webhook.url
        } : null
      }));

      const hasMore = filter.offset + filter.limit < total;

      logger.debug({
        userId,
        filter,
        totalLogs: total,
        returnedLogs: formattedLogs.length,
        hasMore
      }, 'Retrieved user logs');

      return {
        logs: formattedLogs,
        total,
        hasMore
      };

    } catch (error: any) {
      logger.error({
        userId,
        filter,
        error: error.message,
        stack: error.stack
      }, 'Failed to retrieve user logs');
      
      throw new Error('Failed to retrieve logs');
    }
  }
}

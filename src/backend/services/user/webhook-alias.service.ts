import { prisma } from '../../lib/prisma.js';
import { WebhookService } from './webhook.service.js';
import { AliasService } from './alias.service.js';
import { DomainService } from './domain.service.js';
import { logger } from '../../utils/logger.js';

interface CreateWebhookAliasData {
  domainId: string;
  webhookUrl: string;
  webhookName: string;
  webhookDescription?: string;
  aliasType: 'catchall' | 'specific';
  localPart?: string;
  syncWithDomain?: boolean;
  autoVerify?: boolean;
  firstOrCreate?: boolean;
  updateWebhookData?: boolean;
  userId: string;
}

interface WebhookAliasResult {
  success: boolean;
  webhook?: {
    id: string;
    name: string;
    url: string;
    verified: boolean;
    verificationToken?: string;
    createdAt: string;
  };
  alias?: {
    id: string;
    email: string;
    active: boolean;
    createdAt: string;
  };
  action?: 'created' | 'updated' | 'webhook_added' | 'cross_domain_update';
  domain?: {
    id: string;
    domain: string;
    webhookUpdated: boolean;
  };
  warning?: string;
  message?: string;
  error?: string;
}

export class WebhookAliasService {
  private webhookService: WebhookService;
  private aliasService: AliasService;
  private domainService: DomainService;

  constructor() {
    this.webhookService = new WebhookService();
    this.aliasService = new AliasService();
    this.domainService = new DomainService();
  }

  async createWebhookAlias(data: CreateWebhookAliasData): Promise<WebhookAliasResult> {
    // Start a transaction to ensure atomicity
    return await prisma.$transaction(async (tx) => {
      try {
        // 1. Validate domain exists and user has access
        const domain = await tx.domain.findFirst({
          where: {
            id: data.domainId,
            userId: data.userId
          }
        });

        if (!domain) {
          throw new Error('Domain not found or access denied');
        }

        // 2. Construct email address based on alias type
        let emailAddress: string;
        if (data.aliasType === 'catchall') {
          emailAddress = `*@${domain.domain}`;
        } else {
          if (!data.localPart) {
            throw new Error('localPart is required for specific aliases');
          }
          emailAddress = `${data.localPart}@${domain.domain}`;
        }

        // 3. Smart create/update logic with firstOrCreate
        if (data.firstOrCreate && data.aliasType === 'specific') {
          return await this.handleFirstOrCreateLogic(tx, data, domain, emailAddress);
        }

        // 4. Check if alias already exists (legacy behavior)
        const existingAlias = await tx.alias.findFirst({
          where: {
            email: emailAddress,
            domainId: data.domainId
          }
        });

        // For catch-all aliases, update existing instead of failing (existing behavior)
        if (existingAlias && data.aliasType === 'catchall') {
          return await this.handleCatchAllUpdate(tx, data, domain, existingAlias, emailAddress);
        }

        // For non-catch-all aliases without firstOrCreate, still fail if exists
        if (existingAlias && !data.firstOrCreate) {
          throw new Error(`Alias ${emailAddress} already exists`);
        }

        // 5. Create new webhook and alias (standard creation path)
        return await this.handleStandardCreation(tx, data, domain, emailAddress);

      } catch (error) {
        logger.error({ data: error }, 'Error in createWebhookAlias:');
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
      }
    });
  }

  // Handle smart create/update logic for specific aliases
  private async handleFirstOrCreateLogic(tx: any, data: CreateWebhookAliasData, domain: any, emailAddress: string): Promise<WebhookAliasResult> {
    // Check if alias exists (including cross-domain check)
    const existingAlias = await tx.alias.findFirst({
      where: {
        email: emailAddress
      },
      include: {
        webhook: true,
        domain: true
      }
    });

    if (existingAlias) {
      // Cross-domain update check
      if (existingAlias.domainId !== data.domainId) {
        logger.info(`Cross-domain update: alias ${emailAddress} from domain ${existingAlias.domainId} to ${data.domainId}`);
        
        // Update alias to new domain
        const updatedAlias = await tx.alias.update({
          where: { id: existingAlias.id },
          data: { domainId: data.domainId }
        });

        return {
          success: true,
          webhook: existingAlias.webhook ? {
            id: existingAlias.webhook.id,
            name: existingAlias.webhook.name,
            url: existingAlias.webhook.url,
            verified: existingAlias.webhook.verified,
            verificationToken: existingAlias.webhook.verified ? undefined : existingAlias.webhook.id.slice(-5),
            createdAt: existingAlias.webhook.createdAt.toISOString()
          } : undefined,
          alias: {
            id: updatedAlias.id,
            email: updatedAlias.email,
            active: updatedAlias.active,
            createdAt: updatedAlias.createdAt.toISOString()
          },
          action: 'cross_domain_update',
          warning: `Updated alias from domain ${existingAlias.domain.domain} to ${domain.domain}`,
          message: `Cross-domain update: moved alias ${emailAddress} to domain ${domain.domain}`
        };
      }

      // Update existing alias webhook
      if (existingAlias.webhookId) {
        // Update existing webhook if updateWebhookData is true
        if (data.updateWebhookData) {
          const updatedWebhook = await tx.webhook.update({
            where: { id: existingAlias.webhookId },
            data: {
              url: data.webhookUrl,
              name: data.webhookName,
              description: data.webhookDescription,
              verified: data.autoVerify || existingAlias.webhook.verified
            }
          });

          // Auto-verify with token if requested
          if (data.autoVerify && !existingAlias.webhook.verified) {
            await tx.webhook.update({
              where: { id: existingAlias.webhookId },
              data: {
                verified: true,
                verificationToken: existingAlias.webhookId.slice(-5)
              }
            });
            updatedWebhook.verified = true;
          }

          return {
            success: true,
            webhook: {
              id: updatedWebhook.id,
              name: updatedWebhook.name,
              url: updatedWebhook.url,
              verified: updatedWebhook.verified,
              verificationToken: updatedWebhook.verified ? undefined : updatedWebhook.id.slice(-5),
              createdAt: updatedWebhook.createdAt.toISOString()
            },
            alias: {
              id: existingAlias.id,
              email: existingAlias.email,
              active: existingAlias.active,
              createdAt: existingAlias.createdAt.toISOString()
            },
            action: 'updated',
            message: `Updated existing alias ${emailAddress} webhook to ${data.webhookName}`
          };
        } else {
          // Don't update webhook data, just return existing
          return {
            success: true,
            webhook: {
              id: existingAlias.webhook.id,
              name: existingAlias.webhook.name,
              url: existingAlias.webhook.url,
              verified: existingAlias.webhook.verified,
              verificationToken: existingAlias.webhook.verified ? undefined : existingAlias.webhook.id.slice(-5),
              createdAt: existingAlias.webhook.createdAt.toISOString()
            },
            alias: {
              id: existingAlias.id,
              email: existingAlias.email,
              active: existingAlias.active,
              createdAt: existingAlias.createdAt.toISOString()
            },
            action: 'updated',
            message: `Found existing alias ${emailAddress} - no changes made`
          };
        }
      } else {
        // Alias exists but no webhook - create and link
        const webhookResult = await this.webhookService.createWebhook({
          name: data.webhookName,
          url: data.webhookUrl,
          description: data.webhookDescription,
          userId: data.userId
        });

        if (!webhookResult.webhook) {
          throw new Error('Failed to create webhook');
        }

        // Auto-verify if requested
        if (data.autoVerify) {
          await tx.webhook.update({
            where: { id: webhookResult.webhook.id },
            data: {
              verified: true,
              verificationToken: webhookResult.webhook.id.slice(-5)
            }
          });
          webhookResult.webhook.verified = true;
        }

        // Link webhook to existing alias
        const updatedAlias = await tx.alias.update({
          where: { id: existingAlias.id },
          data: {
            webhookId: webhookResult.webhook.id,
            active: webhookResult.webhook.verified
          }
        });

        return {
          success: true,
          webhook: {
            id: webhookResult.webhook.id,
            name: webhookResult.webhook.name,
            url: webhookResult.webhook.url,
            verified: webhookResult.webhook.verified,
            verificationToken: webhookResult.webhook.verified ? undefined : webhookResult.webhook.id.slice(-5),
            createdAt: webhookResult.webhook.createdAt
          },
          alias: {
            id: updatedAlias.id,
            email: updatedAlias.email,
            active: updatedAlias.active,
            createdAt: updatedAlias.createdAt.toISOString()
          },
          action: 'webhook_added',
          message: `Added webhook to existing alias ${emailAddress}`
        };
      }
    } else {
      // Create new alias and webhook
      return await this.handleStandardCreation(tx, data, domain, emailAddress);
    }
  }

  // Handle catch-all alias updates (existing behavior)
  private async handleCatchAllUpdate(tx: any, data: CreateWebhookAliasData, domain: any, existingAlias: any, emailAddress: string): Promise<WebhookAliasResult> {
    logger.info(`Updating existing catch-all alias ${emailAddress} with new webhook`);

    // Create new webhook first
    const webhookResult = await this.webhookService.createWebhook({
      name: data.webhookName,
      url: data.webhookUrl,
      description: data.webhookDescription,
      userId: data.userId
    });

    if (!webhookResult.webhook) {
      throw new Error('Failed to create webhook');
    }

    // Auto-verify webhook if requested
    if (data.autoVerify) {
      await tx.webhook.update({
        where: { id: webhookResult.webhook.id },
        data: {
          verified: true,
          verificationToken: webhookResult.webhook.id.slice(-5)
        }
      });
      webhookResult.webhook.verified = true;
    }

    // Update existing alias with new webhook
    const updatedAlias = await tx.alias.update({
      where: { id: existingAlias.id },
      data: {
        webhookId: webhookResult.webhook.id,
        active: webhookResult.webhook.verified
      }
    });

    // Handle syncWithDomain for catch-all aliases
    let domainWebhookUpdated = false;
    if (data.syncWithDomain) {
      try {
        await tx.domain.update({
          where: { id: domain.id },
          data: { webhookUrl: data.webhookUrl }
        });
        domainWebhookUpdated = true;
      } catch (error) {
        logger.error({ data: error }, 'Failed to sync domain webhook');
      }
    }

    return {
      success: true,
      webhook: {
        id: webhookResult.webhook.id,
        name: webhookResult.webhook.name,
        url: webhookResult.webhook.url,
        verified: webhookResult.webhook.verified,
        verificationToken: webhookResult.webhook.verified ? undefined : webhookResult.webhook.id.slice(-5),
        createdAt: webhookResult.webhook.createdAt
      },
      alias: {
        id: updatedAlias.id,
        email: updatedAlias.email,
        active: updatedAlias.active,
        createdAt: updatedAlias.createdAt.toISOString()
      },
      action: 'updated',
      domain: {
        id: domain.id,
        domain: domain.domain,
        webhookUpdated: domainWebhookUpdated
      },
      message: `Updated catch-all alias ${emailAddress} with webhook ${data.webhookName}`
    };
  }

  // Handle standard creation (new webhook + alias)
  private async handleStandardCreation(tx: any, data: CreateWebhookAliasData, domain: any, emailAddress: string): Promise<WebhookAliasResult> {
    // Create webhook first
    const webhookResult = await this.webhookService.createWebhook({
      name: data.webhookName,
      url: data.webhookUrl,
      description: data.webhookDescription,
      userId: data.userId
    });

    if (!webhookResult.webhook) {
      throw new Error('Failed to create webhook');
    }

    // Auto-verify webhook if requested
    if (data.autoVerify) {
      await tx.webhook.update({
        where: { id: webhookResult.webhook.id },
        data: {
          verified: true,
          verificationToken: webhookResult.webhook.id.slice(-5)
        }
      });
      webhookResult.webhook.verified = true;
    }

    // Create alias
    const alias = await tx.alias.create({
      data: {
        email: emailAddress,
        domainId: data.domainId,
        webhookId: webhookResult.webhook.id,
        active: webhookResult.webhook.verified
      }
    });

    // Handle syncWithDomain for catch-all aliases
    let domainWebhookUpdated = false;
    if (data.aliasType === 'catchall' && data.syncWithDomain) {
      try {
        await tx.domain.update({
          where: { id: domain.id },
          data: { webhookUrl: data.webhookUrl }
        });
        domainWebhookUpdated = true;
      } catch (error) {
        logger.error({ data: error }, 'Failed to sync domain webhook');
      }
    }

    const message = data.aliasType === 'catchall'
      ? `Created catch-all alias ${emailAddress} with webhook ${data.webhookName}`
      : `Created alias ${emailAddress} with webhook ${data.webhookName}`;

    return {
      success: true,
      webhook: {
        id: webhookResult.webhook.id,
        name: webhookResult.webhook.name,
        url: webhookResult.webhook.url,
        verified: webhookResult.webhook.verified,
        verificationToken: webhookResult.webhook.verified ? undefined : webhookResult.webhook.id.slice(-5),
        createdAt: webhookResult.webhook.createdAt
      },
      alias: {
        id: alias.id,
        email: alias.email,
        active: alias.active,
        createdAt: alias.createdAt.toISOString()
      },
      action: 'created',
      domain: data.aliasType === 'catchall' ? {
        id: domain.id,
        domain: domain.domain,
        webhookUpdated: domainWebhookUpdated
      } : undefined,
      message
    };
  }

  // Helper method to validate request data
  validateCreateRequest(data: any): { valid: boolean; error?: string } {
    if (!data.domainId) {
      return { valid: false, error: 'domainId is required' };
    }

    if (!data.webhookUrl) {
      return { valid: false, error: 'webhookUrl is required' };
    }

    if (!data.webhookName) {
      return { valid: false, error: 'webhookName is required' };
    }

    if (!data.aliasType || !['catchall', 'specific'].includes(data.aliasType)) {
      return { valid: false, error: 'aliasType must be either "catchall" or "specific"' };
    }

    if (data.aliasType === 'specific' && !data.localPart) {
      return { valid: false, error: 'localPart is required when aliasType is "specific"' };
    }

    // Validate URL format
    try {
      new URL(data.webhookUrl);
    } catch {
      return { valid: false, error: 'webhookUrl must be a valid URL' };
    }

    return { valid: true };
  }
}

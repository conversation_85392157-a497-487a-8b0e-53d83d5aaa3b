import axios from 'axios'
import { env } from '../config/env.js'

export interface AnnouncementItem {
  title: string
  description?: string
  ctaLabel?: string
  ctaUrl?: string
  icon?: string
}

export interface FaqItem {
  question: string
  answer: string
  helpUrl?: string
  order?: number
}

interface CacheEntry<T> {
  data: T
  expiresAt: number
}

export class NotionContentService {
  private readonly apiBase = 'https://api.notion.com/v1'
  private readonly notionVersion = '2022-06-28'
  private cache: Map<string, CacheEntry<any>> = new Map()
  // Honor 0 to disable cache for testing
  private ttlMs = ((env.NOTION_CACHE_TTL_SECONDS ?? 300)) * 1000

  private get headers() {
    if (!env.NOTION_API_KEY) {
      throw new Error('NOTION_API_KEY is not configured')
    }
    return {
      'Authorization': `Bearer ${env.NOTION_API_KEY}`,
      'Notion-Version': this.notionVersion,
      'Content-Type': 'application/json'
    }
  }

  private setCache<T>(key: string, data: T) {
    if (this.ttlMs <= 0) return
    this.cache.set(key, { data, expiresAt: Date.now() + this.ttlMs })
  }

  private getCache<T>(key: string): T | null {
    if (this.ttlMs <= 0) return null
    const found = this.cache.get(key)
    if (!found) return null
    if (Date.now() > found.expiresAt) {
      this.cache.delete(key)
      return null
    }
    return found.data as T
  }

  private firstText(block: any): string | undefined {
    const arr = block?.title || block?.rich_text
    if (Array.isArray(arr) && arr.length > 0) {
      return arr.map((t: any) => t.plain_text).join('')
    }
    return undefined
  }

  private getProperty(page: any, names: string[]): any {
    for (const n of names) {
      if (page.properties?.[n] != null) return page.properties[n]
    }
    return undefined
  }

  private asText(page: any, names: string[], fallback?: string): string | undefined {
    const p = this.getProperty(page, names)
    return this.firstText(p) ?? fallback
  }

  private asUrl(page: any, names: string[]): string | undefined {
    const p = this.getProperty(page, names)
    if (p?.url) return p.url as string
    // sometimes people store URL as rich_text
    const txt = this.firstText(p)
    if (txt && /^https?:\/\//.test(txt)) return txt
    return undefined
  }

  private asCheckbox(page: any, names: string[], def = false): boolean {
    const p = this.getProperty(page, names)
    return typeof p?.checkbox === 'boolean' ? p.checkbox : def
  }

  private asNumber(page: any, names: string[]): number | undefined {
    const p = this.getProperty(page, names)
    if (typeof p?.number === 'number') return p.number
    // sometimes number stored as text
    const txt = this.firstText(p)
    const num = txt != null ? Number(txt) : NaN
    return Number.isFinite(num) ? num : undefined
  }

  async getRandomActiveAnnouncement(): Promise<AnnouncementItem | null> {
    if (!env.NOTION_ANNOUNCEMENTS_DB_ID) return null
    const cacheKey = 'announcements:one'
    const cached = this.getCache<AnnouncementItem | null>(cacheKey)
    if (cached !== null) return cached

    const resp = await axios.post(
      `${this.apiBase}/databases/${env.NOTION_ANNOUNCEMENTS_DB_ID}/query`,
      {},
      { headers: this.headers }
    )

    const results: any[] = resp.data?.results || []
    const active = results.filter((p: any) => {
      // consider several spellings of the checkbox
      const prop = this.getProperty(p, ['is_active', 'Is Active', 'Active'])
      return !!prop?.checkbox
    })

    if (!active.length) {
      this.setCache(cacheKey, null)
      return null
    }

    const pick = active[Math.floor(Math.random() * active.length)]

    const item: AnnouncementItem = {
      title: this.asText(pick, ['Title', 'Name', 'title']) || 'Announcement',
      description: this.asText(pick, ['Description', 'description']),
      ctaLabel: this.asText(pick, ['cta', 'CTA', 'Label']),
      ctaUrl: this.asUrl(pick, ['url', 'URL', 'Link', 'CTA URL']),
      icon: this.asText(pick, ['icon', 'Icon']) || pick.icon?.emoji
    }

    this.setCache(cacheKey, item)
    return item
  }

  async getPublicFaqs(): Promise<FaqItem[]> {
    if (!env.NOTION_FAQ_DB_ID) return []
    const cacheKey = 'faqs:public'
    const cached = this.getCache<FaqItem[]>(cacheKey)
    if (cached) return cached

    const resp = await axios.post(
      `${this.apiBase}/databases/${env.NOTION_FAQ_DB_ID}/query`,
      {},
      { headers: this.headers }
    )

    const results: any[] = resp.data?.results || []

    const filtered = results.filter((page) => {
      const isActive = !!this.getProperty(page, ['is_active', 'Is Active', 'Active'])?.checkbox
      const isPublic = !!this.getProperty(page, ['is_public', 'Is Public', 'Public'])?.checkbox
      return isActive && isPublic
    })

    const faqs: FaqItem[] = filtered.map((page) => ({
      question: this.asText(page, ['question', 'Question', 'Title', 'Name']) || 'Untitled',
      answer: this.asText(page, ['answer', 'Answer']) || '',
      helpUrl: this.asUrl(page, ['help_url', 'Help URL', 'URL', 'Link']),
      order: this.asNumber(page, ['order', 'Order']) ?? 999
    }))
    .sort((a, b) => (a.order ?? 999) - (b.order ?? 999) || a.question.localeCompare(b.question))

    this.setCache(cacheKey, faqs)
    return faqs
  }
}


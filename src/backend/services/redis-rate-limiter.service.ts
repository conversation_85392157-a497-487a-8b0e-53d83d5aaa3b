import { Redis } from 'ioredis';
import { logger } from '../utils/logger.js';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyPrefix?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalRequests: number;
}

/**
 * Redis-based rate limiter service
 * Replaces memory-based rate limiting for high availability
 */
export class RedisRateLimiterService {
  private redisClient: Redis;
  private initialized = false;

  constructor(redisClient?: Redis) {
    if (redisClient) {
      this.redisClient = redisClient;
      this.initialized = true;
    }
  }

  async initialize(redisClient: Redis) {
    this.redisClient = redisClient;
    this.initialized = true;
    logger.info('Redis rate limiter service initialized');
  }

  /**
   * Check if a request is allowed under the rate limit
   * Uses Redis sorted sets for accurate, distributed rate limiting
   */
  async checkRateLimit(
    identifier: string, 
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    if (!this.initialized) {
      logger.warn('Rate limiter not initialized, allowing request');
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: Date.now() + config.windowMs,
        totalRequests: 1,
      };
    }

    const now = Date.now();
    const windowStart = now - config.windowMs;
    const key = `${config.keyPrefix || 'rate_limit'}:${identifier}`;
    const requestId = `${now}-${Math.random()}`;

    try {
      // Use Redis pipeline for atomic operations
      const pipeline = this.redisClient.pipeline();
      
      // Remove expired entries
      pipeline.zremrangebyscore(key, 0, windowStart);
      
      // Count current requests in window
      pipeline.zcard(key);
      
      // Add current request
      pipeline.zadd(key, now, requestId);
      
      // Set expiry for the key
      pipeline.expire(key, Math.ceil(config.windowMs / 1000) + 1);
      
      const results = await pipeline.exec();
      
      if (!results || results.some(([err]) => err)) {
        logger.error({ key, results }, 'Redis rate limit pipeline failed');
        // Fail open - allow the request if Redis fails
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: now + config.windowMs,
          totalRequests: 1,
        };
      }

      const currentCount = results[1][1] as number;
      const allowed = currentCount <= config.maxRequests;
      
      // If not allowed, remove the request we just added
      if (!allowed) {
        await this.redisClient.zrem(key, requestId);
      }

      const remaining = Math.max(0, config.maxRequests - currentCount);
      const resetTime = now + config.windowMs;

      logger.debug({
        identifier,
        currentCount,
        maxRequests: config.maxRequests,
        allowed,
        remaining,
      }, 'Rate limit check completed');

      return {
        allowed,
        remaining,
        resetTime,
        totalRequests: currentCount,
      };

    } catch (error) {
      logger.error({ 
        error: error.message, 
        identifier, 
        key 
      }, 'Rate limit check failed');
      
      // Fail open - allow the request if there's an error
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
        totalRequests: 1,
      };
    }
  }

  /**
   * Reset rate limit for a specific identifier
   */
  async resetRateLimit(identifier: string, keyPrefix?: string): Promise<boolean> {
    if (!this.initialized) {
      return false;
    }

    try {
      const key = `${keyPrefix || 'rate_limit'}:${identifier}`;
      const result = await this.redisClient.del(key);
      
      logger.info({ identifier, key }, 'Rate limit reset');
      return result > 0;
    } catch (error) {
      logger.error({ 
        error: error.message, 
        identifier 
      }, 'Failed to reset rate limit');
      return false;
    }
  }

  /**
   * Get current rate limit status without incrementing
   */
  async getRateLimitStatus(
    identifier: string, 
    config: RateLimitConfig
  ): Promise<Omit<RateLimitResult, 'allowed'>> {
    if (!this.initialized) {
      return {
        remaining: config.maxRequests,
        resetTime: Date.now() + config.windowMs,
        totalRequests: 0,
      };
    }

    try {
      const now = Date.now();
      const windowStart = now - config.windowMs;
      const key = `${config.keyPrefix || 'rate_limit'}:${identifier}`;

      // Clean up expired entries and count current ones
      const pipeline = this.redisClient.pipeline();
      pipeline.zremrangebyscore(key, 0, windowStart);
      pipeline.zcard(key);
      
      const results = await pipeline.exec();
      const currentCount = results[1][1] as number;
      
      return {
        remaining: Math.max(0, config.maxRequests - currentCount),
        resetTime: now + config.windowMs,
        totalRequests: currentCount,
      };
    } catch (error) {
      logger.error({ 
        error: error.message, 
        identifier 
      }, 'Failed to get rate limit status');
      
      return {
        remaining: config.maxRequests,
        resetTime: Date.now() + config.windowMs,
        totalRequests: 0,
      };
    }
  }

  /**
   * Bulk check rate limits for multiple identifiers
   */
  async checkBulkRateLimit(
    identifiers: string[], 
    config: RateLimitConfig
  ): Promise<Record<string, RateLimitResult>> {
    const results: Record<string, RateLimitResult> = {};
    
    // Process in parallel for better performance
    const promises = identifiers.map(async (identifier) => {
      const result = await this.checkRateLimit(identifier, config);
      return { identifier, result };
    });

    const resolvedResults = await Promise.all(promises);
    
    for (const { identifier, result } of resolvedResults) {
      results[identifier] = result;
    }

    return results;
  }

  /**
   * Clean up expired rate limit entries (maintenance function)
   */
  async cleanupExpiredEntries(keyPattern = 'rate_limit:*'): Promise<number> {
    if (!this.initialized) {
      return 0;
    }

    try {
      const keys = await this.redisClient.keys(keyPattern);
      let cleanedCount = 0;

      for (const key of keys) {
        const now = Date.now();
        // Remove entries older than 24 hours (cleanup safety margin)
        const expiredBefore = now - (24 * 60 * 60 * 1000);
        
        const removedCount = await this.redisClient.zremrangebyscore(key, 0, expiredBefore);
        cleanedCount += removedCount;
        
        // Remove empty keys
        const remainingCount = await this.redisClient.zcard(key);
        if (remainingCount === 0) {
          await this.redisClient.del(key);
        }
      }

      if (cleanedCount > 0) {
        logger.info({ 
          cleanedCount, 
          keysProcessed: keys.length 
        }, 'Cleaned up expired rate limit entries');
      }

      return cleanedCount;
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to cleanup expired rate limit entries');
      return 0;
    }
  }

  /**
   * Get rate limiter health status
   */
  async getHealthStatus(): Promise<{
    initialized: boolean;
    redisConnected: boolean;
    totalKeys: number;
  }> {
    try {
      const redisConnected = this.initialized && await this.redisClient.ping() === 'PONG';
      let totalKeys = 0;
      
      if (redisConnected) {
        const keys = await this.redisClient.keys('rate_limit:*');
        totalKeys = keys.length;
      }

      return {
        initialized: this.initialized,
        redisConnected,
        totalKeys,
      };
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get rate limiter health status');
      return {
        initialized: this.initialized,
        redisConnected: false,
        totalKeys: 0,
      };
    }
  }
}

// Export singleton instance
export const redisRateLimiter = new RedisRateLimiterService();

import Bull from 'bull';
import { logger } from '../utils/logger.js';
import { env } from '../config/env.js';
import { VerificationWorker } from './verification-worker.js';
import { UsageResetService } from './billing/usage-reset.service.js';
import { SubscriptionLifecycleService } from './billing/subscription-lifecycle.service.js';
import { RenewalNotificationService } from './billing/renewal-notification.service.js';
import { DataRetentionService } from './data-retention.service.js';
import { HeartbeatService } from './heartbeat.service.js';
import { TrialService } from './billing/trial.service.js';

interface ScheduledJobData {
  jobType: string;
  timestamp: string;
}

interface JobSchedule {
  name: string;
  cron: string;
  description: string;
  handler: () => Promise<void>;
}

export class SchedulerService {
  private schedulerQueue: Bull.Queue<ScheduledJobData>;
  private verificationWorker: VerificationWorker;
  private usageResetService: UsageResetService;
  private subscriptionLifecycleService: SubscriptionLifecycleService;
  private renewalNotificationService: RenewalNotificationService;
  private dataRetentionService: DataRetentionService;
  private heartbeatService: HeartbeatService;
  private initialized = false;
  private jobSchedules: JobSchedule[];

  constructor() {
    const redisUrl = process.env.REDIS_URL || env.REDIS_URL;
    
    this.schedulerQueue = new Bull('scheduled-jobs', {
      redis: redisUrl,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
      }
    });

    // Initialize workers
    this.verificationWorker = new VerificationWorker();
    this.usageResetService = new UsageResetService();
    this.subscriptionLifecycleService = new SubscriptionLifecycleService();
    this.renewalNotificationService = new RenewalNotificationService();
    this.dataRetentionService = new DataRetentionService();
    this.heartbeatService = new HeartbeatService();

    // Define job schedules
    this.jobSchedules = [
      {
        name: 'domain-verification',
        cron: '*/5 * * * *', // Every 5 minutes
        description: 'Verify domain DNS records',
        handler: () => this.runDomainVerification()
      },
      {
        name: 'usage-reset',
        cron: '0 * * * *', // Every hour at minute 0
        description: 'Reset monthly usage for users',
        handler: () => this.runUsageReset()
      },
      {
        name: 'subscription-lifecycle',
        cron: '0 * * * *', // Every hour at minute 0
        description: 'Process subscription lifecycle events',
        handler: () => this.runSubscriptionLifecycle()
      },
      {
        name: 'renewal-notifications',
        // Run daily at 08:00 UTC (~10:00 CEST) to send T-3 and T-1 reminders
        cron: '0 8 * * *',
        description: 'Send subscription renewal notifications (daily T-3 and T-1)',
        handler: () => this.runRenewalNotifications()
      },
      {
        name: 'data-retention',
        cron: '*/30 * * * *', // Every 30 minutes
        description: 'Clean up expired data',
        handler: () => this.runDataRetention()
      },
      {
        name: 'payment-cleanup',
        cron: '*/5 * * * *', // Every 5 minutes
        description: 'Clean up expired pending payments',
        handler: () => this.runPaymentCleanup()
      },
      {
        name: 'heartbeat',
        cron: `*/${env.HEARTBEAT_INTERVAL_MINUTES} * * * *`, // Configurable interval
        description: 'Send heartbeat signal to monitoring service',
        handler: () => this.runHeartbeat()
      },
      {
        name: 'trial-notifications',
        cron: '0 10 * * *', // Daily at 10:00 AM
        description: 'Send trial ending notifications (3 days, 1 day warnings)',
        handler: () => this.runTrialNotifications()
      },
      {
        name: 'trial-expiry',
        cron: '0 * * * *', // Every hour at minute 0
        description: 'Check and expire trials that have ended',
        handler: () => this.runTrialExpiry()
      }
    ];
  }

  async start(): Promise<void> {
    if (this.initialized) {
      logger.warn('Scheduler service already initialized');
      return;
    }

    logger.info('Starting scheduler service');

    // Process scheduled jobs
    this.schedulerQueue.process('*', async (job) => {
      const { jobType } = job.data;
      const schedule = this.jobSchedules.find(s => s.name === jobType);
      
      if (!schedule) {
        logger.error({ jobType }, 'Unknown scheduled job type');
        throw new Error(`Unknown job type: ${jobType}`);
      }

      logger.info({ 
        jobType, 
        jobId: job.id,
        description: schedule.description 
      }, 'Processing scheduled job');

      const startTime = Date.now();
      
      try {
        await schedule.handler();
        
        const duration = Date.now() - startTime;
        logger.info({ 
          jobType, 
          jobId: job.id,
          duration 
        }, 'Scheduled job completed successfully');
      } catch (error) {
        const duration = Date.now() - startTime;
        logger.error({ 
          jobType, 
          jobId: job.id,
          error: error.message,
          duration 
        }, 'Scheduled job failed');
        throw error;
      }
    });

    // Add repeatable jobs
    for (const schedule of this.jobSchedules) {
      await this.schedulerQueue.add(
        schedule.name,
        { 
          jobType: schedule.name,
          timestamp: new Date().toISOString()
        },
        {
          repeat: { cron: schedule.cron },
          jobId: `repeat:${schedule.name}`
        }
      );
      
      logger.info({ 
        jobName: schedule.name, 
        cron: schedule.cron,
        description: schedule.description
      }, 'Scheduled job registered');
    }

    // Start heartbeat service
    this.heartbeatService.start();

    this.initialized = true;
    logger.info('✅ Scheduler service started successfully');
  }

  async stop(): Promise<void> {
    logger.info('Stopping scheduler service');
    
    // Remove all repeatable jobs
    for (const schedule of this.jobSchedules) {
      await this.schedulerQueue.removeRepeatable(
        schedule.name,
        { cron: schedule.cron }
      );
    }
    
    // Stop heartbeat service
    this.heartbeatService.stop();

    await this.schedulerQueue.close();
    this.initialized = false;
    
    logger.info('Scheduler service stopped');
  }

  // Job handlers that delegate to existing services
  private async runDomainVerification(): Promise<void> {
    await this.verificationWorker.runVerificationCycle();
  }

  private async runUsageReset(): Promise<void> {
    await this.usageResetService.checkAndResetUsage();
  }

  private async runSubscriptionLifecycle(): Promise<void> {
    await this.subscriptionLifecycleService.processEndOfPeriodDowngrades();
  }

  /**
   * Public trigger: run subscription lifecycle now (admin/testing)
   */
  async triggerSubscriptionLifecycle(): Promise<void> {
    await this.runSubscriptionLifecycle();
  }

  /**
   * Public trigger: run renewal notifications now (admin/testing)
   */
  async triggerRenewalNotifications(): Promise<void> {
    await this.runRenewalNotifications();
  }

  private async runRenewalNotifications(): Promise<void> {
    await this.renewalNotificationService.checkAndNotifyRenewals();
  }

  private async runDataRetention(): Promise<void> {
    await this.dataRetentionService.runCleanup();
  }

  private async runPaymentCleanup(): Promise<void> {
    const { paymentWorkflowService } = await import('./payment/payment-workflow.service.js');
    await paymentWorkflowService.cleanupExpiredPendingPayments();
  }

  private async runHeartbeat(): Promise<void> {
    await this.heartbeatService.sendHeartbeat();
  }

  private async runTrialNotifications(): Promise<void> {
    await TrialService.notifyTrialEndings();
  }

  private async runTrialExpiry(): Promise<void> {
    await TrialService.expireTrials();
  }

  // Monitoring methods for Uptime Kuma
  async getJobStatuses(): Promise<any> {
    const repeatableJobs = await this.schedulerQueue.getRepeatableJobs();
    const completedJobs = await this.schedulerQueue.getCompleted(0, 10);
    const failedJobs = await this.schedulerQueue.getFailed(0, 10);
    const activeJobs = await this.schedulerQueue.getActive();
    const waitingJobs = await this.schedulerQueue.getWaiting();

    const jobStatuses = {};
    
    for (const schedule of this.jobSchedules) {
      const repeatable = repeatableJobs.find(j => j.name === schedule.name);
      const lastCompleted = completedJobs.find(j => j.data?.jobType === schedule.name);
      const lastFailed = failedJobs.find(j => j.data?.jobType === schedule.name);
      const isActive = activeJobs.some(j => j.data?.jobType === schedule.name);
      
      jobStatuses[schedule.name] = {
        name: schedule.name,
        description: schedule.description,
        cron: schedule.cron,
        nextRun: repeatable?.next ? new Date(repeatable.next).toISOString() : null,
        lastRun: lastCompleted ? new Date(lastCompleted.finishedOn).toISOString() : null,
        lastSuccess: lastCompleted ? new Date(lastCompleted.finishedOn).toISOString() : null,
        lastFailure: lastFailed ? new Date(lastFailed.finishedOn).toISOString() : null,
        isActive,
        status: isActive ? 'running' : (lastFailed && (!lastCompleted || lastFailed.finishedOn > lastCompleted.finishedOn)) ? 'failed' : 'idle'
      };
    }

    return {
      queue: 'scheduled-jobs',
      jobs: jobStatuses,
      stats: {
        active: activeJobs.length,
        waiting: waitingJobs.length,
        completed: completedJobs.length,
        failed: failedJobs.length,
        repeatable: repeatableJobs.length
      }
    };
  }

  async isHealthy(): Promise<boolean> {
    try {
      const repeatableJobs = await this.schedulerQueue.getRepeatableJobs();
      
      // Check that all expected jobs are registered
      for (const schedule of this.jobSchedules) {
        const found = repeatableJobs.find(j => j.name === schedule.name);
        if (!found) {
          logger.warn({ jobName: schedule.name }, 'Expected scheduled job not found');
          return false;
        }
      }
      
      return true;
    } catch (error) {
      logger.error({ error }, 'Failed to check scheduler health');
      return false;
    }
  }

  /**
   * Get heartbeat service status
   */
  getHeartbeatStatus() {
    return this.heartbeatService.getStatus();
  }

  /**
   * Manually trigger heartbeat
   */
  async triggerHeartbeat(): Promise<void> {
    await this.heartbeatService.triggerManually();
  }
}

// Export singleton instance
export const schedulerService = new SchedulerService();
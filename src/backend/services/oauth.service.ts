import { prisma } from '../lib/prisma.js';
import { env } from '../config/env.js';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger.js';

interface AuthorizationCode {
  id: string;
  code: string;
  userId: string;
  clientId: string;
  redirectUri: string;
  scope: string;
  expiresAt: Date;
  createdAt: Date;
}

interface AccessToken {
  userId: string;
  clientId: string;
  scope: string;
  expiresAt: Date;
}

// OAuth2 clients configuration
const OAUTH_CLIENTS = {
  webhooktest: {
    id: 'webhooktest',
    // Require secret to be configured; validation will fail if missing
    secret: env.WEBHOOKTEST_JWT_SECRET as string,
    name: 'WebhookTest',
    redirectUris: [
      'http://localhost:3001/auth/callback',
      'https://webhooktest.eu/auth/callback'
    ]
  }
};

// Use a dedicated OAuth signing secret; fall back to BetterA<PERSON> secret if needed
const JWT_SECRET = env.WEBHOOKTEST_JWT_SECRET || env.BETTER_AUTH_SECRET || 'fallback-secret-should-not-be-used';

export class OAuthService {
  /**
   * Validate if client ID is registered
   */
  static isValidClient(clientId: string): boolean {
    return clientId in OAUTH_CLIENTS;
  }

  /**
   * Validate client credentials
   */
  static validateClientCredentials(clientId: string, clientSecret: string): boolean {
    const client = OAUTH_CLIENTS[clientId as keyof typeof OAUTH_CLIENTS];
    return client && client.secret === clientSecret;
  }

  /**
   * Validate redirect URI for client
   */
  static isValidRedirectUri(clientId: string, redirectUri: string): boolean {
    const client = OAUTH_CLIENTS[clientId as keyof typeof OAUTH_CLIENTS];
    if (!client) return false;
    
    // In development, allow any redirect URI
    if (env.NODE_ENV === 'development') {
      return true;
    }
    
    // In production, check exact matches only
    return client.redirectUris.includes(redirectUri);
  }

  /**
   * Generate authorization code
   */
  static async generateAuthorizationCode(
    userId: string,
    clientId: string,
    redirectUri: string,
    scope: string
  ): Promise<string> {
    // Generate secure random code
    const code = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store in database (we'll use a simple table for this)
    // For now, we'll use JWT to encode the authorization code data
    const payload = {
      code,
      userId,
      clientId,
      redirectUri,
      scope
    };

    logger.info({ secret: JWT_SECRET.substring(0, 10) + '...' }, 'Generating auth code with JWT_SECRET');
    return jwt.sign(payload, JWT_SECRET, { expiresIn: '10m' });
  }

  /**
   * Exchange authorization code for user data
   */
  static async exchangeAuthorizationCode(
    code: string,
    clientId: string,
    redirectUri: string
  ): Promise<{ userId: string; scope: string } | null> {
    try {
      logger.info({ secret: JWT_SECRET.substring(0, 10) + '...' }, 'Exchanging auth code with JWT_SECRET');
      // Decode and validate the authorization code
      const payload = jwt.verify(code, JWT_SECRET) as any;
      
      logger.info({
        clientId: payload.clientId,
        redirectUri: payload.redirectUri,
        exp: payload.exp,
        currentTime: Date.now() / 1000
      }, 'Auth code payload:');
      logger.info({
        clientIdMatch: payload.clientId === clientId,
        redirectUriMatch: payload.redirectUri === redirectUri,
        notExpired: payload.exp >= Date.now() / 1000
      }, 'Validation checks:');
      
      // Validate the code data
      if (
        payload.clientId !== clientId ||
        payload.redirectUri !== redirectUri ||
        payload.exp < Date.now() / 1000
      ) {
        logger.info('Validation failed');
        return null;
      }

      logger.info('Auth code validation successful');
      return {
        userId: payload.userId,
        scope: payload.scope
      };
    } catch (error) {
      logger.error({ data: error }, 'Error exchanging authorization code:');
      return null;
    }
  }

  /**
   * Generate access token
   */
  static async generateAccessToken(
    userId: string,
    clientId: string,
    scope: string
  ): Promise<string> {
    const payload = {
      userId,
      clientId,
      scope,
      type: 'access_token',
      iss: 'emailconnect',
      aud: clientId
    };

    return jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });
  }

  /**
   * Validate access token
   */
  static async validateAccessToken(token: string): Promise<AccessToken | null> {
    try {
      const payload = jwt.verify(token, JWT_SECRET) as any;
      
      // Validate token type
      if (payload.type !== 'access_token' || payload.iss !== 'emailconnect') {
        return null;
      }

      return {
        userId: payload.userId,
        clientId: payload.clientId,
        scope: payload.scope,
        expiresAt: new Date(payload.exp * 1000)
      };
    } catch (error) {
      logger.error({ data: error }, 'Error validating access token:');
      return null;
    }
  }

  /**
   * Get user by ID for userinfo endpoint
   */
  static async getUserById(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true
        }
      });

      return user;
    } catch (error) {
      logger.error({ data: error }, 'Error getting user by ID:');
      return null;
    }
  }

  /**
   * Generate SSO login URL for WebhookTest
   */
  static generateWebhookTestLoginUrl(baseUrl: string = 'https://webhooktest.eu'): string {
    const clientId = 'webhooktest';
    const redirectUri = `${baseUrl}/auth/callback`;
    const scope = 'profile';
    const state = crypto.randomBytes(16).toString('hex');

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope,
      state
    });

    return `${env.URL}/oauth/authorize?${params.toString()}`;
  }

  /**
   * Create a direct login token for WebhookTest (alternative to OAuth flow)
   * This generates a signed token that WebhookTest can use to auto-login the user
   */
  static async generateDirectLoginToken(userId: string, userEmail: string): Promise<string> {
    const payload = {
      userId,
      email: userEmail,
      action: 'sso_login',
      timestamp: Date.now(),
      nonce: crypto.randomBytes(16).toString('hex'),
      iss: 'emailconnect',
      aud: 'webhooktest'
    };

// Use the shared WebhookTest secret for this token
    if (!env.WEBHOOKTEST_JWT_SECRET && env.NODE_ENV !== 'test') {
      throw new Error('WEBHOOKTEST_JWT_SECRET is required');
    }
    const secret = env.WEBHOOKTEST_JWT_SECRET || 'test-webhook-secret';
    return jwt.sign(payload, secret, { expiresIn: '5m' });
  }
}
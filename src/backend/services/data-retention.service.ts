import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';
import { S3StorageService } from './storage/s3-storage.service.js';

export class DataRetentionService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 30 * 60 * 1000; // Check every 30 minutes

  /**
   * Start the data retention cleanup worker
   */
  start(): void {
    if (this.intervalId) {
      logger.warn('Data retention worker already running');
      return;
    }

    logger.info('Starting data retention cleanup worker');
    
    // Run immediately, then on interval
    this.runCleanup();
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stop the data retention cleanup worker
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Data retention worker stopped');
    }
  }

  /**
   * Run the data retention cleanup process
   */
  async runCleanup(): Promise<void> {
    try {
      logger.debug('Running data retention cleanup');

      const now = new Date();
      
      // Find expired emails based on user settings and plan defaults
      const expiredEmails = await this.findExpiredEmails(now);
      
      if (expiredEmails.length === 0) {
        logger.debug('No expired emails found for cleanup');
        return;
      }

      logger.info({ emailCount: expiredEmails.length }, 'Found expired emails for cleanup');

      // Delete expired emails in batches to avoid overwhelming the database
      const batchSize = 100;
      let deletedCount = 0;
      let attachmentStats = {
        totalFound: 0,
        systemDeleted: 0,
        systemFailed: 0,
        customDeleted: 0,
        customFailed: 0,
        customRetained: 0
      };

      for (let i = 0; i < expiredEmails.length; i += batchSize) {
        const batch = expiredEmails.slice(i, i + batchSize);
        const emailIds = batch.map(email => email.id);

        // Fetch attachments before deletion (cascade will remove them)
        const attachments = await prisma.attachmentFile.findMany({
          where: {
            emailId: { in: emailIds },
            s3Key: { not: null }
          },
          select: {
            id: true,
            emailId: true,
            s3Key: true,
            s3Config: true,
            filename: true,
            size: true
          }
        });

        attachmentStats.totalFound += attachments.length;

        // Delete emails (this cascades to attachment records)
        const deleteResult = await prisma.email.deleteMany({
          where: {
            id: { in: emailIds }
          }
        });

        deletedCount += deleteResult.count;

        // Clean up S3 attachments after successful email deletion
        if (attachments.length > 0) {
          const cleanupResult = await this.cleanupAttachmentsFromS3(attachments, batch[0]?.userId || null);
          attachmentStats.systemDeleted += cleanupResult.systemDeleted;
          attachmentStats.systemFailed += cleanupResult.systemFailed;
          attachmentStats.customDeleted += cleanupResult.customDeleted;
          attachmentStats.customFailed += cleanupResult.customFailed;
          attachmentStats.customRetained += cleanupResult.customRetained;
        }
        
        logger.debug({ 
          batchSize: batch.length, 
          deletedInBatch: deleteResult.count,
          totalDeleted: deletedCount,
          attachmentsInBatch: attachments.length
        }, 'Processed email deletion batch');
      }

      // Create audit log for GDPR compliance
      await this.createRetentionAuditLog({
        emailsDeleted: deletedCount,
        attachmentStats,
        timestamp: now
      });

      logger.info({ 
        totalExpired: expiredEmails.length,
        totalDeleted: deletedCount,
        attachmentStats,
        timestamp: now.toISOString()
      }, 'Data retention cleanup completed');

    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to run data retention cleanup');
    }
  }

  /**
   * Find emails that have expired based on user settings and plan defaults
   */
  private async findExpiredEmails(now: Date): Promise<Array<{ id: string; userId: string | null; createdAt: Date }>> {
    // Get all users with their settings and plan types
    const usersWithSettings = await prisma.user.findMany({
      select: {
        id: true,
        planType: true,
        settings: {
          select: {
            dataRetentionHours: true
          }
        }
      }
    });

    // Build a map of userId to effective retention hours
    const userRetentionMap = new Map<string, number>();

    for (const user of usersWithSettings) {
      let retentionHours: number;

      // Use custom setting if available, otherwise use plan default
      if (user.settings?.dataRetentionHours !== null && user.settings?.dataRetentionHours !== undefined) {
        retentionHours = user.settings.dataRetentionHours;
      } else {
        // Plan-based defaults
        switch (user.planType) {
          case 'free':
            retentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            retentionHours = 24;
            break;
          default:
            retentionHours = 2; // Default to free plan retention
        }
      }

      userRetentionMap.set(user.id, retentionHours);
    }

    // Find expired emails for each user (including emails with userId)
    const expiredEmails: Array<{ id: string; userId: string | null; createdAt: Date }> = [];

    for (const [userId, retentionHours] of userRetentionMap.entries()) {
      const cutoffTime = new Date(now.getTime() - (retentionHours * 60 * 60 * 1000));

      const userExpiredEmails = await prisma.email.findMany({
        where: {
          userId: userId,
          createdAt: {
            lt: cutoffTime
          }
        },
        select: {
          id: true,
          userId: true,
          createdAt: true
        }
      });

      expiredEmails.push(...userExpiredEmails);
    }

    // Also handle legacy emails without userId (use domain-based lookup)
    const legacyExpiredEmails = await prisma.email.findMany({
      where: {
        userId: null,
        createdAt: {
          lt: new Date(now.getTime() - (2 * 60 * 60 * 1000)) // Default to 2 hours for legacy emails
        }
      },
      select: {
        id: true,
        userId: true,
        createdAt: true
      }
    });

    expiredEmails.push(...legacyExpiredEmails);

    return expiredEmails;
  }

  /**
   * Manually clean up expired emails for a specific user
   */
  async cleanupUserEmails(userId: string): Promise<{ success: boolean; deletedCount: number; error?: string }> {
    try {
      const now = new Date();
      
      // Get user's effective retention hours
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          planType: true,
          settings: {
            select: {
              dataRetentionHours: true
            }
          }
        }
      });

      if (!user) {
        return { success: false, deletedCount: 0, error: 'User not found' };
      }

      let retentionHours: number;
      if (user.settings?.dataRetentionHours !== null && user.settings?.dataRetentionHours !== undefined) {
        retentionHours = user.settings.dataRetentionHours;
      } else {
        switch (user.planType) {
          case 'free':
            retentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            retentionHours = 24;
            break;
          default:
            retentionHours = 2;
        }
      }

      const cutoffTime = new Date(now.getTime() - (retentionHours * 60 * 60 * 1000));
      
      // Fetch attachments before deletion
      const emailsToDelete = await prisma.email.findMany({
        where: {
          userId: userId,
          createdAt: { lt: cutoffTime }
        },
        select: { id: true }
      });

      const emailIds = emailsToDelete.map(e => e.id);
      
      const attachments = await prisma.attachmentFile.findMany({
        where: {
          emailId: { in: emailIds },
          s3Key: { not: null }
        },
        select: {
          id: true,
          emailId: true,
          s3Key: true,
          s3Config: true,
          filename: true,
          size: true
        }
      });

      // Delete emails (cascades to attachments)
      const deleteResult = await prisma.email.deleteMany({
        where: {
          userId: userId,
          createdAt: { lt: cutoffTime }
        }
      });

      // Clean up S3 attachments
      let attachmentStats = {
        totalFound: attachments.length,
        systemDeleted: 0,
        systemFailed: 0,
        customDeleted: 0,
        customFailed: 0,
        customRetained: 0
      };

      if (attachments.length > 0) {
        const cleanupResult = await this.cleanupAttachmentsFromS3(attachments, userId);
        attachmentStats = { ...attachmentStats, ...cleanupResult };
      }

      // Create audit log
      await prisma.auditLog.create({
        data: {
          action: 'data.retention.manual_cleanup',
          resourceType: 'emails_and_attachments',
          resourceId: userId,
          metadata: {
            emailsDeleted: deleteResult.count,
            attachments: attachmentStats,
            retentionHours,
            cutoffTime: cutoffTime.toISOString(),
            initiatedBy: 'user_request'
          },
          expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        }
      });

      logger.info({
        userId,
        retentionHours,
        deletedCount: deleteResult.count,
        attachmentStats,
        cutoffTime: cutoffTime.toISOString()
      }, 'Manual user email cleanup completed');

      return { success: true, deletedCount: deleteResult.count };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to cleanup user emails');
      return { success: false, deletedCount: 0, error: error.message };
    }
  }

  /**
   * Clean up attachments from S3 storage
   */
  private async cleanupAttachmentsFromS3(
    attachments: Array<{
      id: string;
      emailId: string;
      s3Key: string | null;
      s3Config: any;
      filename: string;
      size: number;
    }>,
    userId: string | null
  ): Promise<{
    systemDeleted: number;
    systemFailed: number;
    customDeleted: number;
    customFailed: number;
    customRetained: number;
  }> {
    const stats = {
      systemDeleted: 0,
      systemFailed: 0,
      customDeleted: 0,
      customFailed: 0,
      customRetained: 0
    };

    // Separate system vs custom S3 attachments
    const systemAttachments = attachments.filter(a => !a.s3Config && a.s3Key);
    const customAttachments = attachments.filter(a => a.s3Config && a.s3Key);

    // Delete from system S3
    if (systemAttachments.length > 0) {
      const systemS3 = new S3StorageService();
      const deleteResults = await this.batchDeleteFromS3(
        systemS3,
        systemAttachments.map(a => a.s3Key!)
      );
      stats.systemDeleted = deleteResults.succeeded;
      stats.systemFailed = deleteResults.failed;
    }

    // Handle custom S3 attachments
    // Group by unique S3 configs to minimize client instantiation
    const configGroups = new Map<string, typeof customAttachments>();
    
    for (const attachment of customAttachments) {
      const configKey = JSON.stringify(attachment.s3Config);
      if (!configGroups.has(configKey)) {
        configGroups.set(configKey, []);
      }
      configGroups.get(configKey)!.push(attachment);
    }

    // Process each custom S3 configuration group
    for (const [configKey, groupAttachments] of configGroups) {
      const s3Config = JSON.parse(configKey);
      
      // Check if user wants to retain attachments on custom S3
      if (s3Config.retainAttachmentsOnEmailDeletion === true) {
        stats.customRetained += groupAttachments.length;
        logger.info({
          attachmentCount: groupAttachments.length,
          bucket: s3Config.bucket,
          userId
        }, 'Retained attachments on custom S3 per user preference');
        continue;
      }

      // Delete from custom S3
      try {
        const customS3 = new S3StorageService(s3Config);
        const deleteResults = await this.batchDeleteFromS3(
          customS3,
          groupAttachments.map(a => a.s3Key!)
        );
        stats.customDeleted += deleteResults.succeeded;
        stats.customFailed += deleteResults.failed;
      } catch (error: any) {
        logger.error({
          error: error.message,
          bucket: s3Config.bucket,
          attachmentCount: groupAttachments.length
        }, 'Failed to initialize custom S3 client for deletion');
        stats.customFailed += groupAttachments.length;
      }
    }

    return stats;
  }

  /**
   * Batch delete objects from S3
   */
  private async batchDeleteFromS3(
    s3Service: S3StorageService,
    s3Keys: string[]
  ): Promise<{ succeeded: number; failed: number }> {
    let succeeded = 0;
    let failed = 0;

    // Process in batches of 10 to avoid overwhelming S3
    const batchSize = 10;
    for (let i = 0; i < s3Keys.length; i += batchSize) {
      const batch = s3Keys.slice(i, i + batchSize);
      
      // Delete in parallel within batch
      const deletePromises = batch.map(async (s3Key) => {
        try {
          await s3Service.deleteAttachment(s3Key);
          return { success: true };
        } catch (error: any) {
          logger.warn({
            s3Key,
            error: error.message
          }, 'Failed to delete attachment from S3');
          return { success: false };
        }
      });

      const results = await Promise.all(deletePromises);
      succeeded += results.filter(r => r.success).length;
      failed += results.filter(r => !r.success).length;
    }

    return { succeeded, failed };
  }

  /**
   * Create audit log for retention activities
   */
  private async createRetentionAuditLog(data: {
    emailsDeleted: number;
    attachmentStats: {
      totalFound: number;
      systemDeleted: number;
      systemFailed: number;
      customDeleted: number;
      customFailed: number;
      customRetained: number;
    };
    timestamp: Date;
  }): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          action: 'data.retention.cleanup',
          resourceType: 'emails_and_attachments',
          metadata: {
            emailsDeleted: data.emailsDeleted,
            attachments: data.attachmentStats,
            timestamp: data.timestamp.toISOString(),
            retentionType: 'automated_policy'
          },
          expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // Keep audit logs for 90 days
        }
      });
    } catch (error: any) {
      logger.error({
        error: error.message
      }, 'Failed to create retention audit log');
    }
  }

  /**
   * Get data retention statistics
   */
  async getRetentionStats(): Promise<{
    totalEmails: number;
    expiredEmails: number;
    userRetentionSettings: Array<{
      userId: string;
      planType: string;
      customRetentionHours: number | null;
      effectiveRetentionHours: number;
      emailCount: number;
    }>;
  }> {
    const now = new Date();
    
    const [totalEmails, usersWithSettings] = await Promise.all([
      prisma.email.count(),
      prisma.user.findMany({
        select: {
          id: true,
          planType: true,
          settings: {
            select: {
              dataRetentionHours: true
            }
          },
          _count: {
            select: {
              emails: true
            }
          }
        }
      })
    ]);

    let expiredEmailsCount = 0;
    const userRetentionSettings = [];

    for (const user of usersWithSettings) {
      let effectiveRetentionHours: number;
      const customRetentionHours = user.settings?.dataRetentionHours ?? null;
      
      if (customRetentionHours !== null) {
        effectiveRetentionHours = customRetentionHours;
      } else {
        switch (user.planType) {
          case 'free':
            effectiveRetentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            effectiveRetentionHours = 24;
            break;
          default:
            effectiveRetentionHours = 2;
        }
      }

      const cutoffTime = new Date(now.getTime() - (effectiveRetentionHours * 60 * 60 * 1000));
      
      const userExpiredCount = await prisma.email.count({
        where: {
          userId: user.id,
          createdAt: {
            lt: cutoffTime
          }
        }
      });

      expiredEmailsCount += userExpiredCount;

      userRetentionSettings.push({
        userId: user.id,
        planType: user.planType,
        customRetentionHours,
        effectiveRetentionHours,
        emailCount: user._count.emails
      });
    }

    return {
      totalEmails,
      expiredEmails: expiredEmailsCount,
      userRetentionSettings
    };
  }
}

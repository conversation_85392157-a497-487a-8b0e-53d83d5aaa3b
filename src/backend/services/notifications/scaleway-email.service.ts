import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';

export interface EmailRecipient {
  name: string;
  email: string;
}

export interface EmailAttachment {
  name: string;
  type: string;
  content: string; // Base64 encoded
}

export interface SendEmailRequest {
  to: EmailRecipient[];
  subject: string;
  text?: string;
  html?: string;
  cc?: EmailRecipient[];
  bcc?: EmailRecipient[];
  attachments?: EmailAttachment[];
  replyTo?: string;
  headers?: Record<string, string>;
}

export interface ScalewayEmailResponse {
  id: string;
  message_id: string;
  created_at: string;
  updated_at: string;
  status: string;
  subject: string;
  project_id: string;
}

/**
 * Scaleway Transactional Email Service
 * Wrapper around Scaleway's transactional email API
 */
export class ScalewayEmailService {
  private readonly baseUrl: string;
  private readonly region: string;
  private readonly projectId: string;
  private readonly fromEmail: string;
  private readonly fromName: string;

  constructor() {
    this.region = env.SCALEWAY_REGION;
    this.baseUrl = `https://api.scaleway.com/transactional-email/v1alpha1/regions/${this.region}`;
    this.projectId = env.SCALEWAY_PROJECT_ID || '';
    this.fromEmail = env.SCALEWAY_FROM_EMAIL || '';
    this.fromName = env.SCALEWAY_FROM_NAME;

    if (!this.isConfigured()) {
      logger.warn('Scaleway email service not fully configured - emails will not be sent');
    } else {
      logger.info({
        region: this.region,
        projectId: this.projectId ? `${this.projectId.substring(0, 8)}...` : 'not set',
        fromEmail: this.fromEmail
      }, 'Scaleway email service initialized');
    }
  }

  /**
   * Check if Scaleway email service is properly configured
   */
  isConfigured(): boolean {
    return !!(
      env.SCALEWAY_ACCESS_KEY &&
      env.SCALEWAY_SECRET_KEY &&
      env.SCALEWAY_PROJECT_ID &&
      env.SCALEWAY_FROM_EMAIL
    );
  }

  /**
   * Send email via Scaleway Transactional Email API
   */
  async sendEmail(request: SendEmailRequest): Promise<ScalewayEmailResponse> {
    if (!this.isConfigured()) {
      logger.warn('Scaleway email service not configured');
      // Return a mock response when not configured
      return {
        id: 'mock-' + Date.now(),
        message_id: 'mock-message-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'not_configured',
        subject: request.subject,
        project_id: this.projectId || 'not-configured'
      };
    }

    if (!request.text && !request.html) {
      throw new Error('Email must contain either text or HTML content');
    }

    if (request.subject.length < 10) {
      throw new Error('Email subject must be at least 10 characters long');
    }

    try {
      const emailData = {
        from: {
          name: this.fromName,
          email: this.fromEmail
        },
        to: request.to,
        subject: request.subject,
        project_id: this.projectId,
        ...(request.text && { text: request.text }),
        ...(request.html && { html: request.html }),
        ...(request.cc && { cc: request.cc }),
        ...(request.bcc && { bcc: request.bcc }),
        ...(request.attachments && { attachments: request.attachments }),
        ...(request.headers && {
          additional_headers: Object.entries(request.headers).map(([key, value]) => ({
            key,
            value
          }))
        }),
        ...(request.replyTo && {
          additional_headers: [
            ...(request.headers ? Object.entries(request.headers).map(([key, value]) => ({
              key,
              value
            })) : []),
            {
              key: 'Reply-To',
              value: request.replyTo
            }
          ]
        })
      };

      logger.debug({
        to: request.to.map(r => r.email),
        subject: request.subject,
        hasText: !!request.text,
        hasHtml: !!request.html
      }, 'Sending email via Scaleway');

      const response = await fetch(`${this.baseUrl}/emails`, {
        method: 'POST',
        headers: {
          'X-Auth-Token': env.SCALEWAY_SECRET_KEY!,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error({
          status: response.status,
          statusText: response.statusText,
          error: errorText
        }, 'Scaleway email API error');
        
        throw new Error(`Scaleway API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as ScalewayEmailResponse;

      logger.info({
        emailId: result.id,
        messageId: result.message_id,
        status: result.status,
        to: request.to.map(r => r.email),
        subject: request.subject
      }, 'Email sent successfully via Scaleway');

      return result;

    } catch (error: any) {
      logger.error({
        error: error.message,
        to: request.to?.map(r => r.email),
        subject: request.subject
      }, 'Failed to send email via Scaleway');
      
      throw error;
    }
  }

  /**
   * Get email status from Scaleway
   */
  async getEmailStatus(emailId: string): Promise<ScalewayEmailResponse> {
    if (!this.isConfigured()) {
      logger.warn('Scaleway email service not configured');
      // Return a mock response when not configured
      return {
        id: emailId,
        message_id: 'mock-message-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'not_configured',
        subject: 'Not configured',
        project_id: this.projectId || 'not-configured'
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/emails/${emailId}`, {
        method: 'GET',
        headers: {
          'X-Auth-Token': env.SCALEWAY_SECRET_KEY!,
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error({
          emailId,
          status: response.status,
          statusText: response.statusText,
          error: errorText
        }, 'Failed to get email status from Scaleway');
        
        throw new Error(`Scaleway API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json() as ScalewayEmailResponse;
      
      logger.debug({
        emailId,
        status: result.status,
        messageId: result.message_id
      }, 'Retrieved email status from Scaleway');

      return result;

    } catch (error: any) {
      logger.error({
        error: error.message,
        emailId
      }, 'Failed to get email status from Scaleway');
      
      throw error;
    }
  }

  /**
   * Send simple text email (convenience method)
   */
  async sendSimpleEmail(params: {
    to: string;
    toName?: string;
    subject: string;
    text: string;
    replyTo?: string;
  }): Promise<ScalewayEmailResponse> {
    return this.sendEmail({
      to: [{
        email: params.to,
        name: params.toName || params.to
      }],
      subject: params.subject,
      text: params.text,
      replyTo: params.replyTo
    });
  }

  /**
   * Send HTML email (convenience method)
   */
  async sendHtmlEmail(params: {
    to: string;
    toName?: string;
    subject: string;
    html: string;
    text?: string;
    replyTo?: string;
  }): Promise<ScalewayEmailResponse> {
    return this.sendEmail({
      to: [{
        email: params.to,
        name: params.toName || params.to
      }],
      subject: params.subject,
      html: params.html,
      text: params.text,
      replyTo: params.replyTo
    });
  }
}

// Export singleton instance
export const scalewayEmailService = new ScalewayEmailService();
/**
 * Unified Email Template System
 * 
 * Provides consistent branding and layout across all email notifications.
 * Supports customizable template variables while maintaining brand consistency.
 */

interface EmailTemplateVariables {
  // User personalization
  firstName?: string;
  fullName?: string;
  email?: string;
  
  // Content sections
  greeting?: string;          // Default: "Hi {firstName}!" or "Hello!"
  title?: string;            // Email subject/main heading
  body: string;              // Main email content (supports HTML)
  cta?: {                    // Call to action button
    text: string;
    url: string;
    style?: 'primary' | 'secondary' | 'danger' | 'success';
  };
  closing?: string;          // Default: "Happy automating!"
  
  // Additional content
  attachments?: Array<{
    name: string;
    description?: string;
    size?: string;
  }>;
  
  // Branding overrides
  brandColor?: string;       // Override default blue
  footerText?: string;       // Override default footer
}

interface EmailTemplateOptions {
  variables: EmailTemplateVariables;
  includeLogo?: boolean;     // Default: true
  includeUnsubscribe?: boolean; // Default: false (only for marketing emails)
  template?: 'notification' | 'transactional' | 'marketing'; // Default: 'notification'
}

export interface GeneratedEmail {
  html: string;
  text: string;
}

export class EmailTemplateService {
  /**
   * Generate both HTML and text versions of an email from a single set of variables
   */
  static generateEmail(options: EmailTemplateOptions): GeneratedEmail {
    return {
      html: this.generateEmailHtml(options),
      text: this.generateEmailText(options)
    };
  }

  private static readonly DEFAULT_STYLES = `
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        line-height: 1.6;
        color: #1f2937;
        margin: 0;
        padding: 0;
        background-color: #f9fafb;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      .email-header {
        background: linear-gradient(135deg, #57bd7b 0%, #4a9960 100%);
        padding: 24px;
        text-align: center;
      }
      .email-body {
        padding: 32px 24px;
      }
      .email-greeting {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
      }
      .email-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 16px;
      }
      .email-content {
        font-size: 16px;
        line-height: 1.7;
        color: #374151;
        margin-bottom: 24px;
      }
      .email-cta {
        text-align: center;
        margin: 32px 0;
      }
      .email-button {
        display: inline-block;
        padding: 14px 28px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
      }
      .button-primary {
        background-color: #57bd7b;
        color: #ffffff;
      }
      .button-secondary {
        background-color: #6b7280;
        color: #ffffff;
      }
      .button-success {
        background-color: #10b981;
        color: #ffffff;
      }
      .button-danger {
        background-color: #ef4444;
        color: #ffffff;
      }
      .email-attachments {
        background-color: #f3f4f6;
        padding: 16px;
        border-radius: 6px;
        margin: 24px 0;
      }
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e5e7eb;
      }
      .attachment-item:last-child {
        border-bottom: none;
      }
      .attachment-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        opacity: 0.7;
      }
      .email-footer {
        background-color: #f9fafb;
        padding: 24px;
        text-align: center;
        border-top: 1px solid #e5e7eb;
      }
      .email-signature {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.5;
      }
      .email-links {
        margin-top: 16px;
      }
      .email-links a {
        color: #57bd7b;
        text-decoration: none;
        margin: 0 8px;
        font-size: 14px;
      }
      .email-unsubscribe {
        font-size: 12px;
        color: #9ca3af;
        margin-top: 16px;
      }
      .email-unsubscribe a {
        color: #9ca3af;
        text-decoration: underline;
      }
      @media only screen and (max-width: 600px) {
        .email-container {
          margin: 0;
          border-radius: 0;
        }
        .email-body {
          padding: 24px 16px;
        }
        .email-button {
          padding: 12px 24px;
          font-size: 14px;
        }
      }
    </style>
  `;

  /**
   * Generate a complete HTML email using the unified template
   */
  static generateEmailHtml(options: EmailTemplateOptions): string {
    const { variables, includeLogo = true, includeUnsubscribe = false } = options;
    
    const greeting = this.generateGreeting(variables);
    const ctaSection = this.generateCTA(variables.cta);
    const attachmentsSection = this.generateAttachments(variables.attachments);
    const closing = variables.closing || 'Happy automating!';
    
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${variables.title || 'EmailConnect Notification'}</title>
        ${this.DEFAULT_STYLES}
      </head>
      <body>
        <div class="email-container">
          ${this.generateHeader(includeLogo)}
          
          <div class="email-body">
            ${greeting ? `<div class="email-greeting">${greeting}</div>` : ''}
            ${variables.title ? `<h1 class="email-title">${variables.title}</h1>` : ''}
            
            <div class="email-content">
              ${variables.body}
            </div>
            
            ${ctaSection}
            ${attachmentsSection}
            
            <div style="margin-top: 32px; color: #6b7280; font-size: 16px;">
              ${closing}
            </div>
          </div>
          
          ${this.generateFooter(includeUnsubscribe, variables.footerText)}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate a text version of the email
   */
  static generateEmailText(options: EmailTemplateOptions): string {
    const { variables } = options;
    const greeting = this.generateGreeting(variables);
    const closing = variables.closing || 'Happy automating!';
    
    // Strip HTML tags from body
    const textBody = variables.body.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
    
    let textContent = '';
    
    if (greeting) {
      textContent += `${greeting}\n\n`;
    }
    
    if (variables.title) {
      textContent += `${variables.title}\n\n`;
    }
    
    textContent += `${textBody}\n\n`;
    
    if (variables.cta) {
      textContent += `${variables.cta.text}: ${variables.cta.url}\n\n`;
    }
    
    if (variables.attachments && variables.attachments.length > 0) {
      textContent += 'Attachments:\n';
      variables.attachments.forEach(att => {
        textContent += `- ${att.name}${att.size ? ` (${att.size})` : ''}\n`;
      });
      textContent += '\n';
    }
    
    textContent += `${closing}\n\n`;
    textContent += 'Best regards,\n';
    textContent += 'Your EmailConnect Team\n';
    textContent += 'https://emailconnect.eu\n';
    
    return textContent;
  }

  private static generateGreeting(variables: EmailTemplateVariables): string {
    if (variables.greeting) {
      return variables.greeting;
    }
    
    if (variables.firstName) {
      return `Hi ${variables.firstName}!`;
    }
    
    if (variables.fullName) {
      return `Hi ${variables.fullName}!`;
    }
    
    return 'Hello!';
  }

  private static generateHeader(includeLogo: boolean): string {
    if (!includeLogo) return '';
    
    return `
      <div class="email-header">
        <h1 style="color: white; font-size: 18px; font-weight: 700; margin: 0; letter-spacing: -0.5px;">
          EmailConnect
        </h1>
      </div>
    `;
  }

  private static generateCTA(cta?: EmailTemplateVariables['cta']): string {
    if (!cta) return '';
    
    const buttonClass = `button-${cta.style || 'primary'}`;
    
    return `
      <div class="email-cta">
        <a href="${cta.url}" class="email-button ${buttonClass}">
          ${cta.text}
        </a>
      </div>
    `;
  }

  private static generateAttachments(attachments?: EmailTemplateVariables['attachments']): string {
    if (!attachments || attachments.length === 0) return '';
    
    const attachmentItems = attachments.map(att => `
      <div class="attachment-item">
        <svg class="attachment-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
        <div>
          <div style="font-weight: 600; font-size: 14px;">${att.name}</div>
          ${att.description ? `<div style="font-size: 12px; color: #6b7280;">${att.description}</div>` : ''}
          ${att.size ? `<div style="font-size: 12px; color: #9ca3af;">${att.size}</div>` : ''}
        </div>
      </div>
    `).join('');
    
    return `
      <div class="email-attachments">
        <div style="font-weight: 600; margin-bottom: 12px; color: #374151;">Attachments</div>
        ${attachmentItems}
      </div>
    `;
  }

  private static generateFooter(includeUnsubscribe: boolean, customFooterText?: string): string {
    const footerText = customFooterText || `
      Building reliable email-to-webhook automation
    `;
    
    const unsubscribeSection = includeUnsubscribe ? `
      <div class="email-unsubscribe">
        <a href="{{unsubscribe_url}}">Unsubscribe</a> from these emails
      </div>
    ` : '';
    
    return `
      <div class="email-footer">
        <div class="email-signature">
          ${footerText}
        </div>
        
        <div class="email-links">
          <a href="https://emailconnect.eu">Website</a>
          <a href="https://emailconnect.eu/docs">Documentation</a>
          <a href="https://emailconnect.eu/help">Support</a>
        </div>
        
        ${unsubscribeSection}
      </div>
    `;
  }

  /**
   * Dedicated, reusable template: account_deleted_notice
   */
  static accountDeletedNotice(vars: { firstName?: string; fullName?: string; email?: string; includeSupportReply?: boolean }): { subject: string; html: string; text: string } {
    const subject = 'Your EmailConnect account has been deleted';
    const { html, text } = this.generateEmail({
      variables: {
        firstName: vars.firstName || this.extractFirstName(vars.fullName, vars.email),
        fullName: vars.fullName,
        email: vars.email,
        title: subject,
        body: `
          <p>Your account and associated personal data have been removed successfully.</p>
          <p>Your data will still exist in our daily backups for up to 7 days, after which those backups expire and your data is purged automatically.</p>
          <p>If you previously used paid features, we will need to retain anonymized financial transactions for tax compliance.</p>
          ${vars.includeSupportReply !== false ? '<p>Finally, I\'d value your feedback! Simply reply to this email to share yours.</p>' : ''}
        `,
      },
      includeLogo: true,
      includeUnsubscribe: false,
      template: 'transactional'
    });
    return { subject, html, text };
  }

  /**
   * Helper method to extract user's first name from full name or email
   */
  static extractFirstName(fullName?: string, email?: string): string {
    if (fullName) {
      return fullName.split(' ')[0];
    }
    
    if (email) {
      return email.split('@')[0];
    }
    
    return '';
  }
}
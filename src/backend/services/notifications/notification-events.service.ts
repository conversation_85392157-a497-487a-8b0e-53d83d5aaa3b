import { EventEmitter } from 'events';
import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { scalewayEmailService } from './scaleway-email.service.js';
import { webSocketService } from '../websocket.service.js';
import { telegramService } from './telegram.service.js';
import { EmailTemplateService } from './email-template.service.js';

// Event Types
export type NotificationEventType =
  | 'user.registered'
  | 'user.email.verified'
  | 'domain.created'
  | 'domain.verified'
  | 'domain.verification.failed'
  | 'webhook.created'
  | 'webhook.verified'
  | 'webhook.failed'
  | 'payment.success'
  | 'payment.failed'
  | 'payment.reminder'
  | 'subscription.created'
  | 'subscription.renewed'
  | 'subscription.cancelled'
  | 'subscription.expiring'
  | 'subscription.upgraded'
  | 'subscription.downgraded'
  | 'trial.started'
  | 'trial.ending'
  | 'trial.ended'
  | 'plan.upgraded'
  | 'plan.downgraded'
  | 'attachment.rejected'
  | 'quota.warning'
  | 'quota.exceeded'
  // Admin-only tracking events
  | 'user.deleted';

// Notification Channels
export type NotificationChannel = 'email' | 'websocket' | 'database' | 'telegram';

// Base notification event interface
export interface BaseNotificationEvent {
  type: NotificationEventType;
  userId: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  actionUrl?: string;
  actionText?: string;
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  category?: 'SYSTEM' | 'BILLING' | 'SECURITY' | 'DOMAIN' | 'WEBHOOK' | 'PAYMENT' | 'ATTACHMENT';
}

// Email-specific notification data
export interface EmailNotificationData extends BaseNotificationEvent {
  emailSubject?: string;
  emailMessage?: string;   // Detailed email body content
  emailTemplate?: string;
  emailHtml?: string;
  emailText?: string;
  replyTo?: string;
  
  // Template system support
  templateVariables?: {
    firstName?: string;
    fullName?: string;
    greeting?: string;
    closing?: string;
    cta?: {
      text: string;
      url: string;
      style?: 'primary' | 'secondary' | 'danger' | 'success';
    };
    attachments?: Array<{
      name: string;
      description?: string;
      size?: string;
    }>;
  };
  useTemplate?: boolean; // Default: true
}

// WebSocket-specific notification data
export interface WebSocketNotificationData extends BaseNotificationEvent {
  toast?: {
    type: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
  };
}

// Union type for all notification events
export type NotificationEvent = EmailNotificationData & WebSocketNotificationData;

/**
 * Configuration for each event type - defines which channels to use
 */
const EVENT_CHANNEL_CONFIG: Record<NotificationEventType, NotificationChannel[]> = {
  // User events
  'user.registered': ['email'],
  'user.email.verified': ['websocket', 'database'],
  
  // Domain events
  'domain.created': ['websocket', 'database'],
  'domain.verified': ['websocket', 'database'],
  'domain.verification.failed': ['websocket', 'database', 'email'],
  
  // Webhook events
  'webhook.created': ['websocket', 'database'],
  'webhook.verified': ['websocket', 'database'],
  'webhook.failed': ['websocket', 'database'],
  
  // Payment events
  'payment.success': ['websocket', 'database', 'email'],
  'payment.failed': ['websocket', 'database', 'email'],
  'payment.reminder': ['database', 'email'],
  
  // Subscription events
  'subscription.created': ['websocket', 'database', 'email'],
  'subscription.renewed': ['websocket', 'database'],
  'subscription.cancelled': ['websocket', 'database', 'email'],
  'subscription.expiring': ['database', 'email'],
  'subscription.upgraded': ['websocket', 'database', 'email'],
  'subscription.downgraded': ['websocket', 'database', 'email'],
  
  // Trial events
  'trial.started': ['websocket', 'database', 'email'],
  'trial.ending': ['database', 'email'],
  'trial.ended': ['websocket', 'database', 'email'],
  
  // Plan events
  'plan.upgraded': ['websocket', 'database'],
  'plan.downgraded': ['websocket', 'database', 'email'],
  
  // System events
  'attachment.rejected': ['websocket', 'database'],
  'quota.warning': ['websocket', 'database', 'email'],
  'quota.exceeded': ['websocket', 'database', 'email'],
  // Admin-only tracking events
  'user.deleted': ['telegram']
};

/**
 * Channel handler interface
 */
interface NotificationChannelHandler {
  handle(event: NotificationEvent): Promise<void>;
}

/**
 * Email channel handler
 */
class EmailChannelHandler implements NotificationChannelHandler {
  async handle(event: NotificationEvent): Promise<void> {
    if (!scalewayEmailService.isConfigured()) {
      logger.debug({ eventType: event.type, userId: event.userId }, 'Scaleway email not configured - skipping email notification');
      return;
    }

    try {
      // Get user's email and verification status
      const user = await prisma.user.findUnique({
        where: { id: event.userId },
        select: { email: true, name: true, emailVerified: true }
      });

      if (!user) {
        logger.warn({ userId: event.userId }, 'User not found for email notification');
        return;
      }

      // Skip email if user is not verified (due to bounces, spam reports, etc.)
      if (!user.emailVerified) {
        logger.warn({ 
          userId: event.userId, 
          userEmail: user.email,
          eventType: event.type
        }, 'Skipping email notification - user marked as unverified due to deliverability issues');
        
        // Log the skipped email for audit purposes
        await prisma.auditLog.create({
          data: {
            action: 'email.skipped_unverified',
            resourceType: 'notification',
            resourceId: event.userId,
            metadata: {
              eventType: event.type,
              emailAddress: user.email,
              subject: event.emailSubject || event.title,
              reason: 'User marked as unverified due to previous deliverability issues',
              category: event.category || 'SYSTEM',
              priority: event.priority || 'MEDIUM',
              timestamp: new Date().toISOString()
            },
            expiresAt: new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000) // 7 years retention
          }
        });
        
        return;
      }

      // Use custom email content if provided, otherwise generate using template system
      const subject = event.emailSubject || event.message;  // Use message as email subject
      let htmlContent: string;
      let textContent: string;

      if (event.useTemplate !== false) {
        // Use unified template system (default)
        const templateOptions = {
          variables: {
            firstName: event.templateVariables?.firstName || EmailTemplateService.extractFirstName(user.name || undefined, user.email),
            fullName: user.name || undefined,
            email: user.email,
            greeting: event.templateVariables?.greeting,
            title: event.title,
            body: event.emailMessage || event.message,  // Use emailMessage for body, fallback to message
            cta: event.templateVariables?.cta || (event.actionUrl ? {
              text: event.actionText || 'View Details',
              url: event.actionUrl,
              style: 'primary' as const
            } : undefined),
            closing: event.templateVariables?.closing,
            attachments: event.templateVariables?.attachments
          },
          includeLogo: true,
          includeUnsubscribe: false
        };

        const { html, text } = EmailTemplateService.generateEmail(templateOptions);
        htmlContent = event.emailHtml || html;
        textContent = event.emailText || text;
      } else {
        // Fallback to old system if explicitly disabled
        htmlContent = event.emailHtml || this.generateHtmlContent(event);
        textContent = event.emailText || this.generateTextContent(event);
      }

      await scalewayEmailService.sendEmail({
        to: [{
          email: user.email,
          name: user.name || user.email
        }],
        subject,
        html: htmlContent,
        text: textContent,
        replyTo: event.replyTo
      });

      // Log email send to audit trail for GDPR compliance
      await prisma.auditLog.create({
        data: {
          action: 'email.sent',
          resourceType: 'notification',
          resourceId: event.userId,
          metadata: {
            eventType: event.type,
            emailAddress: user.email,
            subject: subject,
            category: event.category || 'SYSTEM',
            priority: event.priority || 'MEDIUM',
            channels: ['email'],
            timestamp: new Date().toISOString()
          },
          expiresAt: new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000) // 7 years retention for GDPR
        }
      });

      logger.info({
        eventType: event.type,
        userId: event.userId,
        userEmail: user.email,
        subject
      }, 'Email notification sent successfully and logged to audit trail');

    } catch (error: any) {
      // Log failed email attempt to audit trail for GDPR compliance
      try {
        const user = await prisma.user.findUnique({
          where: { id: event.userId },
          select: { email: true }
        });

        await prisma.auditLog.create({
          data: {
            action: 'email.send.failed',
            resourceType: 'notification',
            resourceId: event.userId,
            metadata: {
              eventType: event.type,
              emailAddress: user?.email || 'unknown',
              subject: event.emailSubject || event.title,
              category: event.category || 'SYSTEM',
              priority: event.priority || 'MEDIUM',
              channels: ['email'],
              error: error.message,
              timestamp: new Date().toISOString()
            },
            expiresAt: new Date(Date.now() + 7 * 365 * 24 * 60 * 60 * 1000) // 7 years retention for GDPR
          }
        });
      } catch (auditError) {
        // Don't let audit logging failure break the notification system
        logger.error({ error: auditError }, 'Failed to log email failure to audit trail');
      }

      logger.error({
        error: error.message,
        eventType: event.type,
        userId: event.userId
      }, 'Failed to send email notification');
    }
  }

  private generateHtmlContent(event: NotificationEvent): string {
    const actionButton = event.actionUrl ? 
      `<a href="${event.actionUrl}" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 16px 0;">${event.actionText || 'View Details'}</a>` : '';

    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #1f2937;">${event.title}</h2>
            <p>${event.message}</p>
            ${actionButton}
            <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
            <p style="font-size: 14px; color: #6b7280;">
              Best regards,<br>
              The EmailConnect Team<br>
              <a href="https://emailconnect.eu">emailconnect.eu</a>
            </p>
          </div>
        </body>
      </html>
    `;
  }

  private generateTextContent(event: NotificationEvent): string {
    const actionText = event.actionUrl ? 
      `\n\n${event.actionText || 'View Details'}: ${event.actionUrl}` : '';

    return `
${event.title}

${event.message}${actionText}

---
Best regards,
The EmailConnect Team
https://emailconnect.eu
    `.trim();
  }
}

/**
 * WebSocket channel handler
 */
class WebSocketChannelHandler implements NotificationChannelHandler {
  async handle(event: NotificationEvent): Promise<void> {
    try {
      // Send real-time notification via WebSocket
      webSocketService.emitToUser(event.userId, {
        type: 'notification_created',
        data: {
          id: `${event.type}-${Date.now()}`,
          type: event.type,
          title: event.title,        // This is the toast text - no message field needed
          category: event.category || 'SYSTEM',
          priority: event.priority || 'MEDIUM',
          actionUrl: event.actionUrl,
          actionText: event.actionText,
          timestamp: new Date().toISOString()
        }
      });

      logger.debug({
        eventType: event.type,
        userId: event.userId
      }, 'WebSocket notification sent');

    } catch (error: any) {
      logger.error({
        error: error.message,
        eventType: event.type,
        userId: event.userId
      }, 'Failed to send WebSocket notification');
    }
  }
}

/**
 * Database channel handler (notification bell)
 */
class TelegramChannelHandler implements NotificationChannelHandler {
  async handle(event: NotificationEvent): Promise<void> {
    try {
      if (!telegramService.isConfigured()) {
        logger.debug('Telegram not configured - skipping Telegram channel');
        return;
      }

      // Build concise, non-PII message with high-level counts
      let text = event.title || event.type;

      switch (event.type) {
        case 'user.registered': {
          const totalUsers = await prisma.user.count();
          text = `User registered\nTotal users: ${totalUsers}`;
          break;
        }
        case 'user.deleted': {
          const totalUsers = await prisma.user.count();
          text = `User deleted\nTotal users: ${totalUsers}`;
          break;
        }
        case 'domain.created': {
          const totalDomains = await prisma.domain.count();
          text = `Domain created\nTotal domains: ${totalDomains}`;
          break;
        }
        case 'domain.verified': {
          const [verified, total] = await Promise.all([
            prisma.domain.count({ where: { verified: true } }),
            prisma.domain.count()
          ]);
          text = `Domain verified\nVerified: ${verified}/${total}`;
          break;
        }
        default: {
          // Fallback: use safe title/type only (avoid PII)
          text = event.title || event.type;
        }
      }

      await telegramService.sendMessage(text);

      logger.debug({ eventType: event.type }, 'Telegram notification sent');
    } catch (error: any) {
      logger.error({ error: error.message, eventType: event.type }, 'Failed to send Telegram notification');
    }
  }
}

class DatabaseChannelHandler implements NotificationChannelHandler {
  async handle(event: NotificationEvent): Promise<void> {
    try {
      // Map event type to notification type
      const notificationType = this.mapEventToNotificationType(event.type);
      const category = event.category || this.mapEventToCategory(event.type);

      await prisma.notification.create({
        data: {
          userId: event.userId,
          type: notificationType as any, // Cast to handle enum type
          category: category as any,     // Cast to handle enum type
          priority: event.priority as any || 'MEDIUM',
          title: event.title,
          message: event.message,
          actionUrl: event.actionUrl,
          actionText: event.actionText,
          data: event.data || {},
          expiresAt: this.calculateExpiryDate(event.priority || 'MEDIUM')
        }
      });

      logger.debug({
        eventType: event.type,
        userId: event.userId,
        notificationType,
        category
      }, 'Database notification created');

    } catch (error: any) {
      logger.error({
        error: error.message,
        eventType: event.type,
        userId: event.userId
      }, 'Failed to create database notification');
    }
  }

  private mapEventToNotificationType(eventType: NotificationEventType): string {
    const typeMap: Record<string, string> = {
      'payment.success': 'PAYMENT_SUCCESS',
      'payment.failed': 'PAYMENT_FAILED',
      'payment.reminder': 'PAYMENT_REMINDER',
      'domain.verified': 'DOMAIN_VERIFIED',
      'domain.verification.failed': 'DOMAIN_FAILED',
      'webhook.failed': 'WEBHOOK_FAILED',
      'attachment.rejected': 'ATTACHMENT_REJECTED',
      'quota.warning': 'PLAN_LIMIT_WARNING',
      'quota.exceeded': 'PLAN_LIMIT_REACHED',
    };

    return typeMap[eventType] || 'SYSTEM_ALERT';
  }

  private mapEventToCategory(eventType: NotificationEventType): string {
    if (eventType.startsWith('payment.') || eventType.startsWith('subscription.') || eventType.startsWith('plan.')) {
      return 'BILLING';
    }
    if (eventType.startsWith('domain.')) {
      return 'DOMAIN';
    }
    if (eventType.startsWith('webhook.')) {
      return 'WEBHOOK';
    }
    if (eventType.startsWith('attachment.')) {
      return 'ATTACHMENT';
    }
    return 'SYSTEM';
  }

  private calculateExpiryDate(priority: string): Date {
    const now = new Date();
    const hoursToAdd = priority === 'URGENT' ? 72 : priority === 'HIGH' ? 168 : 720; // 3 days, 1 week, 30 days
    return new Date(now.getTime() + hoursToAdd * 60 * 60 * 1000);
  }
}

/**
 * Main notification events service
 * Central hub for all notification events with configurable channels
 */
export class NotificationEventsService extends EventEmitter {
  private readonly channelHandlers: Record<NotificationChannel, NotificationChannelHandler>;

  constructor() {
    super();
    
    // Initialize channel handlers
    this.channelHandlers = {
      email: new EmailChannelHandler(),
      websocket: new WebSocketChannelHandler(),
      database: new DatabaseChannelHandler(),
      telegram: new TelegramChannelHandler()
    };

    // Set up event listeners for all notification types
    this.setupEventListeners();

    logger.info('NotificationEventsService initialized with channels: email, websocket, database');
  }

  /**
   * Send a notification event
   */
  async notify(eventType: NotificationEventType, event: NotificationEvent): Promise<void> {
    try {
      logger.debug({
        eventType,
        userId: event.userId,
        channels: EVENT_CHANNEL_CONFIG[eventType]
      }, 'Sending notification event');

      // Call the parent emit method to trigger listeners
      super.emit(eventType, event);

    } catch (error: any) {
      logger.error({
        error: error.message,
        eventType,
        userId: event.userId
      }, 'Failed to send notification event');
    }
  }

  /**
   * Set up event listeners for all notification types
   */
  private setupEventListeners(): void {
    Object.keys(EVENT_CHANNEL_CONFIG).forEach(eventType => {
      this.on(eventType, async (event: NotificationEvent) => {
        await this.processEvent(eventType as NotificationEventType, event);
      });
    });
  }

  /**
   * Process a notification event through configured channels
   */
  private async processEvent(eventType: NotificationEventType, event: NotificationEvent): Promise<void> {
    const channels = EVENT_CHANNEL_CONFIG[eventType] || [];

    logger.debug({
      eventType,
      userId: event.userId,
      channels
    }, 'Processing notification event');

    // Process each channel concurrently
    const promises = channels.map(async (channel) => {
      try {
        await this.channelHandlers[channel].handle(event);
      } catch (error: any) {
        logger.error({
          error: error.message,
          eventType,
          userId: event.userId,
          channel
        }, 'Channel handler failed');
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Update channel configuration for an event type
   * Useful for dynamic configuration or testing
   */
  updateEventChannels(eventType: NotificationEventType, channels: NotificationChannel[]): void {
    EVENT_CHANNEL_CONFIG[eventType] = channels;
    logger.info({
      eventType,
      channels
    }, 'Updated event channel configuration');
  }

  /**
   * Send notification to specific channel(s) only
   * Useful for targeted notifications with different content per channel
   */
  async notifyChannel(channel: NotificationChannel, eventType: NotificationEventType, event: NotificationEvent): Promise<void>;
  async notifyChannel(channels: NotificationChannel[], eventType: NotificationEventType, event: NotificationEvent): Promise<void>;
  async notifyChannel(
    channelOrChannels: NotificationChannel | NotificationChannel[], 
    eventType: NotificationEventType, 
    event: NotificationEvent
  ): Promise<void> {
    try {
      const channels = Array.isArray(channelOrChannels) ? channelOrChannels : [channelOrChannels];
      
      logger.debug({
        eventType,
        userId: event.userId,
        channels
      }, 'Sending notification to specific channels');

      // Process each specified channel
      const promises = channels.map(async (channel) => {
        try {
          await this.channelHandlers[channel].handle(event);
        } catch (error: any) {
          logger.error({
            error: error.message,
            eventType,
            userId: event.userId,
            channel
          }, 'Channel handler failed');
        }
      });

      await Promise.allSettled(promises);

    } catch (error: any) {
      logger.error({
        error: error.message,
        eventType,
        userId: event.userId
      }, 'Failed to send channel-specific notification');
    }
  }

  /**
   * Get current channel configuration
   */
  getEventChannels(eventType: NotificationEventType): NotificationChannel[] {
    return EVENT_CHANNEL_CONFIG[eventType] || [];
  }
}

// Export singleton instance
export const notificationEvents = new NotificationEventsService();
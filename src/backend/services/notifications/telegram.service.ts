import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';

class TelegramService {
  isConfigured(): boolean {
    return !!(env.TELEGRAM_ENABLED && env.TELEGRAM_BOT_TOKEN && env.TELEGRAM_ADMIN_CHAT_ID);
  }

  async sendMessage(text: string): Promise<void> {
    if (!this.isConfigured()) {
      logger.debug('Telegram not configured - skipping send');
      return;
    }

    try {
      const url = `https://api.telegram.org/bot${env.TELEGRAM_BOT_TOKEN}/sendMessage`;
      const body = {
        chat_id: env.TELEGRAM_ADMIN_CHAT_ID,
        text,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      } as const;

      const res = await (globalThis as any).fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (!res.ok) {
        const errText = await res.text().catch(() => '');
        logger.error({ status: res.status, errText }, 'Telegram sendMessage failed');
      } else {
        logger.info('Telegram message sent');
      }
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to send Telegram message');
    }
  }
}

export const telegramService = new TelegramService();

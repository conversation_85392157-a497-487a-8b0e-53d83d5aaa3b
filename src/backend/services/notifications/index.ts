/**
 * Unified Notification System
 * 
 * Simple API for sending notifications across multiple channels:
 * - Email (Scaleway Transactional Email)
 * - WebSocket (Real-time toasts)
 * - Database (Notification bell)
 * 
 * Usage:
 *   import { notify } from '../services/notifications';
 * 
 *   await notify.paymentSuccess(userId, { amount: 99.50, planType: 'pro' });
 *   await notify.domainVerified(userId, { domain: 'example.com' });
 *   await notify.quotaWarning(userId, { usage: 80, limit: 100 });
 */

import { notificationEvents, NotificationEventType, NotificationEvent } from './notification-events.service.js';
import { logger } from '../../utils/logger.js';

/**
 * Base notification function
 */
async function sendNotification(
  eventType: NotificationEventType,
  userId: string,
  title: string,
  message: string,
  options: Partial<NotificationEvent> & {
    channels?: ('email' | 'websocket' | 'database')[];
    // Channel-specific content
    toastMessage?: string;    // Short message for toast notifications
    emailMessage?: string;    // Detailed message for email (falls back to message)
    bellMessage?: string;     // Message for notification bell (falls back to message)
  } = {}
): Promise<void> {
  try {
    const { channels, toastMessage, emailMessage, bellMessage, ...eventOptions } = options;
    
    // Create base event
    const baseEvent: NotificationEvent = {
      type: eventType,
      userId,
      title,
      message,
      priority: 'MEDIUM',
      category: 'SYSTEM',
      ...eventOptions
    };

    // If channels are specified, send to specific channels with tailored content
    if (channels) {
      if (channels.includes('email')) {
        const emailEvent = {
          ...baseEvent,
          message: emailMessage || message
        };
        await notificationEvents.notifyChannel('email', eventType, emailEvent);
      }
      
      if (channels.includes('websocket')) {
        const toastEvent = {
          ...baseEvent,
          message: toastMessage || message
        };
        await notificationEvents.notifyChannel('websocket', eventType, toastEvent);
      }
      
      if (channels.includes('database')) {
        const bellEvent = {
          ...baseEvent,
          message: bellMessage || message
        };
        await notificationEvents.notifyChannel('database', eventType, bellEvent);
      }
    } else {
      // Use default channel configuration
      await notificationEvents.notify(eventType, baseEvent);
    }

  } catch (error: any) {
    logger.error({
      error: error.message,
      eventType,
      userId
    }, 'Failed to send notification');
  }
}

/**
 * Payment & Billing Notifications
 */
export const paymentNotifications = {
  async success(userId: string, data: { amount: number; currency?: string; planType?: string; method?: string }) {
    await sendNotification(
      'payment.success',
      userId,
      'Payment successful',
      `Your payment of ${data.currency || '€'}${data.amount} has been processed successfully.`,
      {
        category: 'PAYMENT',
        priority: 'HIGH',
        toast: { type: 'success', duration: 5000 },
        data,
        emailSubject: `Payment Confirmation - ${data.currency || '€'}${data.amount}`
      }
    );
  },

  async failed(userId: string, data: { amount: number; currency?: string; reason?: string }) {
    await sendNotification(
      'payment.failed',
      userId,
      'Payment failed',
      `Your payment of ${data.currency || '€'}${data.amount} could not be processed. ${data.reason || ''}`,
      {
        category: 'PAYMENT',
        priority: 'HIGH',
        toast: { type: 'error', duration: 8000 },
        data,
        emailSubject: `Payment failed - ${data.currency || '€'}${data.amount}`
      }
    );
  },

  async reminder(userId: string, data: { amount: number; currency?: string; planType: string; expiryDate: string; checkoutUrl: string; subscriptionId?: string }) {
    const expiryDate = new Date(data.expiryDate).toLocaleDateString();
    
    await sendNotification(
      'payment.reminder',
      userId,
      'Subscription renewal coming up',
      `Your ${data.planType} plan expires on ${expiryDate}. Don't lose access to advanced features like advanced attachment processing, custom headers, and spam protection across 5 domains and 50 aliases. Renew before ${expiryDate} to not lose your Pro benefits.`,
      {
        category: 'PAYMENT',
        priority: 'HIGH',
        actionUrl: data.checkoutUrl, // now a first-party renewal link
        actionText: 'Renew now',
        data,
        emailSubject: `Action required: renew your ${data.planType} plan`,
        replyTo: '<EMAIL>',
        templateVariables: {
          greeting: 'Important notice',
          cta: {
            text: 'Renew subscription',
            url: data.checkoutUrl,
            style: 'danger'
          },
          closing: 'Keep your email automation running smoothly!'
        }
      }
    );
  }
};

/**
 * Subscription Notifications
 */
export const subscriptionNotifications = {
  async created(userId: string, data: { planType: string; interval: string; amount: number }) {
    await sendNotification(
      'subscription.created',
      userId,
      'Subscription activated',
      `Welcome to the ${data.planType} plan! Your ${data.interval} subscription is now active.`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        toast: { type: 'success', duration: 6000 },
        data,
        emailSubject: `Welcome to EmailConnect ${data.planType.charAt(0).toUpperCase() + data.planType.slice(1)}!`
      }
    );
  },

  async renewed(userId: string, data: { planType: string; nextPaymentDate: string; amount: number }) {
    const nextDate = new Date(data.nextPaymentDate).toLocaleDateString();
    
    await sendNotification(
      'subscription.renewed',
      userId,
      'Subscription renewed',
      `Your ${data.planType} plan has been renewed successfully. Next payment due: ${nextDate}`,
      {
        category: 'BILLING',
        priority: 'MEDIUM',
        toast: { type: 'success', duration: 4000 },
        data
      }
    );
  },

  async cancelled(userId: string, data: { planType: string; endDate?: string }) {
    const endMessage = data.endDate ? 
      ` You'll continue to have access until ${new Date(data.endDate).toLocaleDateString()}.` : 
      ' Your access will end at the current billing period.';
    
    await sendNotification(
      'subscription.cancelled',
      userId,
      'Subscription cancelled',
      `Your ${data.planType} subscription has been cancelled.${endMessage}`,
      {
        category: 'BILLING',
        priority: 'MEDIUM',
        data,
        emailSubject: `Subscription cancelled - ${data.planType} Plan`
      }
    );
  },

  async expiring(userId: string, data: { planType: string; expiryDate: string; renewalUrl?: string }) {
    const expiryDate = new Date(data.expiryDate).toLocaleDateString();
    
    await sendNotification(
      'subscription.expiring',
      userId,
      'Subscription expiring soon',
      `Your ${data.planType} plan expires on ${expiryDate}. Don't lose access to its advanced features!`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        actionUrl: data.renewalUrl || '/settings/billing',
        actionText: 'Renew subscription',
        data,
        emailSubject: `Urgent: Your ${data.planType} plan expires soon`
      }
    );
  },

  async upgraded(userId: string, data: { planType: string; upgradedBy?: string; renewalDate?: string }) {
    const byAdmin = data.upgradedBy === 'admin';
    const renewalInfo = data.renewalDate && byAdmin 
      ? ` Your complimentary access expires on ${new Date(data.renewalDate).toLocaleDateString()}, after which you'll need to continue with a paid subscription.`
      : '';
    
    await sendNotification(
      'subscription.upgraded',
      userId,
      `${byAdmin ? 'Account upgraded by admin' : 'Plan upgraded'}`,
      `Your account has been upgraded to the ${data.planType} plan${byAdmin ? ' by an administrator' : ''}. You can now access all advanced features including more domains, custom webhook headers, and enhanced attachment processing.${renewalInfo}`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        toast: { type: 'success', duration: 6000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'Explore Pro features',
        emailSubject: `Welcome to ${data.planType.charAt(0).toUpperCase() + data.planType.slice(1)}!`,
        templateVariables: {
          greeting: byAdmin ? undefined : `Congratulations!`,
          cta: {
            text: 'Explore Pro features',
            url: '/settings/billing',
            style: 'success'
          },
          closing: 'Welcome to the Pro experience!'
        }
      }
    );
  },

  async downgraded(userId: string, data: { planType: string; downgradedBy?: string; preservedData?: any }) {
    const byAdmin = data.downgradedBy === 'admin';
    const preservedInfo = data.preservedData ? 
      ` Your data has been preserved within free plan limits (${data.preservedData.domainsKept} domains kept active).` : 
      '';
    
    await sendNotification(
      'subscription.downgraded',
      userId,
      `${byAdmin ? 'Account downgraded by admin' : 'Plan downgraded'}`,
      `Your account has been changed to the ${data.planType} plan${byAdmin ? ' by an administrator' : ''}.${preservedInfo}`,
      {
        category: 'BILLING',
        priority: 'MEDIUM',
        toast: { type: 'info', duration: 5000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'View plan details',
        emailSubject: `Plan Change: Now on ${data.planType.charAt(0).toUpperCase() + data.planType.slice(1)}`
      }
    );
  }
};

/**
 * Domain Notifications
 */
export const domainNotifications = {
  async created(userId: string, data: { domain: string }) {
    await sendNotification(
      'domain.created',
      userId,
      'Domain added',
      `Domain ${data.domain} has been added to your account. Complete verification to start receiving emails.`,
      {
        category: 'DOMAIN',
        priority: 'MEDIUM',
        toast: { type: 'success', duration: 4000 },
        data,
        actionUrl: '/dashboard/domains',
        actionText: 'Verify domain'
      }
    );
  },

  async verified(userId: string, data: { domain: string }) {
    await sendNotification(
      'domain.verified',
      userId,
      'Domain verified',
      `Excellent! Domain <strong>${data.domain}</strong> is now verified and ready to receive emails. You can now create aliases and start forwarding emails to your webhooks.`,
      {
        category: 'DOMAIN',
        priority: 'HIGH',
        toast: { type: 'success', duration: 5000 },
        data,
        actionUrl: '/domains',
        actionText: 'View domains',
        templateVariables: {
          greeting: 'Success!',
          cta: {
            text: 'Create your first alias',
            url: '/domains',
            style: 'success'
          },
          closing: 'Start automating your email workflows!'
        }
      }
    );
  },

  async verificationFailed(userId: string, data: { domain: string; reason?: string }) {
    await sendNotification(
      'domain.verification.failed',
      userId,
      'Domain verification failed',
      `We couldn't verify ${data.domain}. ${data.reason || 'Please check your DNS settings and try again.'}`,
      {
        category: 'DOMAIN',
        priority: 'HIGH',
        toast: { type: 'error', duration: 8000 },
        data,
        actionUrl: '/domains',
        actionText: 'Fix verification',
        emailSubject: `Action Required: Fix ${data.domain} Verification`
      }
    );
  }
};

/**
 * Webhook Notifications
 */
export const webhookNotifications = {
  async created(userId: string, data: { webhookName: string; url: string }) {
    await sendNotification(
      'webhook.created',
      userId,
      'Webhook created',
      `Webhook "${data.webhookName}" has been created and is ready to receive emails.`,
      {
        category: 'WEBHOOK',
        priority: 'MEDIUM',
        toast: { type: 'success', duration: 4000 },
        data
      }
    );
  },

  async verified(userId: string, data: { webhookName: string; url: string }) {
    await sendNotification(
      'webhook.verified',
      userId,
      'Webhook verified',
      `Webhook "${data.webhookName}" has been successfully verified and is now active.`,
      {
        category: 'WEBHOOK',
        priority: 'HIGH',
        toast: { type: 'success', duration: 5000 },
        data
      }
    );
  },

  async failed(userId: string, data: { webhookName: string; url: string; error?: string }) {
    await sendNotification(
      'webhook.failed',
      userId,
      'Webhook Delivery Failed',
      `Failed to deliver email to webhook "${data.webhookName}". ${data.error || 'Please check your endpoint.'}`,
      {
        category: 'WEBHOOK',
        priority: 'HIGH',
        toast: { type: 'error', duration: 6000 },
        data,
        actionUrl: '/webhooks',
        actionText: 'Check webhook'
      }
    );
  }
};

/**
 * System Notifications
 */
export const systemNotifications = {
  async quotaWarning(userId: string, data: { usage: number; limit: number; percentage: number }) {
    await sendNotification(
      'quota.warning',
      userId,
      'Email Quota Warning',
      `You've used ${data.usage} of ${data.limit} emails this month (${data.percentage}%). Consider upgrading to avoid service interruption.`,
      {
        category: 'SYSTEM',
        priority: 'HIGH',
        toast: { type: 'warning', duration: 8000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'Upgrade plan'
      }
    );
  },

  async quotaExceeded(userId: string, data: { usage: number; limit: number }) {
    await sendNotification(
      'quota.exceeded',
      userId,
      'Email quota exceeded',
      `You've exceeded your monthly email limit (${data.usage}/${data.limit}). Upgrade your plan to continue receiving emails.`,
      {
        category: 'SYSTEM',
        priority: 'URGENT',
        toast: { type: 'error', duration: 10000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'Upgrade now',
        emailSubject: 'Urgent: Email quota exceeded - Service suspended'
      }
    );
  },

  async attachmentRejected(userId: string, data: { filename: string; reason: string; messageId: string }) {
    await sendNotification(
      'attachment.rejected',
      userId,
      'Attachment Rejected',
      `Attachment "${data.filename}" was rejected: ${data.reason}`,
      {
        category: 'ATTACHMENT',
        priority: 'MEDIUM',
        toast: { type: 'warning', duration: 6000 },
        data
      }
    );
  }
};

/**
 * User Notifications
 */
export const userNotifications = {
  async registered(userId: string, data: { email: string; name?: string }) {
    // Extract first name for personalization
    const firstName = data.name ? data.name.split(' ')[0] : null;
    const greeting = firstName ? `Hey ${firstName}` : 'Hey';
    
    await sendNotification(
      'user.registered',
      userId,
      'Welcome to EmailConnect',
      `Welcome to EmailConnect! Give it a spin and reach out anytime if you need help.`,
      {
        category: 'SYSTEM',
        priority: 'HIGH',
        data,
        emailSubject: 'Welcome to EmailConnect',
        emailMessage: `<p>I'm Xander, the founder of EmailConnect.</p>

<p>We're building the first 100% EU-operated email-to-webhook service, designed with privacy and GDPR compliance at its core. Whether you're building automation workflows, collecting form submissions, or integrating email into your applications, we're here to make it simple and secure.</p>

<p>I'd love to help you get the most out of EmailConnect. Email me anytime if you have questions, ideas, or just want to chat about your use case.</p>

<p>btw if you want to schedule a call sometime, <a href="https://zcal.co/xander/emailconnect" style="color: #57bd7b;">here's my calendar</a></p>`,
        replyTo: '<EMAIL>',
        useTemplate: true,
        templateVariables: {
          firstName: firstName || undefined,
          greeting: greeting,
          closing: 'Best,\nXander\n\<EMAIL>',
          cta: {
            text: 'Get started',
            url: 'https://emailconnect.eu/domains',
            style: 'primary'
          }
        }
      }
    );
  },

  async emailVerified(userId: string, data: { email: string }) {
    await sendNotification(
      'user.email.verified',
      userId,
      'Email Verified',
      'Your email has been verified! You can now start using all features.',
      {
        category: 'SYSTEM',
        priority: 'HIGH',
        toast: { type: 'success', duration: 5000 },
        data,
        actionUrl: '/',
        actionText: 'Get started'
      }
    );
  }
};

/**
 * Trial Notifications
 */
export const trialNotifications = {
  async started(userId: string, data: { days: number; activatedBy?: string; expiryDate: string }) {
    const byAdmin = data.activatedBy && data.activatedBy !== 'user';
    const expiryDate = new Date(data.expiryDate).toLocaleDateString();
    
    await sendNotification(
      'trial.started',
      userId,
      `${byAdmin ? 'Pro trial activated by admin' : 'Pro trial started'}`,
      `Your ${data.days}-day Pro trial${byAdmin ? ' has been activated by an administrator' : ' has started'}! You now have access to all advanced features including unlimited domains, custom headers, S3 storage, and priority support. Your trial ends on ${expiryDate}.`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        toast: { type: 'success', duration: 8000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'Explore Pro features',
        emailSubject: `Welcome to your Pro trial!`,
        templateVariables: {
          greeting: byAdmin ? undefined : 'Great news!',
          cta: {
            text: 'Explore Pro features',
            url: '/settings/billing',
            style: 'success'
          },
          closing: `Make the most of your ${data.days} days!`
        }
      }
    );
  },

  async ending(userId: string, data: { daysLeft: number; expiryDate: string; upgradeUrl?: string }) {
    const expiryDate = new Date(data.expiryDate).toLocaleDateString();
    const urgencyLevel = data.daysLeft <= 1 ? 'URGENT' : 'HIGH';
    
    await sendNotification(
      'trial.ending',
      userId,
      `Pro trial ending ${data.daysLeft === 1 ? 'tomorrow' : `in ${data.daysLeft} days`}`,
      `Your Pro trial ends ${data.daysLeft === 1 ? 'tomorrow' : `in ${data.daysLeft} days`} (${expiryDate}). Don't lose access to unlimited domains, custom headers, S3 storage, and priority support. Upgrade now to continue your Pro experience seamlessly.`,
      {
        category: 'BILLING',
        priority: urgencyLevel,
        toast: { type: 'warning', duration: 10000 },
        data,
        actionUrl: data.upgradeUrl || '/settings/billing',
        actionText: 'Upgrade to Pro',
        emailSubject: `${data.daysLeft === 1 ? 'Last chance' : 'Action required'}: Pro trial ending soon`,
        replyTo: '<EMAIL>',
        templateVariables: {
          greeting: data.daysLeft <= 1 ? 'Final reminder!' : 'Important notice',
          cta: {
            text: 'Continue with Pro',
            url: data.upgradeUrl || '/settings/billing',
            style: data.daysLeft <= 1 ? 'danger' : 'primary'
          },
          closing: 'Keep your advanced features active!'
        }
      }
    );
  },

  async ended(userId: string, data: { totalDays: number; startDate: string }) {
    const startDate = new Date(data.startDate).toLocaleDateString();
    
    await sendNotification(
      'trial.ended',
      userId,
      'Pro trial ended',
      `Your ${data.totalDays}-day Pro trial (started ${startDate}) has ended. You've been moved back to the Free plan. Upgrade anytime to regain access to unlimited domains, custom headers, S3 storage, and priority support.`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        toast: { type: 'info', duration: 8000 },
        data,
        actionUrl: '/settings/billing',
        actionText: 'Upgrade to Pro',
        emailSubject: 'Pro trial ended - Upgrade to continue advanced features',
        templateVariables: {
          greeting: 'Trial complete',
          cta: {
            text: 'Upgrade to Pro',
            url: '/settings/billing',
            style: 'primary'
          },
          closing: 'Thanks for trying Pro!'
        }
      }
    );
  },
};

/**
 * Plan notifications
 */
export const planNotifications = {
  async upgraded(userId: string, data: { fromPlan: string; toPlan: string; newLimit: number }) {
    await sendNotification(
      'plan.upgraded',
      userId,
      'Plan upgraded',
      `Congratulations! You've been upgraded from ${data.fromPlan} to ${data.toPlan}. New monthly limit: ${data.newLimit} emails.`,
      {
        category: 'BILLING',
        priority: 'HIGH',
        toast: { type: 'success', duration: 6000 },
        data
      }
    );
  },

  async downgraded(userId: string, data: { fromPlan: string; toPlan: string; newLimit: number; reason?: string }) {
    await sendNotification(
      'plan.downgraded',
      userId,
      'Plan changed',
      `Your plan has been changed from ${data.fromPlan} to ${data.toPlan}. ${data.reason || ''} New monthly limit: ${data.newLimit} emails.`,
      {
        category: 'BILLING',
        priority: 'MEDIUM',
        data,
        emailSubject: `Plan changed: Now on ${data.toPlan} plan`
      }
    );
  }
};

// Export all notification functions
export const notify = {
  payment: paymentNotifications,
  subscription: subscriptionNotifications,
  trial: trialNotifications,
  domain: domainNotifications,
  webhook: webhookNotifications,
  system: systemNotifications,
  // user: userNotifications, // DISABLED: References unimplemented verification features
  plan: planNotifications,
  
  // Direct access to the events service for advanced usage
  events: notificationEvents,
  
  // Custom notification sender with channel control
  custom: sendNotification
};

// Individual notification groups are exported via the notify object
// No need for separate exports to avoid conflicts
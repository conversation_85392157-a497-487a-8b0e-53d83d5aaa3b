// Centralized user account responsibilities that are not authentication mechanics.
// TODO: gradually migrate non-auth logic from UserAuthService here.

import { prisma } from '../lib/prisma.js';
import { PlanConfigService } from './billing/plan-config.service.js';
import { UsageCalculationService } from './billing/usage-calculation.service.js';
import { logger } from '../utils/logger.js';

export class UserAccountService {
  // Check if user can process emails (monthly allowance + credits)
  static async checkUserUsageLimit(userId: string): Promise<boolean> {
    try {
      return await UsageCalculationService.canProcessEmails(userId, 1);
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to check user usage limit');
      return false;
    }
  }

  // Process email usage (monthly allowance first, then credits)
  static async incrementUserEmailUsage(userId: string): Promise<void> {
    try {
      const result = await UsageCalculationService.processEmailUsage(userId, 1);
      if (!result.success) {
        logger.error({ userId, error: result.error }, 'Failed to process email usage');
        throw new Error(result.error || 'Failed to process email usage');
      }
      logger.debug({ userId, source: result.source }, 'Email usage processed');
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to increment user email usage');
      throw error;
    }
  }

  // Update user's plan and adjust limits accordingly
  static async updateUserPlan(userId: string, newPlanType: string) {
    try {
      // Validate plan type
      PlanConfigService.getPlanConfig(newPlanType);

      const currentUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          planType: true,
          _count: { select: { domains: true, webhooks: true } }
        }
      });

      if (!currentUser) {
        return { success: false, error: 'User not found' } as const;
      }

      const aliasCount = await prisma.alias.count({ where: { domain: { userId } } });
      const usageValidation = PlanConfigService.validateUsageForPlan(newPlanType, {
        domains: currentUser._count.domains,
        webhooks: currentUser._count.webhooks,
        aliases: aliasCount
      });

      if (!usageValidation.valid) {
        return { success: false, error: `Cannot change to ${newPlanType} plan: ${usageValidation.violations.join(', ')}` } as const;
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { planType: newPlanType },
        select: { id: true, email: true, name: true, planType: true, currentMonthEmails: true, emailVerified: true }
      });

      // Non-blocking postfix sync
      try {
        const { PostfixManager } = await import('./postfix-manager.js');
        const postfixManager = new PostfixManager();
        await postfixManager.updateUserDomainsPlan(userId, newPlanType);
      } catch (error: any) {
        logger.error({ error: error.message, userId, newPlanType }, 'Failed to sync plan change to Postfix');
      }

      logger.info({ userId, oldPlan: currentUser.planType, newPlan: newPlanType }, 'User plan updated');
      return { success: true, user: updatedUser } as const;
    } catch (error: any) {
      logger.error({ error: error.message, userId, newPlanType }, 'Failed to update user plan');
      return { success: false, error: error.message } as const;
    }
  }

  // Get user's current plan information with enhanced usage data
  static async getUserPlanInfo(userId: string) {
    try {
      const user = await prisma.user.findUnique({ where: { id: userId }, select: { planType: true } });
      if (!user) return null;

      const planConfig = PlanConfigService.getPlanConfig(user.planType);
      const usageInfo = await UsageCalculationService.getUserUsageInfo(userId);

      return { planConfig, usage: usageInfo.currentUsage, limits: usageInfo.limits };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get user plan info');
      return null;
    }
  }
}


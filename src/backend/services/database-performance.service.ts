import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';

export interface DatabasePerformanceMetrics {
  connectionStats: {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    maxConnections: number;
  };
  queryStats: {
    slowQueries: Array<{
      query: string;
      duration: number;
      calls: number;
      avgDuration: number;
    }>;
    totalQueries: number;
    avgQueryTime: number;
  };
  indexUsage: Array<{
    tableName: string;
    indexName: string;
    scans: number;
    tuplesRead: number;
    tuplesReturned: number;
    efficiency: number;
  }>;
  tableStats: Array<{
    tableName: string;
    rowCount: number;
    tableSize: string;
    indexSize: string;
    totalSize: string;
  }>;
  lockStats: {
    activeBlocks: number;
    waitingQueries: number;
    deadlocks: number;
  };
}

/**
 * Database Performance Monitoring Service
 * Provides insights into database performance, slow queries, and optimization opportunities
 */
export class DatabasePerformanceService {
  
  /**
   * Get comprehensive database performance metrics
   */
  static async getPerformanceMetrics(): Promise<DatabasePerformanceMetrics> {
    try {
      const [
        connectionStats,
        queryStats,
        indexUsage,
        tableStats,
        lockStats
      ] = await Promise.all([
        this.getConnectionStats(),
        this.getQueryStats(),
        this.getIndexUsage(),
        this.getTableStats(),
        this.getLockStats(),
      ]);

      return {
        connectionStats,
        queryStats,
        indexUsage,
        tableStats,
        lockStats,
      };
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get database performance metrics');
      throw error;
    }
  }

  /**
   * Get database connection statistics
   */
  private static async getConnectionStats(): Promise<DatabasePerformanceMetrics['connectionStats']> {
    try {
      const result = await prisma.$queryRaw<Array<{
        total_connections: number;
        active_connections: number;
        idle_connections: number;
        max_connections: number;
      }>>`
        SELECT 
          (SELECT count(*) FROM pg_stat_activity) as total_connections,
          (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
          (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
          (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
      `;

      const stats = result[0];
      return {
        totalConnections: stats.total_connections,
        activeConnections: stats.active_connections,
        idleConnections: stats.idle_connections,
        maxConnections: stats.max_connections,
      };
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get connection stats');
      return {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        maxConnections: 0,
      };
    }
  }

  /**
   * Get slow query statistics
   */
  private static async getQueryStats(): Promise<DatabasePerformanceMetrics['queryStats']> {
    try {
      // Get slow queries from pg_stat_statements if available
      const slowQueries = await prisma.$queryRaw<Array<{
        query: string;
        calls: bigint;
        total_time: number;
        mean_time: number;
      }>>`
        SELECT 
          query,
          calls,
          total_time,
          mean_time
        FROM pg_stat_statements 
        WHERE mean_time > 100  -- Queries taking more than 100ms on average
        ORDER BY mean_time DESC 
        LIMIT 10
      `.catch(() => []);

      // Get general query statistics
      const generalStats = await prisma.$queryRaw<Array<{
        total_queries: bigint;
        avg_query_time: number;
      }>>`
        SELECT 
          COALESCE(SUM(calls), 0) as total_queries,
          COALESCE(AVG(mean_time), 0) as avg_query_time
        FROM pg_stat_statements
      `.catch(() => [{ total_queries: BigInt(0), avg_query_time: 0 }]);

      return {
        slowQueries: slowQueries.map(q => ({
          query: q.query.substring(0, 200) + (q.query.length > 200 ? '...' : ''),
          duration: q.total_time,
          calls: Number(q.calls),
          avgDuration: q.mean_time,
        })),
        totalQueries: Number(generalStats[0].total_queries),
        avgQueryTime: generalStats[0].avg_query_time,
      };
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get query stats');
      return {
        slowQueries: [],
        totalQueries: 0,
        avgQueryTime: 0,
      };
    }
  }

  /**
   * Get index usage statistics
   */
  private static async getIndexUsage(): Promise<DatabasePerformanceMetrics['indexUsage']> {
    try {
      const result = await prisma.$queryRaw<Array<{
        table_name: string;
        index_name: string;
        idx_scan: bigint;
        idx_tup_read: bigint;
        idx_tup_fetch: bigint;
      }>>`
        SELECT 
          schemaname || '.' || tablename as table_name,
          indexname as index_name,
          idx_scan,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
        ORDER BY idx_scan DESC
        LIMIT 20
      `;

      return result.map(row => ({
        tableName: row.table_name,
        indexName: row.index_name,
        scans: Number(row.idx_scan),
        tuplesRead: Number(row.idx_tup_read),
        tuplesReturned: Number(row.idx_tup_fetch),
        efficiency: Number(row.idx_tup_fetch) / Math.max(Number(row.idx_tup_read), 1),
      }));
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get index usage stats');
      return [];
    }
  }

  /**
   * Get table size and row count statistics
   */
  private static async getTableStats(): Promise<DatabasePerformanceMetrics['tableStats']> {
    try {
      const result = await prisma.$queryRaw<Array<{
        table_name: string;
        row_count: bigint;
        table_size: string;
        index_size: string;
        total_size: string;
      }>>`
        SELECT 
          tablename as table_name,
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
          pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 15
      `;

      return result.map(row => ({
        tableName: row.table_name,
        rowCount: Number(row.row_count),
        tableSize: row.table_size,
        indexSize: row.index_size,
        totalSize: row.total_size,
      }));
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get table stats');
      return [];
    }
  }

  /**
   * Get lock and blocking statistics
   */
  private static async getLockStats(): Promise<DatabasePerformanceMetrics['lockStats']> {
    try {
      const result = await prisma.$queryRaw<Array<{
        active_blocks: bigint;
        waiting_queries: bigint;
        deadlocks: bigint;
      }>>`
        SELECT 
          (SELECT count(*) FROM pg_locks WHERE NOT granted) as active_blocks,
          (SELECT count(*) FROM pg_stat_activity WHERE wait_event_type = 'Lock') as waiting_queries,
          (SELECT COALESCE(SUM(deadlocks), 0) FROM pg_stat_database WHERE datname = current_database()) as deadlocks
      `;

      const stats = result[0];
      return {
        activeBlocks: Number(stats.active_blocks),
        waitingQueries: Number(stats.waiting_queries),
        deadlocks: Number(stats.deadlocks),
      };
    } catch (error) {
      logger.error({ error: error.message }, 'Failed to get lock stats');
      return {
        activeBlocks: 0,
        waitingQueries: 0,
        deadlocks: 0,
      };
    }
  }

  /**
   * Analyze query performance and suggest optimizations
   */
  static async analyzeQueryPerformance(): Promise<{
    recommendations: string[];
    criticalIssues: string[];
    indexSuggestions: string[];
  }> {
    const metrics = await this.getPerformanceMetrics();
    const recommendations: string[] = [];
    const criticalIssues: string[] = [];
    const indexSuggestions: string[] = [];

    // Analyze connection usage
    const connectionUsage = metrics.connectionStats.totalConnections / metrics.connectionStats.maxConnections;
    if (connectionUsage > 0.8) {
      criticalIssues.push(`High connection usage: ${Math.round(connectionUsage * 100)}% of max connections used`);
    } else if (connectionUsage > 0.6) {
      recommendations.push(`Consider monitoring connection usage: ${Math.round(connectionUsage * 100)}% of max connections used`);
    }

    // Analyze slow queries
    if (metrics.queryStats.slowQueries.length > 0) {
      criticalIssues.push(`${metrics.queryStats.slowQueries.length} slow queries detected (>100ms average)`);
      
      metrics.queryStats.slowQueries.forEach(query => {
        if (query.avgDuration > 1000) {
          criticalIssues.push(`Very slow query detected: ${query.avgDuration.toFixed(2)}ms average`);
        }
      });
    }

    // Analyze index usage
    const unusedIndexes = metrics.indexUsage.filter(idx => idx.scans === 0);
    if (unusedIndexes.length > 0) {
      recommendations.push(`${unusedIndexes.length} unused indexes found - consider removing them`);
    }

    const inefficientIndexes = metrics.indexUsage.filter(idx => idx.efficiency < 0.1 && idx.scans > 0);
    if (inefficientIndexes.length > 0) {
      recommendations.push(`${inefficientIndexes.length} inefficient indexes found - consider optimizing`);
    }

    // Analyze table sizes
    const largeTables = metrics.tableStats.filter(table => 
      parseInt(table.totalSize.replace(/[^\d]/g, '')) > 100 // > 100MB
    );
    
    if (largeTables.length > 0) {
      recommendations.push(`${largeTables.length} large tables detected - consider partitioning or archiving`);
    }

    // Analyze locks and blocking
    if (metrics.lockStats.activeBlocks > 0) {
      criticalIssues.push(`${metrics.lockStats.activeBlocks} active lock blocks detected`);
    }

    if (metrics.lockStats.deadlocks > 0) {
      criticalIssues.push(`${metrics.lockStats.deadlocks} deadlocks detected`);
    }

    // Generate index suggestions based on slow queries
    metrics.queryStats.slowQueries.forEach(query => {
      if (query.query.includes('WHERE') && query.query.includes('ORDER BY')) {
        indexSuggestions.push(`Consider composite index for query with WHERE + ORDER BY clauses`);
      }
      if (query.query.includes('JOIN') && query.avgDuration > 500) {
        indexSuggestions.push(`Consider indexes on JOIN columns for slow JOIN query`);
      }
    });

    return {
      recommendations,
      criticalIssues,
      indexSuggestions,
    };
  }

  /**
   * Get database health score (0-100)
   */
  static async getDatabaseHealthScore(): Promise<{
    score: number;
    factors: Array<{ name: string; score: number; weight: number; impact: string }>;
  }> {
    const metrics = await this.getPerformanceMetrics();
    const factors = [];

    // Connection health (20% weight)
    const connectionUsage = metrics.connectionStats.totalConnections / metrics.connectionStats.maxConnections;
    const connectionScore = Math.max(0, 100 - (connectionUsage * 100));
    factors.push({
      name: 'Connection Usage',
      score: connectionScore,
      weight: 0.2,
      impact: connectionUsage > 0.8 ? 'critical' : connectionUsage > 0.6 ? 'warning' : 'good'
    });

    // Query performance (30% weight)
    const avgQueryScore = Math.max(0, 100 - (metrics.queryStats.avgQueryTime / 10));
    factors.push({
      name: 'Query Performance',
      score: avgQueryScore,
      weight: 0.3,
      impact: metrics.queryStats.avgQueryTime > 100 ? 'critical' : metrics.queryStats.avgQueryTime > 50 ? 'warning' : 'good'
    });

    // Index efficiency (25% weight)
    const avgIndexEfficiency = metrics.indexUsage.length > 0 
      ? metrics.indexUsage.reduce((sum, idx) => sum + idx.efficiency, 0) / metrics.indexUsage.length 
      : 1;
    const indexScore = avgIndexEfficiency * 100;
    factors.push({
      name: 'Index Efficiency',
      score: indexScore,
      weight: 0.25,
      impact: avgIndexEfficiency < 0.3 ? 'critical' : avgIndexEfficiency < 0.6 ? 'warning' : 'good'
    });

    // Lock contention (25% weight)
    const lockScore = Math.max(0, 100 - (metrics.lockStats.activeBlocks * 10 + metrics.lockStats.deadlocks * 20));
    factors.push({
      name: 'Lock Contention',
      score: lockScore,
      weight: 0.25,
      impact: metrics.lockStats.activeBlocks > 5 || metrics.lockStats.deadlocks > 0 ? 'critical' : 'good'
    });

    // Calculate weighted score
    const totalScore = factors.reduce((sum, factor) => sum + (factor.score * factor.weight), 0);

    return {
      score: Math.round(totalScore),
      factors,
    };
  }
}

// Export singleton instance
export const databasePerformanceService = DatabasePerformanceService;

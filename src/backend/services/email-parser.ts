import { simpleParser, ParsedMail, AddressObject, Attachment } from 'mailparser';
import { EnhancedEmailWebhookPayload } from '../types/index.js';
import { env } from '../config/env.js';
import { captureEmailError } from '../lib/sentry-helpers.js';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';

interface AttachmentSource {
  kind: 'buffer' | 'file' | 'excluded';
  data?: Buffer;
  path?: string;
}

export interface ParsedAttachment {
  filename: string | null;
  contentType: string;
  size: number;
  content?: string; // base64 for backward compatibility
  source?: AttachmentSource;
  excluded?: boolean;
  excludeReason?: string;
}

export interface EmailParseOptions {
  includeBinary?: boolean;
  largeAttachmentStrategy?: 'buffer' | 'tempfile';
  syncThresholdMB?: number;
}

export class EmailParser {
  static async parseToWebhookPayload(
    rawEmail: string | Buffer, 
    domain: string, 
    options: EmailParseOptions = {}
  ): Promise<EnhancedEmailWebhookPayload> {
    try {
      const parsed: ParsedMail = await simpleParser(rawEmail);

    const primaryRecipient = this.extractPrimaryAddress(parsed.to) || { address: '', name: undefined };

    const payload: EnhancedEmailWebhookPayload = {
      message: {
        sender: {
          name: parsed.from?.value?.[0]?.name || null,
          email: parsed.from?.value?.[0]?.address || '',
        },
        recipient: {
          name: primaryRecipient.name || null,
          email: primaryRecipient.address,
        },
        subject: parsed.subject || null,
        content: {
          text: parsed.text || null,
          html: typeof parsed.html === 'string' ? parsed.html : null,
        },
        date: (parsed.date || new Date()).toISOString(),
        attachments: (await this.processAttachments(parsed.attachments || [], options)).map(att => ({
          filename: att.filename,
          contentType: att.contentType,
          size: att.size,
          content: att.content,
          excluded: att.excluded,
          excludeReason: att.excludeReason as 'non-text-file' | 'too-large' | 'upload-failed' | 'rejected' | 'failed-to-process' | undefined
        })),
      },

      envelope: {
        messageId: parsed.messageId || `generated-${Date.now()}-${Math.random()}`,
        xMailer: this.getHeader(parsed.headers, 'x-mailer'),
        // deliveredTo: this.getHeader(parsed.headers, 'delivered-to'), // Contains Postix data
        xOriginalTo: this.getHeader(parsed.headers, 'x-original-to'),
        returnPath: this.getHeader(parsed.headers, 'return-path'),
        allRecipients: {
          to: this.extractAddresses(parsed.to),
          cc: this.extractAddresses(parsed.cc),
          bcc: this.extractAddresses(parsed.bcc),
        },
        headers: this.extractHeaders(parsed.headers),
        processed: {
          timestamp: new Date().toISOString(),
          domain: domain,
          alias: this.getHeader(parsed.headers, 'x-original-to') || primaryRecipient.address, // Extract alias from x-original-to header
          originalSize: Buffer.byteLength(typeof rawEmail === 'string' ? rawEmail : rawEmail.toString()),
        },
      },
    };

    // Add spam information if SpamAssassin headers are present
    const spamInfo = this.parseSpamHeaders(parsed.headers);
    if (spamInfo) {
      payload.spam = spamInfo;
    }

    return payload;
    } catch (error) {
      captureEmailError(error as Error, {
        feature: 'parsing',
        domain,
        emailSize: Buffer.byteLength(typeof rawEmail === 'string' ? rawEmail : rawEmail.toString()),
      });
      throw error; // Re-throw to maintain existing behavior
    }
  }

  private static extractPrimaryAddress(field: AddressObject | AddressObject[] | undefined): { address: string; name?: string } | null {
    const obj = field as AddressObject;
    if (obj?.value?.length) {
      return {
        address: obj.value[0].address,
        name: obj.value[0].name,
      };
    }
    return null;
  }

  private static extractAddresses(field: AddressObject | AddressObject[] | undefined): string[] {
    const obj = field as AddressObject;
    return obj?.value?.map(addr => addr.address) || [];
  }

  private static extractHeaders(headers: Map<string, any>): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      if (Array.isArray(value)) {
        result[key.toLowerCase()] = value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ');
      } else {
        result[key.toLowerCase()] = EmailParser.stringifyHeaderValue(value);
      }
    });
    return result;
  }

  private static getHeader(headers: Map<string, any>, key: string): string | null {
    const value = headers.get(key);
    if (value === undefined) return null;
    return Array.isArray(value)
      ? value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ')
      : EmailParser.stringifyHeaderValue(value);
  }

  private static stringifyHeaderValue(value: any): string {
    if (typeof value === 'string') return value;
    if (value?.address) return value.address; // handle AddressObject
    if (typeof value === 'object') return JSON.stringify(value);
    if (value === undefined || value === null) return '';
    return String(value);
  }

  static extractDomainFromEmail(email: string): string {
    const match = email.match(/@(.+)$/);
    return match ? match[1].toLowerCase() : '';
  }

  private static async processAttachments(attachments: Attachment[], options: EmailParseOptions): Promise<ParsedAttachment[]> {
    const {
      includeBinary = false,
      largeAttachmentStrategy = 'tempfile',
      syncThresholdMB = 2
    } = options;

    const processed: ParsedAttachment[] = [];

    for (const att of attachments) {
      const sizeInMB = att.size / (1024 * 1024);
      const sizeInKB = att.size / 1024;

      // Define text-based file types for backward compatibility
      const allowedTextTypes = [
        'text/plain', 'text/csv', 'text/markdown', 'text/calendar',
        'application/pdf', 'application/json', 'application/xml', 'text/xml'
      ];
      const isTextBased = allowedTextTypes.some(type =>
        att.contentType.toLowerCase().startsWith(type)
      );

      if (!includeBinary) {
        // Free plan behavior - only small text files
        const maxInlineKB = 128;
        const shouldInclude = isTextBased && sizeInKB <= maxInlineKB;
        
        processed.push({
          filename: att.filename || null,
          contentType: att.contentType,
          size: att.size,
          content: shouldInclude ? att.content?.toString('base64') : undefined,
          excluded: !shouldInclude,
          excludeReason: !isTextBased ? 'non-text-file' : 'too-large',
          source: { kind: 'excluded' }
        });
      } else {
        // Pro plan with binary support
        try {
          if (sizeInMB <= syncThresholdMB) {
            // Small files: keep in memory as buffer
            processed.push({
              filename: att.filename || null,
              contentType: att.contentType,
              size: att.size,
              content: att.content?.toString('base64'), // For backward compatibility
              source: { kind: 'buffer', data: att.content }
            });
          } else {
            // Large files: write to temp file
            if (largeAttachmentStrategy === 'tempfile') {
              const tempDir = os.tmpdir();
              const tempFile = path.join(tempDir, `attachment-${Date.now()}-${Math.random().toString(36).substring(2)}`);
              
              await fs.writeFile(tempFile, att.content);
              
              processed.push({
                filename: att.filename || null,
                contentType: att.contentType,
                size: att.size,
                source: { kind: 'file', path: tempFile }
              });
            } else {
              // Force buffer even for large files
              processed.push({
                filename: att.filename || null,
                contentType: att.contentType,
                size: att.size,
                content: att.content?.toString('base64'),
                source: { kind: 'buffer', data: att.content }
              });
            }
          }
        } catch (error) {
          // Clear error for Pro users with S3 - this should not happen
          processed.push({
            filename: att.filename || null,
            contentType: att.contentType,
            size: att.size,
            excluded: true,
            excludeReason: `failed-to-process: ${error.message}`,
            source: { kind: 'excluded' }
          });
        }
      }
    }

    return processed;
  }

  /**
   * Parse SpamAssassin headers if present
   */
  private static parseSpamHeaders(headers: Map<string, any>) {
    const spamStatus = this.getHeader(headers, 'x-spam-status');
    const spamScore = this.getHeader(headers, 'x-spam-score');

    // Only return spam info if SpamAssassin headers are present
    if (!spamStatus && !spamScore) {
      return undefined;
    }

    return {
      isSpam: spamStatus?.toLowerCase().includes('yes') || false,
      score: parseFloat(spamScore || '0'),
      level: this.getHeader(headers, 'x-spam-level'),
      report: this.getHeader(headers, 'x-spam-report'),
      // version: this.getHeader(headers, 'x-spam-checker-version'),
      status: spamStatus
    };
  }
}
import { FileTypeRule } from '@prisma/client';
import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { DEFAULT_FILE_TYPE_RULES } from './s3-storage.service.js';

export interface FileTypeValidation {
  allowed: boolean;
  rule?: FileTypeRule;
  reason?: string;
  handling: 'inline' | 'storage' | 'reject';
  shouldUploadSync: boolean;
}

export interface AttachmentInfo {
  filename: string;
  contentType: string;
  size: number;
}

export class FileTypeService {
  /**
   * Get or create default file type rules for a user
   */
  static async getOrCreateUserRules(userId: string): Promise<FileTypeRule[]> {
    // Check if user has any rules
    const existingRules = await prisma.fileTypeRule.findMany({
      where: { userId, active: true },
      orderBy: { category: 'asc' }
    });

    if (existingRules.length > 0) {
      return existingRules;
    }

    // Get user's plan type
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { planType: true }
    });

    // Create default rules based on plan
    const rulesToCreate = await this.getDefaultRulesForPlan(user?.planType || 'free');
    
    const createdRules: FileTypeRule[] = [];
    for (const ruleData of rulesToCreate) {
      const rule = await prisma.fileTypeRule.create({
        data: {
          userId,
          ...ruleData
        }
      });
      createdRules.push(rule);
    }

    logger.info({ userId, rulesCreated: createdRules.length }, 'Created default file type rules for user');
    return createdRules;
  }

  /**
   * Get default rules based on plan type
   */
  static async getDefaultRulesForPlan(planType: string): Promise<any[]> {
    const rules = [];

    if (planType === 'free') {
      // Free users: Only text files, inline only, small sizes
      rules.push({
        category: 'text',
        mimeTypes: DEFAULT_FILE_TYPE_RULES.text.mimeTypes,
        maxSizeMB: 0.128, // 128KB
        handling: 'inline',
        syncThresholdMB: 0.128,
        expirationHours: 1,
        active: true
      });

      // Reject everything else
      ['images', 'documents', 'archives', 'media', 'other'].forEach(category => {
        rules.push({
          category,
          mimeTypes: DEFAULT_FILE_TYPE_RULES[category].mimeTypes,
          maxSizeMB: 0,
          handling: 'reject',
          syncThresholdMB: 0,
          expirationHours: null,
          active: true
        });
      });
    } else {
      // Pro/Enterprise users: All file types with storage
      Object.entries(DEFAULT_FILE_TYPE_RULES).forEach(([category, defaultRule]) => {
        rules.push({
          category,
          mimeTypes: defaultRule.mimeTypes,
          maxSizeMB: defaultRule.maxSizeMB,
          handling: category === 'other' ? 'reject' : 'storage',
          syncThresholdMB: defaultRule.syncThresholdMB,
          expirationHours: defaultRule.expirationHours,
          active: true
        });
      });
    }

    return rules;
  }

  /**
   * Validate an attachment against user's file type rules
   */
  static async validateAttachment(
    attachment: AttachmentInfo, 
    userId: string
  ): Promise<FileTypeValidation> {
    // Get user's file type rules
    const rules = await this.getOrCreateUserRules(userId);

    // Get user settings to check maxInlineSize
    const userSettings = await prisma.userSettings.findUnique({
      where: { userId },
      select: { maxInlineSize: true, fileTypeSettings: true }
    });

    // Find matching rule by MIME type
    const matchingRule = this.findMatchingRule(attachment.contentType, rules);

    if (!matchingRule) {
      return {
        allowed: false,
        reason: `File type ${attachment.contentType} is not allowed`,
        handling: 'reject',
        shouldUploadSync: false
      };
    }

    // Check if handling is reject
    if (matchingRule.handling === 'reject') {
      return {
        allowed: false,
        rule: matchingRule,
        reason: `File type ${attachment.contentType} is blocked by policy`,
        handling: 'reject',
        shouldUploadSync: false
      };
    }

    // Check size limit - use the smaller of rule limit or user's maxInlineSize setting
    const sizeMB = attachment.size / (1024 * 1024);
    const userMaxSizeMB = userSettings?.maxInlineSize || 1.0; // Default to 1MB if no setting
    const effectiveLimit = Math.min(matchingRule.maxSizeMB, userMaxSizeMB);
    
    if (sizeMB > effectiveLimit) {
      const limitSource = effectiveLimit === userMaxSizeMB ? 'user setting' : 'file type rule';
      return {
        allowed: false,
        rule: matchingRule,
        reason: `File size ${sizeMB.toFixed(2)}MB exceeds ${limitSource} limit of ${effectiveLimit}MB`,
        handling: 'reject',
        shouldUploadSync: false
      };
    }

    // User toggle override: if this category is disabled in settings, block with a clear reason
    const toggles = (userSettings?.fileTypeSettings as any) || null;
    if (toggles && matchingRule.category && toggles[matchingRule.category] === false) {
      return {
        allowed: false,
        rule: matchingRule,
        reason: 'disabled-in-settings',
        handling: 'reject',
        shouldUploadSync: false
      };
    }

    // Determine if should upload synchronously - use smaller of rule threshold or user limit
    const effectiveSyncThreshold = Math.min(matchingRule.syncThresholdMB, userMaxSizeMB);
    const shouldUploadSync = sizeMB <= effectiveSyncThreshold;

    return {
      allowed: true,
      rule: matchingRule,
      handling: matchingRule.handling as 'inline' | 'storage',
      shouldUploadSync
    };
  }

  /**
   * Categorize multiple attachments into sync/async groups
   */
  static async categorizeAttachments(
    attachments: AttachmentInfo[],
    userId: string
  ): Promise<{
    syncAttachments: AttachmentInfo[];
    asyncAttachments: AttachmentInfo[];
    rejectedAttachments: Array<AttachmentInfo & { reason: string }>;
  }> {
    const syncAttachments: AttachmentInfo[] = [];
    const asyncAttachments: AttachmentInfo[] = [];
    const rejectedAttachments: Array<AttachmentInfo & { reason: string }> = [];

    for (const attachment of attachments) {
      const validation = await this.validateAttachment(attachment, userId);

      if (!validation.allowed) {
        rejectedAttachments.push({
          ...attachment,
          reason: validation.reason || 'File not allowed'
        });
        continue;
      }

      if (validation.handling === 'inline') {
        // Inline attachments don't need S3 upload
        continue;
      }

      if (validation.shouldUploadSync) {
        syncAttachments.push(attachment);
      } else {
        asyncAttachments.push(attachment);
      }
    }

    logger.debug({
      userId,
      total: attachments.length,
      sync: syncAttachments.length,
      async: asyncAttachments.length,
      rejected: rejectedAttachments.length
    }, 'Categorized attachments for processing');

    return { syncAttachments, asyncAttachments, rejectedAttachments };
  }

  /**
   * Find matching rule for a MIME type
   */
  private static findMatchingRule(contentType: string, rules: FileTypeRule[]): FileTypeRule | null {
    // Normalize content type
    const normalizedType = contentType.toLowerCase().split(';')[0].trim();

    for (const rule of rules) {
      for (const mimePattern of rule.mimeTypes) {
        if (this.matchesMimeType(normalizedType, mimePattern)) {
          return rule;
        }
      }
    }

    return null;
  }

  /**
   * Check if a content type matches a MIME pattern
   */
  private static matchesMimeType(contentType: string, pattern: string): boolean {
    // Handle wildcard patterns like "image/*"
    if (pattern === '*/*') {
      return true;
    }

    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2);
      return contentType.startsWith(prefix + '/');
    }

    return contentType === pattern;
  }

  /**
   * Update user's file type rule
   */
  static async updateRule(
    userId: string,
    category: string,
    updates: Partial<{
      mimeTypes: string[];
      maxSizeMB: number;
      handling: string;
      syncThresholdMB: number;
      expirationHours: number | null;
      active: boolean;
    }>
  ): Promise<FileTypeRule> {
    const rule = await prisma.fileTypeRule.upsert({
      where: {
        userId_category: {
          userId,
          category
        }
      },
      update: updates,
      create: {
        userId,
        category,
        mimeTypes: updates.mimeTypes || DEFAULT_FILE_TYPE_RULES[category]?.mimeTypes || [],
        maxSizeMB: updates.maxSizeMB || DEFAULT_FILE_TYPE_RULES[category]?.maxSizeMB || 10,
        handling: updates.handling || 'storage',
        syncThresholdMB: updates.syncThresholdMB || 2.0,
        expirationHours: updates.expirationHours,
        active: updates.active !== undefined ? updates.active : true
      }
    });

    logger.info({ userId, category, updates }, 'Updated file type rule');
    return rule;
  }

  /**
   * Get attachment expiration hours based on rules
   */
  static getExpirationHours(rule: FileTypeRule | null, defaultHours: number = 168): number {
    if (rule && rule.expirationHours !== null) {
      return rule.expirationHours;
    }
    return defaultHours;
  }
}
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetO<PERSON>Command,
  HeadObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { AttachmentStatus } from '@prisma/client';
import { logger } from '../../utils/logger.js';
import { env } from '../../config/env.js';
import { encryptionService } from '../encryption.service.js';

export interface S3Config {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucket: string;
  endpoint?: string; // For S3-compatible services like Scaleway
}

export interface EmailAttachment {
  filename: string;
  contentType: string;
  size: number;
  content: Buffer | NodeJS.ReadableStream;
}

export interface AttachmentUploadResult {
  fileId: string;
  s3Key: string;
  downloadUrl: string;
  expiresAt: Date;
  uploadStatus: AttachmentStatus;
}

export interface FileTypeRule {
  category: 'text' | 'images' | 'documents' | 'archives' | 'media' | 'other';
  mimeTypes: string[];
  maxSizeMB: number;
  handling: 'inline' | 'storage' | 'reject';
  syncThresholdMB: number;
  expirationHours?: number;
}

export class S3StorageService {
  private client: S3Client;
  private config: S3Config;

  constructor(config?: Partial<S3Config> | any) {
    // Decrypt config if it's encrypted
    const decryptedConfig = config?._encrypted ? 
      encryptionService.decryptS3Config(config) : 
      config;
    
    // Normalize provided config (support keys: accessKey/accessKeyId, secretKey/secretAccessKey)
    const provided = (decryptedConfig || {}) as any;
    const normalized: S3Config = {
      accessKeyId: provided.accessKeyId || provided.accessKey || env.S3_ACCESS_KEY_ID,
      secretAccessKey: provided.secretAccessKey || provided.secretKey || env.S3_SECRET_ACCESS_KEY,
      region: provided.region || env.S3_REGION,
      bucket: provided.bucket || env.S3_BUCKET,
      endpoint: provided.endpoint || env.S3_ENDPOINT,
    };

    this.config = normalized;

    // Configure endpoint/URLStyle correctly for providers like Scaleway
    let endpointOption: string | undefined
    let forcePathStyleOption: boolean | undefined
    if (this.config.endpoint) {
      try {
        const url = new URL(this.config.endpoint)
        const hasBucketPrefix = !!(this.config.bucket && url.hostname.startsWith(`${this.config.bucket}.`))
        if (hasBucketPrefix) {
          // Strip the bucket prefix from the endpoint host so the SDK can add it exactly once
          const baseHost = url.hostname.replace(`${this.config.bucket}.`, '')
          endpointOption = `${url.protocol}//${baseHost}${url.port ? `:${url.port}` : ''}${url.pathname || ''}`
          forcePathStyleOption = false // use virtual-hosted style; SDK will prepend bucket
        } else {
          endpointOption = this.config.endpoint
          // For plain service endpoints, prefer path-style for S3-compatible unless you want vhost style
          // Use virtual-hosted style by default (forcePathStyle=false)
          forcePathStyleOption = false
        }
      } catch {
        endpointOption = this.config.endpoint
        forcePathStyleOption = false
      }
    }

    this.client = new S3Client({
      region: this.config.region,
      credentials: {
        accessKeyId: String(this.config.accessKeyId || ''),
        secretAccessKey: String(this.config.secretAccessKey || ''),
      },
      ...(endpointOption ? { endpoint: endpointOption } : {}),
      ...(typeof forcePathStyleOption === 'boolean' ? { forcePathStyle: forcePathStyleOption } : {}),
    });
  }

  /**
   * Upload attachment to S3 synchronously (for small files)
   */
  async uploadAttachmentSync(
    attachment: EmailAttachment,
    messageId: string,
    folder?: string
  ): Promise<AttachmentUploadResult> {
    const fileId = this.generateFileId();
    const s3Key = this.generateS3Key(messageId, attachment.filename, folder);

    try {
      await this.uploadToS3(s3Key, attachment);

      const expiresAt = this.calculateExpiration();
      const downloadUrl = this.generateDownloadUrl(fileId);

      logger.info({
        fileId,
        s3Key,
        filename: attachment.filename,
        size: attachment.size,
        uploadType: 'sync'
      }, 'Attachment uploaded successfully (sync)');

      return {
        fileId,
        s3Key,
        downloadUrl,
        expiresAt,
        uploadStatus: AttachmentStatus.COMPLETED,
      };
    } catch (error: any) {
      logger.error({
        fileId,
        filename: attachment.filename,
        error: error.message
      }, 'Failed to upload attachment (sync)');

      throw error;
    }
  }

  /**
   * Upload attachment to S3 (can be called async from queue)
   */
  async uploadAttachment(
    attachment: EmailAttachment,
    messageId: string,
    fileId: string,
    folder?: string
  ): Promise<AttachmentUploadResult> {
    const s3Key = this.generateS3Key(messageId, attachment.filename, folder);

    try {
      await this.uploadToS3(s3Key, attachment);

      const expiresAt = this.calculateExpiration();
      const downloadUrl = this.generateDownloadUrl(fileId);

      logger.info({
        fileId,
        s3Key,
        filename: attachment.filename,
        size: attachment.size,
        uploadType: 'async'
      }, 'Attachment uploaded successfully (async)');

      return {
        fileId,
        s3Key,
        downloadUrl,
        expiresAt,
        uploadStatus: AttachmentStatus.COMPLETED,
      };
    } catch (error: any) {
      logger.error({
        fileId,
        filename: attachment.filename,
        error: error.message
      }, 'Failed to upload attachment (async)');

      return {
        fileId,
        s3Key: '',
        downloadUrl: this.generateDownloadUrl(fileId),
        expiresAt: this.calculateExpiration(),
        uploadStatus: AttachmentStatus.FAILED,
        // Surface error message to caller so it can be stored in DB
        ...(error?.message ? { errorMessage: String(error.message) } : {}) as any,
      } as any;
    }
  }

  /**
   * Generate presigned download URL for S3 object
   */
  async generatePresignedUrl(s3Key: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.config.bucket,
      Key: s3Key,
    });

    return await getSignedUrl(this.client, command, { expiresIn });
  }

  /**
   * Delete attachment from S3
   */
  async deleteAttachment(s3Key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.config.bucket,
        Key: s3Key,
      });

      await this.client.send(command);

      logger.info({ s3Key }, 'Attachment deleted from S3');
    } catch (error: any) {
      logger.error({
        s3Key,
        error: error.message
      }, 'Failed to delete attachment from S3');
      throw error;
    }
  }

  /**
   * Check if object exists in S3
   */
  async objectExists(s3Key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.config.bucket,
        Key: s3Key,
      });

      await this.client.send(command);
      return true;
    } catch (error: any) {
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Validate file against user's file type rules
   */
  validateFileType(attachment: EmailAttachment, rules: FileTypeRule[]): FileTypeRule | null {
    // Find matching rule by MIME type
    for (const rule of rules) {
      if (rule.mimeTypes.some(mime => this.matchesMimeType(attachment.contentType, mime))) {
        const sizeMB = attachment.size / (1024 * 1024);

        // Check size limit
        if (sizeMB > rule.maxSizeMB) {
          logger.debug({
            filename: attachment.filename,
            contentType: attachment.contentType,
            sizeMB,
            maxSizeMB: rule.maxSizeMB
          }, 'File exceeds size limit for category');
          return null;
        }

        return rule;
      }
    }

    // No matching rule found
    return null;
  }

  /**
   * Test S3 connection by attempting to list bucket
   */
  async testConnection(): Promise<boolean> {
    try {
      const testKey = `_test/${Date.now()}.txt`;
      const command = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: testKey,
        Body: Buffer.from('test'),
      });

      await this.client.send(command);

      // Clean up test file
      await this.deleteAttachment(testKey);

      return true;
    } catch (error: any) {
      logger.error({
        error: error.message,
        bucket: this.config.bucket
      }, 'S3 connection test failed');
      return false;
    }
  }

  // Private helper methods

  private async uploadToS3(s3Key: string, attachment: EmailAttachment): Promise<void> {
    const body = attachment.content as any;
    const isBuffer = Buffer.isBuffer(body);
    const isStream = !!(body && typeof body.pipe === 'function');

    if (!isBuffer && !isStream) {
      throw new Error(`S3 upload expects Buffer or Readable stream; got ${typeof body}. Attachment: ${attachment.filename}`);
    }

    const command = new PutObjectCommand({
      Bucket: this.config.bucket,
      Key: s3Key,
      Body: body,
      ContentType: attachment.contentType,
      Metadata: {
        filename: attachment.filename,
        originalSize: attachment.size.toString(),
      },
    });

    await this.client.send(command);
  }

  private generateFileId(): string {
    // Generate a unique file ID (CUID-like)
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 9);
    return `af_${timestamp}${random}`;
  }

  private generateS3Key(messageId: string, filename: string, folder?: string): string {
    // Structure: [folder/]messageId/timestamp_filename
    const timestamp = Date.now();
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    const parts = folder ? [folder] : [];
    parts.push(messageId, `${timestamp}_${sanitizedFilename}`);
    return parts.join('/');
  }

  private generateDownloadUrl(fileId: string): string {
    // Generate our tracked download URL
    const baseUrl = env.URL || 'https://emailconnect.eu';
    return `${baseUrl}/attachments/${fileId}/download`;
  }

  private calculateExpiration(hoursOverride?: number): Date {
    const hours = hoursOverride || 168; // Default 7 days
    return new Date(Date.now() + hours * 60 * 60 * 1000);
  }

  private matchesMimeType(contentType: string, pattern: string): boolean {
    // Handle wildcard patterns like "image/*"
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2);
      return contentType.startsWith(prefix + '/');
    }
    return contentType === pattern;
  }
}

// Sanitize user-provided S3 folder strings
// - normalize slashes
// - allow only [A-Za-z0-9-_/]
// - remove leading/trailing slashes
// - collapse duplicate slashes
// - remove '.' and '..' segments
// - cap length to 200 chars
export function sanitizeS3Folder(input?: string | null): string {
  if (!input) return '';
  let s = String(input).trim();
  // Normalize slashes
  s = s.replace(/\\+/g, '/');
  // Collapse multiple slashes
  s = s.replace(/\/{2,}/g, '/');
  // Process path segments with dot-segment handling
  const rawParts = s.split('/').filter(Boolean);
  const out: string[] = [];
  for (const segRaw of rawParts) {
    if (segRaw === '.') continue;
    if (segRaw === '..') { if (out.length) out.pop(); continue; }
    const seg = segRaw.replace(/[^A-Za-z0-9\-_]+/g, '_');
    if (seg.length) out.push(seg);
  }
  s = out.join('/').replace(/^\/+|\/+$/g, '');
  // Enforce max length
  if (s.length > 200) s = s.slice(0, 200);
  return s;
}


// Default file type categories with common MIME types
export const DEFAULT_FILE_TYPE_RULES: Record<string, FileTypeRule> = {
  text: {
    category: 'text',
    mimeTypes: [
      'text/plain',
      'text/csv',
      'text/calendar',
      'text/markdown',
      'application/json',
      'application/xml',
      'text/xml',
    ],
    maxSizeMB: 10,
    handling: 'storage',
    syncThresholdMB: 2,
    expirationHours: 168, // 7 days
  },
  images: {
    category: 'images',
    mimeTypes: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ],
    maxSizeMB: 25,
    handling: 'storage',
    syncThresholdMB: 2,
    expirationHours: 168,
  },
  documents: {
    category: 'documents',
    mimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/epub+zip',
    ],
    maxSizeMB: 50,
    handling: 'storage',
    syncThresholdMB: 2,
    expirationHours: 168,
  },
  archives: {
    category: 'archives',
    mimeTypes: [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-rar-compressed',
      'application/x-tar',
      'application/x-7z-compressed',
      'application/gzip',
    ],
    maxSizeMB: 100,
    handling: 'storage',
    syncThresholdMB: 1, // Archives always async
    expirationHours: 168,
  },
  media: {
    category: 'media',
    mimeTypes: [
      'audio/*',
      'video/*',
    ],
    maxSizeMB: 100,
    handling: 'storage',
    syncThresholdMB: 1, // Media always async
    expirationHours: 72, // 3 days for media
  },
  other: {
    category: 'other',
    mimeTypes: ['*/*'], // Catch-all
    maxSizeMB: 10,
    handling: 'reject', // Default reject unknown types
    syncThresholdMB: 2,
    expirationHours: 168,
  },
};
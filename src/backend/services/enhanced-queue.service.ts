import Bull from 'bull';
import { Redis } from 'ioredis';
import type { Redis as RedisType } from 'ioredis';
import axios from 'axios';
import * as Sentry from '@sentry/node';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';
import { AnyWebhookPayload, InternalQueuePayload } from '../types/index.js';
import { prisma } from '../lib/prisma.js';
import { captureWebhookError, captureQueueError } from '../lib/sentry-helpers.js';
import { EmailAttachment } from './storage/s3-storage.service.js';
import { formatHttpError, isRetryableHttpStatus } from '../utils/http-status-messages.js';

interface WebhookJobData {
  webhookUrl: string;
  payload: InternalQueuePayload;
  webhookSecret?: string;
  customHeaders?: Record<string, string>;
}

interface AttachmentUploadJobData {
  fileId: string;
  attachment: EmailAttachment;
  messageId: string;
  userId: string;
  folder?: string;
  s3Config?: any;
}

interface DeadLetterJobData {
  originalQueue: string;
  originalJobData: any;
  failureReason: string;
  failedAt: Date;
  attempts: number;
}

export class EnhancedQueueService {
  private webhookQueue: Bull.Queue<WebhookJobData>;
  private attachmentUploadQueue: Bull.Queue<AttachmentUploadJobData>;
  private deadLetterQueue: Bull.Queue<DeadLetterJobData>;
  private redisClient: RedisType;
  private sentinelClient: RedisType;
  private initialized = false;
  private rateLimitCache = new Map<string, number>();

  constructor() {
    // Initialize will be called separately
  }

  async initialize() {
    if (this.initialized) {
      logger.debug('Enhanced queue service already initialized');
      return;
    }

    try {
      // Initialize Redis with Sentinel support
      await this.initializeRedis();
      
      // Initialize queues
      await this.initializeQueues();
      
      // Setup queue processors
      await this.setupProcessors();
      
      // Setup monitoring
      this.setupMonitoring();
      
      this.initialized = true;
      logger.info('Enhanced queue service initialized with Redis Sentinel support');
    } catch (error) {
      logger.error({ error }, 'Failed to initialize enhanced queue service');
      throw error;
    }
  }

  private async initializeRedis() {
    const redisUrl = process.env.REDIS_URL || env.REDIS_URL;
    
    // Check if we should use Sentinel (production) or direct connection (development)
    // Temporarily disable Sentinel until it's properly configured in production
    const useSentinel = process.env.USE_REDIS_SENTINEL === 'true'; // Explicitly opt-in only
    
    if (useSentinel) {
      logger.info('Initializing Redis with Sentinel support for high availability');
      
      // Connect to Redis via Sentinel
      this.sentinelClient = new Redis({
        sentinels: [
          { host: 'redis-sentinel', port: 26379 },
          // Add more sentinels for production
        ],
        name: 'emailconnect-master',
        password: process.env.REDIS_PASSWORD || 'dev_redis_password_change_in_prod',
        enableOfflineQueue: false,
        maxRetriesPerRequest: 3,
      });
      
      this.redisClient = this.sentinelClient;
    } else {
      logger.info('Initializing Redis with direct connection (development mode)');
      this.redisClient = new Redis(redisUrl);
    }

    // Test connection
    const pingResult = await this.redisClient.ping();
    if (pingResult !== 'PONG') {
      throw new Error('Redis connection failed');
    }
    
    logger.info('Redis connection established successfully');
  }

  private async initializeQueues() {
    const redisUrl = process.env.REDIS_URL || env.REDIS_URL;
    const redisConfig = {
      redis: redisUrl
    };

    // Main webhook delivery queue - DISABLED unless explicitly enabled
    // This prevents duplicate processing with the main queue.ts processor
    const enableWebhookProcessor = process.env.ENHANCED_WEBHOOK_PROCESSOR === 'true';
    
    if (enableWebhookProcessor) {
      logger.warn('ENHANCED_WEBHOOK_PROCESSOR is enabled - this may cause duplicate webhook processing!');
      this.webhookQueue = new Bull('webhook-delivery', {
        ...redisConfig,
        defaultJobOptions: {
          attempts: env.WEBHOOK_RETRY_ATTEMPTS,
          backoff: {
            type: 'exponentialWithJitter',
            delay: 1000,
          },
          removeOnComplete: 100,
          removeOnFail: false, // Don't remove failed jobs - send to dead letter queue
        },
      });
    } else {
      logger.info('Enhanced webhook processor disabled (default) - using main queue.ts processor');
    }

    // Attachment upload queue
    this.attachmentUploadQueue = new Bull('attachment-upload', {
      ...redisConfig,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 50,
        removeOnFail: false, // Don't remove failed jobs
      },
    });

    // Dead letter queue for permanently failed jobs
    this.deadLetterQueue = new Bull('dead-letter-queue', {
      ...redisConfig,
      defaultJobOptions: {
        removeOnComplete: 1000, // Keep more dead letter records
        removeOnFail: false,
      },
    });

    logger.info('All queues initialized with dead letter queue support');
  }

  private async setupProcessors() {
    // Webhook delivery processor with dead letter queue support
    // Only process if explicitly enabled to avoid duplicate processing
    if (this.webhookQueue) {
      this.webhookQueue.process('webhook-delivery', async (job) => {
        try {
          return await this.processWebhookJob(job);
        } catch (error) {
          // If this is the last attempt, send to dead letter queue
          if (job.attemptsMade >= job.opts.attempts - 1) {
            await this.sendToDeadLetterQueue('webhook-delivery', job.data, error.message);
          }
          throw error;
        }
      });
    }

    // Attachment upload processor
    this.attachmentUploadQueue.process('upload', async (job) => {
      try {
        return await this.processAttachmentJob(job);
      } catch (error) {
        if (job.attemptsMade >= job.opts.attempts - 1) {
          await this.sendToDeadLetterQueue('attachment-upload', job.data, error.message);
        }
        throw error;
      }
    });

    // Dead letter queue processor (for manual intervention)
    this.deadLetterQueue.process('dead-letter', async (job) => {
      logger.warn({ 
        originalQueue: job.data.originalQueue,
        failureReason: job.data.failureReason,
        failedAt: job.data.failedAt 
      }, 'Processing dead letter queue job - manual intervention may be required');
      
      // This could trigger alerts, notifications, or manual review processes
      return { status: 'logged', requiresManualReview: true };
    });
  }

  private setupMonitoring() {
    // Queue event monitoring - filter out undefined queues
    const queues = [this.webhookQueue, this.attachmentUploadQueue, this.deadLetterQueue].filter(Boolean);
    queues.forEach(queue => {
      queue.on('completed', (job) => {
        logger.info({ 
          queue: queue.name, 
          jobId: job.id,
          processingTime: Date.now() - job.processedOn 
        }, 'Job completed');
      });

      queue.on('failed', (job, err) => {
        logger.error({ 
          queue: queue.name, 
          jobId: job.id, 
          error: err.message,
          attempts: job.attemptsMade 
        }, 'Job failed');
      });

      queue.on('stalled', (job) => {
        logger.warn({ 
          queue: queue.name, 
          jobId: job.id 
        }, 'Job stalled - may indicate worker issues');
      });
    });

    // Redis connection monitoring
    this.redisClient.on('error', (error) => {
      logger.error({ error }, 'Redis connection error');
      captureQueueError(error, { feature: 'redis-connection' });
    });

    this.redisClient.on('reconnecting', () => {
      logger.warn('Redis reconnecting...');
    });

    this.redisClient.on('connect', () => {
      logger.info('Redis connected');
    });
  }

  private async processWebhookJob(job: Bull.Job<WebhookJobData>): Promise<any> {
    const { webhookUrl, payload, webhookSecret, customHeaders } = job.data;
    
    // Add breadcrumb for webhook processing start
    Sentry.addBreadcrumb({
      category: 'webhook',
      message: 'Starting webhook delivery (enhanced queue)',
      level: 'info',
      data: {
        webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...'),
        attempt: job.attemptsMade + 1,
        jobId: job.id
      }
    });

    try {
      // Get messageId from either old or new payload structure
      const messageId = this.getMessageId(payload);

      logger.debug({
        webhookUrl,
        messageId,
        hasSecret: !!webhookSecret,
        payloadType: 'type' in payload ? payload.type : 'email',
        queue: 'enhanced'
      }, 'Delivering webhook via enhanced queue');

      // Update delivery attempt in database
      await this.updateEmailDeliveryAttempt(messageId);

      // Prepare webhook payload by removing internal tracking fields
      const webhookPayload = { ...payload };
      delete webhookPayload._internalMessageId;

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'EmailConnect/1.0',
        'X-Email-Webhook': 'true',
        'X-Queue-Version': 'enhanced', // Track which queue processed this
        ...customHeaders // Merge custom headers
      };

      // Add HMAC signature if webhook secret is provided
      if (webhookSecret) {
        const crypto = await import('crypto');
        const payloadString = JSON.stringify(webhookPayload);
        const signature = crypto
          .createHmac('sha256', webhookSecret)
          .update(payloadString, 'utf8')
          .digest('hex');

        headers['X-Webhook-Signature'] = `sha256=${signature}`;
        headers['X-Webhook-Timestamp'] = Math.floor(Date.now() / 1000).toString();
      }

      const response = await axios.post(webhookUrl, webhookPayload, {
        timeout: env.WEBHOOK_TIMEOUT_MS,
        headers,
        maxRedirects: 3, // Allow reasonable redirects
        validateStatus: (status) => status < 500, // Don't throw on 4xx errors
      });

      // Check if response indicates a non-retryable error
      if (response.status >= 400 && response.status < 500) {
        const errorMsg = formatHttpError(response.status, false);
        logger.warn({
          webhookUrl,
          messageId,
          status: response.status,
          errorMsg,
          queue: 'enhanced'
        }, 'Webhook delivery rejected with 4xx error');

        // Update email status for non-retryable errors
        const emailStatus = response.status === 413 ? 'REJECTED' : 'FAILED';
        await this.updateEmailDeliveryStatus(messageId, emailStatus as any, errorMsg);
        
        // Return failure without throwing (prevents retry)
        return { status: 'failed', httpStatus: response.status, error: errorMsg };
      }

      logger.info({
        webhookUrl,
        messageId,
        status: response.status,
        hasSignature: !!webhookSecret,
        queue: 'enhanced'
      }, 'Webhook delivered successfully via enhanced queue');

      // Update email status to DELIVERED on success
      await this.updateEmailDeliveryStatus(messageId, 'DELIVERED', null);
      
      // Add breadcrumb for successful delivery
      Sentry.addBreadcrumb({
        category: 'webhook',
        message: 'Webhook delivered successfully (enhanced queue)',
        level: 'info',
        data: {
          httpStatus: response.status,
          messageId
        }
      });

      return { status: 'delivered', httpStatus: response.status };
    } catch (error: any) {
      const messageId = this.getMessageId(payload);
      const httpStatus = error?.response?.status;
      
      // Use the helper function to determine if this error is retryable
      const isRetryable = !httpStatus || isRetryableHttpStatus(httpStatus);
      
      logger.error({
        error: error?.message,
        webhookUrl,
        messageId,
        httpStatus,
        isRetryable,
        attemptsMade: job.attemptsMade,
        maxAttempts: job.opts.attempts,
        queue: 'enhanced'
      }, 'Webhook delivery failed in enhanced queue');
      
      // Add breadcrumb for failed delivery
      Sentry.addBreadcrumb({
        category: 'webhook',
        message: `Delivery failed (enhanced): ${error?.message}`,
        level: 'error',
        data: {
          webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...'),
          httpStatus,
          isRetryable,
          attempt: job.attemptsMade + 1,
          maxAttempts: job.opts.attempts
        }
      });
      
      // Capture error in Sentry with enhanced context
      captureWebhookError(error, {
        feature: 'delivery-enhanced',
        messageId,
        webhookUrl: webhookUrl.replace(/^(https?:\/\/[^\/]+).*/, '$1/...'),
        attemptsMade: job.attemptsMade,
        jobId: job.id,
        errorCode: error?.code,
        httpStatus,
        isRetryable,
        queue: 'enhanced'
      });

      // Update email status based on whether this is the final attempt or non-retryable
      const maxAttempts = job.opts.attempts || 1;
      const isLastAttempt = (job.attemptsMade + 1) >= maxAttempts;
      const shouldFailImmediately = !isRetryable;

      logger.debug({
        messageId,
        attemptsMade: job.attemptsMade,
        currentAttemptNumber: job.attemptsMade + 1,
        maxAttempts,
        isLastAttempt,
        httpStatus,
        isRetryable,
        shouldFailImmediately,
        jobId: job.id,
        queue: 'enhanced'
      }, 'Enhanced queue webhook job status');

      // For non-retryable errors, fail immediately regardless of attempts remaining
      if (shouldFailImmediately) {
        const status = httpStatus === 413 ? 'REJECTED' : 'FAILED';
        const errorMsg = httpStatus 
          ? formatHttpError(httpStatus, false)
          : error?.message || 'Unknown error';
        
        await this.updateEmailDeliveryStatus(messageId, status as any, errorMsg);
        
        // Don't throw the error to prevent Bull from retrying
        return { status: 'failed', httpStatus, error: errorMsg };
      }

      if (isLastAttempt) {
        await this.updateEmailDeliveryStatus(messageId, 'FAILED', error?.message || 'Unknown error');
      } else {
        await this.updateEmailDeliveryStatus(messageId, 'RETRYING', error?.message || 'Unknown error');
      }

      throw error; // This will trigger retry logic for retryable errors
    }
  }

  // Helper method to extract messageId from various payload formats
  private getMessageId(payload: InternalQueuePayload): string {
    // Check if it's a webhook verification payload (not an email)
    if ('type' in payload && payload.type === 'webhook_verification') {
      return 'webhook_verification'; // Special identifier for verification payloads
    }

    // Check for preserved internal messageId (used when envelope is filtered out)
    if ('_internalMessageId' in payload && payload._internalMessageId) {
      return payload._internalMessageId;
    }

    // Check if it's the new enhanced structure
    if ('envelope' in payload && payload.envelope?.messageId) {
      return payload.envelope.messageId;
    }

    // Check if it's the old structure
    if ('messageId' in payload && payload.messageId) {
      return payload.messageId;
    }

    return 'unknown';
  }

  // Update email delivery attempt count and timestamp
  private async updateEmailDeliveryAttempt(messageId: string) {
    // Skip update if messageId is unknown/invalid or for webhook verification
    if (!messageId || messageId === 'unknown' || messageId === 'webhook_verification') {
      logger.debug({ messageId }, 'Skipping delivery attempt update for non-email payload');
      return;
    }

    try {
      await prisma.email.update({
        where: { messageId },
        data: {
          deliveryAttempts: { increment: 1 },
          lastAttemptAt: new Date(),
        },
      });
    } catch (error: any) {
      logger.error({ messageId, error: error.message }, 'Failed to update email delivery attempt');
    }
  }

  // Update email delivery status and related fields
  private async updateEmailDeliveryStatus(
    messageId: string,
    status: 'DELIVERED' | 'FAILED' | 'RETRYING' | 'REJECTED',
    errorMessage: string | null
  ) {
    // Skip update if messageId is unknown/invalid or for webhook verification
    if (!messageId || messageId === 'unknown' || messageId === 'webhook_verification') {
      logger.debug({ messageId, status }, 'Skipping delivery status update for non-email payload');
      return;
    }

    try {
      const updateData: any = {
        deliveryStatus: status,
        lastAttemptAt: new Date(),
      };

      // Set deliveredAt timestamp only on successful delivery
      if (status === 'DELIVERED') {
        updateData.deliveredAt = new Date();
        updateData.errorMessage = null; // Clear any previous error
      } else if (errorMessage) {
        updateData.errorMessage = errorMessage;
      }

      await prisma.email.update({
        where: { messageId },
        data: updateData,
      });

      logger.debug({ 
        messageId, 
        status, 
        errorMessage,
        queue: 'enhanced' 
      }, 'Email delivery status updated');
    } catch (error: any) {
      // Don't fail the job if we can't update the email record
      logger.error({ 
        messageId, 
        status, 
        error: error.message 
      }, 'Failed to update email delivery status');
    }
  }

  private async processAttachmentJob(job: Bull.Job<AttachmentUploadJobData>): Promise<any> {
    // Implementation similar to original queue.ts
    const { fileId, attachment, messageId, userId, folder, s3Config } = job.data;
    
    // Add attachment processing logic here
    // TODO: Copy implementation from queue.ts when ready
    
    return { status: 'uploaded', fileId };
  }

  private async sendToDeadLetterQueue(originalQueue: string, originalJobData: any, failureReason: string) {
    try {
      await this.deadLetterQueue.add('dead-letter', {
        originalQueue,
        originalJobData,
        failureReason,
        failedAt: new Date(),
        attempts: originalJobData.attempts || 0,
      });
      
      logger.warn({ 
        originalQueue, 
        failureReason 
      }, 'Job sent to dead letter queue');
    } catch (error) {
      logger.error({ error }, 'Failed to send job to dead letter queue');
    }
  }

  // Redis-based rate limiting (replaces memory-based)
  async checkRateLimit(key: string, limit: number, windowMs: number): Promise<boolean> {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Use Redis sorted set for rate limiting
    const pipeline = this.redisClient.pipeline();
    
    // Remove old entries
    pipeline.zremrangebyscore(`rate_limit:${key}`, 0, windowStart);
    
    // Count current entries
    pipeline.zcard(`rate_limit:${key}`);
    
    // Add current request
    pipeline.zadd(`rate_limit:${key}`, now, `${now}-${Math.random()}`);
    
    // Set expiry
    pipeline.expire(`rate_limit:${key}`, Math.ceil(windowMs / 1000));
    
    const results = await pipeline.exec();
    const currentCount = results[1][1] as number;
    
    return currentCount < limit;
  }

  // Fallback webhook delivery (direct HTTP without queue)
  async deliverWebhookDirectly(
    webhookUrl: string, 
    payload: InternalQueuePayload, 
    webhookSecret?: string,
    customHeaders?: Record<string, string>
  ): Promise<boolean> {
    try {
      logger.warn({ webhookUrl }, 'Using fallback direct webhook delivery (queue unavailable)');
      
      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'EmailConnect-EU/1.0',
        ...customHeaders,
      };

      if (webhookSecret) {
        // Add webhook signature
        const crypto = await import('crypto');
        const signature = crypto
          .createHmac('sha256', webhookSecret)
          .update(JSON.stringify(payload))
          .digest('hex');
        headers['X-Webhook-Signature'] = `sha256=${signature}`;
      }

      const response = await axios.post(webhookUrl, payload, {
        headers,
        timeout: env.WEBHOOK_TIMEOUT_MS,
        maxRedirects: 3,
      });

      logger.info({ 
        webhookUrl, 
        statusCode: response.status 
      }, 'Fallback webhook delivery successful');
      
      return true;
    } catch (error) {
      logger.error({ 
        webhookUrl, 
        error: error.message 
      }, 'Fallback webhook delivery failed');
      
      return false;
    }
  }

  // Queue health monitoring
  async getQueueHealth(): Promise<{
    redis: boolean;
    queues: Record<string, any>;
    deadLetterCount: number;
  }> {
    try {
      const redisHealth = await this.redisClient.ping() === 'PONG';
      
      const [webhookCounts, attachmentCounts, deadLetterCounts] = await Promise.all([
        this.webhookQueue.getJobCounts(),
        this.attachmentUploadQueue.getJobCounts(),
        this.deadLetterQueue.getJobCounts(),
      ]);

      return {
        redis: redisHealth,
        queues: {
          webhook: webhookCounts,
          attachment: attachmentCounts,
          deadLetter: deadLetterCounts,
        },
        deadLetterCount: deadLetterCounts.waiting + deadLetterCounts.active,
      };
    } catch (error) {
      logger.error({ error }, 'Failed to get queue health');
      return {
        redis: false,
        queues: {},
        deadLetterCount: -1,
      };
    }
  }

  // Public methods for queue operations
  async queueWebhookDelivery(
    webhookUrl: string,
    payload: InternalQueuePayload,
    webhookSecret?: string,
    customHeaders?: Record<string, string>
  ): Promise<string> {
    if (!this.initialized || !this.webhookQueue) {
      // Use fallback delivery if queue is not available
      const success = await this.deliverWebhookDirectly(webhookUrl, payload, webhookSecret, customHeaders);
      return success ? 'fallback-delivered' : 'fallback-failed';
    }

    const job = await this.webhookQueue.add('webhook-delivery', {
      webhookUrl,
      payload,
      webhookSecret,
      customHeaders,
    });

    return job.id.toString();
  }

  async queueAttachmentUpload(
    fileId: string,
    attachment: EmailAttachment,
    messageId: string,
    userId: string,
    folder?: string,
    s3Config?: any
  ): Promise<string> {
    if (!this.initialized || !this.attachmentUploadQueue) {
      throw new Error('Attachment upload queue not available');
    }

    const job = await this.attachmentUploadQueue.add('upload', {
      fileId,
      attachment,
      messageId,
      userId,
      folder,
      s3Config,
    });

    return job.id.toString();
  }

  async shutdown() {
    if (!this.initialized) return;

    logger.info('Shutting down enhanced queue service...');
    
    await Promise.all([
      this.webhookQueue?.close(),
      this.attachmentUploadQueue?.close(),
      this.deadLetterQueue?.close(),
    ]);

    try {
      await this.redisClient?.quit();
    } catch (error) {
      logger.warn({ error }, 'Error closing Redis connection');
    }

    this.initialized = false;
    logger.info('Enhanced queue service shut down');
  }
}

// Export singleton instance
export const enhancedQueueService = new EnhancedQueueService();

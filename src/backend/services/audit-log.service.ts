import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';
import { FastifyRequest } from 'fastify';

export type AuditAction = 
  | 'auth.login'
  | 'auth.logout'
  | 'auth.failed_login'
  | 'auth.password_reset'
  | 'auth.mfa_enabled'
  | 'auth.mfa_disabled'
  | 'api_key.created'
  | 'api_key.deleted'
  | 'api_key.used'
  | 'webhook.created'
  | 'webhook.updated'
  | 'webhook.deleted'
  | 'webhook.tested'
  | 'domain.created'
  | 'domain.verified'
  | 'domain.deleted'
  | 'alias.created'
  | 'alias.updated'
  | 'alias.deleted'
  | 'email.accessed'
  | 'email.deleted'
  | 'attachment.uploaded'
  | 'attachment.downloaded'
  | 'attachment.deleted'
  | 'settings.updated'
  | 'settings.s3_configured'
  | 'settings.s3_removed'
  | 'subscription.created'
  | 'subscription.started'
  | 'subscription.cancelled'
  | 'subscription.prolonged'
  | 'subscription.expired'
  | 'payment.processed'
  | 'payment.failed'
  | 'data.exported'
  | 'data.retention_cleanup'
  | 'security.suspicious_activity'
  | 'security.rate_limit_exceeded'
  | 'security.unauthorized_access'
  | 'user.created'
  | 'user.deleted'
  | 'user.sessions_revoked';

export interface AuditLogEntry {
  userId?: string;
  action: AuditAction;
  resourceType?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  apiKeyId?: string;
  success?: boolean;
  errorMessage?: string;
}

export class AuditLogService {
  private static instance: AuditLogService;
  private readonly LOG_RETENTION_DAYS = 90;

  private constructor() {}

  static getInstance(): AuditLogService {
    if (!AuditLogService.instance) {
      AuditLogService.instance = new AuditLogService();
    }
    return AuditLogService.instance;
  }

  /**
   * Log an audit event
   */
  async log(entry: AuditLogEntry): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + this.LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000);

      await prisma.auditLog.create({
        data: {
          action: entry.action,
          resourceType: entry.resourceType,
          resourceId: entry.resourceId,
          metadata: {
            ...entry.metadata,
            userId: entry.userId,
            success: entry.success ?? true,
            errorMessage: entry.errorMessage,
            sessionId: entry.sessionId,
            apiKeyId: entry.apiKeyId,
            timestamp: new Date().toISOString()
          },
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          expiresAt
        }
      });

      // Also log to application logger for real-time monitoring
      logger.info({
        audit: true,
        action: entry.action,
        userId: entry.userId,
        resourceType: entry.resourceType,
        resourceId: entry.resourceId,
        success: entry.success ?? true
      }, `Audit: ${entry.action}`);

    } catch (error: any) {
      logger.error({
        error: error.message,
        auditEntry: entry
      }, 'Failed to create audit log entry');
    }
  }

  /**
   * Log authentication event
   */
  async logAuth(
    action: 'login' | 'logout' | 'failed_login' | 'password_reset',
    userId: string | undefined,
    request: FastifyRequest,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      userId,
      action: `auth.${action}`,
      resourceType: 'user',
      resourceId: userId,
      ipAddress: this.getClientIp(request),
      userAgent: request.headers['user-agent'] as string,
      metadata: {
        ...metadata,
        method: request.method,
        path: request.url
      },
      success: action !== 'failed_login'
    });
  }

  /**
   * Log API key usage
   */
  async logApiKeyUsage(
    apiKeyId: string,
    userId: string,
    request: FastifyRequest,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      userId,
      action: 'api_key.used',
      resourceType: 'api_key',
      resourceId: apiKeyId,
      ipAddress: this.getClientIp(request),
      userAgent: request.headers['user-agent'] as string,
      apiKeyId,
      success,
      errorMessage,
      metadata: {
        endpoint: request.url,
        method: request.method
      }
    });
  }

  /**
   * Log data access
   */
  async logDataAccess(
    userId: string,
    resourceType: 'email' | 'attachment' | 'webhook' | 'domain' | 'alias' | 'audit_log',
    resourceId: string,
    action: 'read' | 'write' | 'delete',
    request?: FastifyRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: `${resourceType}.${action === 'read' ? 'accessed' : action === 'write' ? 'updated' : 'deleted'}` as AuditAction,
      resourceType,
      resourceId,
      ipAddress: request ? this.getClientIp(request) : undefined,
      userAgent: request?.headers['user-agent'] as string,
      metadata: {
        operation: action
      }
    });
  }

  /**
   * Log security events
   */
  async logSecurityEvent(
    event: 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access',
    request: FastifyRequest,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      action: `security.${event}` as AuditAction,
      resourceType: 'security',
      ipAddress: this.getClientIp(request),
      userAgent: request.headers['user-agent'] as string,
      success: false,
      metadata: {
        ...metadata,
        url: request.url,
        method: request.method,
        headers: this.sanitizeHeaders(request.headers)
      }
    });
  }

  /**
   * Get audit logs for a user
   */
  async getUserAuditLogs(
    userId: string,
    options?: {
      startDate?: Date;
      endDate?: Date;
      actions?: string[]; // supports exact or prefix patterns ending with '*'
      categories?: string[]; // e.g., ['security', 'auth'] maps to action prefix 'security.'
      success?: boolean;
      resourceType?: string;
      apiKeySuffix?: string;
      ipContains?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<{ logs: any[]; total: number }> {
    const where: any = {
      metadata: {
        path: ['userId'],
        equals: userId
      }
    };

    // Date range
    if (options?.startDate || options?.endDate) {
      where.createdAt = {};
      if (options.startDate) where.createdAt.gte = options.startDate;
      if (options.endDate) where.createdAt.lte = options.endDate;
    }

    // Success flag
    if (typeof options?.success === 'boolean') {
      where.AND = where.AND || [];
      where.AND.push({
        metadata: {
          path: ['success'],
          equals: options.success
        }
      });
    }

    // Resource type
    if (options?.resourceType) {
      where.resourceType = options.resourceType;
    }

    // IP contains
    if (options?.ipContains) {
      where.ipAddress = { contains: options.ipContains } as any;
    }

    // API key suffix
    if (options?.apiKeySuffix) {
      where.AND = where.AND || [];
      where.AND.push({
        metadata: {
          path: ['apiKeyId'],
          string_contains: options.apiKeySuffix // Prisma JSON string contains
        } as any
      });
    }

    // Categories -> action prefixes like 'security.'
    const actionOr: any[] = [];
    if (options?.categories && options.categories.length > 0) {
      for (const cat of options.categories) {
        actionOr.push({ action: { startsWith: `${cat}.` } });
      }
    }

    // Actions (exact or prefix if endsWith '*')
    if (options?.actions && options.actions.length > 0) {
      for (const act of options.actions) {
        if (act.endsWith('*')) {
          actionOr.push({ action: { startsWith: act.slice(0, -1) } });
        } else {
          actionOr.push({ action: act });
        }
      }
    }

    if (actionOr.length > 0) {
      where.AND = where.AND || [];
      where.AND.push({ OR: actionOr });
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: options?.limit || 100,
        skip: options?.offset || 0
      }),
      prisma.auditLog.count({ where })
    ]);

    return { logs, total };
  }

  /**
   * Admin: Get audit logs across all users with filters
   */
  async getAuditLogsAdmin(
    options?: {
      startDate?: Date;
      endDate?: Date;
      actions?: string[]; // supports exact or prefix patterns ending with '*'
      categories?: string[]; // e.g., ['security', 'auth'] maps to action prefix 'security.'
      success?: boolean;
      resourceType?: string;
      apiKeySuffix?: string;
      ipContains?: string;
      userId?: string; // restrict to a specific user when provided
      limit?: number;
      offset?: number;
    }
  ): Promise<{ logs: any[]; total: number }> {
    const where: any = {};

    // Date range
    if (options?.startDate || options?.endDate) {
      where.createdAt = {};
      if (options.startDate) where.createdAt.gte = options.startDate;
      if (options.endDate) where.createdAt.lte = options.endDate;
    }

    // Optional userId scope
    if (options?.userId) {
      where.metadata = {
        path: ['userId'],
        equals: options.userId
      };
    }

    // Success flag
    if (typeof options?.success === 'boolean') {
      where.AND = where.AND || [];
      where.AND.push({
        metadata: {
          path: ['success'],
          equals: options.success
        }
      });
    }

    // Resource type
    if (options?.resourceType) {
      where.resourceType = options.resourceType;
    }

    // IP contains
    if (options?.ipContains) {
      where.ipAddress = { contains: options.ipContains } as any;
    }

    // API key suffix
    if (options?.apiKeySuffix) {
      where.AND = where.AND || [];
      where.AND.push({
        metadata: {
          path: ['apiKeyId'],
          string_contains: options.apiKeySuffix
        } as any
      });
    }

    // Categories and actions
    const actionOr: any[] = [];
    if (options?.categories && options.categories.length > 0) {
      for (const cat of options.categories) {
        actionOr.push({ action: { startsWith: `${cat}.` } });
      }
    }

    if (options?.actions && options.actions.length > 0) {
      for (const act of options.actions) {
        if (act.endsWith('*')) {
          actionOr.push({ action: { startsWith: act.slice(0, -1) } });
        } else {
          actionOr.push({ action: act });
        }
      }
    }

    if (actionOr.length > 0) {
      where.AND = where.AND || [];
      where.AND.push({ OR: actionOr });
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: options?.limit || 100,
        skip: options?.offset || 0
      }),
      prisma.auditLog.count({ where })
    ]);

    return { logs, total };
  }

  /**
   * Export audit logs for compliance
   */
  async exportAuditLogs(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    const { logs } = await this.getUserAuditLogs(userId, {
      startDate,
      endDate,
      limit: 10000 // Maximum export size
    });

    // Format for export
    return logs.map(log => ({
      id: log.id,
      timestamp: log.createdAt,
      action: log.action,
      resourceType: log.resourceType,
      resourceId: log.resourceId,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      success: log.metadata?.success ?? true,
      metadata: log.metadata
    }));
  }

  /**
   * Clean up expired audit logs
   */
  async cleanupExpiredLogs(): Promise<void> {
    try {
      const result = await prisma.auditLog.deleteMany({
        where: {
          expiresAt: {
            lte: new Date()
          }
        }
      });

      if (result.count > 0) {
        logger.info({ count: result.count }, 'Cleaned up expired audit logs');
      }
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to cleanup expired audit logs');
    }
  }

  /**
   * Get client IP address from request
   */
  private getClientIp(request: FastifyRequest): string {
    // Check for proxy headers
    const forwardedFor = request.headers['x-forwarded-for'] as string;
    if (forwardedFor) {
      return forwardedFor.split(',')[0].trim();
    }

    const realIp = request.headers['x-real-ip'] as string;
    if (realIp) {
      return realIp;
    }

    return request.ip;
  }

  /**
   * Sanitize headers for logging (remove sensitive data)
   */
  private sanitizeHeaders(headers: any): Record<string, string> {
    const sanitized: Record<string, string> = {};
    const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie', 'x-webhook-signature'];

    for (const [key, value] of Object.entries(headers)) {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = String(value);
      }
    }

    return sanitized;
  }
}

// Export singleton instance
export const auditLogService = AuditLogService.getInstance();

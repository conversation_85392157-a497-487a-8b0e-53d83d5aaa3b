import { FastifyInstance } from 'fastify';
import { Server as SocketIOServer } from 'socket.io';
import { logger } from '../utils/logger.js';

interface AuthenticatedSocket {
  userId: string;
  email: string;
}

interface EmailProcessedEvent {
  type: 'email_processed';
  data: {
    messageId: string;
    fromAddress: string;
    subject: string;
    isTestWebhook: boolean;
    deliveryStatus: 'DELIVERED' | 'PENDING' | 'FAILED';
    webhookPayload?: any;
    timestamp: string;
  };
}

interface MetricsUpdatedEvent {
  type: 'metrics_updated';
  data: {
    domains: number;
    aliases: number;
    webhooks: number;
    emails: number;
  };
}

interface DomainVerificationEvent {
  type: 'domain_verification_updated';
  data: {
    domainId: string;
    domain: string;
    verified: boolean;
    verificationStatus: 'PENDING' | 'VERIFIED' | 'ACTIVE' | 'WARNING' | 'SUSPENDED' | 'FAILED'
    verificationFailureCount: number;
    nextVerificationCheck?: string;
    error?: string;
    timestamp: string;
  };
}

interface NotificationEvent {
  type: 'notification_created';
  data: {
    id: string;
    type: string;
    title: string;
    message?: string;  // Make message optional since toasts only need title
    category: string;
    priority: string;
    actionUrl?: string;
    actionText?: string;
    timestamp: string;
  };
}

type WebSocketEvent = EmailProcessedEvent | MetricsUpdatedEvent | DomainVerificationEvent | NotificationEvent;

class WebSocketService {
  private io: SocketIOServer | null = null;
  private userSockets = new Map<string, Set<string>>(); // userId -> Set of socketIds

  constructor() {
    // No longer need UserAuthService instance
  }

  initialize(fastify: FastifyInstance) {
    // Configure CORS origins based on environment
    const allowedOrigins = process.env.NODE_ENV === 'development'
      ? ["http://localhost:3000", "https://hardy-content-pig.ngrok-free.app"]
      : process.env.ALLOWED_ORIGINS
        ? process.env.ALLOWED_ORIGINS.split(',')
        : ["https://emailconnect.eu"];

    this.io = new SocketIOServer(fastify.server, {
      cors: {
        origin: allowedOrigins,
        credentials: true
      },
      transports: ['websocket', 'polling'],
      // Add production-specific configuration
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 30000,
      allowEIO3: true
    });

    this.io.use(async (socket, next) => {
      try {
        logger.debug(`WebSocket auth: Attempting authentication for socket ${socket.id}`);
        
        // Try BetterAuth session authentication first
        try {
          const { auth } = await import('../lib/better-auth.js');
          
          // Create headers object from socket handshake
          const headers = {
            cookie: socket.handshake.headers.cookie || '',
            ...socket.handshake.headers
          };
          
          logger.debug(`WebSocket auth: Checking BetterAuth session with cookies: ${headers.cookie}`);
          
          const session = await auth.api.getSession({ headers: headers as any });
          
          if (session) {
            logger.debug(`WebSocket auth: BetterAuth session found for user ${session.user.email}`);
            
            // Get full user data from our User table
            const { prisma } = await import('../lib/prisma.js');
            const user = await prisma.user.findUnique({
              where: { id: session.user.id },
              select: { id: true, email: true, role: true, planType: true }
            });
            
            if (user) {
              // Attach user info to socket
              (socket as any).user = {
                userId: user.id,
                email: user.email
              };
              
              logger.info(`WebSocket auth: Successfully authenticated user ${user.email} via BetterAuth`);
              return next();
            } else {
              logger.warn(`WebSocket auth: BetterAuth session user ${session.user.id} not found in User table`);
            }
          } else {
            logger.debug('WebSocket auth: No BetterAuth session found');
          }
        } catch (betterAuthError) {
          logger.debug({ error: betterAuthError }, 'WebSocket auth: BetterAuth session check failed');
        }

        // If we reach here without a BetterAuth session, deny
        logger.warn('WebSocket auth: No authentication found - BetterAuth session required');
        return next(new Error('Authentication required'));
      } catch (error) {
        logger.error({ error: error.message }, 'WebSocket authentication failed');
        next(new Error('Authentication failed'));
      }
    });

    this.io.on('connection', (socket) => {
      const user = (socket as any).user as AuthenticatedSocket;
      
      logger.info({ 
        userId: user.userId, 
        socketId: socket.id 
      }, 'User connected to WebSocket');

      // Track user socket
      if (!this.userSockets.has(user.userId)) {
        this.userSockets.set(user.userId, new Set());
      }
      this.userSockets.get(user.userId)!.add(socket.id);

      // Join user-specific room
      socket.join(`user:${user.userId}`);

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info({ 
          userId: user.userId, 
          socketId: socket.id 
        }, 'User disconnected from WebSocket');

        // Remove socket from tracking
        const userSocketSet = this.userSockets.get(user.userId);
        if (userSocketSet) {
          userSocketSet.delete(socket.id);
          if (userSocketSet.size === 0) {
            this.userSockets.delete(user.userId);
          }
        }
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });

    logger.info('WebSocket service initialized');
  }

  /**
   * Emit event to specific user
   */
  emitToUser(userId: string, event: WebSocketEvent) {
    if (!this.io) {
      // In tests, WebSocket might not be initialized - that's OK
      if (process.env.NODE_ENV !== 'test') {
        logger.warn('WebSocket service not initialized');
      }
      return;
    }

    this.io.to(`user:${userId}`).emit(event.type, event.data);
    
    logger.debug({ 
      userId, 
      eventType: event.type,
      connectedSockets: this.userSockets.get(userId)?.size || 0
    }, 'Event emitted to user');
  }

  /**
   * Emit email processed event
   */
  emitEmailProcessed(userId: string, emailData: EmailProcessedEvent['data']) {
    this.emitToUser(userId, {
      type: 'email_processed',
      data: emailData
    });
  }

  /**
   * Emit metrics updated event
   */
  emitMetricsUpdated(userId: string, metrics: MetricsUpdatedEvent['data']) {
    this.emitToUser(userId, {
      type: 'metrics_updated',
      data: metrics
    });
  }

  /**
   * Emit domain verification updated event
   */
  emitDomainVerificationUpdated(userId: string, verificationData: DomainVerificationEvent['data']) {
    this.emitToUser(userId, {
      type: 'domain_verification_updated',
      data: verificationData
    });
  }

  /**
   * Emit notification created event
   */
  emitNotificationCreated(userId: string, notificationData: NotificationEvent['data']) {
    this.emitToUser(userId, {
      type: 'notification_created',
      data: notificationData
    });
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.userSockets.size;
  }

  /**
   * Get user connection status
   */
  isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId) && this.userSockets.get(userId)!.size > 0;
  }

  /**
   * Broadcast to all connected users (admin use)
   */
  broadcast(event: WebSocketEvent) {
    if (!this.io) {
      // In tests, WebSocket might not be initialized - that's OK
      if (process.env.NODE_ENV !== 'test') {
        logger.warn('WebSocket service not initialized');
      }
      return;
    }

    this.io.emit(event.type, event.data);
    logger.debug({ eventType: event.type }, 'Event broadcasted to all users');
  }
}

export const webSocketService = new WebSocketService();

import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';
import { PlanConfigService } from './billing/plan-config.service.js';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * PostfixSyncService handles synchronization between the main application
 * and PostgreSQL postfix tables for virtual domain and alias management.
 * 
 * This service replaces the Go postfix-manager service with direct
 * PostgreSQL integration from the Node.js application.
 */
export class PostfixSyncService {
  
  /**
   * Sync a domain to Postfix PostgreSQL tables
   * This creates/updates both the domain and its catch-all alias
   */
  async syncDomainToPostfix(domain: string, userId: string, active: boolean = true): Promise<void> {
    try {
      logger.info({ domain, userId, active }, 'Syncing domain to Postfix PostgreSQL tables');

      // Get user's plan type to determine spam filtering
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { planType: true }
      });

      if (!user) {
        throw new Error(`User ${userId} not found`);
      }

      // Determine spam filtering based on plan type
      const spamFiltering = PlanConfigService.userHasPermission(user.planType, 'spam_filtering');
      
      // Special handling for system domain - never uses advanced processing
      let advancedProcessing = false;
      let hasStorageAliases = false;
      
      if (domain === 'user.emailconnect.eu') {
        // System domain always uses basic processing (no S3 storage allowed)
        advancedProcessing = false;
        logger.debug({ 
          domain,
          reason: 'system_domain_restriction'
        }, 'System domain forced to basic processing');
      } else {
        // Check if any aliases require storage (for advanced_processing computation)
        const domainRecord = await prisma.domain.findUnique({
          where: { domain },
          include: {
            aliases: {
              where: { active: true },
              select: { configuration: true }
            }
          }
        });
        
        hasStorageAliases = domainRecord?.aliases.some(alias => {
          const config = alias.configuration as any;
          return config?.allowAttachments === true && config?.attachmentHandling === 'storage';
        }) || false;
        
        // Compute advanced_processing: true if spam filtering OR storage aliases
        advancedProcessing = spamFiltering || hasStorageAliases;
      }
      
      logger.debug({ 
        domain, 
        planType: user.planType, 
        spamFiltering,
        hasStorageAliases,
        advancedProcessing
      }, 'Determined routing settings for domain');

      // Upsert domain in postfix_virtual_domains (dual-write strategy)
      await prisma.postfixVirtualDomain.upsert({
        where: { domain },
        create: {
          domain,
          active,
          spamFiltering,
          advancedProcessing, // New field for routing
        },
        update: {
          active,
          spamFiltering,
          advancedProcessing, // Update both fields
          updatedAt: new Date(),
        },
      });

      // Create/update catch-all alias for the domain
      const catchAllEmail = `@${domain}`;
      await prisma.postfixVirtualAlias.upsert({
        where: { email: catchAllEmail },
        create: {
          email: catchAllEmail,
          domain,
          active,
        },
        update: {
          active,
          updatedAt: new Date(),
        },
      });

      logger.info({ 
        domain, 
        spamFiltering, 
        active,
        catchAllEmail 
      }, 'Successfully synced domain to Postfix PostgreSQL');

      // Reload Postfix configuration (async, don't wait for completion)
      this.reloadPostfixAsync();

    } catch (error) {
      logger.error({ 
        domain, 
        userId, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to sync domain to Postfix');
      throw error;
    }
  }

  /**
   * Update spam filtering setting for a domain
   * This updates the routing decision in postfix_virtual_domains
   * Also recomputes advanced_processing based on spam filtering and storage aliases
   */
  async updateDomainSpamFiltering(domain: string, enabled: boolean): Promise<void> {
    try {
      logger.info({ domain, enabled }, 'Updating domain spam filtering in Postfix PostgreSQL');

      // Special handling for system domain - never uses advanced processing
      let advancedProcessing = false;
      let hasStorageAliases = false;
      
      if (domain === 'user.emailconnect.eu') {
        // System domain always uses basic processing (no S3 storage allowed)
        advancedProcessing = false;
        logger.debug({ 
          domain,
          reason: 'system_domain_restriction'
        }, 'System domain forced to basic processing');
      } else {
        // Check if any aliases require storage for advanced_processing computation
        const domainRecord = await prisma.domain.findUnique({
          where: { domain },
          include: {
            aliases: {
              where: { active: true },
              select: { configuration: true }
            }
          }
        });
        
        hasStorageAliases = domainRecord?.aliases.some(alias => {
          const config = alias.configuration as any;
          return config?.allowAttachments === true && config?.attachmentHandling === 'storage';
        }) || false;
        
        // Compute advanced_processing: true if spam filtering OR storage aliases
        advancedProcessing = enabled || hasStorageAliases;
      }
      
      // Update the domain's spam filtering setting and advanced_processing (dual-write)
      const updated = await prisma.postfixVirtualDomain.update({
        where: { domain },
        data: {
          spamFiltering: enabled,
          advancedProcessing,
          updatedAt: new Date(),
        },
      });

      logger.info({ 
        domain, 
        enabled,
        hasStorageAliases,
        advancedProcessing,
        previousSpamFiltering: !enabled 
      }, 'Updated domain spam filtering and routing in Postfix PostgreSQL');

      // Reload Postfix configuration (async, don't wait for completion)
      this.reloadPostfixAsync();

    } catch (error) {
      logger.error({ 
        domain, 
        enabled, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to update domain spam filtering');
      throw error;
    }
  }

  /**
   * Remove a domain from Postfix PostgreSQL tables
   * This removes both the domain and all its aliases
   */
  async removeDomainFromPostfix(domain: string): Promise<void> {
    try {
      logger.info({ domain }, 'Removing domain from Postfix PostgreSQL');

      // Delete domain (cascades to aliases due to foreign key)
      await prisma.postfixVirtualDomain.delete({
        where: { domain },
      });

      logger.info({ domain }, 'Successfully removed domain from Postfix PostgreSQL');

      // Reload Postfix configuration (async, don't wait for completion)
      this.reloadPostfixAsync();

    } catch (error) {
      logger.error({ 
        domain, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to remove domain from Postfix');
      throw error;
    }
  }

  /**
   * Sync a specific alias to Postfix PostgreSQL tables
   * This is used for non-catch-all aliases
   */
  async syncAliasToPostfix(email: string, domain: string, active: boolean = true): Promise<void> {
    try {
      logger.info({ email, domain, active }, 'Syncing alias to Postfix PostgreSQL');

      // Ensure the domain exists in postfix_virtual_domains
      const domainExists = await prisma.postfixVirtualDomain.findUnique({
        where: { domain },
      });

      if (!domainExists) {
        throw new Error(`Domain ${domain} not found in Postfix PostgreSQL tables`);
      }

      // Upsert alias
      await prisma.postfixVirtualAlias.upsert({
        where: { email },
        create: {
          email,
          domain,
          active,
        },
        update: {
          active,
          updatedAt: new Date(),
        },
      });

      logger.info({ email, domain, active }, 'Successfully synced alias to Postfix PostgreSQL');

      // Reload Postfix configuration (async, don't wait for completion)
      this.reloadPostfixAsync();

    } catch (error) {
      logger.error({ 
        email, 
        domain, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to sync alias to Postfix');
      throw error;
    }
  }

  /**
   * Remove an alias from Postfix PostgreSQL tables
   */
  async removeAliasFromPostfix(email: string): Promise<void> {
    try {
      logger.info({ email }, 'Removing alias from Postfix PostgreSQL');

      await prisma.postfixVirtualAlias.delete({
        where: { email },
      });

      logger.info({ email }, 'Successfully removed alias from Postfix PostgreSQL');

      // Reload Postfix configuration (async, don't wait for completion)
      this.reloadPostfixAsync();

    } catch (error) {
      logger.error({ 
        email, 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to remove alias from Postfix');
      throw error;
    }
  }

  /**
   * Get all configured domains from Postfix PostgreSQL
   * This replaces the equivalent method from the Go service
   */
  async getConfiguredDomains(): Promise<string[]> {
    try {
      const domains = await prisma.postfixVirtualDomain.findMany({
        where: { active: true },
        select: { domain: true },
      });

      return domains.map(d => d.domain);
    } catch (error) {
      logger.error({ 
        error: error instanceof Error ? error.message : error 
      }, 'Failed to get configured domains from Postfix PostgreSQL');
      throw error;
    }
  }

  /**
   * Reload Postfix configuration (async, fire-and-forget)
   * This triggers Postfix to reload its configuration without waiting for completion
   * Use this for better API response times
   */
  private reloadPostfixAsync(): void {
    this.reloadPostfix().catch(error => {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'Async Postfix reload failed');
    });
  }

  /**
   * Reload Postfix configuration
   * This triggers Postfix to reload its configuration and pick up changes
   * Uses file-based signaling to communicate with host Postfix service
   */
  private async reloadPostfix(): Promise<void> {
    try {
      logger.debug('Requesting Postfix configuration reload');

      // Primary method: File-based signaling for containerized environments
      try {
        await execAsync('touch /tmp/postfix-reload-requested');
        logger.debug('Created Postfix reload signal file');

        // Wait briefly and check for success/error feedback
        await this.waitForReloadFeedback();
        return;
      } catch (signalError) {
        logger.warn({
          error: signalError instanceof Error ? signalError.message : signalError
        }, 'Failed to create Postfix reload signal file');
      }

      // Fallback: Direct systemctl call (only works if container has host access)
      try {
        await execAsync('systemctl reload postfix');
        logger.debug('Postfix reloaded successfully via systemctl');
        return;
      } catch (systemctlError) {
        logger.warn({
          error: systemctlError instanceof Error ? systemctlError.message : systemctlError
        }, 'Failed to reload Postfix via systemctl');
      }

      // If all methods fail, log warning but don't throw
      logger.warn('Unable to reload Postfix configuration - manual reload may be required');

    } catch (error) {
      logger.error({
        error: error instanceof Error ? error.message : error
      }, 'Failed to reload Postfix configuration');
      // Don't throw here - the database updates succeeded
    }
  }

  /**
   * Wait for feedback from the host Postfix reload watcher
   * Checks for success/error indicator files
   */
  private async waitForReloadFeedback(timeoutMs: number = 3000): Promise<void> {
    const startTime = Date.now();
    const checkInterval = 500; // Check every 500ms

    while (Date.now() - startTime < timeoutMs) {
      try {
        // Check for success indicator
        await execAsync('test -f /tmp/postfix-reload-success');
        logger.debug('Postfix reload confirmed successful');
        // Clean up the indicator file
        await execAsync('rm -f /tmp/postfix-reload-success').catch(() => {});
        return;
      } catch {
        // Success file doesn't exist, continue checking
      }

      try {
        // Check for error indicator
        await execAsync('test -f /tmp/postfix-reload-error');
        logger.warn('Postfix reload reported error');
        // Clean up the indicator file
        await execAsync('rm -f /tmp/postfix-reload-error').catch(() => {});
        return; // Don't throw, just log the warning
      } catch {
        // Error file doesn't exist, continue checking
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }

    // Timeout reached - assume reload was processed but no feedback received
    logger.debug('Postfix reload feedback timeout - assuming processed');
  }

  /**
   * Health check for Postfix PostgreSQL integration
   * Verifies that the service can connect to and query the postfix tables
   */
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    try {
      // Test database connectivity
      const domainCount = await prisma.postfixVirtualDomain.count();
      const aliasCount = await prisma.postfixVirtualAlias.count();

      // Test a simple query
      const activeDomains = await prisma.postfixVirtualDomain.count({
        where: { active: true }
      });

      return {
        healthy: true,
        details: {
          totalDomains: domainCount,
          totalAliases: aliasCount,
          activeDomains,
          timestamp: new Date().toISOString(),
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : error,
          timestamp: new Date().toISOString(),
        }
      };
    }
  }
}

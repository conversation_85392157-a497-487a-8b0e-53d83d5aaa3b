import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';
import { prisma } from '../lib/prisma.js';
import { PlanConfigService } from './billing/plan-config.service.js';
import { CreditService } from './billing/credit.service.js';

export interface UsageTrackingResult {
  success: boolean;
  source: 'monthly' | 'credits' | 'failed';
  remainingMonthly: number;
  remainingCredits: number;
  usedCredits?: number;
  error?: string;
}

/**
 * Atomic Usage Tracking Service
 * Fixes race conditions in currentMonthEmails counter using database transactions
 * and atomic operations to prevent data loss under concurrent load.
 */
export class AtomicUsageTrackingService {
  
  /**
   * Atomically increment user email usage with proper concurrency control
   * Uses database transactions and SELECT FOR UPDATE to prevent race conditions
   */
  static async incrementEmailUsage(
    userId: string, 
    emailCount: number = 1
  ): Promise<UsageTrackingResult> {
    if (emailCount <= 0) {
      return {
        success: false,
        source: 'failed',
        remainingMonthly: 0,
        remainingCredits: 0,
        error: 'Email count must be positive',
      };
    }

    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        // Use a transaction with proper isolation level
        const result = await prisma.$transaction(async (tx) => {
          // Lock the user row to prevent concurrent modifications
          const user = await tx.user.findUnique({
            where: { id: userId },
            select: {
              id: true,
              currentMonthEmails: true,
              planType: true,
              lastUsageReset: true,
            },
          });

          if (!user) {
            throw new Error(`User ${userId} not found`);
          }

          // Check if we need to reset monthly usage (new month)
          const now = new Date();
          const lastReset = new Date(user.lastUsageReset);
          const shouldReset = (
            now.getMonth() !== lastReset.getMonth() || 
            now.getFullYear() !== lastReset.getFullYear()
          );

          let currentMonthEmails = user.currentMonthEmails;
          
          // Reset monthly usage if needed
          if (shouldReset) {
            await tx.user.update({
              where: { id: userId },
              data: {
                currentMonthEmails: 0,
                lastUsageReset: now,
              },
            });
            currentMonthEmails = 0;
            
            logger.info({ 
              userId, 
              previousCount: user.currentMonthEmails,
              resetDate: now 
            }, 'Monthly email usage reset');
          }

          // Get plan configuration
          const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
          const monthlyAllowance = planConfig.monthlyEmailLimit;
          const monthlyRemaining = monthlyAllowance - currentMonthEmails;

          // Try to use monthly allowance first
          if (monthlyRemaining >= emailCount) {
            // Use monthly allowance - atomic increment
            await tx.user.update({
              where: { id: userId },
              data: {
                currentMonthEmails: { increment: emailCount }
              }
            });

            const creditBalance = await CreditService.getCreditBalance(userId);

            logger.debug({
              userId,
              emailCount,
              source: 'monthly',
              newMonthlyUsage: currentMonthEmails + emailCount,
              remainingMonthly: monthlyRemaining - emailCount,
            }, 'Email usage tracked via monthly allowance');

            return {
              success: true,
              source: 'monthly' as const,
              remainingMonthly: monthlyRemaining - emailCount,
              remainingCredits: creditBalance.totalCredits,
            };
          }

          // Monthly allowance insufficient, try to use credits
          const creditsNeeded = emailCount - monthlyRemaining;
          const creditBalance = await CreditService.getCreditBalance(userId);

          if (creditBalance.totalCredits >= creditsNeeded) {
            // Use combination of monthly allowance + credits

            // First, use up remaining monthly allowance
            if (monthlyRemaining > 0) {
              await tx.user.update({
                where: { id: userId },
                data: {
                  currentMonthEmails: { increment: monthlyRemaining }
                }
              });
            }

            // Then use credits for the remainder
            const creditUsageResult = await CreditService.useCredits(userId, creditsNeeded);

            if (!creditUsageResult.success) {
              throw new Error(`Credit usage failed: ${creditUsageResult.error}`);
            }

            const newCreditBalance = creditBalance.totalCredits - creditsNeeded;

            logger.info({
              userId,
              emailCount,
              monthlyUsed: monthlyRemaining,
              creditsUsed: creditsNeeded,
              remainingCredits: newCreditBalance,
            }, 'Email usage tracked via monthly allowance + credits');

            return {
              success: true,
              source: 'credits' as const,
              remainingMonthly: 0,
              remainingCredits: newCreditBalance,
              usedCredits: creditsNeeded,
            };
          }

          // Insufficient credits and monthly allowance
          logger.warn({
            userId,
            emailCount,
            monthlyRemaining,
            creditBalance: creditBalance.totalCredits,
            creditsNeeded,
          }, 'Insufficient email allowance and credits');

          return {
            success: false,
            source: 'failed' as const,
            remainingMonthly: monthlyRemaining,
            remainingCredits: creditBalance.totalCredits,
            error: `Insufficient email allowance. Need ${creditsNeeded} credits but only have ${creditBalance.totalCredits}`,
          };

        }, {
          // Use serializable isolation level to prevent race conditions
          isolationLevel: 'Serializable',
          timeout: 10000, // 10 second timeout
        });

        return result;

      } catch (error) {
        attempt++;
        
        // Handle serialization failures (concurrent access)
        if (error.code === 'P2034' || error.message.includes('serialization')) {
          if (attempt < maxRetries) {
            // Exponential backoff with jitter
            const delay = Math.min(100 * Math.pow(2, attempt) + Math.random() * 100, 1000);
            await new Promise(resolve => setTimeout(resolve, delay));
            
            logger.warn({
              userId,
              attempt,
              maxRetries,
              delay,
            }, 'Retrying usage tracking due to serialization conflict');
            
            continue;
          }
        }

        logger.error({
          error: error.message,
          userId,
          emailCount,
          attempt,
        }, 'Failed to increment email usage');

        return {
          success: false,
          source: 'failed',
          remainingMonthly: 0,
          remainingCredits: 0,
          error: `Database error: ${error.message}`,
        };
      }
    }

    // All retries exhausted
    return {
      success: false,
      source: 'failed',
      remainingMonthly: 0,
      remainingCredits: 0,
      error: 'Max retries exhausted due to concurrent access',
    };
  }

  /**
   * Get current usage statistics for a user (read-only, no locking needed)
   */
  static async getUserUsageStats(userId: string): Promise<{
    currentMonthEmails: number;
    monthlyLimit: number;
    remainingMonthly: number;
    creditBalance: number;
    planType: string;
    lastUsageReset: Date;
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        currentMonthEmails: true,
        planType: true,
        lastUsageReset: true,
      },
    });

    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    const planConfig = PlanConfigService.getPlanConfig(user.planType || 'free');
    const monthlyLimit = planConfig.monthlyEmailLimit;
    const remainingMonthly = Math.max(0, monthlyLimit - user.currentMonthEmails);
    const creditBalance = await CreditService.getCreditBalance(userId);

    return {
      currentMonthEmails: user.currentMonthEmails,
      monthlyLimit,
      remainingMonthly,
      creditBalance: creditBalance.totalCredits,
      planType: user.planType || 'free',
      lastUsageReset: user.lastUsageReset,
    };
  }

  /**
   * Bulk increment usage for multiple users (batch processing)
   * Uses individual transactions to prevent one failure from affecting others
   */
  static async bulkIncrementEmailUsage(
    userEmailCounts: Array<{ userId: string; emailCount: number }>
  ): Promise<Array<{ userId: string; result: UsageTrackingResult }>> {
    const results: Array<{ userId: string; result: UsageTrackingResult }> = [];

    // Process in parallel but with concurrency limit
    const concurrencyLimit = 5;
    const chunks = [];
    
    for (let i = 0; i < userEmailCounts.length; i += concurrencyLimit) {
      chunks.push(userEmailCounts.slice(i, i + concurrencyLimit));
    }

    for (const chunk of chunks) {
      const chunkResults = await Promise.all(
        chunk.map(async ({ userId, emailCount }) => {
          const result = await this.incrementEmailUsage(userId, emailCount);
          return { userId, result };
        })
      );
      
      results.push(...chunkResults);
    }

    const successCount = results.filter(r => r.result.success).length;
    const failureCount = results.length - successCount;

    logger.info({
      totalProcessed: results.length,
      successCount,
      failureCount,
    }, 'Bulk email usage tracking completed');

    return results;
  }

  /**
   * Reset monthly usage for all users (scheduled job)
   * Uses cursor-based pagination to handle large datasets
   */
  static async resetMonthlyUsageForAllUsers(): Promise<{
    processedCount: number;
    resetCount: number;
    errors: string[];
  }> {
    let processedCount = 0;
    let resetCount = 0;
    const errors: string[] = [];
    let cursor: string | undefined;

    const batchSize = 100;
    const now = new Date();

    logger.info('Starting monthly usage reset for all users');

    try {
      while (true) {
        const users = await prisma.user.findMany({
          take: batchSize,
          skip: cursor ? 1 : 0,
          cursor: cursor ? { id: cursor } : undefined,
          select: {
            id: true,
            lastUsageReset: true,
            currentMonthEmails: true,
          },
          orderBy: { id: 'asc' },
        });

        if (users.length === 0) break;

        for (const user of users) {
          try {
            const lastReset = new Date(user.lastUsageReset);
            const shouldReset = (
              now.getMonth() !== lastReset.getMonth() || 
              now.getFullYear() !== lastReset.getFullYear()
            );

            if (shouldReset && user.currentMonthEmails > 0) {
              await prisma.user.update({
                where: { id: user.id },
                data: {
                  currentMonthEmails: 0,
                  lastUsageReset: now,
                },
              });
              resetCount++;
            }

            processedCount++;
          } catch (error) {
            errors.push(`User ${user.id}: ${error.message}`);
          }
        }

        cursor = users[users.length - 1].id;
        
        // Log progress
        if (processedCount % 1000 === 0) {
          logger.info({
            processedCount,
            resetCount,
            errorCount: errors.length,
          }, 'Monthly usage reset progress');
        }
      }

      logger.info({
        processedCount,
        resetCount,
        errorCount: errors.length,
      }, 'Monthly usage reset completed');

      return { processedCount, resetCount, errors };

    } catch (error) {
      logger.error({ error: error.message }, 'Failed to reset monthly usage');
      throw error;
    }
  }
}

// Export for backward compatibility
export const atomicUsageTracking = AtomicUsageTrackingService;

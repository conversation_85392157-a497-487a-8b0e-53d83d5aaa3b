# Local development
NGROK_AUTHTOKEN=
NGROK_DOMAIN=acme.ngrok-free.app

# Environment variables for production deployment
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
URL=https://emailconnect.eu # Or ${NGROK_DOMAIN}
LOG_RETENTION_DAYS=7

# Database - Update with your production database
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=eu_email_webhook
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/${DB_NAME}

# Better Auth
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=${URL}
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# Encyption key
ENCRYPTION_MASTER_KEY=your-very-strong-and-secret-key-for-encryption

# HMAC secret for signed links (magic/renewal/etc.)
SIGNED_LINK_SECRET=please-change-me

# Postfix integration
POSTFIX_DB_PASSWORD=your_postfix_user_password_here

# Redis
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_URL=redis://:${REDIS_PASSWORD}@localhost:6379
# For host-side scripts (advanced-process-email.js) - uses Docker Redis on port 6380
REDIS_HOST_URL=redis://:${REDIS_PASSWORD}@127.0.0.1:6380
# Enhanced queue settings (keep false to prevent duplicate webhook processing)
ENHANCED_WEBHOOK_PROCESSOR=false
USE_REDIS_SENTINEL=false

# SPAMC
SPAMC_PATH=/usr/bin/spamc
SPAMC_TIMEOUT_MS=10000

# Error tracking (Bugsink)
SENTRY_ENABLED=false # Set to true to enable in development (auto-enabled in production)
SENTRY_DSN=https://<EMAIL>/1
SENTRY_TRACES_SAMPLE_RATE=0 # 0-1, set to 0 as per Bugsink docs
SENTRY_REPLAYS_SESSION_SAMPLE_RATE=0 # 0-1, only capture replays on errors
SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE=1.0 # 0-1, always capture replay when error occurs
VITE_SENTRY_ENABLED=${SENTRY_ENABLED}
VITE_SENTRY_DSN=${SENTRY_DSN}

# Email processing limits
MAX_EMAIL_SIZE_MB=25
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3

# DNS verification settings
DNS_VERIFICATION_TIMEOUT_MS=5000
DNS_VERIFICATION_CACHE_TTL_MS=300000
DNS_VERIFICATION_RETRY_ATTEMPTS=3

# GDPR compliance settings
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90

# Usage tracking and billing settings
DEFAULT_MONTHLY_EMAIL_LIMIT=50
FREE_PLAN_EMAIL_LIMIT=50
PRO_PLAN_EMAIL_LIMIT=1000
ENTERPRISE_PLAN_EMAIL_LIMIT=10000

# WebhookTest Integration
WEBHOOKTEST_API_URL=http://localhost:3002
WEBHOOKTEST_JWT_SECRET=shared-secret-with-emailconnect  # Should match WebhookTest

# Payment processing (Mollie)
MOLLIE_API_KEY=test_dHa...
MOLLIE_WEBHOOK_URL=https://${URL}/api/webhooks/mollie
MOLLIE_WEBHOOK_SECRET=your-mollie-webhook-secret-key
MOLLIE_TEST_MODE=true

# CORS Configuration (websocket, BetterAuth)
ALLOWED_ORIGINS=https://${URL},http://localhost:3000,https://hardy-content-pig.ngrok-free.app,https://webhooksite.eu

# Attachment Processing
MAX_INLINE_ATTACHMENT_SIZE_KB=128
DEFAULT_ATTACHMENT_RETENTION_HOURS=1
PAID_ATTACHMENT_RETENTION_HOURS=24

# Scaleway
## Scaleway Transactional Email (for notifications)
SCALEWAY_ACCESS_KEY=your-scaleway-access-key
SCALEWAY_SECRET_KEY=your-scaleway-secret-key
SCALEWAY_PROJECT_ID=your-scaleway-project-id
SCALEWAY_REGION=fr-par
SCALEWAY_FROM_EMAIL=<EMAIL>
SCALEWAY_FROM_NAME=EmailConnect

## S3 Storage Configuration (for Pro+ users with attachment storage)
S3_REGION=nl-ams
S3_BUCKET=eceu-data-test
S3_ACCESS_KEY_ID=your-s3-access-key-id
S3_SECRET_ACCESS_KEY=your-s3-secret-access-key
S3_ENDPOINT=https://eceu-data.s3.nl-ams.scw.cloud

# Notion content integration
NOTION_API_KEY=
NOTION_ANNOUNCEMENTS_DB_ID=
NOTION_FAQ_DB_ID=
# Cache TTL in seconds for Notion responses (default 300)
NOTION_CACHE_TTL_SECONDS=300
# Comma-separated hostnames allowed for redirect proxy (e.g. producthunt.com,example.com)
NOTION_REDIRECT_HOST_WHITELIST=producthunt.com,www.producthunt.com,emailconnect.eu,www.emailconnect.eu

# Heartbeat monitoring
HEARTBEAT_URL=
HEARTBEAT_INTERVAL_MINUTES=5

# CSP reporting
CSP_REPORT_URI=${URL}/csp-report

# Telegram admin notifications (no PII; admin-only channel)
TELEGRAM_ENABLED=false
TELEGRAM_BOT_TOKEN=
TELEGRAM_ADMIN_CHAT_ID=

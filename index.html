<!DOCTYPE html>
<html lang="en" data-theme="emerald">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/src/frontend/assets/images/favicon.ico" />
    <link rel="manifest" href="/src/frontend/assets/images/site.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta name="keywords" content="email, webhook, GDPR, EU, compliance, developer, API" />
    <title>EmailConnect.eu – Turn emails into webhooks, EU-based.</title>
    <link rel="canonical" href="https://emailconnect.eu/" />
    <meta property="og:title" content="EmailConnect.eu – Turn emails into webhooks, EU-based." />
    <meta property="og:description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta property="og:image" content="https://emailconnect.eu/og-image.png" />
    <meta property="og:url" content="https://emailconnect.eu" />
    <meta name="twitter:card" content="summary_large_image" />
    <link rel="apple-touch-icon" sizes="180x180" href="/src/frontend/assets/images/apple-touch-icon.png" />
    <link rel="icon" type="image/svg+xml" href="/src/frontend/assets/images/favicon.svg" />
    <link rel="icon" type="image/png" sizes="96x96" href="/src/frontend/assets/images/favicon-96x96.png" />
  
    <!-- Plausible Analytics -->
    <script defer data-domain="emailconnect.eu" src="https://plausible.io/js/script.hash.outbound-links.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function() { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>
  </head>
  <body>
    <div id="app">
      <!-- Fallback content for users without JavaScript and crawlers -->
      <noscript>
        <style>
          .noscript-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.6;
          }
          .noscript-header {
            text-align: center;
            padding: 3rem 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
          }
          .noscript-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
          }
          .noscript-subtitle {
            font-size: 1.25rem;
            opacity: 0.95;
          }
          .noscript-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
          }
          .noscript-feature {
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
          }
          .noscript-feature h3 {
            color: #4b5563;
            margin-bottom: 0.5rem;
          }
          .noscript-cta {
            text-align: center;
            padding: 2rem;
            background: #f9fafb;
            border-radius: 0.5rem;
            margin-top: 2rem;
          }
          .noscript-warning {
            background: #fef2f2;
            border: 1px solid #fee2e2;
            color: #991b1b;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            text-align: center;
          }
        </style>
        
        <div class="noscript-container">
          <div class="noscript-warning">
            ⚠️ JavaScript is required for the full EmailConnect.eu experience. Please enable JavaScript to access all features.
          </div>
          
          <div class="noscript-header">
            <div class="noscript-title">EmailConnect.eu</div>
            <div class="noscript-subtitle">Turn emails into webhooks — 100% EU-hosted, GDPR-compliant</div>
          </div>
          
          <div style="text-align: center; margin: 2rem 0;">
            <h2 style="font-size: 1.875rem; color: #1f2937; margin-bottom: 1rem;">
              The easiest way to connect emails to your automation workflow
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280; max-width: 800px; margin: 0 auto;">
              EmailConnect.eu transforms incoming emails into structured JSON webhooks, 
              enabling seamless integration with Zapier, Make, n8n, and custom applications. 
              Built with European privacy standards at its core.
            </p>
          </div>
          
          <div class="noscript-features">
            <div class="noscript-feature">
              <h3>🇪🇺 100% EU-Hosted</h3>
              <p>All data processing happens on European servers. Full GDPR compliance with data sovereignty guaranteed.</p>
            </div>
            
            <div class="noscript-feature">
              <h3>🔒 Privacy-First Design</h3>
              <p>End-to-end encryption, automatic data deletion, and granular access controls protect your sensitive information.</p>
            </div>
            
            <div class="noscript-feature">
              <h3>⚡ Instant Webhooks</h3>
              <p>Emails are converted to JSON and delivered to your endpoints in real-time with retry logic and error handling.</p>
            </div>
            
            <div class="noscript-feature">
              <h3>🔧 Developer-Friendly</h3>
              <p>RESTful API, comprehensive documentation, and support for custom headers and authentication methods.</p>
            </div>
            
            <div class="noscript-feature">
              <h3>📊 Built for Scale</h3>
              <p>Handle thousands of emails per minute with our robust infrastructure and queue management system.</p>
            </div>
            
            <div class="noscript-feature">
              <h3>🎯 Smart Filtering</h3>
              <p>Advanced spam filtering and content validation ensure only legitimate emails reach your webhooks.</p>
            </div>
          </div>
          
          <div class="noscript-cta">
            <h2 style="color: #1f2937; margin-bottom: 1rem;">Ready to get started?</h2>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">
              Join hundreds of European businesses automating their email workflows while maintaining complete data privacy.
            </p>
            <p style="font-size: 1.125rem;">
              <strong>Enable JavaScript to sign up for your free account</strong><br>
              or contact us at <a href="mailto:<EMAIL>" style="color: #6366f1;"><EMAIL></a>
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e5e7eb; color: #6b7280;">
            <p>© 2024 EmailConnect.eu - Built with ❤️ in Europe</p>
            <p style="margin-top: 0.5rem;">
              <a href="/privacy-policy" style="color: #6366f1; margin: 0 1rem;">Privacy Policy</a>
              <a href="/terms-of-service" style="color: #6366f1; margin: 0 1rem;">Terms of Service</a>
              <a href="/docs" style="color: #6366f1; margin: 0 1rem;">API Documentation</a>
            </p>
          </div>
        </div>
      </noscript>
      
      <!-- Loading state for JavaScript users -->
      <div id="app-loading" style="display: flex; justify-content: center; align-items: center; min-height: 100vh; font-family: system-ui, -apple-system, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 48px; height: 48px; border: 4px solid #e5e7eb; border-top-color: #6366f1; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
          <p style="color: #6b7280;">Loading EmailConnect.eu...</p>
        </div>
      </div>
      
      <style>
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        /* Hide loading spinner when Vue app mounts */
        #app[data-v-app] #app-loading {
          display: none !important;
        }
      </style>
    </div>
    <script type="module" src="/src/frontend/main.ts"></script>
  </body>
</html>

{"permissions": {"allow": ["Bash(npx prisma migrate dev:*)", "Bash(npm run lint)", "<PERSON><PERSON>(npx jest:*)", "Bash(node:*)", "Bash(npx prisma migrate:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run dev:backend:*)", "Bash(grep:*)", "Bash(npm test:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(docker compose up:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(docker logs:*)", "Bash(docker network inspect:*)", "mcp__sequential-thinking__sequentialthinking"], "deny": [], "ask": []}}
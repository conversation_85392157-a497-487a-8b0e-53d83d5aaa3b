# Notification System Documentation

## Overview

EmailConnect uses a unified, multi-channel notification system that can deliver notifications through:
- **Email** (via Scaleway Transactional Email)
- **WebSocket** (real-time in-app toasts)
- **Database** (persistent notification bell)

Each notification type is automatically routed to the appropriate channels based on its configuration.

## Quick Start

### Sending a Simple Notification

```typescript
import { notify } from '../services/notifications';

// Payment success notification (goes to email, websocket, and database)
await notify.payment.success(userId, { 
  amount: 25.00, 
  currency: '€',
  planType: 'pro' 
});

// Domain verified notification (goes to websocket and database only)
await notify.domain.verified(userId, { 
  domain: 'example.com' 
});

// Quota warning (goes to all three channels)
await notify.system.quotaWarning(userId, { 
  usage: 80, 
  limit: 100,
  percentage: 80
});
```

## Available Notification Types

### Payment Notifications (`notify.payment`)
- `success(userId, data)` - Payment processed successfully
- `failed(userId, data)` - Payment failed
- `reminder(userId, data)` - Payment renewal reminder with checkout URL

### Subscription Notifications (`notify.subscription`)
- `created(userId, data)` - New subscription activated
- `renewed(userId, data)` - Subscription renewed
- `cancelled(userId, data)` - Subscription cancelled
- `expiring(userId, data)` - Subscription expiring soon

### Domain Notifications (`notify.domain`)
- `created(userId, data)` - Domain added
- `verified(userId, data)` - Domain verified
- `verificationFailed(userId, data)` - Domain verification failed

### Webhook Notifications (`notify.webhook`)
- `created(userId, data)` - Webhook created
- `verified(userId, data)` - Webhook verified
- `failed(userId, data)` - Webhook delivery failed

### System Notifications (`notify.system`)
- `quotaWarning(userId, data)` - Email quota warning
- `quotaExceeded(userId, data)` - Email quota exceeded
- `attachmentRejected(userId, data)` - Attachment rejected

### User Notifications (`notify.user`)
- `registered(userId, data)` - User registered
- `emailVerified(userId, data)` - Email verified

### Plan Notifications (`notify.plan`)
- `upgraded(userId, data)` - Plan upgraded
- `downgraded(userId, data)` - Plan downgraded

## Channel Configuration

Each notification type is pre-configured to use specific channels:

```typescript
// Example channel configurations
'payment.success': ['websocket', 'database', 'email']
'domain.created': ['websocket', 'database']
'payment.reminder': ['database', 'email']
```

Channels are defined in `notification-events.service.ts` in the `EVENT_CHANNEL_CONFIG` object.

## Custom Notifications

For special cases, you can send custom notifications:

```typescript
import { notify } from '../services/notifications';

await notify.custom(
  'custom.event',
  userId,
  'Custom Title',
  'Custom message text',
  {
    category: 'SYSTEM',
    priority: 'HIGH',
    actionUrl: '/dashboard',
    actionText: 'View Dashboard',
    toast: { type: 'info', duration: 5000 },
    emailSubject: 'Custom Email Subject',
    data: { customField: 'value' }
  }
);
```

## Notification Options

### Common Options
- `category`: SYSTEM, BILLING, SECURITY, DOMAIN, WEBHOOK, PAYMENT, ATTACHMENT
- `priority`: LOW, MEDIUM, HIGH, URGENT
- `actionUrl`: URL for action button
- `actionText`: Text for action button
- `data`: Additional data to store with notification

### Email-Specific Options
- `emailSubject`: Custom email subject
- `emailHtml`: Custom HTML content
- `emailText`: Custom plain text content
- `replyTo`: Reply-to email address

### WebSocket-Specific Options
- `toast.type`: success, error, warning, info
- `toast.duration`: Duration in milliseconds

## Architecture Details

### Components

1. **`index.ts`** - High-level API wrapper with categorized notification functions
2. **`notification-events.service.ts`** - Core event system with channel handlers
3. **`scaleway-email.service.ts`** - Email delivery implementation

### Channel Handlers

Each channel has its own handler class:

- **EmailChannelHandler**: Sends transactional emails via Scaleway
- **WebSocketChannelHandler**: Emits real-time events to connected clients
- **DatabaseChannelHandler**: Persists notifications in database

### Database Storage

Notifications stored in database include:
- Automatic expiry dates based on priority
- Read/unread status tracking
- Action URLs and custom data
- Category and type classification

### Event Flow

1. Call notification function (e.g., `notify.payment.success()`)
2. Event is created with all necessary data
3. Event is emitted to the notification events service
4. Service routes to configured channels
5. Each channel handler processes the notification
6. Errors are logged but don't stop other channels

## Testing Notifications

### Manual Testing

```typescript
// Test payment notification
await notify.payment.success('test-user-id', {
  amount: 25.00,
  currency: '€',
  planType: 'pro',
  method: 'creditcard'
});

// Test with custom options
await notify.system.quotaWarning('test-user-id', {
  usage: 80,
  limit: 100,
  percentage: 80
});
```

### Environment Requirements

Email notifications require these environment variables:
```env
SCALEWAY_PROJECT_ID=your-project-id
SCALEWAY_ACCESS_KEY=your-access-key
SCALEWAY_SECRET_KEY=your-secret-key
SCALEWAY_REGION=fr-par
```

If not configured, email notifications will be skipped (logged but not sent).

## Best Practices

1. **Use specific notification types** instead of custom when possible
2. **Include actionable data** in notifications (URLs, amounts, dates)
3. **Set appropriate priority** to control retention and urgency
4. **Test channel delivery** before production deployment
5. **Monitor logs** for notification failures

## Common Use Cases

### After Payment Processing
```typescript
// In payment webhook handler
if (payment.status === 'paid') {
  await notify.payment.success(userId, {
    amount: payment.amount,
    currency: payment.currency,
    planType: subscription.planType,
    method: payment.method
  });
}
```

### Domain Verification
```typescript
// After DNS check
if (dnsVerified) {
  await notify.domain.verified(userId, {
    domain: domainName
  });
} else {
  await notify.domain.verificationFailed(userId, {
    domain: domainName,
    reason: 'DNS records not found'
  });
}
```

### Subscription Renewal Reminders
```typescript
// In scheduled job
const expiringSubscriptions = await findExpiringSubscriptions();
for (const sub of expiringSubscriptions) {
  await notify.subscription.expiring(sub.userId, {
    planType: sub.planType,
    expiryDate: sub.nextPaymentDate,
    renewalUrl: `/settings/billing`
  });
}
```

## Troubleshooting

### Notifications Not Appearing

1. Check logs for errors with specific channel handlers
2. Verify environment variables for email service
3. Ensure WebSocket connection is established
4. Check database for notification records

### Email Not Sending

1. Verify Scaleway credentials are configured
2. Check user has valid email address
3. Review email service logs for API errors
4. Test with Scaleway dashboard

### WebSocket Not Updating

1. Verify WebSocket connection in browser console
2. Check user ID matches in emit call
3. Review WebSocket service logs
4. Test with manual WebSocket emit

## Future Improvements

- [ ] Add SMS channel support
- [ ] Implement notification preferences per user
- [ ] Add batching for digest emails
- [ ] Create notification templates system
- [ ] Add webhook channel for external systems
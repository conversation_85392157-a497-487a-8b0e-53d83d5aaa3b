# EmailConnect App - TODO List (ARCHIVED)

**Last Updated**: August 2025
**Status**: ARCHIVED - See TODO-CURRENT.md for active priorities

## 🔴 CRITICAL - Active Paying Customer Support

- [ ] **Review test coverage and identify gaps**: Comprehensive testing audit
  - [ ] Audit current test coverage (especially subscription service)
  - [ ] Identify critical paths missing tests
  - [ ] Add tests for payment flows and edge cases


### Testing & Production Monitoring (CRITICAL)
- [ ] **Automated end-to-end testing**: Create twice-daily automated tests + trigger from AdminDashboard.vue. Conceptual steps:
  - [ ] Create alias via our API
  - [ ] Create webhook via external API
  - [ ] Attach webhook to alias via our API
  - [ ] Send test email (once a day with attachment (randomly pick either img, txt, zip), once without)
  - [ ] Verify 200 response and confirm payload via webhook's API
  - [ ] Clean up (delete webhook and alias)
  - [ ] Run every X hours for production monitoring
  - [ ] Add all required keys to .env
  - [ ] Isolate script on separate server (?)
  - [ ] Once we get to this, ask me about a PRD
  - [ ] Consider adding to CI/CD pipeline

## 🟡 HIGH PRIORITY - Current Sprint

### 1. Queue System Reliability
- [ ] Add webhook failure notifications for users

### 2. Legal & Compliance
- [ ] Update Privacy Policy with GDPR specifics
- [ ] Revise Terms of Service for email forwarding
- [ ] Add Cookie Policy
- [ ] Create Data Processing Agreement (DPA) template

### 3. SOC2 Security Requirements
- [ ] Encrypt sensitive data (including `UserSettings.s3Config`)

### Documentation & API
- [ ] Update API documentation to reflect current functionality
- [ ] Update and clean ./docs files with latest progress
- [ ] TODO: Plausible success-only auth tracking
  - Currently login events for OAuth and magic-link are tracked on attempt (pre-redirect / link sent)
  - Add post-auth callback hook to emit Login only on successful session establishment
  - Option A: create a lightweight /auth/callback route to detect authenticated session and fire trackUserAction.login('oauth'|'email')
  - Option B: on first successful checkAuth() after redirect, gate by sessionStorage flag to avoid duplicate events
  - Keep existing attempt-based tracking for now; migrate to success-only when callback page is in place

## 🟢 MEDIUM PRIORITY - Next Phase

### Database & Technical Debt
- [ ] Optimize database indexes for common queries


### Growth & Marketing
- [ ] Add About page to attract new users
- [ ] Implement referral system
- [ ] Create landing pages for specific use cases
- [ ] Plan off-channel marketing strategy

## 🔵 LOW PRIORITY - Backlog
_For many of these holds: once requested by user_

### User Experience
- [ ] In-app feedback collection system
- [ ] Actually send/deliver generated invoices (currently only downloadable)
- [ ] Refund processing (not critical)
- [ ] Implement two-factor authentication (2FA), once requested by user
- [ ] Add comprehensive audit logging -> audit_logs table implemented, more advanced upon request

### Analytics Dashboard (Pro Feature)
_Let the users ask for this before developing_
- [ ] Email processing trends and volume charts
- [ ] Webhook success rates and latency metrics
- [ ] API usage tracking with rate limit visualization
- [ ] Security events dashboard (failed logins, suspicious activity)

### Developer Experience
- [ ] API schema validation tooling
- [ ] Improve S3 error handling and logging
- [ ] CI/CD pipeline improvements

### Misc
- [ ] Determine approach for load testing email processing volumes
- [ ] Create admin panel for plan management 
- [ ] Fix middleware rate limiting to use subscription status (lower priority)
- [ ] Add security monitoring and alerting
- [ ] Auto archive old webhook logs (needed?)
- [ ] Show queue depth and failure alerts in AdminDashboard -> or consider external service? Investigate.

## ✅ COMPLETED (Latest First)

### August 2025
- ✅ **Multi-Channel Notification Control**: Smart content mapping and channel-specific notifications
  - ✅ Added `notifyChannel()` method to NotificationEventsService for targeted notifications
  - ✅ Smart content hierarchy: title→toast, message→bell+email subject, emailMessage→email body
  - ✅ Enhanced `sendNotification()` with channel control and custom messages per channel
  - ✅ Updated WebSocket interface to make message optional (toasts only need title)
  - ✅ Disabled unimplemented userNotifications that reference verification features
- ✅ **Unified Email Template System**: Professional, consistent email branding across all notifications
  - ✅ Created comprehensive EmailTemplateService with modern HTML/CSS design
  - ✅ Template variables system: greeting, body, CTA buttons, closing, attachments
  - ✅ Automatic personalization with firstName extraction from user data
  - ✅ Responsive design with mobile optimization and EmailConnect green (#57bd7b) branding
  - ✅ Both HTML and text email generation for maximum deliverability
  - ✅ Integrated with existing notification definitions and enhanced key emails
  - ✅ Support for different CTA button styles (primary, success, danger, secondary)
  - ✅ Textual branding header (no external image dependencies until CDN setup)
  - ✅ Transactional email focus (no unsubscribe links until mechanism implemented)
- ✅ **Email Deliverability System**: Scaleway webhook integration for sender reputation protection
  - ✅ Created `/api/webhooks/email-reports` endpoint for bounce/spam tracking
  - ✅ Automatic user verification management (verified=true by default, disabled on issues)
  - ✅ Email blocking for unverified users to maintain sender reputation
  - ✅ GDPR-compliant audit logging for all verification status changes
  - ✅ Support for all Scaleway events: blocklist_created, email_blocklisted, email_dropped, email_spam, email_mailbox_not_found
- ✅ **Admin Subscription Management**: Complete manual upgrade/downgrade system
  - ✅ AdminDashboard.vue user lookup and management interface
  - ✅ Custom renewal date picker for flexible trial periods
  - ✅ Backend API endpoints for upgrade/downgrade operations
  - ✅ Enhanced notification emails with renewal date information
  - ✅ Proper data preservation during pro → free downgrades
  - ✅ Fixed pricing logic to use plan-config.service.ts (removed hardcoded amounts)
- ✅ **Manual Payment System**: Complete one-off payment system for EU bank transfers
  - ✅ Created `/api/payments/create` endpoint for manual subscription payments
  - ✅ Added PaymentController for one-off payment handling
  - ✅ Integrated with OneOffSubscriptionService for virtual subscriptions
  - ✅ Fixed invoice generation for one-off payments (virtualSubscription metadata)
  - ✅ Added renewal notification system (3-day advance email alerts)
  - ✅ Fixed renewal payment redirect URLs (`/settings/billing` instead of `/dashboard/billing`)
  - ✅ Added frontend handling for renewal success/cancelled states
  - ✅ Fixed subscription cancellation for virtual subscriptions (no Mollie ID)
  - ✅ Duplicate email prevention (only one pending renewal payment per subscription)
- ✅ **GDPR Compliance Enhancements**: Complete email audit logging system
  - ✅ Added comprehensive email send tracking to `audit_logs` table
  - ✅ 7-year retention policy for email communication records
  - ✅ Both successful and failed email attempts logged with full metadata
  - ✅ GDPR-compliant data structure for user rights requests
- ✅ **UI/UX Improvements**: Fixed payment form issues
  - ✅ Fixed radio button highlighting for Credit Card and Direct Debit options
  - ✅ Replaced CSS peer selectors with Vue reactive classes for better compatibility
  - ✅ Added proper visual feedback for payment method selection
- ✅ **Development Infrastructure**: CORS and ngrok support
  - ✅ Added ngrok URL support in development mode for testing
  - ✅ Fixed CORS issues preventing API calls in development environment
- ✅ **Subscription E2E Flow Fix**: Implemented correct Mollie recurring payment flow
  - ✅ Customer → First Payment (sequenceType='first') → Mandate creation
  - ✅ Subscription API with proper startDate calculation (today + 1 period)
  - ✅ Unified subscription creation handling both new and existing customers
  - ✅ Fixed webhook processing to avoid double charging
  - ✅ Verify webhook payment processing
  - ✅ Implement subscription cancellation
  - ✅ Automate webhook → account upgrade flow
- ✅ **Invoice System**: Professional PDF generation with VAT, database storage, S3 storage
- ✅ **Payment Processing**: Mollie webhook handling for account upgrades
- ✅ **User Impersonation**: Testing system for customer support
- ✅ **Pro Plan Features Verified**:
  - ✅ Data retention policy extension (24h for Pro vs 2h for Free)
  - ✅ Volume limits increase
  - ✅ Additional domains/aliases
  - ✅ Document pro vs free limitations

### January 2025
- ✅ **S3 Attachment Storage**: Custom bucket configuration with per-alias folders
- ✅ **File Type Controls**: Granular settings for text, images, documents, archives, media
- ✅ **Size Limit Enforcement**: User-configurable limits with proper validation
- ✅ **ZIP File Support**: Full support in archives category with S3 storage
- ✅ **Profile Management Fix**: Name updates properly reflect in frontend
- ✅ **Database Migration**: Converted to flexible `fileTypeSettings` JSON field

### December 2024 - January 2025
- ✅ **Bug Tracking**: Bugfender integration for error monitoring
- ✅ **Documentation Cleanup**: Consolidated and updated all docs
- ✅ **Data Retention**: 2h free, 24h Pro with auto-cleanup
- ✅ **Email Payload Viewer**: JSON syntax highlighting modal
- ✅ **Spam Filtering UI**: Simplified threshold slider
- ✅ **Notification System**: Real-time bell notifications
- ✅ **Domain Verification**: DNS-based verification working
- ✅ **Webhook Testing**: Test webhook functionality

## 📊 Progress Summary

**Critical Issues**: 2 areas (Testing/Monitoring, Test Coverage)
**High Priority**: 4 areas in active development  
**Medium Priority**: 2 areas for next sprint
**Low Priority**: 7 areas in backlog
**Completed**: 40+ major features shipped

### 🎯 Recent Achievements (August 2025)
- ✅ **Multi-Channel Notification Control** - Smart content mapping with channel-specific notifications
- ✅ **Unified Email Template System** - Professional EmailConnect branding with textual headers
- ✅ **Email Deliverability System** - Scaleway webhook integration for sender reputation protection
- ✅ **Complete Subscription System** - Admin management, one-off payments, Mollie integration
- ✅ **GDPR Compliance** - Full email audit logging with 7-year retention
- ✅ **Production-Ready Notifications** - Transactional emails with smart content hierarchy

---
*Note: Items marked with 🔴 are blocking revenue or affecting paying customers*
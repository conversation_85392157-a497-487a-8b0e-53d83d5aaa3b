# Plausible Analytics - Custom Events List

## Events to Add to Plausible Dashboard

Copy and paste these event names into your Plausible dashboard under **Settings > Goals > Add Goal > Custom Event**:

### 🔐 Authentication Events
- `Login`
- `Login Failed`
- `Register`
- `Register Failed`
- `Logout`

### 🌐 Domain Management Events
- `Domain Created`
- `Domain Verify` 
- `Domain Deleted`

### 🔗 Webhook Events
- `Webhook Created`
- `Webhook Test`
- `Webhook Deleted`

### 📧 Alias Events
- `Alias Created`
- `Alias Deleted`

### 💰 Plan & Billing Events
- `Plan Upgrade`
- `Plan Downgrade`
- `Payment Success`
- `Payment Failed`

### ⚙️ Settings & Feature Events
- `Settings Updated`
- `Spam Filter Toggle`
- `Help Article View`
- `Onboarding Complete`
- `Feature Discovery`

### 🚨 Error Events
- `Critical Error`

---

## Total Events: 21

## Event Properties

These events include useful properties for filtering and analysis:

**Login/Register**: `method` (email/oauth), `reason` (for failures)
**Domain Events**: `domain` (domain name), `result` (success/failed)
**Webhook Events**: `domain` (associated domain), `result` (success/failed)
**Alias Events**: `domain` (associated domain)
**Plan Events**: `from`, `to` (plan names), `amount` (for payments)
**Settings Events**: `section` (which settings area), `enabled` (true/false)

## Business Intelligence Use Cases

### Conversion Funnels:
1. **Registration → First Domain**: `Register` → `Domain Created`
2. **Domain → Verification**: `Domain Created` → `Domain Verify`
3. **Domain → Webhook**: `Domain Created` → `Webhook Created`
4. **Free → Pro**: `Register` → `Plan Upgrade`

### Key Metrics:
- **User Activation**: Users who create domains after registration
- **Feature Adoption**: Spam filter usage, webhook testing
- **Support Efficiency**: Help article views vs. support tickets
- **Payment Success Rate**: Payment success vs. failure rates
- **Domain Success Rate**: Domain verification success rate

### Alerts to Set Up:
- High `Payment Failed` rates
- Low `Domain Verify` success rates
- Spikes in `Critical Error` events
- Drops in `Register` → `Domain Created` conversion

---

## Setup Instructions

1. **Go to your Plausible dashboard**
2. **Navigate to Settings > Goals**
3. **Click "Add Goal"**
4. **Select "Custom Event"**
5. **Add each event name from the list above**
6. **Set up funnels for conversion tracking**

## Verification

After adding events, you can verify they're working by:
1. Running the app in development mode
2. Looking for the PlausibleTest component (appears in dev mode)
3. Clicking test buttons to trigger events
4. Checking your Plausible dashboard for real-time events

---

**Note**: Events may take 1-2 minutes to appear in your dashboard. The test component includes console logging for immediate verification.
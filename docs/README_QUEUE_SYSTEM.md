# Enhanced Queue System - High Availability

This document describes the enhanced queue system implemented to resolve the **Queue System Single Point of Failure** critical issue.

## 🎯 **Problems Solved**

### Before (Single Points of Failure):
- ❌ Single Redis instance - no failover
- ❌ Memory-based rate limiting - lost on restart  
- ❌ No dead letter queue - failed jobs lost
- ❌ No fallback delivery - Redis failure = complete outage
- ❌ No queue monitoring - blind spots in production

### After (High Availability):
- ✅ Redis Sentinel with automatic failover
- ✅ Redis-based rate limiting - persistent across restarts
- ✅ Dead letter queue for permanently failed jobs
- ✅ Fallback webhook delivery when queue unavailable
- ✅ Comprehensive queue monitoring and alerting

## 🏗️ **Architecture Overview**

### Redis High Availability Setup
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Redis Master   │◄──►│ Redis Replica   │    │ Redis Sentinel  │
│   Port: 6379    │    │   Port: 6380    │    │  Port: 26379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Application     │
                    │ (Auto-failover) │
                    └─────────────────┘
```

### Queue Architecture
```
┌──────────────┐    ┌──────────────┐    ┌──────────────┐
│   Webhook    │    │ Attachment   │    │ Dead Letter  │
│    Queue     │    │   Upload     │    │    Queue     │
│              │    │   Queue      │    │              │
└──────────────┘    └──────────────┘    └──────────────┘
         │                   │                   │
         └───────────────────┼───────────────────┘
                             │
                ┌─────────────────────┐
                │ Queue Monitoring    │
                │ Service             │
                │ - Health checks     │
                │ - Alerting          │
                │ - Metrics           │
                └─────────────────────┘
```

## 🚀 **New Features**

### 1. Redis Sentinel Failover
- **Automatic failover** when master Redis fails
- **Health monitoring** of Redis instances
- **Transparent reconnection** for applications

### 2. Dead Letter Queue
- **Permanently failed jobs** are preserved for analysis
- **Manual retry capability** for dead letter jobs
- **Failure analysis** and debugging support

### 3. Fallback Webhook Delivery
- **Direct HTTP delivery** when queue system is unavailable
- **Graceful degradation** instead of complete failure
- **Automatic recovery** when queue system comes back online

### 4. Redis-Based Rate Limiting
- **Persistent rate limits** across server restarts
- **Distributed rate limiting** for multiple server instances
- **Automatic cleanup** of expired rate limit entries

### 5. Queue Monitoring & Alerting
- **Real-time metrics** for all queues
- **Automated alerts** for high queue depth, failure rates, stalled jobs
- **Health dashboards** for operational visibility

## 📊 **Monitoring Endpoints**

### Admin Queue Health API
```bash
# Overall queue system health
GET /api/admin/queue/health

# Detailed queue status
GET /api/admin/queue/{queueName}/status

# Queue alerts
GET /api/admin/queue/alerts?severity=critical&minutes=60

# Retry dead letter queue jobs
POST /api/admin/queue/retry-dead-letter
```

### Health Check Response Example
```json
{
  "status": "healthy",
  "timestamp": "2024-08-26T15:00:00Z",
  "redis": {
    "connected": true,
    "sentinel": true,
    "rateLimiter": true
  },
  "queues": [
    {
      "name": "webhook-delivery",
      "waiting": 5,
      "active": 2,
      "completed": 1250,
      "failed": 3,
      "health": "healthy",
      "avgProcessingTime": 1500
    }
  ],
  "alerts": [],
  "deadLetterCount": 0
}
```

## 🔧 **Configuration**

### Environment Variables
```bash
# Redis Configuration
REDIS_URL=redis://:password@localhost:6379
REDIS_PASSWORD=your_redis_password

# Enable Redis Sentinel (production)
USE_REDIS_SENTINEL=true

# Queue Configuration  
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_TIMEOUT_MS=30000
```

### Docker Compose Services
- `redis-master` - Primary Redis instance
- `redis-replica` - Replica for failover
- `redis-sentinel` - Monitors and manages failover
- `redis-test` - Separate Redis for testing

## 🚨 **Alert Types**

### Critical Alerts
- **Redis connection errors** - Queue system unavailable
- **High failure rates** - >20% of jobs failing
- **Extremely high queue depth** - >2000 pending jobs

### Warning Alerts  
- **High queue depth** - >1000 pending jobs
- **Moderate failure rates** - >10% of jobs failing
- **Stalled jobs** - No processing for >5 minutes
- **Slow processing** - Average time >30 seconds

## 🔄 **Failover Process**

1. **Redis Master Fails**
   - Sentinel detects failure within 5 seconds
   - Promotes replica to new master
   - Updates application connections

2. **Application Response**
   - Automatic reconnection to new master
   - Queue processing continues seamlessly
   - No webhook deliveries lost

3. **Recovery**
   - Failed master can rejoin as replica
   - Automatic data synchronization
   - Full redundancy restored

## 📈 **Capacity Planning**

### Current Thresholds
- **Queue Depth Warning**: 1,000 jobs
- **Queue Depth Critical**: 2,000 jobs
- **Failure Rate Warning**: 10%
- **Failure Rate Critical**: 20%
- **Processing Time Warning**: 30 seconds

### Scaling Recommendations
- **<1,000 users**: Current setup sufficient
- **1,000-10,000 users**: Add more Redis replicas
- **>10,000 users**: Consider Redis Cluster mode

## 🛠️ **Operational Procedures**

### Daily Monitoring
1. Check `/api/admin/queue/health` endpoint
2. Review any critical/warning alerts
3. Monitor dead letter queue count

### Weekly Maintenance
1. Review queue performance metrics
2. Clean up old completed jobs if needed
3. Check Redis memory usage

### Emergency Procedures
1. **Queue System Down**: Webhooks use fallback delivery
2. **High Failure Rate**: Check webhook endpoints, review dead letter queue
3. **Redis Failover**: Monitor automatic recovery, verify data consistency

## 🔍 **Troubleshooting**

### Common Issues
- **High queue depth**: Check webhook endpoint availability
- **Redis connection errors**: Verify Redis Sentinel configuration
- **Stalled jobs**: Restart queue workers, check Redis memory

### Debug Commands
```bash
# Check Redis Sentinel status
redis-cli -p 26379 SENTINEL masters

# Monitor queue in real-time
redis-cli MONITOR

# Check queue job counts
redis-cli EVAL "return redis.call('zcard', 'bull:webhook-delivery:waiting')" 0
```

This enhanced queue system provides the reliability and observability needed for production-scale email webhook delivery.

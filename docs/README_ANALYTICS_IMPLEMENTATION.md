# Analytics Implementation - Plausible

## Overview

EmailConnect uses Plausible Analytics for privacy-focused user analytics. The implementation includes both pageview tracking and custom event tracking for key user actions.

## Current Setup

### Base Configuration
- **Script**: `script.hash.outbound-links.tagged-events.js` (loaded in `index.html`)
- **Domain**: `emailconnect.eu`
- **CSP**: Configured to allow `plausible.io` connections
- **Pageview Tracking**: Automatic SPA support via Vue Router

### Custom Event Tracking

#### Events Being Tracked

**Authentication Events:**
- `Login` - User login attempts with method (email/oauth)
- `Login Failed` - Failed login attempts with reason
- `Register` - User registration attempts
- `Register Failed` - Failed registration attempts
- `Logout` - User logout actions

**Domain Management:**
- `Domain Created` - New domain creation
- `Domain Verify` - Domain verification attempts (success/failed)
- `Domain Deleted` - Domain deletion

**Webhook Events:**
- `Webhook Created` - New webhook creation
- `Webhook Test` - Webhook test attempts (success/failed)
- `Webhook Deleted` - Webhook deletion

**Alias Events:**
- `Alias Created` - New alias creation
- `<PERSON>as Deleted` - Alias deletion

**Plan & Billing:**
- `Plan Upgrade` - Plan upgrades (from/to)
- `Plan Downgrade` - Plan downgrades
- `Payment Success` - Successful payments
- `Payment Failed` - Failed payments

**Settings & Features:**
- `Settings Updated` - Settings changes by section
- `Spam Filter Toggle` - Spam filter enable/disable
- `Help Article View` - Help documentation usage
- `Onboarding Complete` - Onboarding flow completion

## Implementation Details

### Composable: `usePlausible()`

Location: `/src/frontend/composables/usePlausible.ts`

```typescript
import { usePlausible } from '@/composables/usePlausible'

const { trackUserAction, trackEvent } = usePlausible()

// Track specific user actions
trackUserAction.login('email')
trackUserAction.domainCreate('example.com')

// Track custom events
trackEvent('Custom Event', { 
  props: { key: 'value' },
  callback: () => console.log('tracked')
})
```

### Integration Points

**Authentication:**
- `LoginPage.vue` - Login success/failure tracking
- `RegisterPage.vue` - Registration success/failure tracking

**API Layer:**
- `useApi.ts` - Domain and webhook API actions
- Automatic tracking for domain creation, verification, webhook testing

**Development Testing:**
- `PlausibleTest.vue` - Debug component for testing events (dev mode only)
- Console logging in development mode

## Configuration

### Environment Variables
- No additional env vars needed
- Uses existing Plausible script configuration

### Plausible Dashboard Setup
1. Enable "Custom events" in site settings
2. Create goals for tracked events:
   - Login, Register, Domain Created, etc.
3. Set up funnels for user journey analysis

## Privacy Considerations

- **No PII**: Domain names tracked, but no email addresses or sensitive data
- **GDPR Compliant**: Plausible is GDPR-compliant by design
- **No Cross-Site Tracking**: No cookies, no persistent user tracking
- **EU Hosted**: Plausible infrastructure is EU-based

## Monitoring & Debugging

**Development:**
- Console logs show all events: `[Plausible] Event Name`
- Test component available in dev mode
- Network tab shows requests to `plausible.io`

**Production:**
- Events appear in Plausible dashboard within minutes
- Goal conversions tracked for business metrics
- Real-time visitor analytics available

## Future Enhancements

- **Revenue Tracking**: Add revenue properties to payment events
- **Cohort Analysis**: Track user journey from registration to paid conversion
- **Feature Adoption**: Track usage of Pro features
- **Performance Metrics**: Track page load times and user engagement

---

**Note**: This implementation complements (not replaces) Bugfender for error tracking. Plausible handles user behavior analytics while Bugfender handles error monitoring.
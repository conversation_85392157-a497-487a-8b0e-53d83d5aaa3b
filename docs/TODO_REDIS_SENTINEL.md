# Redis Sentinel & Enhanced Queue Service - Lessons Learned

## Executive Summary

The enhanced queue service (`enhanced-queue.service.ts`) was created to add high-availability features including Redis Sentinel support, dead letter queues, and enhanced monitoring. However, it caused a critical production issue where webhooks appeared to be delivered but weren't actually sent.

## The Problem

### What Happened
1. **Duplicate Queue Processors**: Both `queue.ts` and `enhanced-queue.service.ts` were registering processors for the same Bull queue name (`webhook-delivery`)
2. **Stub Implementation**: The enhanced queue's webhook processor had a stub implementation that returned `{ status: 'delivered', timestamp: new Date() }` without actually sending HTTP requests
3. **Load Balancing Issue**: Bull distributed jobs between both processors, causing ~50% of webhooks to be "delivered" without actually being sent
4. **Misleading Status**: Jobs processed by the stub showed as "completed" in Redis, making the issue hard to detect

### Root Causes
1. **Premature Activation**: Enhanced queue service was initialized alongside the original queue service without proper feature flagging
2. **Incomplete Migration**: The processWebhookJob method was never fully implemented - it remained a stub
3. **Same Queue Name**: Using identical queue names caused <PERSON> to treat both processors as workers for the same queue
4. **Missing Environment Guards**: No environment variable to control which queue processor should be active

## What Didn't Work

### 1. Sentinel Configuration
```javascript
// This was attempted but Redis Sentinel wasn't actually running
this.sentinelClient = new Redis({
  sentinels: [
    { host: 'redis-sentinel', port: 26379 },
  ],
  name: 'emailconnect-master',
  // ...
});
```
**Issue**: No Redis Sentinel was deployed, causing connection failures

### 2. Parallel Queue Processing
Having two services process the same queue name led to unpredictable behavior:
- Jobs were randomly distributed between processors
- No way to ensure a specific processor handled specific jobs
- Difficult to debug which processor handled which job

### 3. Hot-swapping Queue Processors
Attempting to disable one processor while jobs were active caused:
- Jobs stuck in "active" state
- Required manual Redis operations to move jobs back to waiting queue
- Risk of job loss during transitions

## The Solution Applied

### 1. Feature Flag Implementation
```javascript
// Only enable if explicitly requested
const enableWebhookProcessor = process.env.ENHANCED_WEBHOOK_PROCESSOR === 'true';

if (enableWebhookProcessor) {
  logger.warn('ENHANCED_WEBHOOK_PROCESSOR is enabled - this may cause duplicate processing!');
  this.webhookQueue = new Bull('webhook-delivery', {...});
} else {
  logger.info('Enhanced webhook processor disabled (default) - using main queue.ts processor');
}
```

### 2. Defensive Queue Monitoring
```javascript
// Filter out undefined queues to prevent errors
const queues = [this.webhookQueue, this.attachmentUploadQueue, this.deadLetterQueue].filter(Boolean);
queues.forEach(queue => {
  // monitoring logic
});
```

## Correct Implementation Strategy

### Phase 1: Preparation
1. **Complete the Implementation**
   ```javascript
   private async processWebhookJob(job: Bull.Job<WebhookJobData>): Promise<any> {
     // Copy full implementation from queue.ts, not a stub
     const { webhookUrl, payload, webhookSecret, customHeaders } = job.data;
     
     // Full axios implementation with proper error handling
     const response = await axios.post(webhookUrl, payload, {
       headers: preparedHeaders,
       timeout: env.WEBHOOK_TIMEOUT_MS,
     });
     
     return { status: 'delivered', httpStatus: response.status };
   }
   ```

2. **Use Different Queue Names During Migration**
   ```javascript
   // During transition period
   this.webhookQueue = new Bull('webhook-delivery-v2', {
     // Enhanced configuration
   });
   ```

3. **Implement Proper Sentinel Support**
   ```yaml
   # docker-compose.prod.yml
   redis-sentinel:
     image: redis:7-alpine
     command: redis-sentinel /etc/redis-sentinel/sentinel.conf
     volumes:
       - ./redis-sentinel.conf:/etc/redis-sentinel/sentinel.conf
     networks:
       - app-network
   ```

### Phase 2: Migration Strategy

1. **Blue-Green Deployment**
   ```javascript
   // Environment variables for controlled rollout
   QUEUE_VERSION=v1  # or v2
   ENHANCED_FEATURES_ENABLED=false
   REDIS_SENTINEL_ENABLED=false
   DEAD_LETTER_QUEUE_ENABLED=false
   ```

2. **Gradual Feature Enablement**
   ```javascript
   class QueueManager {
     async initialize() {
       if (process.env.QUEUE_VERSION === 'v2') {
         return this.initializeEnhancedQueue();
       }
       return this.initializeLegacyQueue();
     }
   }
   ```

3. **Job Migration Tool**
   ```javascript
   // Script to migrate jobs between queue versions
   async function migrateJobs(fromQueue: string, toQueue: string) {
     const oldQueue = new Bull(fromQueue);
     const newQueue = new Bull(toQueue);
     
     const waiting = await oldQueue.getWaiting();
     for (const job of waiting) {
       await newQueue.add(job.data, job.opts);
       await job.remove();
     }
   }
   ```

### Phase 3: Monitoring & Validation

1. **Health Checks**
   ```javascript
   async getQueueHealth() {
     const health = {
       primary: await this.primaryQueue?.getJobCounts(),
       enhanced: await this.enhancedQueue?.getJobCounts(),
       deadLetter: await this.deadLetterQueue?.getJobCounts(),
       activeProcessor: process.env.QUEUE_VERSION,
     };
     return health;
   }
   ```

2. **Metrics Collection**
   ```javascript
   // Track which processor handles jobs
   queue.on('completed', (job) => {
     metrics.increment('queue.job.completed', {
       processor: 'enhanced',
       queue: job.queue.name,
     });
   });
   ```

## Best Practices Going Forward

### 1. Never Run Duplicate Processors
- Use unique queue names during migration
- Implement mutex/lock mechanisms if needed
- Clear feature flags to control which processor is active

### 2. Complete Before Deploy
- Never deploy stub implementations to production
- Use integration tests that verify actual HTTP calls
- Implement health checks that validate end-to-end delivery

### 3. Staged Rollout
```javascript
// Rollout stages
const ROLLOUT_STAGES = {
  DISABLED: 'disabled',        // Original queue only
  SHADOW: 'shadow',            // Both run, compare results
  CANARY: 'canary',           // Small % to enhanced
  GRADUAL: 'gradual',         // Increasing % to enhanced
  COMPLETE: 'complete',        // Enhanced only
};
```

### 4. Rollback Plan
- Keep original queue code intact during migration
- Document how to revert: environment variables, deployments
- Test rollback procedure in staging

### 5. Testing Strategy
```javascript
// Test to verify no duplicate processing
describe('Queue Processing', () => {
  it('should only process each job once', async () => {
    const jobId = await queueWebhookDelivery(...);
    
    // Wait for processing
    await sleep(5000);
    
    // Verify only one HTTP request was made
    expect(httpMock.calls()).toHaveLength(1);
    
    // Verify job wasn't processed twice
    const job = await queue.getJob(jobId);
    expect(job.finishedOn).toBeDefined();
    expect(job.processedOn).toBeDefined();
  });
});
```

## Environment Variables Required

```bash
# Enhanced Queue Configuration
ENHANCED_WEBHOOK_PROCESSOR=false       # Keep false until fully tested
USE_REDIS_SENTINEL=false              # Enable only when Sentinel is deployed
DEAD_LETTER_QUEUE_ENABLED=false       # Enable for production
QUEUE_MIGRATION_MODE=disabled         # disabled|shadow|canary|gradual|complete

# Redis Sentinel Configuration (when ready)
REDIS_SENTINEL_HOSTS=sentinel1:26379,sentinel2:26379,sentinel3:26379
REDIS_SENTINEL_MASTER_NAME=emailconnect-master
REDIS_SENTINEL_PASSWORD=${REDIS_PASSWORD}
```

## Conclusion

The enhanced queue service has valuable features (Sentinel support, dead letter queues, better monitoring) but needs careful implementation:

1. **Complete the implementation** - No stubs in production
2. **Use different queue names** - Prevent processor conflicts  
3. **Feature flag everything** - Controlled rollout and rollback
4. **Test the actual behavior** - Verify HTTP calls are made
5. **Monitor both systems** - Compare metrics during migration
6. **Have a rollback plan** - Quick reversion if issues arise

The issue was not with the enhanced queue concept but with its premature and incomplete deployment alongside the existing system.

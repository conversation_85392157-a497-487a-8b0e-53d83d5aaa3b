# Environment Variables

## Core
- NODE_ENV: development | test | production

## Redis
- REDIS_URL or REDIS_HOST_URL: connection string for Redis used by advanced processing and queues
- TEST_REDIS_URL: separate Redis instance for testing (default: redis://localhost:6380 without auth)

## Admin impersonation

## S3 / Storage
- AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION, AWS_S3_BUCKET
- S3_ATTACHMENT_EXPIRY_HOURS
- S3_MAX_FILE_SIZE_MB
- S3_PRESIGNED_URL_EXPIRY_SECONDS

## Testing
- DATABASE_URL: test database (postgresql://postgres:password@localhost:5432/eu_email_webhook_test)
- REDIS_URL: test Redis without authentication on port 6380
- WEBHOOKTEST_API_URL: external WebhookTest service URL for integration tests

Notes
- <PERSON> reads some values directly from process.env at call time to support tests that mutate env during runtime.
- Test environment uses separate Redis instance on port 6380 to avoid authentication issues


# Admin Impersonation

This document describes the current impersonation implementation using BetterAuth Admin plugin.

## Admin determination
- Admin access is determined solely by the user's role in the database: user.role === 'admin'.
- No environment flags are required for admin/impersonation.

## Endpoints
- POST /api/auth/admin/impersonate-user
  - Starts impersonation for the specified userId (requires admin role).
  - Requires session cookies; send JSON body: { "userId": "<target-user-id>" }.
- POST /api/auth/admin/stop-impostering
  - Stops the current impersonation session, restoring the original admin session.
  - Requires session cookies.
- GET /api/auth-status
  - Returns the current authenticated user context and session info.
  - Use to determine:
    - canImpersonate = (data.user.role === 'admin')
    - isImpersonating via session/session.impersonatedBy as exposed by the backend helper.

## Frontend usage
- To start impersonation: fetch('/api/auth/admin/impersonate-user', { method: 'POST', headers: { 'Content-Type': 'application/json' }, credentials: 'include', body: JSON.stringify({ userId }) })
- To stop impersonation: fetch('/api/auth/admin/stop-impostering', { method: 'POST', credentials: 'include' })
- To check admin/canImpersonate: fetch('/api/auth-status', { credentials: 'include' })

## Security & audit
- Admin-only actions require user.role === 'admin'.
- Audit logs (where implemented) capture admin actions.


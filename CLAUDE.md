# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an EU-compliant email-to-webhook service called "emailconnect.eu" that processes incoming emails and delivers them as JSON payloads to configured HTTP endpoints. The application is built for GDPR compliance with EU-only hosting requirements.

## Key Technologies

- **Backend**: Fastify + TypeScript + Prisma ORM + PostgreSQL
- **Frontend**: Vue 3 + Composition API + TypeScript + Tailwind CSS + DaisyUI
- **Queue System**: Bull (Redis-based) for webhook delivery
- **Email Processing**: Mailparser for parsing incoming emails
- **Infrastructure**: Docker Compose + GitHub Actions CI/CD
- **Authentication**: JWT-based with bcrypt password hashing

## Essential Development Commands

### Development
```bash
# Start development environment (both backend and frontend)
npm run dev

# Start only backend
npm run dev:backend

# Start only frontend  
npm run dev:frontend

# Start supporting services (PostgreSQL, Redis)
docker-compose up -d
```

### Testing
```bash
# Run all tests with PostgreSQL test database
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:webhook

# Watch mode for development
npm run test:watch

# Coverage reports
npm run test:coverage
```

### Build & Deployment
```bash
# Build both backend and frontend
npm run build

# Build backend only
npm run build:backend

# Build frontend only
npm run build:frontend

# Generate OpenAPI types from running server
npm run generate:types
```

### Code Quality
```bash
# Lint TypeScript files
npm run lint

# Auto-fix linting issues
npm run lint:fix
```

### Database Operations
```bash
# Apply pending migrations
npx prisma migrate dev

# Deploy migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

## Project Architecture

### Backend Structure (`src/backend/`)
- **index.ts**: Fastify server setup with all plugins and routes
- **routes/**: API route handlers organized by feature (auth, domains, webhooks, etc.)
- **controllers/**: Business logic separated from route handlers
- **services/**: Core business services (email parsing, queue management, billing, etc.)
- **middleware/**: Authentication, authorization, and plan limits
- **schemas/**: Zod validation schemas and OpenAPI documentation
- **lib/**: Shared utilities (database, auth, Swagger)

### Frontend Structure (`src/frontend/`)
- **Vue 3 SPA** with TypeScript and Composition API
- **components/**: Organized by feature (dashboard, auth, settings, etc.)
- **composables/**: Reusable Vue composition functions
- **layouts/**: Different layout components (AuthLayout, GuestLayout, UserLayout)
- **router.ts**: Vue Router configuration with dynamic imports

### Key Services
- **Queue System**: Redis-based Bull queues for reliable webhook delivery with retry logic
- **Email Parser**: Processes raw email data into structured JSON
- **DNS Verifier**: Validates domain ownership via DNS records
- **Billing System**: Credit-based usage tracking with Mollie payment integration
- **Postfix Manager**: Manages Postfix configuration files for mail server integration

### Database Schema
- **Multi-tenant architecture** with User → Domains → Aliases → Webhooks hierarchy
- **PostgreSQL primary database** for application data
- **Automatic data retention** (configurable per user/plan)
- **GDPR compliance** features built into data models

## Important Configuration

### Environment Files
- **`.env`**: Development environment variables
- **`.env.prod`**: Production environment variables
- **Key variables**: DATABASE_URL, REDIS_URL, JWT_SECRET, MOLLIE_API_KEY

### Testing Configuration
- **PostgreSQL test database required** (see DATABASE_URL in test script)
- **Jest configuration** supports both TypeScript and JavaScript
- **Coverage thresholds** set at 70% for all metrics
- **Integration tests** require running database and Redis

### Docker Development
```bash
# Start development databases
npm run docker:dev

# View logs
npm run docker:logs

# Clean up containers and volumes
npm run docker:clean
```

## GDPR & Security Considerations

- **No email content persistence**: Emails are processed and deleted according to retention policies
- **EU-only deployment**: Must be hosted on EU servers (currently Hetzner Germany)
- **Data retention**: Configurable automatic expiration of email data
- **Audit logging**: All data processing activities are logged
- **API key scoping**: Fine-grained permissions for API access

## Development Tips

- **TypeScript strict mode disabled** for faster development, but type safety still encouraged
- **ESLint configuration** with TypeScript and Prettier integration
- **Concurrent development**: Use `npm run dev` to run both backend and frontend simultaneously
- **API documentation**: Available at `/docs` when server is running (Swagger UI)
- **Database migrations**: Always create migrations for schema changes, never modify schema.prisma directly in production

## Common Patterns

- **Route handlers** typically delegate to controller methods
- **Controllers** use services for business logic
- **Zod schemas** provide both validation and OpenAPI documentation
- **Fastify plugins** used for reusable functionality
- **Vue Composition API** with TypeScript for type-safe frontend development
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model User {
  id                 String              @id @default(cuid())
  email              String              @unique
  password           String?
  name               String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  currentMonthEmails Int                 @default(0)
  lastUsageReset     DateTime            @default(now())
  planType           String              @default("free")
  organizationId     String?
  mollieCustomerId   String?
  trialStartedAt     DateTime?
  trialEndsAt        DateTime?
  trialActivatedBy   String?
  emailVerified      Boolean             @default(false)
  image              String?
  role               String              @default("user")
  twoFactorEnabled   Boolean             @default(false)
  accounts           Account[]
  apiKeys            ApiKey[]
  creditBatches      CreditBatch[]
  creditTransactions CreditTransaction[]
  domains            Domain[]
  emails             Email[]
  fileTypeRules      FileTypeRule[]
  invoices           Invoice[]
  notifications      Notification[]
  paymentMethods     PaymentMethod[]
  payments           Payment[]
  sessions           Session[]
  subscriptions      Subscription[]
  twoFactor          TwoFactor?
  settings           UserSettings?
  organization       Organization?       @relation(fields: [organizationId], references: [id])
  webhooks           Webhook[]

  @@map("users")
}

model Organization {
  id                 String   @id @default(cuid())
  name               String
  registrationNumber String?
  addressStreet      String?
  addressApartment   String?
  addressZipCode     String?
  addressCity        String?
  addressCountry     String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  users              User[]

  @@map("organizations")
}

model UserSettings {
  id                   String   @id @default(cuid())
  userId               String   @unique
  maxInlineSize        Float    @default(1.0)
  storageProvider      String   @default("default")
  s3Config             Json?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  dataRetentionHours   Int?
  allowFallbackStorage Boolean  @default(true)
  fallbackNotification Boolean  @default(true)
  fileTypeSettings     Json?
  useGravatar          Boolean  @default(false)
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}

model Webhook {
  id            String   @id @default(cuid())
  userId        String
  name          String
  url           String
  description   String?
  active        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  verified      Boolean  @default(false)
  webhookSecret String?
  customHeaders Json?
  aliases       Alias[]
  emails        Email[]
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("webhooks")
}

model Domain {
  id                       String             @id @default(cuid())
  userId                   String
  domain                   String             @unique
  active                   Boolean            @default(true)
  verified                 Boolean            @default(false)
  verificationStatus       VerificationStatus @default(PENDING)
  lastVerificationAttempt  DateTime?
  nextVerificationCheck    DateTime?
  verificationFailureCount Int                @default(0)
  verificationToken        String?
  createdAt                DateTime           @default(now())
  updatedAt                DateTime           @updatedAt
  configuration            Json?
  aliases                  Alias[]
  user                     User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  emails                   Email[]

  @@map("domains")
}

model Alias {
  id            String   @id @default(cuid())
  domainId      String
  webhookId     String
  email         String
  active        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  configuration Json?
  domain        Domain   @relation(fields: [domainId], references: [id], onDelete: Cascade)
  webhook       Webhook  @relation(fields: [webhookId], references: [id], onDelete: Cascade)
  emails        Email[]

  @@unique([email, domainId])
  @@map("aliases")
}

model Email {
  id               String           @id @default(cuid())
  domainId         String?
  messageId        String           @unique
  fromAddress      String
  toAddresses      String[]
  subject          String?
  deliveryStatus   DeliveryStatus   @default(PENDING)
  deliveryAttempts Int              @default(0)
  lastAttemptAt    DateTime?
  deliveredAt      DateTime?
  errorMessage     String?
  expiresAt        DateTime
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  isTestWebhook    Boolean          @default(false)
  webhookPayload   Json?
  userId           String?
  aliasId          String?
  httpStatus       Int?
  webhookId        String?
  hasAsyncUploads  Boolean          @default(false)
  attachments      AttachmentFile[]
  alias            Alias?           @relation(fields: [aliasId], references: [id])
  domain           Domain?          @relation(fields: [domainId], references: [id], onDelete: Cascade)
  user             User?            @relation(fields: [userId], references: [id], onDelete: Cascade)
  webhook          Webhook?         @relation(fields: [webhookId], references: [id])

  @@index([userId, createdAt])
  @@index([deliveryStatus, lastAttemptAt])
  @@index([expiresAt])
  @@map("emails")
}

model AuditLog {
  id           String   @id @default(cuid())
  action       String
  resourceId   String?
  resourceType String?
  metadata     Json?
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime @default(now())
  expiresAt    DateTime

  @@map("audit_logs")
}

model Subscription {
  id                   String             @id @default(cuid())
  mollieId             String?            @unique
  status               SubscriptionStatus @default(PENDING)
  planType             String
  interval             String
  amount               Decimal            @db.Decimal(10, 2)
  currency             String             @default("EUR")
  description          String?
  mollieCustomerId     String?
  molliePaymentMethod  String?
  startDate            DateTime?
  nextPaymentDate      DateTime?
  cancelledAt          DateTime?
  cancelReason         String?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  userId               String
  grandfatheredAt      DateTime?
  grandfatheredPrice   Decimal?           @db.Decimal(10, 2)
  grandfatheringReason String?
  isGrandfathered      Boolean            @default(false)
  payments             Payment[]
  user                 User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model Payment {
  id                  String        @id @default(cuid())
  mollieId            String        @unique
  status              PaymentStatus @default(PENDING)
  amount              Decimal       @db.Decimal(10, 2)
  currency            String        @default("EUR")
  description         String?
  method              String?
  paidAt              DateTime?
  cancelledAt         DateTime?
  expiredAt           DateTime?
  failedAt            DateTime?
  failureReason       String?
  mollieCustomerId    String?
  molliePaymentMethod String?
  mollieWebhookData   Json?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  userId              String?
  subscriptionId      String?
  creditBatches       CreditBatch[]
  invoices            Invoice[]
  subscription        Subscription? @relation(fields: [subscriptionId], references: [id])
  user                User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId, status])
  @@index([userId, createdAt])
  @@index([status, createdAt])
  @@map("payments")
}

model Invoice {
  id            String   @id @default(cuid())
  invoiceNumber String   @unique
  generatedAt   DateTime @default(now())
  paymentId     String
  userId        String?
  amount        Decimal  @db.Decimal(10, 2)
  currency      String   @default("EUR")
  description   String
  billingPeriod String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  s3Key         String?
  payment       Payment  @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  user          User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId, createdAt])
  @@map("invoices")
}

model PaymentMethod {
  id          String   @id @default(cuid())
  type        String
  description String?
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  mandateId   String   @unique
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isDefault])
  @@map("payment_methods")
}

model ApiKey {
  id          String    @id @default(cuid())
  name        String
  keyHash     String    @unique
  keyPrefix   String
  lastUsedAt  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  userId      String
  description String?
  scopes      Json      @default("[\"*\"]")
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model CreditBatch {
  id                String              @id @default(cuid())
  userId            String?
  amount            Int
  remainingAmount   Int
  purchasedAt       DateTime            @default(now())
  expiresAt         DateTime
  paymentId         String?
  isExpired         Boolean             @default(false)
  originalExpiresAt DateTime?
  extensionCount    Int                 @default(0)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  payment           Payment?            @relation(fields: [paymentId], references: [id])
  user              User?               @relation(fields: [userId], references: [id], onDelete: SetNull)
  transactions      CreditTransaction[]

  @@index([userId])
  @@index([expiresAt])
  @@index([isExpired])
  @@map("credit_batches")
}

model CreditTransaction {
  id          String       @id @default(cuid())
  userId      String?
  batchId     String?
  type        String
  amount      Int
  description String?
  createdAt   DateTime     @default(now())
  batch       CreditBatch? @relation(fields: [batchId], references: [id])
  user        User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([batchId])
  @@index([type])
  @@map("credit_transactions")
}

model Notification {
  id         String               @id @default(cuid())
  userId     String
  type       NotificationType
  title      String
  message    String
  category   NotificationCategory
  priority   NotificationPriority @default(MEDIUM)
  data       Json?
  actionUrl  String?
  actionText String?
  isRead     Boolean              @default(false)
  readAt     DateTime?
  createdAt  DateTime             @default(now())
  updatedAt  DateTime             @updatedAt
  expiresAt  DateTime?
  user       User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([userId, createdAt])
  @@map("notifications")
}

model AttachmentFile {
  id             String           @id @default(cuid())
  emailId        String
  messageId      String
  filename       String
  contentType    String
  size           Int
  s3Key          String?
  s3Config       Json?
  downloadUrl    String
  downloadCount  Int              @default(0)
  uploadStatus   AttachmentStatus @default(PENDING)
  processingType String
  expiresAt      DateTime
  customExpiry   Boolean          @default(false)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  lastDownloadAt DateTime?
  errorMessage   String?
  email          Email            @relation(fields: [emailId], references: [id], onDelete: Cascade)

  @@index([expiresAt])
  @@index([uploadStatus, createdAt])
  @@map("attachment_files")
}

model FileTypeRule {
  id              String   @id @default(cuid())
  userId          String
  category        String
  mimeTypes       String[]
  maxSizeMB       Float
  handling        String
  syncThresholdMB Float    @default(2.0)
  expirationHours Int?
  active          Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, category])
  @@map("file_type_rules")
}

/// Postfix virtual domains configuration for email routing
/// Controls spam filtering routing and thresholds
model PostfixVirtualDomain {
  domain             String                @id @db.VarChar(255)
  active             Boolean               @default(true)
  spamFiltering      Boolean               @default(false) @map("spam_filtering")
  createdAt          DateTime              @default(now()) @map("created_at")
  updatedAt          DateTime              @default(now()) @updatedAt @map("updated_at")
  advancedProcessing Boolean               @default(false) @map("advanced_processing")
  aliases            PostfixVirtualAlias[]

  @@index([domain, active], map: "idx_postfix_virtual_domains_active")
  @@index([domain, spamFiltering, active], map: "idx_postfix_virtual_domains_spam_filtering")
  @@index([domain, advancedProcessing, active], map: "idx_postfix_virtual_domains_advanced")
  @@map("postfix_virtual_domains")
}

/// Postfix virtual aliases configuration for email routing
/// Maps email addresses to processing destinations
model PostfixVirtualAlias {
  email     String               @id @db.VarChar(255)
  domain    String               @db.VarChar(255)
  active    Boolean              @default(true)
  createdAt DateTime             @default(now()) @map("created_at")
  updatedAt DateTime             @default(now()) @updatedAt @map("updated_at")
  domainRef PostfixVirtualDomain @relation(fields: [domain], references: [domain], onDelete: Cascade)

  @@index([email, active], map: "idx_postfix_virtual_aliases_active")
  @@index([domain], map: "idx_postfix_virtual_aliases_domain")
  @@index([email, active], map: "idx_postfix_virtual_aliases_lookup")
  @@index([domain, email, active], map: "idx_postfix_virtual_aliases_domain_join")
  @@index([email, active], map: "idx_postfix_virtual_aliases_email_active")
  @@map("postfix_virtual_aliases")
}

/// Session management for BetterAuth
model Session {
  id             String   @id @default(cuid())
  expiresAt      DateTime
  ipAddress      String?
  userAgent      String?
  userId         String
  createdAt      DateTime @default(now())
  token          String   @unique // BetterAuth needs unique constraint for session lookups
  updatedAt      DateTime @updatedAt
  impersonatedBy String?  // For BetterAuth admin impersonation - ID of the admin user
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("sessions")
}

/// OAuth and social login accounts
model Account {
  id                     String    @id @default(cuid())
  accountId              String
  providerId             String
  userId                 String
  accessToken            String?
  refreshToken           String?
  accessTokenExpiresAt   DateTime?
  refreshTokenExpiresAt  DateTime?
  scope                  String?
  idToken                String?
  password               String?   // Password for credential provider (BetterAuth)
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt
  user                   User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([providerId, accountId])
  @@index([userId])
  @@map("accounts")
}

/// Two-factor authentication settings
model TwoFactor {
  id          String   @id @default(cuid())
  userId      String   @unique
  secret      String
  backupCodes String
  enabled     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("two_factors")
}

/// Email verification tokens - BetterAuth expects this exact structure
model verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, value])
  @@index([identifier])
  @@map("verifications")
}

enum NotificationType {
  PLAN_LIMIT_REACHED
  PLAN_LIMIT_WARNING
  EMAIL_QUOTA_EXHAUSTED
  SYSTEM_ALERT
  FEATURE_ANNOUNCEMENT
  SECURITY_ALERT
  PAYMENT_REMINDER
  PAYMENT_FAILED
  PAYMENT_SUCCESS
  DOMAIN_VERIFIED
  DOMAIN_FAILED
  WEBHOOK_FAILED
  EMAIL_QUOTA_WARNING
  ATTACHMENT_REJECTED
}

enum NotificationCategory {
  BILLING
  SYSTEM
  SECURITY
  FEATURE
  DOMAIN
  WEBHOOK
  PAYMENT
  ATTACHMENT
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  RETRYING
  EXPIRED
  REJECTED
}

enum AttachmentStatus {
  PENDING
  UPLOADING
  COMPLETED
  FAILED
  EXPIRED
}

enum VerificationStatus {
  PENDING
  VERIFIED
  ACTIVE
  WARNING
  SUSPENDED
  FAILED
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  SUSPENDED
  CANCELLED
  COMPLETED
}

enum PaymentStatus {
  PENDING
  PAID
  CANCELLED
  EXPIRED
  FAILED
  AUTHORIZED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum BillingInterval {
  MONTHLY
  YEARLY
  ONE_TIME
}

-- Critical Performance Indexes Migration
-- Addresses Priority 3: Database Performance Nightmare
-- These indexes are essential for dashboard queries, webhook lookups, and admin operations

-- ========================================
-- 1. CRITICAL DASHBOARD QUERY INDEXES
-- ========================================

-- Index for dashboard email queries (most common query)
-- Supports: SELECT * FROM emails WHERE userId = ? ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "emails_userId_createdAt_desc_idx" 
ON "emails"("userId", "createdAt" DESC) 
WHERE "userId" IS NOT NULL;

-- Index for webhook message lookups (webhook delivery status checks)
-- Supports: SELECT * FROM emails WHERE messageId = ?
CREATE INDEX IF NOT EXISTS "emails_messageId_idx" 
ON "emails"("messageId") 
WHERE "messageId" IS NOT NULL;

-- ========================================
-- 2. ADMIN QUERY PERFORMANCE INDEXES
-- ========================================

-- Index for admin user queries (user management, search)
-- Supports: SELECT * FROM users WHERE email LIKE ? ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "users_email_createdAt_idx" 
ON "users"("email", "createdAt" DESC);

-- Index for admin user search by creation date
-- Supports: SELECT * FROM users ORDER BY createdAt DESC LIMIT ?
CREATE INDEX IF NOT EXISTS "users_createdAt_desc_idx" 
ON "users"("createdAt" DESC);

-- ========================================
-- 3. DOMAIN MANAGEMENT PERFORMANCE
-- ========================================

-- Index for domain management queries (most critical for email routing)
-- Supports: SELECT * FROM domains WHERE userId = ? AND verified = true
CREATE INDEX IF NOT EXISTS "domains_userId_verified_idx" 
ON "domains"("userId", "verified");

-- Index for domain verification status queries
-- Supports: SELECT * FROM domains WHERE verified = false ORDER BY createdAt
CREATE INDEX IF NOT EXISTS "domains_verified_createdAt_idx" 
ON "domains"("verified", "createdAt" DESC);

-- ========================================
-- 4. WEBHOOK ROUTING PERFORMANCE
-- ========================================

-- Index for active webhook routing (critical for email processing)
-- Supports: SELECT * FROM webhooks WHERE userId = ? AND active = true
CREATE INDEX IF NOT EXISTS "webhooks_userId_active_idx" 
ON "webhooks"("userId", "active");

-- Index for webhook management queries
-- Supports: SELECT * FROM webhooks WHERE active = true ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "webhooks_active_createdAt_idx" 
ON "webhooks"("active", "createdAt" DESC);

-- ========================================
-- 5. EMAIL PROCESSING PERFORMANCE
-- ========================================

-- Index for email processing by domain (email routing)
-- Supports: SELECT * FROM emails WHERE domainId = ? ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "emails_domainId_createdAt_idx" 
ON "emails"("domainId", "createdAt" DESC) 
WHERE "domainId" IS NOT NULL;

-- Index for failed email retry processing
-- Supports: SELECT * FROM emails WHERE deliveryStatus IN ('FAILED', 'RETRYING') AND lastAttemptAt < ?
CREATE INDEX IF NOT EXISTS "emails_retry_processing_idx" 
ON "emails"("deliveryStatus", "lastAttemptAt") 
WHERE "deliveryStatus" IN ('FAILED', 'RETRYING', 'PENDING');

-- ========================================
-- 6. ALIAS MANAGEMENT PERFORMANCE
-- ========================================

-- Index for alias queries by domain (alias management)
-- Supports: SELECT * FROM aliases WHERE domainId = ? ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "aliases_domainId_createdAt_idx" 
ON "aliases"("domainId", "createdAt" DESC);

-- Index for active alias lookups (email routing)
-- Supports: SELECT * FROM aliases WHERE email = ? AND active = true
CREATE INDEX IF NOT EXISTS "aliases_email_active_idx" 
ON "aliases"("email", "active");

-- ========================================
-- 7. SESSION AND AUTHENTICATION PERFORMANCE
-- ========================================

-- Index for session cleanup (BetterAuth)
-- Supports: DELETE FROM sessions WHERE expiresAt < ?
CREATE INDEX IF NOT EXISTS "sessions_expiresAt_idx" 
ON "sessions"("expiresAt");

-- Index for user session queries
-- Supports: SELECT * FROM sessions WHERE userId = ? AND expiresAt > ?
CREATE INDEX IF NOT EXISTS "sessions_userId_expiresAt_idx" 
ON "sessions"("userId", "expiresAt" DESC);

-- ========================================
-- 8. API KEY PERFORMANCE
-- ========================================

-- Index for API key lookups (authentication)
-- Supports: SELECT * FROM api_keys WHERE keyHash = ?
CREATE INDEX IF NOT EXISTS "api_keys_keyHash_idx" 
ON "api_keys"("keyHash");

-- Index for user API key management
-- Supports: SELECT * FROM api_keys WHERE userId = ? ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "api_keys_userId_createdAt_idx" 
ON "api_keys"("userId", "createdAt" DESC);

-- ========================================
-- 9. NOTIFICATION PERFORMANCE
-- ========================================

-- Index for unread notifications (dashboard)
-- Supports: SELECT * FROM notifications WHERE userId = ? AND isRead = false ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS "notifications_userId_unread_idx" 
ON "notifications"("userId", "isRead", "createdAt" DESC) 
WHERE "isRead" = false;

-- ========================================
-- 10. BILLING AND SUBSCRIPTION PERFORMANCE
-- ========================================

-- Index for active subscriptions (billing queries)
-- Supports: SELECT * FROM subscriptions WHERE userId = ? AND status = 'ACTIVE'
CREATE INDEX IF NOT EXISTS "subscriptions_userId_status_idx" 
ON "subscriptions"("userId", "status");

-- Index for payment processing
-- Supports: SELECT * FROM payments WHERE status = 'PENDING' ORDER BY createdAt
CREATE INDEX IF NOT EXISTS "payments_status_createdAt_idx" 
ON "payments"("status", "createdAt" DESC);

-- ========================================
-- PERFORMANCE ANALYSIS QUERIES
-- ========================================

-- Create a view for monitoring slow queries (PostgreSQL specific)
CREATE OR REPLACE VIEW "slow_query_analysis" AS
SELECT 
    schemaname,
    tablename,
    attname as column_name,
    n_distinct,
    correlation,
    most_common_vals::text as most_common_vals,
    most_common_freqs::text as most_common_freqs
FROM pg_stats 
WHERE schemaname = 'public' 
AND tablename IN ('emails', 'users', 'domains', 'webhooks', 'aliases')
ORDER BY tablename, attname;

-- Create a view for index usage monitoring
CREATE OR REPLACE VIEW "index_usage_stats" AS
SELECT 
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- ========================================
-- MIGRATION COMPLETION LOG
-- ========================================

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS "migration_log" (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    applied_at TIMESTAMP NOT NULL DEFAULT NOW(),
    description TEXT
);

-- Log the completion of this migration
INSERT INTO "migration_log" (migration_name, applied_at, description) 
VALUES (
    '20250826000000_critical_performance_indexes',
    NOW(),
    'Added critical performance indexes for dashboard queries, webhook lookups, admin operations, and email processing'
) ON CONFLICT DO NOTHING;

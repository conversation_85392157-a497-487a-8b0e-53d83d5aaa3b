-- Migration: Add PostgreSQL tables for Postfix virtual domains and aliases
-- This migration creates the tables needed for Postfix to query PostgreSQL directly
-- instead of using SQLite for virtual domain and alias resolution.

-- ========================================
-- Table: postfix_virtual_domains
-- ========================================
CREATE TABLE "postfix_virtual_domains" (
    "domain" VARCHAR(255) PRIMARY KEY,
    "destination" VARCHAR(255) NOT NULL DEFAULT 'process-email',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "spam_filtering" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- Table: postfix_virtual_aliases
-- ========================================
CREATE TABLE "postfix_virtual_aliases" (
    "email" VARCHAR(255) PRIMARY KEY,
    "destination" VARCHAR(255) NOT NULL,
    "domain" VARCHAR(255) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "postfix_virtual_aliases_domain_fkey"
        FOREIGN KEY ("domain") REFERENCES "postfix_virtual_domains"("domain")
        ON DELETE CASCADE ON UPDATE CASCADE
);

-- ========================================
-- Indexes for performance
-- ========================================

-- Indexes for postfix_virtual_domains
CREATE INDEX "idx_postfix_virtual_domains_active" 
    ON "postfix_virtual_domains"("domain", "active");

CREATE INDEX "idx_postfix_virtual_domains_spam_filtering" 
    ON "postfix_virtual_domains"("domain", "spam_filtering", "active");

-- Indexes for postfix_virtual_aliases
CREATE INDEX "idx_postfix_virtual_aliases_active"
    ON "postfix_virtual_aliases"("email", "active");

CREATE INDEX "idx_postfix_virtual_aliases_domain"
    ON "postfix_virtual_aliases"("domain");

CREATE INDEX "idx_postfix_virtual_aliases_email_active"
    ON "postfix_virtual_aliases"("email", "active");

CREATE INDEX "idx_postfix_virtual_aliases_domain_active"
    ON "postfix_virtual_aliases"("domain", "active");

-- Composite index for the main Postfix query (JOIN performance)
CREATE INDEX "idx_postfix_virtual_aliases_lookup"
    ON "postfix_virtual_aliases"("email", "active", "destination");

-- Index for JOIN between aliases and domains
CREATE INDEX "idx_postfix_virtual_aliases_domain_join"
    ON "postfix_virtual_aliases"("domain", "email", "active");

-- ========================================
-- Triggers for automatic timestamp updates
-- ========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_postfix_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for postfix_virtual_domains
CREATE TRIGGER update_postfix_virtual_domains_updated_at 
    BEFORE UPDATE ON "postfix_virtual_domains"
    FOR EACH ROW EXECUTE FUNCTION update_postfix_updated_at_column();

-- Trigger for postfix_virtual_aliases
CREATE TRIGGER update_postfix_virtual_aliases_updated_at 
    BEFORE UPDATE ON "postfix_virtual_aliases"
    FOR EACH ROW EXECUTE FUNCTION update_postfix_updated_at_column();

-- ========================================
-- Views for monitoring and administration
-- ========================================

-- View for monitoring spam filtering distribution
CREATE VIEW "postfix_spam_filtering_stats" AS
SELECT
    CASE
        WHEN spam_filtering = true THEN 'Pro+ (Spam Filtering)'
        ELSE 'Free (Direct Processing)'
    END as processing_type,
    COUNT(*) as domain_count,
    COUNT(CASE WHEN active = true THEN 1 END) as active_domains
FROM "postfix_virtual_domains"
GROUP BY spam_filtering;

-- View for domain summary with alias information
CREATE VIEW "postfix_domain_summary" AS
SELECT
    pvd.domain,
    pvd.active as domain_active,
    pvd.spam_filtering,
    COUNT(pva.email) as alias_count,
    COUNT(CASE WHEN pva.active = true THEN 1 END) as active_aliases,
    pvd.created_at as domain_created,
    MAX(pva.updated_at) as last_alias_update
FROM "postfix_virtual_domains" pvd
LEFT JOIN "postfix_virtual_aliases" pva ON pvd.domain = pva.domain
GROUP BY pvd.domain, pvd.active, pvd.spam_filtering, pvd.created_at;

-- ========================================
-- Comments for documentation
-- ========================================

COMMENT ON TABLE "postfix_virtual_domains" IS 'Virtual domains configuration for Postfix. Controls spam filtering routing and thresholds.';
COMMENT ON TABLE "postfix_virtual_aliases" IS 'Virtual aliases configuration for Postfix. Maps email addresses to processing destinations.';
COMMENT ON COLUMN "postfix_virtual_aliases"."destination" IS 'Legacy field for compatibility. Actual routing determined by domain spam_filtering setting.';

COMMENT ON COLUMN "postfix_virtual_domains"."spam_filtering" IS 'Enable/disable spam filtering routing. true = advanced-process-email, false = process-email';

-- Manual SQL improvements to run after Prisma migrations
-- These are non-breaking changes that can be applied immediately

-- 1. Add missing indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_payments_user_status ON payments("userId", status);
CREATE INDEX IF NOT EXISTS idx_payments_created ON payments("createdAt");
CREATE INDEX IF NOT EXISTS idx_emails_user_created ON emails("userId", "createdAt") WHERE "userId" IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_status ON subscriptions("userId", status);

-- 2. Add CHECK constraints for data integrity (if not already exists)
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_positive_amounts') THEN
        ALTER TABLE credit_batches 
        ADD CONSTRAINT check_positive_amounts 
        CHECK (amount > 0 AND "remainingAmount" >= 0 AND "remainingAmount" <= amount);
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_positive_subscription_amount') THEN
        ALTER TABLE subscriptions
        ADD CONSTRAINT check_positive_subscription_amount
        CHECK (amount > 0);
    END IF;
END $$;

DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_positive_payment_amount') THEN
        ALTER TABLE payments
        ADD CONSTRAINT check_positive_payment_amount
        CHECK (amount > 0);
    END IF;
END $$;

-- 5. Optimize the most common queries with composite indexes
CREATE INDEX IF NOT EXISTS idx_emails_domain_status_created 
ON emails("domainId", "deliveryStatus", "createdAt" DESC) 
WHERE "domainId" IS NOT NULL;

-- 6. Index for credit transaction queries
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_created 
ON credit_transactions("userId", "createdAt" DESC);

-- 7. Index for finding active subscriptions quickly
CREATE INDEX IF NOT EXISTS idx_subscriptions_active 
ON subscriptions("userId", status) 
WHERE status = 'ACTIVE';

-- 8. Index for webhook delivery queries
CREATE INDEX IF NOT EXISTS idx_emails_delivery_status 
ON emails("deliveryStatus", "lastAttemptAt") 
WHERE "deliveryStatus" IN ('PENDING', 'RETRYING');

-- 9. Partial index for non-expired credit batches
CREATE INDEX IF NOT EXISTS idx_credit_batches_active 
ON credit_batches("userId", "expiresAt") 
WHERE "isExpired" = false;

-- 10. Index for notification queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread 
ON notifications("userId", "createdAt" DESC) 
WHERE "isRead" = false;
/*
  Warnings:

  - You are about to drop the column `pdfPath` on the `invoices` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "BillingInterval" AS ENUM ('MONTHLY', 'YEARLY', 'ONE_TIME');

-- AlterTable
ALTER TABLE "invoices" DROP COLUMN "pdfPath";

-- CreateIndex
CREATE INDEX "emails_userId_createdAt_idx" ON "emails"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "emails_deliveryStatus_lastAttemptAt_idx" ON "emails"("deliveryStatus", "lastAttemptAt");

-- CreateIndex
CREATE INDEX "emails_expiresAt_idx" ON "emails"("expiresAt");

-- CreateIndex
CREATE INDEX "invoices_userId_createdAt_idx" ON "invoices"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "payments_userId_status_idx" ON "payments"("userId", "status");

-- CreateIndex
CREATE INDEX "payments_userId_createdAt_idx" ON "payments"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "payments_status_createdAt_idx" ON "payments"("status", "createdAt");

-- CreateEnum
CREATE TYPE "AttachmentStatus" AS ENUM ('PENDING', 'UPLOADING', 'COMPLETED', 'FAILED', 'EXPIRED');

-- AlterTable
ALTER TABLE "emails" ADD COLUMN     "hasAsyncUploads" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "attachment_files" (
    "id" TEXT NOT NULL,
    "emailId" TEXT NOT NULL,
    "messageId" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "contentType" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "s3Key" TEXT,
    "s3Config" JSONB,
    "downloadUrl" TEXT NOT NULL,
    "downloadCount" INTEGER NOT NULL DEFAULT 0,
    "uploadStatus" "AttachmentStatus" NOT NULL DEFAULT 'PENDING',
    "processingType" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "customExpiry" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastDownloadAt" TIMESTAMP(3),
    "errorMessage" TEXT,

    CONSTRAINT "attachment_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_type_rules" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "mimeTypes" TEXT[],
    "maxSizeMB" DOUBLE PRECISION NOT NULL,
    "handling" TEXT NOT NULL,
    "syncThresholdMB" DOUBLE PRECISION NOT NULL DEFAULT 2.0,
    "expirationHours" INTEGER,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "file_type_rules_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "attachment_files_expiresAt_idx" ON "attachment_files"("expiresAt");

-- CreateIndex
CREATE INDEX "attachment_files_uploadStatus_createdAt_idx" ON "attachment_files"("uploadStatus", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "file_type_rules_userId_category_key" ON "file_type_rules"("userId", "category");

-- AddForeignKey
ALTER TABLE "attachment_files" ADD CONSTRAINT "attachment_files_emailId_fkey" FOREIGN KEY ("emailId") REFERENCES "emails"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_type_rules" ADD CONSTRAINT "file_type_rules_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Remove unused destination columns from Postfix tables
-- These columns were legacy fields that are no longer needed

-- Drop indexes that reference the destination column
DROP INDEX IF EXISTS "idx_postfix_virtual_aliases_lookup";

-- Remove destination column from postfix_virtual_aliases
ALTER TABLE "postfix_virtual_aliases" DROP COLUMN IF EXISTS "destination";

-- Remove destination column from postfix_virtual_domains  
ALTER TABLE "postfix_virtual_domains" DROP COLUMN IF EXISTS "destination";

-- Recreate the lookup index without destination column
CREATE INDEX "idx_postfix_virtual_aliases_lookup"
    ON "postfix_virtual_aliases"("email", "active");

-- Update table comments
COMMENT ON TABLE "postfix_virtual_aliases" IS 'Virtual aliases configuration for Postfix. Contains catch-all aliases for domain routing.';

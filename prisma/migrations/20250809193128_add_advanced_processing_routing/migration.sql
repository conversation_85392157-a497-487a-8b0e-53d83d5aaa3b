-- AlterTable: Add advanced_processing column for improved routing
ALTER TABLE "postfix_virtual_domains" ADD COLUMN "advanced_processing" BOOLEAN NOT NULL DEFAULT false;

-- CreateIndex: Optimize lookups for advanced processing routing
CREATE INDEX "idx_postfix_virtual_domains_advanced" ON "postfix_virtual_domains"("domain", "advanced_processing", "active");

-- Backfill Phase 1: Copy spam_filtering to advanced_processing
UPDATE "postfix_virtual_domains" 
SET "advanced_processing" = "spam_filtering"
WHERE "spam_filtering" = true;

-- Backfill Phase 2: Enable advanced_processing for domains with storage aliases
UPDATE "postfix_virtual_domains" pd
SET "advanced_processing" = true,
    "updated_at" = NOW()
WHERE EXISTS (
    SELECT 1
    FROM "domains" d
    JOIN "aliases" a ON a."domainId" = d."id"
    WHERE d."domain" = pd."domain"
    AND d."domain" != 'user.emailconnect.eu'  -- Exclude system domain
    AND a."active" = true
    AND a."configuration"::jsonb->>'allowAttachments' = 'true'
    AND a."configuration"::jsonb->>'attachmentHandling' = 'storage'
);

-- Backfill Phase 3: Ensure system domain always uses basic processing
UPDATE "postfix_virtual_domains" 
SET "advanced_processing" = false,
    "updated_at" = NOW()
WHERE "domain" = 'user.emailconnect.eu';

-- Create function to recompute advanced_processing when needed
CREATE OR REPLACE FUNCTION recompute_advanced_processing()
RETURNS TRIGGER AS $$
DECLARE
    v_domain_name TEXT;
    v_has_spam_filtering BOOLEAN;
    v_has_storage_aliases BOOLEAN;
    v_should_use_advanced BOOLEAN;
BEGIN
    -- Get the domain name based on the trigger context
    IF TG_TABLE_NAME = 'aliases' THEN
        -- For alias changes, get domain from the relationship
        SELECT d.domain INTO v_domain_name
        FROM domains d
        WHERE d.id = COALESCE(NEW."domainId", OLD."domainId");
    ELSIF TG_TABLE_NAME = 'domains' THEN
        -- For domain changes, use the domain directly
        v_domain_name := COALESCE(NEW.domain, OLD.domain);
    END IF;

    -- Skip if domain not found
    IF v_domain_name IS NULL THEN
        RETURN NEW;
    END IF;

    -- Special handling for system domain - always use basic processing (no S3 storage allowed)
    IF v_domain_name = 'user.emailconnect.eu' THEN
        UPDATE postfix_virtual_domains
        SET advanced_processing = false,
            updated_at = NOW()
        WHERE domain = 'user.emailconnect.eu'
        AND advanced_processing != false;
        RETURN NEW;
    END IF;

    -- Check if domain has spam filtering enabled
    SELECT COALESCE(
        (d.configuration::jsonb->'spamFiltering'->>'enabled')::boolean,
        false
    ) INTO v_has_spam_filtering
    FROM domains d
    WHERE d.domain = v_domain_name;

    -- Check if any active alias requires storage
    SELECT EXISTS (
        SELECT 1
        FROM domains d
        JOIN aliases a ON a."domainId" = d.id
        WHERE d.domain = v_domain_name
        AND a.active = true
        AND a.configuration::jsonb->>'allowAttachments' = 'true'
        AND a.configuration::jsonb->>'attachmentHandling' = 'storage'
    ) INTO v_has_storage_aliases;

    -- Compute whether advanced processing is needed
    v_should_use_advanced := v_has_spam_filtering OR v_has_storage_aliases;

    -- Update postfix_virtual_domains if needed
    UPDATE postfix_virtual_domains
    SET advanced_processing = v_should_use_advanced,
        updated_at = NOW()
    WHERE domain = v_domain_name
    AND advanced_processing != v_should_use_advanced;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers on aliases table for storage configuration changes
CREATE TRIGGER trigger_recompute_advanced_on_alias_insert
    AFTER INSERT ON aliases
    FOR EACH ROW
    WHEN (NEW.configuration IS NOT NULL)
    EXECUTE FUNCTION recompute_advanced_processing();

CREATE TRIGGER trigger_recompute_advanced_on_alias_update
    AFTER UPDATE ON aliases
    FOR EACH ROW
    WHEN (
        OLD.configuration IS DISTINCT FROM NEW.configuration
        OR OLD.active IS DISTINCT FROM NEW.active
    )
    EXECUTE FUNCTION recompute_advanced_processing();

CREATE TRIGGER trigger_recompute_advanced_on_alias_delete
    AFTER DELETE ON aliases
    FOR EACH ROW
    EXECUTE FUNCTION recompute_advanced_processing();

-- Create trigger on domains table for spam filtering changes
CREATE TRIGGER trigger_recompute_advanced_on_domain_update
    AFTER UPDATE ON domains
    FOR EACH ROW
    WHEN (OLD.configuration IS DISTINCT FROM NEW.configuration)
    EXECUTE FUNCTION recompute_advanced_processing();

-- Verify the backfill (optional diagnostic query, commented out)
-- SELECT 
--     pd.domain,
--     pd.spam_filtering,
--     pd.advanced_processing,
--     bool_or(
--         a.configuration::jsonb->>'attachmentHandling' = 'storage'
--     ) as has_storage_aliases,
--     (d.configuration::jsonb->'spamFiltering'->>'enabled')::boolean as spam_enabled
-- FROM postfix_virtual_domains pd
-- LEFT JOIN domains d ON d.domain = pd.domain
-- LEFT JOIN aliases a ON a."domainId" = d.id AND a.active = true
-- GROUP BY pd.domain, pd.spam_filtering, pd.advanced_processing, d.configuration;

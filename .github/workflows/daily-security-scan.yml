name: Daily Security Scan

on:
  schedule:
    - cron: '0 2 * * *'  # daily at 02:00 UTC
  workflow_dispatch:

jobs:
  npm-security-audit:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: |
          npm audit --audit-level=moderate || true
          npm audit --json > npm-audit.json || true

      - name: Check for critical vulnerabilities
        run: |
          if npm audit --audit-level=critical; then
            echo "✅ No critical vulnerabilities found"
          else
            echo "❌ Critical vulnerabilities detected"
            npm audit --audit-level=critical
            exit 1
          fi

  trivy-docker-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: read
      security-events: write
      actions: read  # Required for workflow run information
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build production image (no push)
        run: docker build -t emailconnect:ci-scan .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'emailconnect:ci-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'

      - name: Upload Trivy SARIF
        uses: github/codeql-action/upload-sarif@v3
        if: always()  # Upload even if previous steps fail
        continue-on-error: true  # Don't fail the workflow if upload fails
        with:
          sarif_file: 'trivy-results.sarif'
          category: 'trivy-docker'


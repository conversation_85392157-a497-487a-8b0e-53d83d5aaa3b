# Multi-stage Docker build for optimal production deployment
# Stage 1: Build stage
FROM node:22-alpine AS builder

WORKDIR /app

# Install necessary system dependencies for Prisma and building
RUN apk add --no-cache \
    openssl \
    ca-certificates \
    wget \
    libc6-compat

# Install dockerize for better dependency management
ARG TARGETARCH
ENV DOCKERIZE_VERSION=v0.7.0
RUN wget --progress=dot:giga https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && tar -C /usr/local/bin -xzvf dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && rm dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz

# Install dependencies for building (including devDependencies)
COPY package*.json ./
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Generate Prisma client BEFORE building (needed for TypeScript compilation)
RUN npx prisma generate

# Build arguments for Bugsink error tracking
ARG VITE_SENTRY_ENABLED
ARG VITE_SENTRY_DSN
ARG VITE_SENTRY_TRACES_SAMPLE_RATE
ARG VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE
ARG VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE

# Set environment variables for Vite build
ENV VITE_SENTRY_ENABLED=${VITE_SENTRY_ENABLED}
ENV VITE_SENTRY_DSN=${VITE_SENTRY_DSN}
ENV VITE_SENTRY_TRACES_SAMPLE_RATE=${VITE_SENTRY_TRACES_SAMPLE_RATE}
ENV VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE=${VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE}
ENV VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE=${VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE}

# Build the application (npm run build will read from .env file if present)
RUN rm -rf dist && npm run build

# Stage 2: Production stage  
FROM node:22-alpine AS production

# Install necessary system dependencies including curl for health checks
RUN apk add --no-cache \
    openssl \
    ca-certificates \
    curl \
    wget \
    libc6-compat

# Install dockerize for better dependency management
ARG TARGETARCH
ENV DOCKERIZE_VERSION=v0.7.0
RUN wget --progress=dot:giga https://github.com/jwilder/dockerize/releases/download/${DOCKERIZE_VERSION}/dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && tar -C /usr/local/bin -xzvf dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz \
    && rm dockerize-linux-${TARGETARCH:-amd64}-${DOCKERIZE_VERSION}.tar.gz

WORKDIR /app

# Create non-root user (Alpine already has node user)
# Using the built-in node user from Alpine

# Copy package files and install production dependencies only
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/public ./public
COPY --from=builder /app/content ./content

# Copy any additional necessary files
COPY --from=builder /app/package.json ./package.json

# Change ownership to node user
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Default command
CMD ["npm", "start"]
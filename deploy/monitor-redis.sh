#!/bin/bash

# Redis Monitoring Script
# Monitors the health and status of Red<PERSON> (Standard or Sentinel mode)
# Works with the infrastructure-manager.sh deployment

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

clear
echo -e "${BLUE}═════════════════════════════════════════════════════════${NC}"
echo -e "${BLUE}              Redis Monitoring Dashboard               ${NC}"
echo -e "${BLUE}═════════════════════════════════════════════════════════${NC}"

# Function to check if container is running
check_container() {
    if docker ps --format '{{.Names}}' | grep -q "^$1$"; then
        return 0
    else
        return 1
    fi
}

# Function to get Redis info
get_redis_info() {
    local container=$1
    
    # Try to find password from various sources
    local password=""
    if [ -f ".env.prod" ]; then
        password=$(grep "^REDIS_PASSWORD=" .env.prod 2>/dev/null | head -1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    fi
    if [ -z "$password" ] && [ -f ".env" ]; then
        password=$(grep "^REDIS_PASSWORD=" .env 2>/dev/null | head -1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    fi
    
    if check_container "$container"; then
        local role=""
        local connected_slaves=""
        
        # Try with password first if we have one
        if [ -n "$password" ]; then
            role=$(docker exec "$container" sh -c "redis-cli -a '$password' info replication 2>/dev/null" | grep "role:" | cut -d':' -f2 | sed 's/\r//g' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            connected_slaves=$(docker exec "$container" sh -c "redis-cli -a '$password' info replication 2>/dev/null" | grep "connected_slaves:" | cut -d':' -f2 | sed 's/\r//g' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        fi
        
        # If no password or auth failed, try without password
        if [ -z "$role" ]; then
            role=$(docker exec "$container" redis-cli info replication 2>/dev/null | grep "role:" | cut -d':' -f2 | tr -d '\r')
            connected_slaves=$(docker exec "$container" redis-cli info replication 2>/dev/null | grep "connected_slaves:" | cut -d':' -f2 | tr -d '\r')
        fi
        
        echo "$role|$connected_slaves"
    else
        echo "down|0"
    fi
}

# Detect Redis mode
USE_SENTINEL=false
if check_container "emailconnect-sentinel-1" || check_container "emailconnect-sentinel-2" || check_container "emailconnect-sentinel-3"; then
    USE_SENTINEL=true
fi

# Check Redis instances based on mode
if [ "$USE_SENTINEL" = true ]; then
    echo -e "\n${YELLOW}Redis Mode: Sentinel (High Availability)${NC}"
    echo "────────────────────────────────────────"
    
    # Check Redis Master
    master_info=$(get_redis_info "emailconnect-redis-master")
    master_role=$(echo "$master_info" | cut -d'|' -f1)
    master_slaves=$(echo "$master_info" | cut -d'|' -f2)
    
    if [ "$master_role" = "master" ]; then
        echo -e "Master: ${GREEN}● UP${NC} (Role: $master_role, Connected slaves: $master_slaves)"
    elif [ "$master_role" = "slave" ]; then
        echo -e "Master: ${YELLOW}● UP${NC} (Role: $master_role - Acting as replica after failover)"
    else
        echo -e "Master: ${RED}● DOWN${NC}"
    fi
    
    # Check Redis Replica
    replica_info=$(get_redis_info "emailconnect-redis-replica")
    replica_role=$(echo "$replica_info" | cut -d'|' -f1)
    
    if [ "$replica_role" = "slave" ]; then
        echo -e "Replica: ${GREEN}● UP${NC} (Role: $replica_role)"
    elif [ "$replica_role" = "master" ]; then
        echo -e "Replica: ${GREEN}● UP${NC} (Role: $replica_role - Promoted to master)"
    else
        echo -e "Replica: ${RED}● DOWN${NC}"
    fi
    
    # Check Sentinels
    echo -e "\n${YELLOW}Sentinel Instances:${NC}"
    echo "────────────────────────────────────────"
else
    echo -e "\n${YELLOW}Redis Mode: Standard (Single Instance)${NC}"
    echo "────────────────────────────────────────"
    
    # Check standard Redis container
    if check_container "emailconnect-app-redis-1"; then
        redis_info=$(get_redis_info "emailconnect-app-redis-1")
        role=$(echo "$redis_info" | cut -d'|' -f1)
        
        if [ "$role" = "master" ]; then
            echo -e "Redis: ${GREEN}● UP${NC} (Role: $role)"
        elif [ -n "$role" ]; then
            echo -e "Redis: ${YELLOW}● UP${NC} (Unexpected role: $role)"
        else
            echo -e "Redis: ${YELLOW}● UP${NC} (Unable to determine role)"
        fi
    else
        echo -e "Redis: ${RED}● DOWN${NC}"
    fi
fi

if [ "$USE_SENTINEL" = true ]; then
    # Only check sentinels if in Sentinel mode
    for i in 1 2 3; do
        if check_container "emailconnect-sentinel-$i"; then
            # Get master info from sentinel
            master_status=$(docker exec "emailconnect-sentinel-$i" redis-cli -p 26379 sentinel masters 2>/dev/null | head -20)
            if [ -n "$master_status" ]; then
                echo -e "Sentinel $i: ${GREEN}● UP${NC}"
            else
                echo -e "Sentinel $i: ${YELLOW}● UP${NC} (No master info)"
            fi
        else
            echo -e "Sentinel $i: ${RED}● DOWN${NC}"
        fi
    done
    
    # Get detailed Sentinel information from first available sentinel
    echo -e "\n${YELLOW}Sentinel Master Information:${NC}"
    echo "────────────────────────────────────────"
    
    for i in 1 2 3; do
        if check_container "emailconnect-sentinel-$i"; then
            # Extract key information from sentinel
            master_info=$(docker exec "emailconnect-sentinel-$i" redis-cli -p 26379 sentinel masters 2>/dev/null)
            
            if [ -n "$master_info" ]; then
                # Parse the response (it comes as a flat list)
                master_name=$(echo "$master_info" | grep -A1 "^name$" | tail -1)
                master_ip=$(echo "$master_info" | grep -A1 "^ip$" | tail -1)
                master_port=$(echo "$master_info" | grep -A1 "^port$" | tail -1)
                num_slaves=$(echo "$master_info" | grep -A1 "^num-slaves$" | tail -1)
                num_sentinels=$(echo "$master_info" | grep -A1 "^num-other-sentinels$" | tail -1)
                quorum=$(echo "$master_info" | grep -A1 "^quorum$" | tail -1)
                
                echo "Master Name: $master_name"
                echo "Master Address: $master_ip:$master_port"
                echo "Number of Slaves: $num_slaves"
                echo "Number of Sentinels: $((num_sentinels + 1))"
                echo "Quorum: $quorum"
                break
            fi
        fi
    done
fi

# Check Application Connection
echo -e "\n${YELLOW}Application Status:${NC}"
echo "────────────────────────────────────────"

if check_container "emailconnect-app-app-1"; then
    app_health=$(docker inspect emailconnect-app-app-1 --format='{{.State.Health.Status}}' 2>/dev/null)
    if [ "$app_health" = "healthy" ]; then
        echo -e "Application: ${GREEN}● HEALTHY${NC}"
        
        # Check if using Sentinel
        use_sentinel=$(docker exec emailconnect-app-app-1 sh -c 'echo $USE_REDIS_SENTINEL' 2>/dev/null)
        if [ "$use_sentinel" = "true" ]; then
            echo -e "Redis Mode: ${GREEN}Sentinel (HA)${NC}"
        else
            echo -e "Redis Mode: ${YELLOW}Direct Connection${NC}"
        fi
        
        # Check recent Redis errors in app logs
        recent_errors=$(docker logs emailconnect-app-app-1 --since 5m 2>&1 | grep -c "Redis.*error" || true)
        if [ "$recent_errors" -gt 0 ]; then
            echo -e "Recent Redis Errors: ${YELLOW}$recent_errors errors in last 5 minutes${NC}"
        else
            echo -e "Recent Redis Errors: ${GREEN}None${NC}"
        fi
    else
        echo -e "Application: ${YELLOW}● $app_health${NC}"
    fi
else
    echo -e "Application: ${RED}● DOWN${NC}"
fi

# Memory Usage
echo -e "\n${YELLOW}Memory Usage:${NC}"
echo "────────────────────────────────────────"

# Determine which Redis container to check for memory
if [ "$USE_SENTINEL" = true ]; then
    REDIS_CONTAINER="emailconnect-redis-master"
else
    REDIS_CONTAINER="emailconnect-app-redis-1"
fi

if check_container "$REDIS_CONTAINER"; then
    # Try to find password from various sources
    password=""
    if [ -f ".env.prod" ]; then
        password=$(grep "^REDIS_PASSWORD=" .env.prod 2>/dev/null | head -1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    fi
    if [ -z "$password" ] && [ -f ".env" ]; then
        password=$(grep "^REDIS_PASSWORD=" .env 2>/dev/null | head -1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    fi
    
    memory_info=""
    max_memory=""
    
    # Try with password first if we have one
    if [ -n "$password" ]; then
        memory_info=$(docker exec "$REDIS_CONTAINER" sh -c "redis-cli -a '$password' info memory 2>/dev/null" | grep "used_memory_human:" | cut -d':' -f2 | sed 's/\r//g' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        max_memory=$(docker exec "$REDIS_CONTAINER" sh -c "redis-cli -a '$password' config get maxmemory 2>/dev/null" | tail -1)
    fi
    
    # If no password or auth failed, try without password
    if [ -z "$memory_info" ]; then
        memory_info=$(docker exec "$REDIS_CONTAINER" redis-cli info memory 2>/dev/null | grep "used_memory_human:" | cut -d':' -f2 | tr -d '\r')
        max_memory=$(docker exec "$REDIS_CONTAINER" redis-cli config get maxmemory 2>/dev/null | tail -1)
    fi
    
    if [ -n "$memory_info" ]; then
        echo "Used Memory: $memory_info"
        if [ "$max_memory" != "0" ] && [ -n "$max_memory" ]; then
            echo "Max Memory: $((max_memory / 1024 / 1024))MB"
        else
            echo "Max Memory: Unlimited"
        fi
    else
        echo "Unable to retrieve memory information"
    fi
else
    echo "Redis container not running"
fi

# Show recent events
echo -e "\n${YELLOW}Recent Events:${NC}"
echo "────────────────────────────────────────"

if [ "$USE_SENTINEL" = true ]; then
    # Show failover events for Sentinel mode
    for i in 1 2 3; do
        if check_container "emailconnect-sentinel-$i"; then
            events=$(docker logs emailconnect-sentinel-$i --since 1h 2>&1 | grep -E "\+switch-master|\+failover" | tail -3)
            if [ -n "$events" ]; then
                echo "Recent failover events:"
                echo "$events"
            else
                echo "No recent failover events (last hour)"
            fi
            break
        fi
    done
else
    # Show Redis events for standard mode
    if check_container "emailconnect-app-redis-1"; then
        events=$(docker logs emailconnect-app-redis-1 --since 15m 2>&1 | grep -E "save|Background|persistence" | tail -3)
        if [ -n "$events" ]; then
            echo "Recent persistence events:"
            echo "$events"
        else
            echo "No recent persistence events (last 15 minutes)"
        fi
    else
        echo "Redis container not running"
    fi
fi

echo -e "\n${BLUE}═══════════════════════════════════════════════════════${NC}"
echo "Last updated: $(date '+%Y-%m-%d %H:%M:%S')"
echo "Press Ctrl+C to exit"

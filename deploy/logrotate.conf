# Logrotate configuration for EmailConnect
# Place this file in /etc/logrotate.d/emailconnect or run with:
# logrotate -f /path/to/this/logrotate.conf

/var/log/emailconnect/*.log {
    # Rotate logs daily
    daily
    
    # Keep 30 days of logs
    rotate 30
    
    # Compress rotated logs (except the most recent)
    compress
    delaycompress
    
    # Don't rotate if file is empty
    notifempty
    
    # Create new log files with specific permissions
    create 0640 www-data www-data
    
    # Truncate the original log file after copying
    copytruncate
    
    # Maximum file size before forced rotation
    maxsize 100M
    
    # Pattern for dated archives
    dateext
    dateformat -%Y%m%d
    
    # Run post-rotation script once for all logs
    sharedscripts
    
    # Post-rotation: Send SIGUSR1 to Node.js process to reopen logs
    postrotate
        # Find the Node.js process and send signal to reopen logs
        if [ -f /var/run/emailconnect.pid ]; then
            kill -USR1 $(cat /var/run/emailconnect.pid) 2>/dev/null || true
        fi
        
        # Alternative: If using Docker, you can use:
        # docker exec emailconnect-app kill -USR1 1 2>/dev/null || true
    endscript
}
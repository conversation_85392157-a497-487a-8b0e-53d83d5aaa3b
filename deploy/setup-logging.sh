#!/bin/bash

# Setup logging for EmailConnect
# This script configures log rotation and creates necessary directories

set -e

echo "📝 Setting up EmailConnect logging..."

# Create logs directory if it doesn't exist
LOG_DIR="/var/log/emailconnect"
if [ ! -d "$LOG_DIR" ]; then
    echo "Creating log directory: $LOG_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo chown -R $(whoami):$(whoami) "$LOG_DIR"
fi

# Install logrotate if not present
if ! command -v logrotate &> /dev/null; then
    echo "Installing logrotate..."
    sudo apt-get update && sudo apt-get install -y logrotate
fi

# Copy logrotate configuration
LOGROTATE_CONFIG="/etc/logrotate.d/emailconnect"
if [ -f "logrotate.conf" ]; then
    echo "Installing logrotate configuration..."
    sudo cp logrotate.conf "$LOGROTATE_CONFIG"
    sudo chmod 644 "$LOGROTATE_CONFIG"
    echo "✅ Logrotate configured at $LOGROTATE_CONFIG"
fi

# Create a systemd service file for better process management (optional)
if [ "$1" == "--systemd" ]; then
    echo "Creating systemd service..."
    cat << 'EOF' | sudo tee /etc/systemd/system/emailconnect.service
[Unit]
Description=EmailConnect Application
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/app
ExecStart=/usr/bin/node /app/dist/backend/index.js
Restart=on-failure
RestartSec=10

# Logging
StandardOutput=append:/var/log/emailconnect/app.log
StandardError=append:/var/log/emailconnect/error.log

# Environment
Environment="NODE_ENV=production"
EnvironmentFile=/app/.env.prod

# Process management
PIDFile=/var/run/emailconnect.pid
KillMode=mixed
KillSignal=SIGTERM

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    echo "✅ Systemd service created"
fi

# Test logrotate configuration
echo "Testing logrotate configuration..."
sudo logrotate -d "$LOGROTATE_CONFIG" 2>&1 | head -20

echo ""
echo "✅ Logging setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Logs will be written to: $LOG_DIR"
echo "2. Logs will rotate daily and keep 30 days of history"
echo "3. To manually rotate logs: sudo logrotate -f $LOGROTATE_CONFIG"
echo "4. To view logs: tail -f $LOG_DIR/app.log"
echo ""
echo "For Docker deployments, use:"
echo "  docker-compose -f docker-compose.yml -f docker-compose.logging.yml up"
echo ""
echo "For future monitoring options:"
echo "  - Cloud: Better Stack (Logtail), Axiom, or Grafana Cloud"
echo "  - Self-hosted: Loki + Grafana (config included in docker-compose.logging.yml)"
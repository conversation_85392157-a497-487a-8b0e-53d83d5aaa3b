# Deployment & Operations Scripts

This directory contains scripts for deploying, validating, and monitoring the EmailConnect application in production.

## Deployment Scripts

### setup-server.sh
Initial server setup script that configures a fresh Ubuntu server with all required dependencies and services.

### blue-green-deploy.sh
Blue-green deployment strategy script for zero-downtime deployments with instant rollback capability.

### zero-downtime-deploy.sh  
Rolling deployment script that updates the application without any service interruption.

### validate-deployment.sh
Comprehensive validation script that checks all aspects of a deployment (services, connectivity, health checks).

### validate-server.sh
Server-level validation to ensure the infrastructure is properly configured.

### verify-deployment.js
Node.js script for testing the application's webhook functionality after deployment.

## Monitoring Scripts

### monitor-redis.sh
Real-time Redis monitoring dashboard that shows:
- Redis instance status and role
- Memory usage statistics
- Application connection health
- Recent events and errors

Usage: `./deploy/monitor-redis.sh`

## Postfix Configuration

### postfix/
Directory containing Postfix mail server configuration templates for email processing.

## Usage Examples

```bash
# Initial server setup
./deploy/setup-server.sh

# Deploy with zero downtime
./deploy/zero-downtime-deploy.sh

# Validate deployment
./deploy/validate-deployment.sh

# Monitor Redis
./deploy/monitor-redis.sh

# Test webhook functionality
node ./deploy/verify-deployment.js
```

## Notes

- All deployment scripts should be run from the project root directory
- Ensure proper environment variables are set before running deployment scripts
- The monitoring scripts can be run at any time to check system health

## Email Processing Environment Setup

The advanced email processing script (`advanced-process-email.js`) requires access to production environment variables for:
- **Redis authentication**: Connects to Redis for domain-specific spam filtering thresholds
- **Database access**: Fetches domain configuration and user settings

The `setup-server.sh` script automatically creates `advanced-process-email-wrapper.sh` which:
1. Loads `.env.prod` environment variables
2. Sets up Redis and database connection strings
3. Calls the main processing script

**Important**: When updating Redis passwords or database credentials, ensure the wrapper script has access to the updated `.env.prod` file.

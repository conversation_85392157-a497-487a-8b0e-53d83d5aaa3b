#!/bin/bash
set -e

# Production Server Setup Script for EmailConnect.eu
# This script sets up a fresh Ubuntu server with PostgreSQL integration

echo "🚀 EmailConnect.eu - Production Server Setup"
echo "=================================================="
echo ""

# 1. Check if running as root
if [ "$EUID" -ne 0 ]; then 
   echo "❌ Please run as root (use sudo)"
   exit 1
fi

# 2. Update system
echo "📦 Updating system packages..."
apt-get update
apt-get upgrade -y

# 3. Install essential packages
echo "🔧 Installing essential packages..."
apt-get install -y \
    curl \
    wget \
    git \
    htop \
    vim \
    ufw \
    certbot \
    python3-certbot-nginx \
    nginx \
    redis-server \
    postgresql-client \
    postfix \
    postfix-pgsql \
    libsasl2-modules \
    mailutils \
    docker.io \
    docker-compose-v2 \
    nodejs \
    npm \
    spamassassin \
    spamc

# 4. Configure firewall
echo "🔥 Configuring firewall..."
ufw allow 22/tcp
ufw allow 25/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 587/tcp
ufw --force enable

# 5. Create application directory
echo "📁 Creating application directory..."
mkdir -p /opt/emailconnect-app
chown -R $SUDO_USER:$SUDO_USER /opt/emailconnect-app

# 6. Clone repository
echo "📥 Cloning repository..."
if [ ! -d "/opt/emailconnect-app/.git" ]; then
    git clone https://github.com/xadi-hq/emailconnect-app.git /opt/emailconnect-app
    chown -R $SUDO_USER:$SUDO_USER /opt/emailconnect-app
else
    echo "Repository already exists, pulling latest..."
    cd /opt/emailconnect-app
    git pull origin main
fi

# 7. Create production scripts directory
echo "📁 Creating production scripts directory..."
mkdir -p /opt/emailconnect-app/scripts/production
chown -R $SUDO_USER:$SUDO_USER /opt/emailconnect-app/scripts

# 8. Configure Nginx
echo "🌐 Configuring Nginx..."
cat > /etc/nginx/sites-available/emailconnect.eu << 'EOF'
server {
    listen 80;
    server_name emailconnect.eu www.emailconnect.eu localhost;

    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # Health check endpoint
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name emailconnect.eu www.emailconnect.eu;

    # SSL configuration will be added by Certbot

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Proxy settings
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

ln -sf /etc/nginx/sites-available/emailconnect.eu /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t && systemctl restart nginx

# 9. Configure Postfix basic settings
echo "📧 Configuring Postfix..."
postconf -e "myhostname = mx.emailconnect.eu"
postconf -e "mydomain = emailconnect.eu"
postconf -e "myorigin = \$mydomain"
# CRITICAL: Add int.emailconnect.eu to mydestination for internal routing
postconf -e "mydestination = \$myhostname, localhost.\$mydomain, localhost, int.emailconnect.eu"
postconf -e "relayhost ="
postconf -e "inet_interfaces = all"
postconf -e "inet_protocols = ipv4"
postconf -e "recipient_delimiter = +"
postconf -e "home_mailbox = Maildir/"
postconf -e "mailbox_size_limit = 0"
postconf -e "message_size_limit = ********"
postconf -e "smtpd_banner = \$myhostname ESMTP"
postconf -e "biff = no"
postconf -e "append_dot_mydomain = no"
postconf -e "readme_directory = no"
postconf -e "compatibility_level = 2"

# Security settings
postconf -e "smtpd_tls_cert_file=/etc/ssl/certs/ssl-cert-snakeoil.pem"
postconf -e "smtpd_tls_key_file=/etc/ssl/private/ssl-cert-snakeoil.key"
postconf -e "smtpd_tls_security_level=may"
postconf -e "smtp_tls_CApath=/etc/ssl/certs"
postconf -e "smtp_tls_security_level=may"
postconf -e "smtp_tls_session_cache_database = btree:\${data_directory}/smtp_scache"
postconf -e "smtpd_relay_restrictions = permit_mynetworks permit_sasl_authenticated defer_unauth_destination"

# CRITICAL: Disable SASL Authentication for incoming mail processing
# This prevents authentication failures that block external email delivery
postconf -e "smtpd_sasl_auth_enable = no"
postconf -e "smtpd_recipient_restrictions = permit_mynetworks,reject_unauth_destination"
postconf -e "smtpd_relay_restrictions = permit_mynetworks,defer_unauth_destination"

echo ""

# 10. Wait for Docker containers to be deployed first
echo "⏳ Waiting for Docker containers to be deployed..."
echo "Please deploy the application first using docker-compose, then press Enter to continue..."
read -p "Press Enter after deploying the application containers..."

# 11. Set up PostgreSQL configuration for Postfix
echo "📧 Configuring Postfix for PostgreSQL support..."

# Create PostgreSQL query configuration files
# CRITICAL: Use int.emailconnect.eu for internal routing to avoid conflicts
cat > /etc/postfix/pgsql-virtual-domains.cf << 'EOF'
hosts = localhost
user = postfix_user
password = %POSTFIX_DB_PASSWORD%
dbname = eceu_db
query = SELECT CASE WHEN spam_filtering = true THEN '<EMAIL>' ELSE '<EMAIL>' END FROM postfix_virtual_domains WHERE domain='%s' AND active=true
EOF

# CRITICAL: Update aliases query to support spam filtering via domain join
cat > /etc/postfix/pgsql-virtual-aliases.cf << 'EOF'
hosts = localhost
user = postfix_user
password = %POSTFIX_DB_PASSWORD%
dbname = eceu_db
query = SELECT CASE WHEN d.spam_filtering = true THEN '<EMAIL>' ELSE '<EMAIL>' END FROM postfix_virtual_aliases a JOIN postfix_virtual_domains d ON RIGHT(a.email, LENGTH(a.email) - 1) = d.domain WHERE a.email='%s' AND a.active=true AND d.active=true
EOF

# Set proper permissions
chown root:postfix /etc/postfix/pgsql-*.cf
chmod 640 /etc/postfix/pgsql-*.cf

# Configure Postfix to use PostgreSQL
postconf -e "virtual_alias_domains = pgsql:/etc/postfix/pgsql-virtual-domains.cf"
postconf -e "virtual_alias_maps = pgsql:/etc/postfix/pgsql-virtual-aliases.cf"

# 12. CRITICAL: Create email processing aliases with proper Node.js execution
# These must use full Node.js path and run from app directory for dependency resolution
echo "📧 Setting up email processing aliases..."

# Remove any existing aliases
sed -i '/^process-email:/d; /^advanced-process-email:/d' /etc/aliases

# Add new aliases with correct execution context
echo "process-email: \"|cd /opt/emailconnect-app && /usr/bin/node scripts/production/process-email.js\"" >> /etc/aliases
echo "advanced-process-email: \"|/opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh\"" >> /etc/aliases
newaliases

# Install Node.js dependencies for email processing scripts
echo "📦 Installing Node.js dependencies..."
cd /opt/emailconnect-app
sudo -u $SUDO_USER npm install

# Create wrapper script for advanced processing with environment variables
cat > /opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh << 'EOF'
#!/bin/bash
cd /opt/emailconnect-app
# Load production environment variables
if [ -f .env.prod ]; then
    source .env.prod 2>/dev/null || true
    export REDIS_HOST_URL="redis://:${REDIS_PASSWORD}@127.0.0.1:6379"
    export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@localhost:5432/${DB_NAME}"
fi
exec /usr/bin/node scripts/production/advanced-process-email.js
EOF

# Ensure scripts are executable
chmod +x /opt/emailconnect-app/scripts/production/*.js
chmod +x /opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh

# 12.1. Set up SpamAssassin for Pro features
echo "🛡️ Setting up SpamAssassin..."
systemctl start spamd
systemctl enable spamd

# Update SpamAssassin rules
sa-update || echo "⚠️ SpamAssassin rules update failed (often normal on first run)"

# 13. Set up reload watcher service
echo "🔄 Setting up Postfix reload watcher service..."
cat > /usr/local/bin/postfix-reload-watcher.sh << 'EOF'
#!/bin/bash
# Postfix reload watcher - monitors signal files and reloads Postfix when needed

SIGNAL_FILE="/tmp/postfix-reload-requested"
CHECK_INTERVAL=5
MAX_AGE=3600  # Remove signal files older than 1 hour

while true; do
    # Check if signal file exists
    if [ -f "$SIGNAL_FILE" ]; then
        echo "$(date): Reload signal detected, reloading Postfix..."
        
        # Reload Postfix
        /usr/sbin/postfix reload
        
        # Remove the signal file
        rm -f "$SIGNAL_FILE"
        
        echo "$(date): Postfix reloaded successfully"
    fi
    
    # Clean up old signal files (in case of race conditions)
    find /tmp -name "postfix-reload-requested*" -type f -mmin +60 -delete 2>/dev/null
    
    sleep $CHECK_INTERVAL
done
EOF

chmod +x /usr/local/bin/postfix-reload-watcher.sh

# Create systemd service
cat > /etc/systemd/system/postfix-reload-watcher.service << 'EOF'
[Unit]
Description=Postfix Reload Watcher
After=postfix.service
Requires=postfix.service

[Service]
Type=simple
ExecStart=/usr/local/bin/postfix-reload-watcher.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=postfix-reload-watcher

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable postfix-reload-watcher
systemctl start postfix-reload-watcher

# 14. Configure mail logging
echo "📋 Configuring mail logging..."
# Ensure rsyslog is configured for mail logging
if ! grep -q "mail\.\*" /etc/rsyslog.conf; then
    echo "mail.*                          /var/log/mail.log" >> /etc/rsyslog.conf
    systemctl restart rsyslog
fi

# Create mail.log if it doesn't exist
touch /var/log/mail.log
chown syslog:adm /var/log/mail.log
chmod 640 /var/log/mail.log

# 15. Docker permissions
echo "🐳 Setting up Docker permissions..."
usermod -aG docker $SUDO_USER

# 16. Create environment file template
echo "📝 Creating environment file template..."
cat > /opt/emailconnect-app/.env.example << 'EOF'
# Environment variables for production deployment
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
URL=https://emailconnect.eu

# Database - Update with your production database
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=eu_email_webhook
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/${DB_NAME}

# Redis
REDIS_URL=redis://localhost:6379

# Better Auth
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=${URL}
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# Email processing limits
MAX_EMAIL_SIZE_MB=25
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3

# DNS verification settings
DNS_VERIFICATION_TIMEOUT_MS=5000
DNS_VERIFICATION_CACHE_TTL_MS=300000
DNS_VERIFICATION_RETRY_ATTEMPTS=3

# GDPR compliance settings
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90

# Usage tracking and billing settings
DEFAULT_MONTHLY_EMAIL_LIMIT=50
FREE_PLAN_EMAIL_LIMIT=50
PRO_PLAN_EMAIL_LIMIT=1000
ENTERPRISE_PLAN_EMAIL_LIMIT=10000

# WebhookTest Integration
WEBHOOKTEST_API_URL=http://localhost:3002
WEBHOOKTEST_JWT_SECRET=shared-secret-with-emailconnect  # Should match WebhookTest

# Payment processing (Mollie)
MOLLIE_API_KEY=test_dHa...
MOLLIE_WEBHOOK_URL=https://yourdomain.com/api/webhooks/mollie
MOLLIE_WEBHOOK_SECRET=your-mollie-webhook-secret-key
MOLLIE_TEST_MODE=true

# WebSocket Configuration
ALLOWED_ORIGINS=https://yourdomain.com

# Attachment Processing
MAX_INLINE_ATTACHMENT_SIZE_KB=128
DEFAULT_ATTACHMENT_RETENTION_HOURS=1
PAID_ATTACHMENT_RETENTION_HOURS=24

# Postfix Database Password (for PostgreSQL queries)
POSTFIX_DB_PASSWORD=your_postfix_user_password_here
EOF

chown $SUDO_USER:$SUDO_USER /opt/emailconnect-app/.env.example

# 17. Final instructions
echo ""
echo "✅ Server setup complete!"
echo ""
echo "📋 Next steps:"
echo ""
echo "1. 🔐 Set up SSL certificate:"
echo "   sudo certbot --nginx -d emailconnect.eu -d www.emailconnect.eu"
echo ""
echo "2. 📝 Configure environment:"
echo "   cd /opt/emailconnect-app"
echo "   cp .env.example .env.prod"
echo "   # Edit .env.prod with your actual values"
echo ""
echo "3. 🐳 Deploy the application:"
echo "   cd /opt/emailconnect-app"
echo "   docker compose -f docker-compose.prod.yml --env-file .env.prod up -d"
echo ""
echo "4. 🗄️ Set up PostgreSQL user for Postfix:"
echo "   # Check your .env.prod for actual DB_USER and DB_NAME values"
echo "   docker compose -f docker-compose.prod.yml --env-file .env.prod exec postgres psql -U \${DB_USER} -d \${DB_NAME}"
echo "   CREATE USER postfix_user WITH PASSWORD 'your_secure_password';"
echo "   GRANT USAGE ON SCHEMA public TO postfix_user;"
echo "   GRANT SELECT ON postfix_virtual_domains TO postfix_user;"
echo "   GRANT SELECT ON postfix_virtual_aliases TO postfix_user;"
echo "   \\q"
echo ""
echo "5. 🔑 Update Postfix PostgreSQL configuration with the password:"
echo "   sudo sed -i 's/%POSTFIX_DB_PASSWORD%/your_secure_password/g' /etc/postfix/pgsql-*.cf"
echo ""
echo "6. 🔄 Reload Postfix:"
echo "   sudo systemctl reload postfix"
echo ""
echo "7. 🧪 Validate deployment:"
echo "   ./deploy/validate-deployment.sh"
echo ""
echo "📊 Monitoring:"
echo "  Application: docker compose -f docker-compose.prod.yml logs -f app"
echo "  Postfix: sudo journalctl -u postfix -f"
echo "  Reload Watcher: sudo journalctl -u postfix-reload-watcher -f"
echo "  Nginx: sudo tail -f /var/log/nginx/access.log"
echo ""
echo "🚨 CRITICAL TROUBLESHOOTING NOTES:"
echo "=========================================="
echo ""
echo "❌ Common Issue #1: SASL Authentication Errors"
echo "   Symptom: 'fatal: no SASL authentication mechanisms'"
echo "   Solution: Ensure smtpd_sasl_auth_enable = no in /etc/postfix/main.cf"
echo "   Command: sudo postconf smtpd_sasl_auth_enable"
echo ""
echo "❌ Common Issue #2: Email Processing Script Errors"
echo "   Symptom: 'Cannot find package @prisma/client'"
echo "   Solution: Scripts must run from app directory with Node.js dependencies"
echo "   Check: Aliases use 'cd /opt/emailconnect-app && /usr/bin/node...'"
echo "   Command: grep process-email /etc/aliases"
echo ""
echo "❌ Common Issue #3: Spam Filtering Not Working"
echo "   Symptom: All emails go to basic processing instead of advanced"
echo "   Solution: Check domain has spam_filtering=true and aliases query joins domains table"
echo "   Command: sudo postmap -q @domain.com pgsql:/etc/postfix/pgsql-virtual-aliases.cf"
echo ""
echo "❌ Common Issue #4: Database Connection from Scripts"
echo "   Symptom: Scripts can't connect to PostgreSQL"
echo "   Solution: Ensure scripts run with proper working directory and environment"
echo "   Check: postfix_user has correct permissions on required tables"
echo ""
echo "❌ Common Issue #5: Wrong Docker Image"
echo "   Symptom: Old application version, missing features"
echo "   Solution: Use ghcr.io/xadi-hq/emailconnect-app:latest"
echo "   Command: docker images | grep emailconnect"
echo ""
echo "✅ Verification Commands:"
echo "   Test domain routing: sudo postmap -q domain.com pgsql:/etc/postfix/pgsql-virtual-domains.cf"
echo "   Test alias routing: sudo postmap -q @domain.com pgsql:/etc/postfix/pgsql-virtual-aliases.cf"
echo "   Check mail queue: mailq"
echo "   Test SMTP connection: telnet localhost 25"
echo "   Monitor email processing: sudo tail -f /var/log/syslog | grep postfix"
echo "   Check container health: docker compose ps"
echo "   Test advanced script: echo 'test' | cd /opt/emailconnect-app && /usr/bin/node scripts/production/advanced-process-email.js"
echo ""

# Postfix PostgreSQL Query Configuration for Virtual Domains
# This file defines how Postfix queries PostgreSQL for virtual domain routing
# 
# Purpose: Determines if a domain should be handled locally and routes to appropriate processor
# - Basic processing (advanced_processing = false): <EMAIL>
# - Advanced processing (advanced_processing = true): <EMAIL>
# Note: advanced_processing is true when spam filtering is enabled OR S3 storage is required

# Database connection settings
user = postfix_user
password = %POSTFIX_DB_PASSWORD%
hosts = localhost:5432
dbname = eu_email_webhook

# Main query for virtual domain lookup
# Returns the appropriate email processor based on spam filtering setting
query = SELECT CASE WHEN advanced_processing = true THEN '<EMAIL>' ELSE '<EMAIL>' END FROM postfix_virtual_domains WHERE domain='%s' AND active=true

# Alternative query for debugging (uncomment to test domain existence)
# query = SELECT domain FROM postfix_virtual_domains WHERE domain='%s' AND active=true

# Connection settings for better performance
result_format = %s
expansion_limit = 0

# Security settings
# domain = 
# hosts = localhost
# user = postfix_user
# password = (set above)

# Debugging (set to yes for troubleshooting)
# debuglevel = 0

# Connection pooling and timeout settings
# These help with performance under load
# option_file = 
# option_group = 
# tls_cert_file = 
# tls_key_file = 
# tls_CAfile = 
# tls_CApath = 
# tls_ciphers = 
# tls_protocols = 
# tls_eccert_file = 
# tls_eckey_file = 
# tls_fingerprint_cert_match = 
# tls_fingerprint_digest = 
# tls_verify_cert = 

# Performance tuning
# cache_size = 100
# negative_cache_size = 100
# cache_expiry = 300
# negative_cache_expiry = 60

# Postfix PostgreSQL Query Configuration for Virtual Aliases
# This file defines how Postfix queries PostgreSQL for specific email alias routing
#
# Purpose: Maps specific email addresses to appropriate processors based on domain's advanced_processing flag
# Uses JOIN between virtual_aliases and virtual_domains to inherit routing settings
# Note: advanced_processing determines whether to use basic or advanced email processor

# Database connection settings
user = postfix_user
password = %POSTFIX_DB_PASSWORD%
hosts = localhost:5432
dbname = eu_email_webhook

# Main query for virtual alias lookup with spam filtering inheritance
# This query:
# 1. Looks up the specific email alias in postfix_virtual_aliases
# 2. JOINs with postfix_virtual_domains to get advanced_processing setting
# 3. Routes based on domain's advanced_processing flag for proper email processing
query = SELECT CASE WHEN pvd.advanced_processing = true THEN '<EMAIL>' ELSE '<EMAIL>' END FROM postfix_virtual_aliases pva JOIN postfix_virtual_domains pvd ON pva.domain = pvd.domain WHERE pva.email='%s' AND pvd.active=true AND pva.active=true

# Fallback query for catch-all domain handling (uncomment if needed)
# This would check if domain has catch-all when specific alias not found
# query = SELECT CASE 
#     WHEN spam_filtering = true THEN '<EMAIL>' 
#     ELSE '<EMAIL>' 
# END FROM postfix_virtual_domains WHERE domain='%d' AND active=true

# Alternative query for debugging (returns destination field)
# query = SELECT pva.destination FROM postfix_virtual_aliases pva 
# JOIN postfix_virtual_domains pvd ON pva.domain = pvd.domain 
# WHERE pva.email='%s' AND pva.active=true AND pvd.active=true

# Connection settings for better performance
result_format = %s
expansion_limit = 0

# Security settings  
# domain = 
# hosts = localhost
# user = postfix_user
# password = (set above)

# Debugging (set to yes for troubleshooting)
# debuglevel = 0

# Performance tuning
# cache_size = 100
# negative_cache_size = 100
# cache_expiry = 300
# negative_cache_expiry = 60

# Connection pooling settings
# These help maintain stable connections to PostgreSQL
# option_file = 
# option_group = 
# connect_timeout = 10
# read_timeout = 10
# write_timeout = 10

# SSL/TLS settings (uncomment if using SSL)
# tls_cert_file = 
# tls_key_file = 
# tls_CAfile = 
# tls_CApath = 
# tls_ciphers = 
# tls_protocols = 
# tls_verify_cert = no

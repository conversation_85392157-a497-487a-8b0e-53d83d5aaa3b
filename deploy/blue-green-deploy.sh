#!/bin/bash
set -e

# Blue-Green Deployment Script for EU Email Webhook
# This script enables zero-downtime deployments by running new containers
# alongside old ones, then switching traffic after health checks pass

echo "🚀 Starting Blue-Green Deployment..."

# Configuration
DEPLOY_DIR="/opt/emailconnect-app"
COMPOSE_FILE="docker-compose.prod.yml"
HEALTH_CHECK_URL="http://localhost:3000/health"
MAX_HEALTH_RETRIES=30
HEALTH_CHECK_INTERVAL=2

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Detect Docker Compose version
if docker compose version &>/dev/null; then
  DOCKER_COMPOSE="docker compose"
elif command -v docker-compose &>/dev/null; then
  DOCKER_COMPOSE="docker-compose"
else
  echo -e "${RED}❌ No supported Docker Compose found${NC}" >&2
  exit 1
fi

cd "$DEPLOY_DIR"

# Function to check container health
check_health() {
  local container_name=$1
  local port=$2
  local retry_count=0
  
  echo -e "${BLUE}🏥 Checking health of $container_name on port $port...${NC}"
  
  while [ $retry_count -lt $MAX_HEALTH_RETRIES ]; do
    if curl -f --max-time 5 "http://localhost:$port/health" &>/dev/null; then
      echo -e "${GREEN}✅ $container_name is healthy${NC}"
      return 0
    else
      echo "⏳ Waiting for $container_name... (attempt $((retry_count + 1))/$MAX_HEALTH_RETRIES)"
      sleep $HEALTH_CHECK_INTERVAL
      retry_count=$((retry_count + 1))
    fi
  done
  
  echo -e "${RED}❌ $container_name health check failed${NC}"
  return 1
}

# Step 1: Pull latest images
echo -e "${BLUE}📥 Pulling latest images...${NC}"
$DOCKER_COMPOSE -f $COMPOSE_FILE pull

# Step 2: Start new containers with temporary names (Blue environment)
echo -e "${BLUE}🐳 Starting new containers (Blue environment)...${NC}"

# Create a temporary override file for blue environment
cat > docker-compose.blue.yml << EOF
version: '3.8'
services:
  app:
    container_name: emailconnect-app-blue
    ports:
      - "3001:3000"  # Temporary port for health check
    labels:
      - "deployment=blue"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
EOF

# Start blue environment (app only, reusing existing Redis/Postgres)
echo -e "${BLUE}🔄 Starting Blue environment...${NC}"
$DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.blue.yml up -d app

# Step 3: Wait for blue environment to be healthy
if ! check_health "Blue environment" 3001; then
  echo -e "${RED}❌ Blue environment failed health check, rolling back...${NC}"
  $DOCKER_COMPOSE -f $COMPOSE_FILE -f docker-compose.blue.yml down
  rm docker-compose.blue.yml
  exit 1
fi

# Step 4: Get current (green) container name
GREEN_CONTAINER=$(docker ps --filter "label=deployment!=blue" --filter "name=app" --format "{{.Names}}" | grep -v blue || echo "")

if [ -n "$GREEN_CONTAINER" ]; then
  echo -e "${GREEN}📊 Current container: $GREEN_CONTAINER${NC}"
fi

# Step 5: Switch traffic to blue environment
echo -e "${BLUE}🔄 Switching traffic to Blue environment...${NC}"

# Stop green container gracefully
if [ -n "$GREEN_CONTAINER" ]; then
  echo -e "${GREEN}⏸️  Stopping Green environment gracefully...${NC}"
  docker stop --time=30 "$GREEN_CONTAINER" || true
  docker rm "$GREEN_CONTAINER" || true
fi

# Update port mapping for blue to production port
docker stop emailconnect-app-blue
$DOCKER_COMPOSE -f $COMPOSE_FILE up -d app

# Step 6: Verify production is healthy
if ! check_health "Production environment" 3000; then
  echo -e "${RED}❌ Production environment failed after switch!${NC}"
  # This is critical - old container is gone, we're committed
  exit 1
fi

# Step 7: Cleanup
echo -e "${GREEN}🧹 Cleaning up...${NC}"
rm -f docker-compose.blue.yml

# Step 8: Prune old images (optional)
echo -e "${GREEN}🧹 Pruning old images...${NC}"
docker image prune -f

echo -e "${GREEN}✅ Blue-Green deployment completed successfully!${NC}"
echo -e "${GREEN}🎉 Zero-downtime deployment achieved!${NC}"

# Log deployment
echo "$(date): Blue-Green deployment completed successfully" >> /var/log/deployments.log
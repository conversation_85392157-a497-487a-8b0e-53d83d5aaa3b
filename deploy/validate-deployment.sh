#!/bin/bash

# Deployment Validation Script for EmailConnect.eu
# This script validates that the deployment is working correctly

set -e

echo "🔍 EmailConnect.eu - Deployment Validation"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
WARNINGS=0

# Test result tracking
declare -a FAILED_TESTS=()
declare -a WARNING_TESTS=()

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

print_failure() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
    FAILED_TESTS+=("$1")
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNINGS++))
    WARNING_TESTS+=("$1")
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test endpoint function
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    if curl -f -s -o /dev/null --max-time 10 "$url"; then
        local status=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url")
        if [ "$status" -eq "$expected_status" ]; then
            print_success "$name endpoint is responding (HTTP $status)"
        else
            print_failure "$name endpoint returned HTTP $status (expected $expected_status)"
        fi
    else
        print_failure "$name endpoint is not responding"
    fi
}

# Check if Docker container is running
check_container() {
    local service="$1"
    local compose_file="${2:-docker-compose.prod.yml}"
    
    cd /opt/emailconnect-app
    
    if docker-compose -f "$compose_file" ps "$service" | grep -q "Up"; then
        print_success "$service container is running"
    else
        print_failure "$service container is not running"
        print_info "Container logs:"
        docker-compose -f "$compose_file" logs --tail=10 "$service" | sed 's/^/    /'
    fi
}

# Test PostgreSQL connection
test_postgresql_connection() {
    local container_name="$1"
    local db_name="$2"
    
    cd /opt/emailconnect-app
    
    if docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d "$db_name" -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "PostgreSQL connection successful"
    else
        print_failure "PostgreSQL connection failed"
    fi
}

# Test PostgreSQL tables
test_postgresql_tables() {
    cd /opt/emailconnect-app
    
    # Test postfix tables
    if docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d eceu_db -c "SELECT count(*) FROM postfix_virtual_domains;" > /dev/null 2>&1; then
        print_success "Postfix PostgreSQL tables exist"
    else
        print_failure "Postfix PostgreSQL tables missing"
    fi
    
    # Test application tables
    if docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d eceu_db -c "SELECT count(*) FROM users;" > /dev/null 2>&1; then
        print_success "Application PostgreSQL tables exist"
    else
        print_failure "Application PostgreSQL tables missing"
    fi
}

# Test postfix user access
test_postfix_user_access() {
    cd /opt/emailconnect-app
    
    # Try to connect as postfix_user
    if docker-compose -f docker-compose.prod.yml exec -T postgres psql -h localhost -U postfix_user -d eceu_db -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Postfix user can access PostgreSQL"
    else
        print_failure "Postfix user cannot access PostgreSQL"
        print_info "Make sure postfix_user exists and has correct permissions"
    fi
}

echo ""
echo "🧪 Starting validation tests..."
echo ""

# 1. Test Docker containers
echo "1. 🐳 Testing Docker containers..."
if [ -d "/opt/emailconnect-app" ]; then
    check_container "app"
    check_container "postgres"
    check_container "redis"
else
    print_failure "Application directory /opt/emailconnect-app not found"
fi

echo ""

# 2. Test PostgreSQL database
echo "2. 🗄️ Testing PostgreSQL database..."
if [ -d "/opt/emailconnect-app" ]; then
    test_postgresql_connection "postgres" "eceu_db"
    test_postgresql_tables
    test_postfix_user_access
else
    print_failure "Cannot test PostgreSQL - application directory not found"
fi

echo ""

# 3. Test web endpoints
echo "3. 🌐 Testing web endpoints..."
test_endpoint "Main Application Health" "http://localhost:3000/health"
test_endpoint "HTTPS Application Health" "https://emailconnect.eu/health"
test_endpoint "Nginx Health" "http://localhost/nginx-health"

echo ""

# 4. Test system services
echo "4. 🛠️ Testing system services..."

# Check Postfix
if systemctl is-active --quiet postfix; then
    print_success "Postfix service is running"
else
    print_failure "Postfix service is not running"
fi

# Check Nginx
if systemctl is-active --quiet nginx; then
    print_success "Nginx service is running"
else
    print_failure "Nginx service is not running"
fi

# Check Redis
if systemctl is-active --quiet redis-server; then
    print_success "Redis service is running"
else
    print_failure "Redis service is not running"
fi

# Check postfix-reload-watcher
if systemctl is-active --quiet postfix-reload-watcher; then
    print_success "Postfix reload watcher service is running"
else
    print_failure "Postfix reload watcher service is not running"
fi

# Check SpamAssassin (for Pro features)
if systemctl is-active --quiet spamd 2>/dev/null || systemctl is-active --quiet spamassassin 2>/dev/null; then
    print_success "SpamAssassin service is running"
else
    print_warning "SpamAssassin service is not running (required for Pro features)"
fi

# Check Node.js installation
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is installed ($NODE_VERSION)"
else
    print_failure "Node.js is not installed (required for email processing)"
fi

echo ""

# 5. Test Postfix PostgreSQL integration
echo "5. 📧 Testing Postfix PostgreSQL integration..."

# Check if postfix-pgsql package is installed
if dpkg -l | grep -q postfix-pgsql; then
    print_success "postfix-pgsql package is installed"
else
    print_failure "postfix-pgsql package missing (install with: sudo apt install postfix-pgsql)"
fi

# Test PostgreSQL configuration files
if [ -f "/etc/postfix/pgsql-virtual-domains.cf" ]; then
    print_success "Postfix PostgreSQL domain config exists"
else
    print_failure "Postfix PostgreSQL domain config missing"
fi

if [ -f "/etc/postfix/pgsql-virtual-aliases.cf" ]; then
    print_success "Postfix PostgreSQL alias config exists"
else
    print_failure "Postfix PostgreSQL alias config missing"
fi

# Test Postfix configuration
if postconf virtual_alias_domains | grep -q "pgsql:"; then
    print_success "Postfix configured for PostgreSQL virtual domains"
else
    print_failure "Postfix not configured for PostgreSQL virtual domains"
fi

if postconf virtual_alias_maps | grep -q "pgsql:"; then
    print_success "Postfix configured for PostgreSQL virtual aliases"
else
    print_failure "Postfix not configured for PostgreSQL virtual aliases"
fi

# Check critical int.emailconnect.eu configuration
if postconf mydestination | grep -q "int.emailconnect.eu"; then
    print_success "Postfix configured with int.emailconnect.eu for internal routing"
else
    print_failure "Postfix missing int.emailconnect.eu in mydestination (causes mail loops)"
fi

# Check SASL is disabled (critical for receiving external mail)
SASL_STATUS=$(postconf smtpd_sasl_auth_enable | awk '{print $3}')
if [ "$SASL_STATUS" = "no" ]; then
    print_success "SASL authentication is correctly disabled for incoming mail"
else
    print_failure "SASL authentication is enabled (blocks external mail delivery)"
fi

# Test postfix user PostgreSQL access from host (with actual DB name)
# Note: This will prompt for password - that's expected
print_info "Testing postfix user PostgreSQL access (password prompt is normal)..."
if timeout 10 sudo -u postfix psql -h localhost -U postfix_user -d eceu_db -c "SELECT 1;" > /dev/null 2>&1; then
    print_success "Postfix user can access PostgreSQL from host"
else
    print_warning "Postfix user PostgreSQL access test failed (may need password or wrong DB name)"
    print_info "To debug: sudo -u postfix psql -h localhost -U postfix_user -d YOUR_DB_NAME"
fi

# Test PostgreSQL permissions
cd /opt/emailconnect-app
if docker compose -f docker-compose.prod.yml --env-file .env.prod exec -T postgres psql -U eceu_user -d eceu_db -c "SET ROLE postfix_user; SELECT * FROM postfix_virtual_domains LIMIT 1;" > /dev/null 2>&1; then
    print_success "Postfix user has correct PostgreSQL table permissions"
else
    print_failure "Postfix user missing PostgreSQL permissions (run: GRANT USAGE ON SCHEMA public TO postfix_user;)"
fi

echo ""

# 6. Test email processing setup
echo "6. 📬 Testing email processing setup..."

# Check email processing aliases
if grep -q "process-email:" /etc/aliases; then
    print_success "Email processing aliases configured"
    # Check if aliases use correct Node.js execution format
    if grep "process-email:" /etc/aliases | grep -q "cd /opt/emailconnect-app && /usr/bin/node"; then
        print_success "Email aliases use correct Node.js execution format"
    else
        print_failure "Email aliases have incorrect format (must use 'cd /opt/emailconnect-app && /usr/bin/node')"
    fi
else
    print_failure "Email processing aliases not configured"
fi

# Check processing scripts exist
if [ -f "/opt/emailconnect-app/scripts/production/process-email.js" ]; then
    print_success "Email processing script exists"
    # Check if script is executable
    if [ -x "/opt/emailconnect-app/scripts/production/process-email.js" ]; then
        print_success "Email processing script is executable"
    else
        print_failure "Email processing script is not executable"
    fi
else
    print_warning "Email processing script not found (will be created on first deployment)"
fi

# Check advanced processing script
if [ -f "/opt/emailconnect-app/scripts/production/advanced-process-email.js" ]; then
    print_success "Advanced email processing script exists"
else
    print_warning "Advanced email processing script not found"
fi

# Check advanced processing wrapper script with environment variables
if [ -f "/opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh" ]; then
    print_success "Advanced email processing wrapper script exists"
    # Test if wrapper has proper environment setup
    if grep -q "REDIS_HOST_URL" "/opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh"; then
        print_success "Wrapper script includes Redis authentication"
    else
        print_error "Wrapper script missing Redis authentication setup"
    fi
    # Check if wrapper is executable
    if [ -x "/opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh" ]; then
        print_success "Wrapper script is executable"
    else
        print_error "Wrapper script is not executable (run: chmod +x /opt/emailconnect-app/scripts/production/advanced-process-email-wrapper.sh)"
    fi
else
    print_error "Advanced email processing wrapper script not found (required for environment variable access)"
fi

echo ""

# 7. Test SSL configuration
echo "7. 🔒 Testing SSL configuration..."

# Test SSL certificate
if curl -f -s --max-time 10 "https://emailconnect.eu/health" > /dev/null; then
    print_success "SSL certificate is working"
else
    print_warning "SSL certificate may not be configured (run: sudo certbot --nginx -d emailconnect.eu)"
fi

echo ""

# 8. Test firewall configuration
echo "8. 🔥 Testing firewall configuration..."

if command -v ufw >/dev/null 2>&1; then
    if ufw status | grep -q "Status: active"; then
        print_success "UFW firewall is active"
        
        # Check required ports
        if ufw status | grep -q "22/tcp"; then
            print_success "SSH port (22) is open"
        else
            print_warning "SSH port (22) may not be explicitly allowed"
        fi
        
        if ufw status | grep -q "25/tcp"; then
            print_success "SMTP port (25) is open"
        else
            print_failure "SMTP port (25) is not open"
        fi
        
        if ufw status | grep -q "80/tcp"; then
            print_success "HTTP port (80) is open"
        else
            print_failure "HTTP port (80) is not open"
        fi
        
        if ufw status | grep -q "443/tcp"; then
            print_success "HTTPS port (443) is open"
        else
            print_failure "HTTPS port (443) is not open"
        fi
    else
        print_warning "UFW firewall is not active"
    fi
else
    print_warning "UFW firewall is not installed"
fi

echo ""

# 9. Test email routing (if domains exist)
echo "9. 🎯 Testing email routing..."

# Check if we have any domains configured
cd /opt/emailconnect-app
DOMAIN_COUNT=$(docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d eceu_db -t -c "SELECT count(*) FROM postfix_virtual_domains WHERE active=true;" 2>/dev/null | xargs || echo "0")

if [ "$DOMAIN_COUNT" -gt 0 ]; then
    print_success "Found $DOMAIN_COUNT active domains in PostgreSQL"
    
    # Test a domain lookup
    TEST_DOMAIN=$(docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres -d eceu_db -t -c "SELECT domain FROM postfix_virtual_domains WHERE active=true LIMIT 1;" 2>/dev/null | xargs || echo "")
    
    if [ -n "$TEST_DOMAIN" ]; then
        print_info "Testing domain lookup for: $TEST_DOMAIN"
        
        if LOOKUP_RESULT=$(sudo postmap -q "$TEST_DOMAIN" pgsql:/etc/postfix/pgsql-virtual-domains.cf 2>/dev/null); then
            if [ -n "$LOOKUP_RESULT" ]; then
                print_success "Domain lookup successful: $TEST_DOMAIN -> $LOOKUP_RESULT"
            else
                print_failure "Domain lookup returned empty result for $TEST_DOMAIN"
            fi
        else
            print_failure "Domain lookup failed for $TEST_DOMAIN"
        fi
    fi
else
    print_info "No domains configured yet (this is normal for fresh installations)"
fi

echo ""

# 10. Test reload watcher functionality
echo "10. 🔄 Testing reload watcher functionality..."

# Test signal file mechanism
if [ -f "/usr/local/bin/postfix-reload-watcher.sh" ]; then
    print_success "Postfix reload watcher script exists"
    
    # Test signal file creation and cleanup
    sudo touch /tmp/postfix-reload-requested
    if [ -f "/tmp/postfix-reload-requested" ]; then
        print_success "Signal file mechanism working"
        # Wait a moment for the watcher to process it
        sleep 3
        if [ ! -f "/tmp/postfix-reload-requested" ]; then
            print_success "Signal file processed and cleaned up"
        else
            print_warning "Signal file not processed (watcher may be slow)"
            sudo rm -f /tmp/postfix-reload-requested
        fi
    else
        print_failure "Cannot create signal file"
    fi
else
    print_failure "Postfix reload watcher script missing"
fi

echo ""

# Summary
echo "📊 Validation Summary"
echo "===================="
echo ""
echo -e "${GREEN}✅ Tests passed: $TESTS_PASSED${NC}"
echo -e "${RED}❌ Tests failed: $TESTS_FAILED${NC}"
echo -e "${YELLOW}⚠️  Warnings: $WARNINGS${NC}"

if [ ${#FAILED_TESTS[@]} -gt 0 ]; then
    echo ""
    echo -e "${RED}Failed tests:${NC}"
    for test in "${FAILED_TESTS[@]}"; do
        echo "  • $test"
    done
fi

if [ ${#WARNING_TESTS[@]} -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}Warnings:${NC}"
    for test in "${WARNING_TESTS[@]}"; do
        echo "  • $test"
    done
fi

echo ""
echo "🔍 Useful commands for troubleshooting:"
echo "  • Application logs: docker compose -f /opt/emailconnect-app/docker-compose.prod.yml logs -f app"
echo "  • PostgreSQL logs: docker compose -f /opt/emailconnect-app/docker-compose.prod.yml logs -f postgres"
echo "  • Postfix logs: sudo tail -f /var/log/mail.log"
echo "  • Reload watcher logs: sudo journalctl -u postfix-reload-watcher -f"
echo "  • Test PostgreSQL: docker compose -f /opt/emailconnect-app/docker-compose.prod.yml exec postgres psql -U eceu_user -d eceu_db"
echo "  • Test domain lookup: sudo postmap -q 'your-domain.com' pgsql:/etc/postfix/pgsql-virtual-domains.cf"
echo "  • Test alias lookup: sudo postmap -q '@your-domain.com' pgsql:/etc/postfix/pgsql-virtual-aliases.cf"
echo "  • Check postfix config: postconf -n | grep -E '(mydestination|virtual_alias|sasl)'"
echo "  • Test email script: echo 'test' | cd /opt/emailconnect-app && /usr/bin/node scripts/production/process-email.js"
echo "  • Check SpamAssassin: systemctl status spamd"
echo "  • Fix permissions: GRANT USAGE ON SCHEMA public TO postfix_user; GRANT SELECT ON postfix_virtual_domains, postfix_virtual_aliases TO postfix_user;"

echo ""

# Exit with appropriate code
if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All critical tests passed! Your deployment appears to be working correctly.${NC}"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please review the issues above before proceeding.${NC}"
    exit 1
fi
#!/usr/bin/env node

/**
 * Fix logger calls to use proper Pino format
 * <PERSON><PERSON> expects: logger.info(object, message) not logger.info(message, object)
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixLoggerCalls(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const originalContent = content;
  
  // Pattern 1: logger.method(string, object)
  // Should be: logger.method(object, string)
  const pattern1 = /logger\.(info|error|warn|debug)\(([`'"].*?[`'"])\s*,\s*(\{[^}]*\})\)/g;
  
  content = content.replace(pattern1, (match, method, message, object) => {
    modified = true;
    return `logger.${method}(${object}, ${message})`;
  });
  
  // Pattern 2: logger.method('string', variable) where variable might be an object
  const pattern2 = /logger\.(info|error|warn|debug)\(([`'"].*?[`'"])\s*,\s*([a-zA-Z_][a-zA-Z0-9_]*)\)/g;
  
  content = content.replace(pattern2, (match, method, message, variable) => {
    // Check if this looks like it might be an object variable (heuristic)
    if (!variable.match(/^(true|false|null|undefined|\d+)$/)) {
      modified = true;
      return `logger.${method}({ data: ${variable} }, ${message})`;
    }
    return match;
  });
  
  // Pattern 3: Fix logger.error with 4 parameters (special case for unhandled rejection)
  content = content.replace(
    /logger\.error\(['"](.+?)['"],\s*([^,]+),\s*['"](.+?)['"],\s*([^)]+)\)/g,
    (match, msg1, var1, msg2, var2) => {
      modified = true;
      return `logger.error({ ${var1.trim()}, ${var2.trim()} }, '${msg1} ${msg2}')`;
    }
  );
  
  if (modified) {
    // Create backup if it doesn't exist
    const backupPath = filePath + '.logger-backup';
    if (!fs.existsSync(backupPath)) {
      fs.writeFileSync(backupPath, originalContent);
    }
    
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Fixed logger calls`);
    return true;
  }
  
  return false;
}

// Get list of files with errors from TypeScript
const filesWithErrors = [
  'src/backend/controllers/user/webhook-alias.controller.ts',
  'src/backend/index.ts',
  'src/backend/routes/oauth.routes.ts',
  'src/backend/routes/webhooktest.routes.ts',
  'src/backend/services/content.service.ts',
  'src/backend/services/oauth.service.ts',
  'src/backend/services/queue.ts',
  'src/backend/services/user/domain.service.ts',
  'src/backend/services/user/webhook-alias.service.ts',
  'src/backend/services/webhooktest-integration.service.ts'
];

console.log('Fixing logger calls in files with TypeScript errors...\n');

let fixedCount = 0;
filesWithErrors.forEach(file => {
  const fullPath = path.join(__dirname, '..', file);
  if (fs.existsSync(fullPath)) {
    if (fixLoggerCalls(fullPath)) {
      fixedCount++;
    }
  }
});

console.log(`\n✅ Fixed ${fixedCount} files`);
console.log('Run npm run build:backend to verify the fixes');
#!/bin/bash

# Clean up backup files created during logger migration
# Run this after verifying the migration worked correctly

echo "🧹 Cleaning up logger migration backup files..."

# Find and count backup files
BACKUP_FILES=$(find src -name "*.backup" -o -name "*.logger-backup" -o -name "*.console-backup" 2>/dev/null)
BACKUP_COUNT=$(echo "$BACKUP_FILES" | wc -l)

if [ -z "$BACKUP_FILES" ] || [ "$BACKUP_COUNT" -eq 0 ]; then
    echo "ℹ️  No backup files found"
    exit 0
fi

echo "Found $BACKUP_COUNT backup files:"
echo "$BACKUP_FILES" | sed 's/^/  /'

echo ""
read -p "🗑️  Delete all backup files? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "$BACKUP_FILES" | xargs rm -f
    echo "✅ Deleted $BACKUP_COUNT backup files"
    
    # Also clean up the migration scripts since they're no longer needed
    if [ -f "scripts/migrate-console-to-logger.js" ]; then
        echo "🧹 Cleaning up migration scripts..."
        rm -f scripts/migrate-console-to-logger.js
        rm -f scripts/fix-logger-calls.js
        rm -f scripts/migrate-frontend-console.js
        echo "✅ Migration scripts cleaned up"
    fi
else
    echo "ℹ️  Backup files kept"
    echo "    Run this script again when you're ready to clean up"
fi
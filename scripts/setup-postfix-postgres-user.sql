-- Setup script for PostgreSQL user dedicated to Postfix
-- This creates a minimal-privilege user that can only read the postfix tables
-- 
-- Usage: Run this as postgres superuser
-- sudo -u postgres psql -d eu_email_webhook -f scripts/setup-postfix-postgres-user.sql

-- ========================================
-- Create dedicated user for Postfix
-- ========================================

-- Drop user if exists (for re-running script)
DROP USER IF EXISTS postfix_user;

-- Create user with login capability but no other privileges
CREATE USER postfix_user WITH 
    LOGIN 
    NOSUPERUSER 
    NOCREATEDB 
    NOCREATEROLE 
    NOINHERIT 
    NOREPLICATION 
    CONNECTION LIMIT 10
    PASSWORD 'CHANGE_THIS_PASSWORD_IN_PRODUCTION';

-- ========================================
-- Grant minimal required permissions
-- ========================================

-- Grant CONNECT privilege on the database
GRANT CONNECT ON DATABASE eu_email_webhook TO postfix_user;

-- Grant USAGE on the public schema
GRANT USAGE ON SCHEMA public TO postfix_user;

-- Grant SELECT only on the postfix tables
GRANT SELECT ON TABLE postfix_virtual_domains TO postfix_user;
GRANT SELECT ON TABLE postfix_virtual_aliases TO postfix_user;

-- Grant SELECT on the views for monitoring (optional)
GRANT SELECT ON postfix_spam_filtering_stats TO postfix_user;
GRANT SELECT ON postfix_domain_summary TO postfix_user;

-- ========================================
-- Security: Revoke any unnecessary privileges
-- ========================================

-- Ensure no other privileges are granted
REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM postfix_user;
REVOKE ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public FROM postfix_user;
REVOKE ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public FROM postfix_user;

-- Re-grant only the specific tables we need
GRANT SELECT ON TABLE postfix_virtual_domains TO postfix_user;
GRANT SELECT ON TABLE postfix_virtual_aliases TO postfix_user;

-- ========================================
-- Verify permissions
-- ========================================

-- Show granted privileges
\echo 'Privileges granted to postfix_user:'
SELECT 
    schemaname,
    tablename,
    privilege_type
FROM information_schema.table_privileges 
WHERE grantee = 'postfix_user'
ORDER BY schemaname, tablename, privilege_type;

-- Test connection (this will show if user can connect)
\echo 'Testing postfix_user connection...'
\echo 'User created successfully. Test with:'
\echo 'psql -h localhost -U postfix_user -d eu_email_webhook -c "SELECT COUNT(*) FROM postfix_virtual_domains;"'

-- ========================================
-- Security recommendations
-- ========================================

\echo ''
\echo 'SECURITY RECOMMENDATIONS:'
\echo '1. Change the default password immediately'
\echo '2. Consider using SSL/TLS for database connections'
\echo '3. Restrict network access to PostgreSQL port (5432)'
\echo '4. Monitor postfix_user activity in PostgreSQL logs'
\echo '5. Regularly rotate the password'
\echo ''
\echo 'To change password:'
\echo 'ALTER USER postfix_user PASSWORD ''your_secure_password_here'';'

#!/usr/bin/env node

/**
 * Advanced email processor script called by Postfix for Pro+ users
 * This script provides comprehensive email processing including:
 * - Spam filtering with custom thresholds via SpamAssassin
 * - Attachment processing (base64 encoding or S3 storage)
 * - Domain-specific configuration handling
 * - Enhanced webhook payload generation
 *
 * Flow: Postfix → advanced-process-email → SpamAssassin → Attachment Processing → Main App
 * Used for: Pro and Enterprise users only
 * Free users use: process-email.js (direct processing)
 */

import http from 'http';
import { spawn } from 'child_process';
import Redis from 'ioredis';
import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';
import { simpleParser } from 'mailparser';
// Import our new services (compiled JavaScript versions)
// TODO: These services need to be available on the host filesystem
// For now, disabling S3 processing until dist files are available
// import { S3StorageService } from '../../dist/backend/services/storage/s3-storage.service.js';
// import { FileTypeService } from '../../dist/backend/services/storage/file-type.service.js';

// Configuration - use environment variables or defaults
const APP_HOST = process.env.APP_HOST || 'localhost';
const APP_PORT = process.env.APP_PORT || 3000;
const TIMEOUT_MS = parseInt(process.env.WEBHOOK_TIMEOUT_MS) || 30000;
const SPAMC_TIMEOUT = parseInt(process.env.SPAMC_TIMEOUT_MS) || 10000;

// Redis configuration for host-side script
const REDIS_HOST_URL = process.env.REDIS_HOST_URL || 'redis://127.0.0.1:6379';
const redis = new Redis(REDIS_HOST_URL);

// SpamAssassin configuration
const SPAMC_PATH = process.env.SPAMC_PATH || '/usr/bin/spamc';

// Base64 inline attachment size limits by plan
const MAX_INLINE_BASE64_SIZE_KB = {
  free: 128,    // 128KB for free users (matches EmailParser)
  pro: 750,     // 750KB for Pro users (~1MB base64)
  enterprise: 750
};

// Legacy configuration - keeping for S3 fallback scenarios
const MAX_INLINE_ATTACHMENT_SIZE_KB = parseInt(process.env.MAX_INLINE_ATTACHMENT_SIZE_KB) || 128;

// Database configuration
const prisma = new PrismaClient({
  datasourceUrl: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/eu_email_webhook'
});

/**
 * Extract email address from email headers
 * @param {string} rawEmail - Raw email content
 * @returns {string|null} - Extracted email address or null
 */
function extractEmailAddress(rawEmail) {
  try {
    const lines = rawEmail.split('\n');

    // Try multiple header patterns for email address extraction
    const patterns = [
      /^X-Original-To:\s*(.+)$/i,
      /^Delivered-To:\s*(.+)$/i,
      /^To:\s*(.+)$/i,
      /^Envelope-To:\s*(.+)$/i,
    ];

    for (const line of lines) {
      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          const emailMatch = match[1].match(/([^<>\s]+@[^<>\s]+)/);
          if (emailMatch) {
            console.log(`[${new Date().toISOString()}] Extracted email address: ${emailMatch[1]} from header: ${line.trim()}`);
            return emailMatch[1].toLowerCase();
          }
        }
      }
    }

    console.log(`[${new Date().toISOString()}] Could not extract email address from headers`);
    return null;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error extracting email address:`, error.message);
    return null;
  }
}

/**
 * Extract domain from email headers
 * @param {string} rawEmail - Raw email content
 * @returns {string|null} - Extracted domain or null
 */
function extractDomainFromEmail(rawEmail) {
  try {
    const lines = rawEmail.split('\n');

    // Look for X-Original-To header first (most reliable for our setup)
    for (const line of lines) {
      if (line.toLowerCase().startsWith('x-original-to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    // Fallback to Delivered-To header
    for (const line of lines) {
      if (line.toLowerCase().startsWith('delivered-to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    // Fallback to To header
    for (const line of lines) {
      if (line.toLowerCase().startsWith('to:')) {
        const email = line.split(':')[1]?.trim();
        if (email && email.includes('@')) {
          return email.split('@')[1];
        }
      }
    }

    return null;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error extracting domain: ${error.message}`);
    return null;
  }
}

/**
 * Get spam filtering thresholds for a domain from Postgres
 * @param {string} domain - Domain name
 * @returns {Promise<{green: number, red: number}>} - Spam thresholds
 */
async function getDomainThresholds(domain) {
  try {
    const domainRecord = await prisma.domain.findFirst({
      where: { domain: domain },
      select: { configuration: true }
    });

    if (!domainRecord) {
      console.log(`[${new Date().toISOString()}] Domain ${domain} not found, using default thresholds`);
      return { green: 2.0, red: 5.0 };
    }

    const config = domainRecord.configuration || {};
    const spamConfig = config.spamFiltering || {};
    const thresholds = spamConfig.thresholds || {};

    const result = {
      green: thresholds.green || 2.0,
      red: thresholds.red || 5.0
    };

    console.log(`[${new Date().toISOString()}] Domain ${domain} thresholds: green=${result.green}, red=${result.red}`);
    return result;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fetching thresholds for ${domain}: ${error.message}`);
    return { green: 2.0, red: 5.0 }; // Default fallback
  }
}

/**
 * Extract spam score from SpamAssassin processed email
 * @param {string} processedEmail - Email with spam headers
 * @returns {number} - Spam score or 0 if not found
 */
function extractSpamScore(processedEmail) {
  try {
    const lines = processedEmail.split('\n');

    for (const line of lines) {
      if (line.toLowerCase().startsWith('x-spam-status:')) {
        const scoreMatch = line.match(/score=([\d.-]+)/);
        if (scoreMatch) {
          return parseFloat(scoreMatch[1]);
        }
      }
    }

    return 0;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error extracting spam score: ${error.message}`);
    return 0;
  }
}

/**
 * Process email through SpamAssassin using spamc with custom thresholds
 * @param {string} rawEmail - Raw email content
 * @param {Object} thresholds - Spam thresholds {green, red}
 * @returns {Promise<string|null>} - Email with spam headers added, or null if discarded
 */
function processWithSpamAssassin(rawEmail, thresholds) {
  return new Promise((resolve, reject) => {
    // Use red threshold for SpamAssassin's required score
    const spamcArgs = ['-t', thresholds.red.toString()];

    const spamc = spawn(SPAMC_PATH, spamcArgs, {
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout: SPAMC_TIMEOUT
    });

    let spamProcessedEmail = '';
    let errorOutput = '';

    // Send email to SpamAssassin
    spamc.stdin.write(rawEmail);
    spamc.stdin.end();

    // Collect processed email
    spamc.stdout.on('data', (chunk) => {
      spamProcessedEmail += chunk.toString();
    });

    // Collect error output
    spamc.stderr.on('data', (chunk) => {
      errorOutput += chunk.toString();
    });

    // Handle completion
    spamc.on('close', (code) => {
      if (code === 0) {
        // SpamAssassin processed successfully
        console.log(`[${new Date().toISOString()}] SpamAssassin processing completed (exit code: ${code})`);

        // Extract spam score and implement three-tier filtering
        const spamScore = extractSpamScore(spamProcessedEmail);
        console.log(`[${new Date().toISOString()}] Spam score: ${spamScore}, thresholds: green=${thresholds.green}, red=${thresholds.red}`);

        if (spamScore > thresholds.red) {
          // RED ZONE: Discard email entirely
          console.log(`[${new Date().toISOString()}] Email discarded: spam score ${spamScore} > red threshold ${thresholds.red}`);
          resolve(null); // Return null to indicate email should be discarded
        } else if (spamScore > thresholds.green) {
          // ORANGE ZONE: Mark as spam but deliver
          console.log(`[${new Date().toISOString()}] Email marked as spam: score ${spamScore} > green threshold ${thresholds.green}`);
          resolve(spamProcessedEmail);
        } else {
          // GREEN ZONE: Pass through with spam headers
          console.log(`[${new Date().toISOString()}] Email passed spam filtering: score ${spamScore} <= green threshold ${thresholds.green}`);
          resolve(spamProcessedEmail);
        }
      } else {
        // SpamAssassin failed, but don't block email delivery
        console.warn(`[${new Date().toISOString()}] SpamAssassin processing failed (exit code: ${code}), continuing with original email`);
        console.warn(`[${new Date().toISOString()}] SpamAssassin error: ${errorOutput}`);
        // Return original email if SpamAssassin fails
        resolve(rawEmail);
      }
    });

    // Handle timeout
    spamc.on('error', (error) => {
      console.error(`[${new Date().toISOString()}] SpamAssassin process error: ${error.message}`);
      // Return original email if SpamAssassin fails
      resolve(rawEmail);
    });

    // Set timeout for the entire process
    setTimeout(() => {
      if (!spamc.killed) {
        console.warn(`[${new Date().toISOString()}] SpamAssassin timeout after ${SPAMC_TIMEOUT}ms, killing process`);
        spamc.kill('SIGTERM');
        // Return original email on timeout
        resolve(rawEmail);
      }
    }, SPAMC_TIMEOUT);
  });
}

/**
 * Forward processed email to main application
 * @param {string} processedEmail - Email with spam headers
 * @param {Object} metadata - Additional processing metadata
 */
function forwardToMainApp(processedEmail, headers = {}) {
  return new Promise((resolve, reject) => {
    const defaultHeaders = {
      'Content-Type': 'text/plain',
      'Content-Length': Buffer.byteLength(processedEmail),
      'X-Email-Source': 'postfix',
      'X-Processing-Script': '/opt/emailconnect-app/scripts/production/advanced-process-email.js',
      'X-Spam-Processed': 'true', // Indicate this email was processed for spam
    };
    
    const options = {
      hostname: APP_HOST,
      port: APP_PORT,
      path: '/api/email/process',
      method: 'POST',
      headers: { ...defaultHeaders, ...headers },
      timeout: TIMEOUT_MS,
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`[${new Date().toISOString()}] Spam-processed email delivered successfully (${res.statusCode})`);
          resolve(responseData);
        } else {
          console.error(`[${new Date().toISOString()}] Email processing failed: ${res.statusCode} ${responseData}`);
          reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
        }
      });
    });

    req.on('timeout', () => {
      console.error(`[${new Date().toISOString()}] Email processing timeout after ${TIMEOUT_MS}ms`);
      req.destroy();
      reject(new Error(`Timeout after ${TIMEOUT_MS}ms`));
    });

    req.on('error', (error) => {
      console.error(`[${new Date().toISOString()}] Failed to forward email:`, error.message);
      reject(error);
    });

    req.write(processedEmail);
    req.end();
  });
}

/**
 * Get domain and alias configuration for attachment processing
 * @param {string} domain - Domain name
 * @param {string} emailAddress - Full email address
 * @returns {Promise<Object>} - Configuration object
 */
async function getDomainAndAliasConfig(domain, emailAddress) {
  try {
    // Get domain configuration with user info for plan-based limits
    const domainRecord = await prisma.domain.findFirst({
      where: { domain: domain },
      select: {
        id: true,
        userId: true,
        configuration: true,
        user: {
          select: {
            planType: true
          }
        },
        aliases: {
          where: {
            OR: [
              { email: emailAddress },
              { email: `*@${domain}` }, // catch-all
            ],
            active: true
          },
          select: {
            configuration: true
          }
        }
      }
    });

    if (!domainRecord) {
      return { attachmentProcessing: false };
    }

    // Get alias configuration (prefer specific alias over catch-all)
    const aliasConfig = domainRecord.aliases[0]?.configuration || {};
    const domainConfig = domainRecord.configuration || {};

    return {
      userId: domainRecord.userId,
      userPlanType: domainRecord.user?.planType || 'free',
      attachmentProcessing: aliasConfig.allowAttachments || false,
      attachmentHandling: aliasConfig.attachmentHandling || 'inline', // 'inline' or 'storage'
      s3Folder: aliasConfig.s3Folder || 'attachments',
      domainConfig,
      aliasConfig
    };
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Failed to get domain/alias config:`, error.message);
    return { attachmentProcessing: false };
  }
}

/**
 * Initialize S3 storage service with user's configuration or system defaults
 * @param {Object} domainConfig - Domain configuration
 * @returns {Object|null} - S3StorageService instance or null if not configured
 */
function initializeS3Service(domainConfig) {
  try {
    const s3Config = domainConfig.s3Config || {};

    // If domain has custom S3 config, use it
    if (s3Config.accessKey && s3Config.secretKey) {
      const customConfig = {
        accessKeyId: s3Config.accessKey,
        secretAccessKey: s3Config.secretKey,
        region: s3Config.region || process.env.S3_REGION || 'eu-west-1',
        bucket: s3Config.bucket || process.env.S3_BUCKET || 'emailconnect-attachments',
        endpoint: s3Config.endpoint || process.env.S3_ENDPOINT,
      };
      
      console.log(`[${new Date().toISOString()}] Using domain-specific S3 configuration`);
      // TODO: S3StorageService not available on host yet
      // return new S3StorageService(customConfig);
      return null;
    }

    // Otherwise use system default (if configured)
    if (process.env.S3_ACCESS_KEY_ID && process.env.S3_SECRET_ACCESS_KEY) {
      console.log(`[${new Date().toISOString()}] Using system default S3 configuration`);
      // TODO: S3StorageService not available on host yet
      // return new S3StorageService(); // Uses env vars
      return null;
    }

    console.log(`[${new Date().toISOString()}] S3 not configured, skipping S3 upload`);
    return null;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Failed to initialize S3 service:`, error.message);
    return null;
  }
}

/**
 * Process attachment with our new S3 storage system
 * @param {Object} s3Service - S3StorageService instance
 * @param {Object} attachment - Attachment object from mailparser
 * @param {string} messageId - Email message ID
 * @param {string} s3Folder - S3 folder path
 * @param {string} userId - User ID for database tracking
 * @returns {Promise<Object>} - Processed attachment data
 */
async function processAttachmentWithS3(s3Service, attachment, messageId, s3Folder, userId) {
  try {
    // Convert mailparser attachment to our format
    const emailAttachment = {
      filename: attachment.filename || 'attachment',
      contentType: attachment.contentType,
      size: attachment.size,
      content: attachment.content,
    };

    // Check file size for sync vs async processing
    const sizeMB = attachment.size / (1024 * 1024);
    
    if (sizeMB <= 2) {
      // Sync upload for small files
      console.log(`[${new Date().toISOString()}] Uploading ${attachment.filename} synchronously (${sizeMB.toFixed(2)} MB)`);
      const result = await s3Service.uploadAttachmentSync(emailAttachment, messageId, s3Folder);
      
      return {
        filename: attachment.filename,
        contentType: attachment.contentType,
        size: attachment.size,
        downloadUrl: result.downloadUrl,
        status: 'completed',
        storage: 's3',
        uploadType: 'sync'
      };
    } else {
      // Queue for async upload (large files)
      console.log(`[${new Date().toISOString()}] Queuing ${attachment.filename} for async upload (${sizeMB.toFixed(2)} MB)`);
      
      // Generate file ID and download URL for immediate response
      const fileId = `af_${Date.now().toString(36)}${Math.random().toString(36).substring(2, 9)}`;
      const downloadUrl = `${process.env.URL || 'https://emailconnect.eu'}/attachments/${fileId}/download`;
      
      // Queue the upload (this will create the AttachmentFile record)
      if (userId) {
        await queueAttachmentUpload({
          fileId,
          attachment: emailAttachment,
          messageId,
          userId
        });
      } else {
        console.warn(`[${new Date().toISOString()}] No userId available, cannot queue attachment upload for ${attachment.filename}`);
      }
      
      return {
        filename: attachment.filename,
        contentType: attachment.contentType,
        size: attachment.size,
        downloadUrl,
        status: 'pending',
        storage: 's3',
        uploadType: 'async'
      };
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Failed to process attachment with S3:`, error.message);
    throw error;
  }
}

/**
 * Process email attachments using our new FileTypeService and S3StorageService
 * @param {string} rawEmail - Raw email content
 * @param {Object} config - Domain and alias configuration
 * @param {string} userId - User ID for file type rules
 * @param {string} messageId - Email message ID
 * @returns {Promise<Object>} - Processed email with attachment data
 */
async function processAttachments(rawEmail, config, userId, messageId) {
  try {
    if (!config.attachmentProcessing) {
      // No attachment processing enabled, return email as-is
      return { rawEmail, attachments: [] };
    }

    // Parse email to extract attachments
    const parsed = await simpleParser(rawEmail);

    if (!parsed.attachments || parsed.attachments.length === 0) {
      return { rawEmail, attachments: [] };
    }

    console.log(`[${new Date().toISOString()}] Processing ${parsed.attachments.length} attachments for user ${userId}`);

    // Convert to our attachment format for validation
    const attachmentInfos = parsed.attachments.map(att => ({
      filename: att.filename || 'attachment',
      contentType: att.contentType,
      size: att.size,
    }));

    // Use FileTypeService to categorize attachments based on user rules
    // TODO: FileTypeService not available on host yet, using basic categorization
    // const categorized = await FileTypeService.categorizeAttachments(attachmentInfos, userId);
    const categorized = {
      syncAttachments: attachmentInfos.filter(a => a.size < 2 * 1024 * 1024), // <2MB
      asyncAttachments: attachmentInfos.filter(a => a.size >= 2 * 1024 * 1024), // >=2MB
      rejectedAttachments: [] // No rejection for now
    };
    console.log(`[${new Date().toISOString()}] Categorized: ${categorized.syncAttachments.length} sync, ${categorized.asyncAttachments.length} async, ${categorized.rejectedAttachments.length} rejected`);

    // If alias is configured for inline handling, treat all valid attachments as inline (no S3 processing needed)
    if (config.attachmentHandling === 'inline') {
      // Move all sync/async attachments to inline processing - they'll be handled as base64 in the main processing loop
      console.log(`[${new Date().toISOString()}] Alias configured for inline handling - all valid attachments will be processed as base64`);
    }

    const processedAttachments = [];
    let s3Service = null;

    // Initialize S3 service if storage handling is configured
    if (config.attachmentHandling === 'storage') {
      s3Service = initializeS3Service(config.domainConfig);
    }

    // Process rejected attachments
    for (const rejected of categorized.rejectedAttachments) {
      processedAttachments.push({
        filename: rejected.filename,
        contentType: rejected.contentType,
        size: rejected.size,
        excluded: true,
        excludeReason: rejected.reason, // Use the specific rejection reason from FileTypeService
        storage: 'none'
      });
    }

    // Process sync attachments (small files uploaded immediately)
    for (const syncInfo of categorized.syncAttachments) {
      const attachment = parsed.attachments.find(att => 
        att.filename === syncInfo.filename && att.contentType === syncInfo.contentType
      );
      
      if (!attachment) continue;

      if (config.attachmentHandling === 'storage' && s3Service) {
        try {
          const result = await processAttachmentWithS3(s3Service, attachment, messageId, config.s3Folder || 'attachments', userId);
          processedAttachments.push(result);
        } catch (error) {
          // Fallback to inline if S3 upload fails and it's a small text file
          const sizeInKB = attachment.size / 1024;
          if (sizeInKB <= MAX_INLINE_ATTACHMENT_SIZE_KB) {
            processedAttachments.push({
              filename: attachment.filename,
              contentType: attachment.contentType,
              size: attachment.size,
              content: attachment.content.toString('base64'),
              storage: 'inline',
              fallbackReason: 's3-upload-failed'
            });
          } else {
            processedAttachments.push({
              filename: attachment.filename,
              contentType: attachment.contentType,
              size: attachment.size,
              excluded: true,
              excludeReason: 's3-upload-failed',
              storage: 'none'
            });
          }
        }
      } else {
        // Inline base64 encoding (for inline handling or S3 fallback)
        // Apply plan-specific size limits for inline processing
        const sizeInKB = attachment.size / 1024;
        const planType = config.userPlanType || 'free';
        const maxInlineKB = MAX_INLINE_BASE64_SIZE_KB[planType] || MAX_INLINE_BASE64_SIZE_KB.free;
        
        if (config.attachmentHandling === 'inline' && sizeInKB > maxInlineKB) {
          // User chose inline but file exceeds plan limit
          const sizeMB = attachment.size / (1024 * 1024);
          processedAttachments.push({
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            excluded: true,
            excludeReason: `File size ${sizeMB.toFixed(2)}MB exceeds ${planType} plan inline limit of ${(maxInlineKB/1024).toFixed(2)}MB`,
            storage: 'none'
          });
        } else {
          // Process as base64 - either S3 fallback or within inline limits
          processedAttachments.push({
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            content: attachment.content.toString('base64'),
            storage: 'inline'
          });
        }
      }
    }

    // Process async attachments (large files queued for background upload)
    for (const asyncInfo of categorized.asyncAttachments) {
      const attachment = parsed.attachments.find(att => 
        att.filename === asyncInfo.filename && att.contentType === asyncInfo.contentType
      );
      
      if (!attachment) continue;

      if (config.attachmentHandling === 'storage' && s3Service) {
        try {
          const result = await processAttachmentWithS3(s3Service, attachment, messageId, config.s3Folder || 'attachments', userId);
          processedAttachments.push(result);
        } catch (error) {
          processedAttachments.push({
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            excluded: true,
            excludeReason: 's3-queue-failed',
            storage: 'none'
          });
        }
      } else if (config.attachmentHandling === 'inline') {
        // For inline handling, check plan-specific base64 size limit
        const sizeInKB = attachment.size / 1024;
        const planType = config.userPlanType || 'free';
        const maxInlineKB = MAX_INLINE_BASE64_SIZE_KB[planType] || MAX_INLINE_BASE64_SIZE_KB.free;
        
        if (sizeInKB > maxInlineKB) {
          // Too large even for inline processing at this plan level
          const sizeMB = attachment.size / (1024 * 1024);
          processedAttachments.push({
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            excluded: true,
            excludeReason: `File size ${sizeMB.toFixed(2)}MB exceeds ${planType} plan inline limit of ${(maxInlineKB/1024).toFixed(2)}MB`,
            storage: 'none'
          });
        } else {
          // Process as base64 - within plan limits
          processedAttachments.push({
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size,
            content: attachment.content.toString('base64'),
            storage: 'inline'
          });
        }
      } else {
        // Can't handle large files without S3 or explicit inline choice, exclude them
        processedAttachments.push({
          filename: attachment.filename,
          contentType: attachment.contentType,
          size: attachment.size,
          excluded: true,
          excludeReason: 'too-large',
          storage: 'none'
        });
      }
    }

    console.log(`[${new Date().toISOString()}] Processed ${processedAttachments.length} attachments`);

    // Prepare rejection info for notifications
    const rejectedAttachmentInfo = categorized.rejectedAttachments.map(rejected => ({
      filename: rejected.filename,
      reason: rejected.reason,
      size: rejected.size
    }));

    return {
      rawEmail,
      attachments: processedAttachments,
      attachmentMetadata: {
        totalCount: parsed.attachments.length,
        processedCount: processedAttachments.filter(a => !a.excluded).length,
        excludedCount: processedAttachments.filter(a => a.excluded).length,
        syncCount: categorized.syncAttachments.length,
        asyncCount: categorized.asyncAttachments.length,
        rejectedCount: categorized.rejectedAttachments.length,
        rejectedAttachments: rejectedAttachmentInfo // Include detailed rejection info for notifications
      }
    };

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Failed to process attachments:`, error.message);
    // Return email without attachment processing on error
    return { rawEmail, attachments: [], error: error.message };
  }
}

// Main processing logic
async function main() {
  try {
    // Read email from stdin (pipe from Postfix)
    let rawEmail = '';
    
    process.stdin.setEncoding('utf8');
    
    // Collect all input
    for await (const chunk of process.stdin) {
      rawEmail += chunk;
    }

    if (!rawEmail.trim()) {
      console.error(`[${new Date().toISOString()}] No email content received from stdin`);
      process.exit(1);
    }

    console.log(`[${new Date().toISOString()}] Processing email with advanced features (${rawEmail.length} bytes)`);

    // Step 1: Extract domain and email address from email
    const domain = extractDomainFromEmail(rawEmail);
    if (!domain) {
      console.error(`[${new Date().toISOString()}] Could not extract domain from email, using default settings`);
    }

    // Extract email address for alias configuration
    const emailAddress = extractEmailAddress(rawEmail);
    console.log(`[${new Date().toISOString()}] Processing for domain: ${domain}, email: ${emailAddress}`);

    // Step 2: Get domain-specific spam thresholds
    const thresholds = await getDomainThresholds(domain);

    // Step 3: Process through SpamAssassin with custom thresholds
    const spamProcessedEmail = await processWithSpamAssassin(rawEmail, thresholds);

    // Step 4: Check if email was discarded (null return)
    if (spamProcessedEmail === null) {
      console.log(`[${new Date().toISOString()}] Email discarded due to high spam score, not forwarding to main app`);
      process.exit(0); // Exit successfully but don't forward email
    }

    // Step 5: Get domain and alias configuration for attachment processing (includes user settings)
    const config = await getDomainAndAliasConfig(domain, emailAddress);

    // Step 6: User ID is already in config from getDomainAndAliasConfig
    const userId = config.userId || null;

    // Step 7: Generate or extract message ID for tracking
    const parsedHeaders = await simpleParser(spamProcessedEmail, { 
      skipHtmlToText: true,
      skipTextContent: true,
      skipImageLinks: true
    });
    const messageId = parsedHeaders.messageId || 
                     crypto.randomBytes(16).toString('hex') + '@' + domain;

    // Step 8: Process attachments if user ID available
    // Note: Backend will handle the actual processing, we just prepare metadata
    let attachmentMetadata = { processed: false, totalAttachments: 0 };
    if (userId) {
      // TODO: Full attachment processing disabled until S3 services available on host
      // For now, just count attachments for metadata
      const parsed = await simpleParser(spamProcessedEmail, { 
        skipHtmlToText: true,
        skipTextContent: true,
        skipImageLinks: true
      });
      
      attachmentMetadata = {
        processed: true,
        totalAttachments: parsed.attachments ? parsed.attachments.length : 0,
        rejected: []
      };
      
      console.log(`[${new Date().toISOString()}] Found ${attachmentMetadata.totalAttachments} attachments (S3 processing disabled)`);
    } else {
      console.log(`[${new Date().toISOString()}] No userId available, skipping attachment processing`);
    }
    
    // Step 9: Store metadata in Redis with 30-minute TTL
    const normalizedMessageId = (messageId || '').replace(/[<>]/g, '');
    const metaKey = `advmeta:${normalizedMessageId}`;
    await redis.setex(metaKey, 1800, JSON.stringify(attachmentMetadata));
    
    console.log(`[${new Date().toISOString()}] Stored metadata in Redis with key: ${metaKey}`);
    
    // Step 10: Forward email with minimal headers
    await forwardToMainApp(spamProcessedEmail, {
      'X-Advanced-Processing': 'true',
      'X-Message-ID': messageId
    });
    
    // Clean up Redis connection
    redis.disconnect();
    
    console.log(`[${new Date().toISOString()}] Email forwarded successfully with messageId: ${messageId}`);
    process.exit(0);

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Email processing error:`, error.message);
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGTERM', () => {
  console.log(`[${new Date().toISOString()}] Received SIGTERM, shutting down gracefully`);
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log(`[${new Date().toISOString()}] Received SIGINT, shutting down gracefully`);
  process.exit(0);
});

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(`[${new Date().toISOString()}] Uncaught exception:`, error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`[${new Date().toISOString()}] Unhandled rejection at:`, promise, 'reason:', reason);
  process.exit(1);
});

// Start processing
main();

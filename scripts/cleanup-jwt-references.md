# JWT Cleanup Guide

## Files to Clean Up

### 1. Frontend useAuth Composable
**File**: `src/frontend/composables/useAuth.ts`

Remove:
- Any `localStorage.getItem('token')` references
- JWT decode logic
- Token refresh logic
- Bearer token headers (except for API key auth)

Keep:
- Session-based auth checks
- `credentials: 'include'` for requests

### 2. API Utils
**File**: `src/frontend/utils/api.ts`

Remove:
- JWT token handling in headers
- Token refresh interceptors

Keep:
- API key authentication for machine-to-machine
- `credentials: 'include'` for browser requests

### 3. Old Auth Routes (if any remain)
**Files**: `src/backend/routes/auth.ts`

Remove:
- JWT generation endpoints
- Token validation endpoints
- Refresh token logic

Keep:
- Profile endpoints
- User data endpoints

### 4. Package Dependencies
After verification, remove:
```bash
npm uninstall jsonwebtoken @types/jsonwebtoken
```

Keep `bcrypt` if still used for legacy password verification during transition.

### 5. Environment Variables
Can be removed after full migration:
- `USER_JWT_SECRET`
- `USER_JWT_EXPIRES_IN`

### 6. Test Files
Update tests to:
- Remove JWT token generation
- Use session-based auth mocks
- Update auth headers to use cookies

## Verification Steps

1. Search for JWT references:
```bash
grep -r "jwt\|JWT\|token" src/ --exclude-dir=node_modules
```

2. Search for localStorage token:
```bash
grep -r "localStorage.*token" src/
```

3. Search for Bearer headers:
```bash
grep -r "Bearer" src/
```

## Safe Cleanup Order

1. **Phase 1** (Immediate):
   - Update rate limiting
   - Fix frontend auth state
   - Remove obvious dead code

2. **Phase 2** (After testing):
   - Remove JWT from package.json
   - Clean environment variables
   - Update documentation

3. **Phase 3** (After 1 week stable):
   - Remove users.password column
   - Archive old auth code
   - Finalize documentation
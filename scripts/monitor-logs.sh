#!/bin/bash

# Log monitoring utility for EmailConnect
# Usage: ./monitor-logs.sh [options]

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default settings
LOG_LEVEL="all"
FOLLOW=true
LINES=50

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --level|-l)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --no-follow|-n)
            FOLLOW=false
            shift
            ;;
        --lines)
            LINES="$2"
            shift 2
            ;;
        --help|-h)
            echo "EmailConnect Log Monitor"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  -l, --level <level>   Filter by log level (error, warn, info, debug, all)"
            echo "  -n, --no-follow       Don't follow log output"
            echo "  --lines <n>           Number of lines to show (default: 50)"
            echo "  -h, --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Follow all logs"
            echo "  $0 -l error          # Only show errors"
            echo "  $0 -l warn -n        # Show last warnings without following"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Determine log source (Docker or local)
if docker ps --format '{{.Names}}' | grep -q 'emailconnect'; then
    echo "📋 Monitoring Docker logs..."
    LOG_SOURCE="docker"
else
    echo "📋 Monitoring local logs..."
    LOG_SOURCE="local"
    LOG_FILE="${LOG_FILE:-./logs/app.log}"
    if [ ! -f "$LOG_FILE" ]; then
        LOG_FILE="/var/log/emailconnect/app.log"
    fi
fi

# Function to colorize log output
colorize_logs() {
    while IFS= read -r line; do
        if echo "$line" | grep -q '"level":50\|"level":"error"\|ERROR\|error'; then
            echo -e "${RED}$line${NC}"
        elif echo "$line" | grep -q '"level":40\|"level":"warn"\|WARN\|warn'; then
            echo -e "${YELLOW}$line${NC}"
        elif echo "$line" | grep -q '"level":30\|"level":"info"\|INFO\|info'; then
            echo -e "${GREEN}$line${NC}"
        elif echo "$line" | grep -q '"level":20\|"level":"debug"\|DEBUG\|debug'; then
            echo -e "${BLUE}$line${NC}"
        else
            echo "$line"
        fi
    done
}

# Build filter pattern based on log level
case $LOG_LEVEL in
    error)
        FILTER='"level":50\|"level":"error"\|ERROR'
        ;;
    warn)
        FILTER='"level":40\|"level":50\|"level":"warn"\|"level":"error"\|WARN\|ERROR'
        ;;
    info)
        FILTER='"level":30\|"level":40\|"level":50\|"level":"info"\|"level":"warn"\|"level":"error"'
        ;;
    debug)
        FILTER='.*'  # Show everything
        ;;
    all|*)
        FILTER='.*'  # Show everything
        ;;
esac

# Monitor logs based on source
if [ "$LOG_SOURCE" == "docker" ]; then
    CONTAINER=$(docker ps --format '{{.Names}}' | grep 'emailconnect' | head -1)
    
    if [ "$FOLLOW" == true ]; then
        docker logs -f --tail "$LINES" "$CONTAINER" 2>&1 | grep -E "$FILTER" | colorize_logs
    else
        docker logs --tail "$LINES" "$CONTAINER" 2>&1 | grep -E "$FILTER" | colorize_logs
    fi
else
    if [ ! -f "$LOG_FILE" ]; then
        echo "❌ Log file not found: $LOG_FILE"
        echo "   Make sure the application is running and logging is configured."
        exit 1
    fi
    
    if [ "$FOLLOW" == true ]; then
        tail -f -n "$LINES" "$LOG_FILE" | grep -E "$FILTER" | colorize_logs
    else
        tail -n "$LINES" "$LOG_FILE" | grep -E "$FILTER" | colorize_logs
    fi
fi
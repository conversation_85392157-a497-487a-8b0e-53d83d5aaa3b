[Unit]
Description=Postfix Reload Watcher Service
Documentation=https://github.com/xadi-hq/emailconnect-app
After=network.target postfix.service
Wants=postfix.service

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/postfix-reload-watcher.sh
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/tmp /var/log /var/run
PrivateTmp=false
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=false
RestrictNamespaces=true

# Resource limits
LimitNOFILE=1024
LimitNPROC=512

[Install]
WantedBy=multi-user.target

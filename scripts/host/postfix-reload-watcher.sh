#!/bin/bash

# Postfix Reload Watcher Script
# This script monitors for reload requests from the containerized application
# and triggers Postfix reloads on the host system.
#
# Installation:
#   1. Copy this script to /usr/local/bin/postfix-reload-watcher.sh
#   2. Make it executable: chmod +x /usr/local/bin/postfix-reload-watcher.sh
#   3. Create systemd service: /etc/systemd/system/postfix-reload-watcher.service
#   4. Enable and start: systemctl enable --now postfix-reload-watcher
#
# Usage:
#   The containerized app creates signal files that this script monitors:
#   - /tmp/postfix-reload-requested: Triggers a Postfix reload
#   - /tmp/postfix-restart-requested: Triggers a Postfix restart (for major changes)

set -euo pipefail

# Configuration
SIGNAL_DIR="/tmp"
RELOAD_SIGNAL_FILE="$SIGNAL_DIR/postfix-reload-requested"
RESTART_SIGNAL_FILE="$SIGNAL_DIR/postfix-restart-requested"
LOG_FILE="/var/log/postfix-reload-watcher.log"
LOCK_FILE="/var/run/postfix-reload-watcher.lock"
CHECK_INTERVAL=5  # seconds

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Cleanup function
cleanup() {
    log "Shutting down postfix-reload-watcher"
    rm -f "$LOCK_FILE"
    exit 0
}

# Signal handlers
trap cleanup SIGTERM SIGINT

# Check if already running
if [ -f "$LOCK_FILE" ]; then
    PID=$(cat "$LOCK_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        log "ERROR: postfix-reload-watcher is already running (PID: $PID)"
        exit 1
    else
        log "Removing stale lock file"
        rm -f "$LOCK_FILE"
    fi
fi

# Create lock file
echo $$ > "$LOCK_FILE"

log "Starting postfix-reload-watcher (PID: $$)"
log "Monitoring signal files in $SIGNAL_DIR"
log "Check interval: ${CHECK_INTERVAL}s"

# Ensure signal directory exists
mkdir -p "$SIGNAL_DIR"

# Main monitoring loop
while true; do
    # Check for reload signal
    if [ -f "$RELOAD_SIGNAL_FILE" ]; then
        log "Reload signal detected, reloading Postfix configuration"
        
        # Remove signal file first to prevent duplicate processing
        rm -f "$RELOAD_SIGNAL_FILE"
        
        # Reload Postfix
        if systemctl reload postfix; then
            log "Postfix reloaded successfully"
            
            # Create success indicator file
            echo "$(date '+%Y-%m-%d %H:%M:%S'): Postfix reloaded successfully" > "$SIGNAL_DIR/postfix-reload-success"
        else
            log "ERROR: Failed to reload Postfix"
            
            # Create error indicator file
            echo "$(date '+%Y-%m-%d %H:%M:%S'): Failed to reload Postfix" > "$SIGNAL_DIR/postfix-reload-error"
        fi
    fi
    
    # Check for restart signal (for major configuration changes)
    if [ -f "$RESTART_SIGNAL_FILE" ]; then
        log "Restart signal detected, restarting Postfix service"
        
        # Remove signal file first
        rm -f "$RESTART_SIGNAL_FILE"
        
        # Restart Postfix
        if systemctl restart postfix; then
            log "Postfix restarted successfully"
            
            # Create success indicator file
            echo "$(date '+%Y-%m-%d %H:%M:%S'): Postfix restarted successfully" > "$SIGNAL_DIR/postfix-restart-success"
        else
            log "ERROR: Failed to restart Postfix"
            
            # Create error indicator file
            echo "$(date '+%Y-%m-%d %H:%M:%S'): Failed to restart Postfix" > "$SIGNAL_DIR/postfix-restart-error"
        fi
    fi
    
    # Clean up old status files (older than 1 hour)
    find "$SIGNAL_DIR" -name "postfix-*-success" -o -name "postfix-*-error" -mmin +60 -delete 2>/dev/null || true
    
    # Wait before next check
    sleep "$CHECK_INTERVAL"
done

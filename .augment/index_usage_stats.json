[{"schemaname": "public", "tablename": "credit_transactions", "indexname": "credit_transactions_userId_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 22}, {"schemaname": "public", "tablename": "payments", "indexname": "payments_userId_status_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "invoices", "indexname": "invoices_userId_createdAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "users", "indexname": "users_id_suffix_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "api_keys", "indexname": "api_keys_keyHash_key", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "postfix_virtual_domains", "indexname": "idx_postfix_virtual_domains_advanced", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "attachment_files", "indexname": "attachment_files_uploadStatus_createdAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_batches", "indexname": "credit_batches_userId_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 1669}, {"schemaname": "public", "tablename": "attachment_files", "indexname": "attachment_files_expiresAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_transactions", "indexname": "credit_transactions_type_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_transactions", "indexname": "credit_transactions_batchId_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_transactions", "indexname": "credit_transactions_pkey", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "user_settings", "indexname": "user_settings_pkey", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_batches", "indexname": "credit_batches_isExpired_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_batches", "indexname": "credit_batches_expiresAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "payment_methods", "indexname": "payment_methods_userId_isDefault_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 202}, {"schemaname": "public", "tablename": "payment_methods", "indexname": "payment_methods_pkey", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "idx_postfix_virtual_aliases_email_active", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "idx_postfix_virtual_aliases_domain", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "idx_postfix_virtual_aliases_active", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "postfix_virtual_domains", "indexname": "idx_postfix_virtual_domains_active", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "sessions", "indexname": "sessions_token_key", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "verifications", "indexname": "verifications_identifier_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "payment_methods", "indexname": "payment_methods_mandateId_key", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "webhooks", "indexname": "webhooks_userId_active_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "webhooks", "indexname": "webhooks_active_createdAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "api_keys", "indexname": "api_keys_keyHash_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "sessions", "indexname": "sessions_pkey", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "payments", "indexname": "payments_status_createdAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "payments", "indexname": "payments_userId_createdAt_idx", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 0}, {"schemaname": "public", "tablename": "credit_batches", "indexname": "credit_batches_pkey", "idx_tup_read": 0, "idx_tup_fetch": 0, "idx_scan": 10}, {"schemaname": "public", "tablename": "invoices", "indexname": "invoices_invoiceNumber_key", "idx_tup_read": 1, "idx_tup_fetch": 0, "idx_scan": 4}, {"schemaname": "public", "tablename": "audit_logs", "indexname": "audit_logs_pkey", "idx_tup_read": 2, "idx_tup_fetch": 0, "idx_scan": 2}, {"schemaname": "public", "tablename": "sessions", "indexname": "sessions_userId_idx", "idx_tup_read": 2, "idx_tup_fetch": 0, "idx_scan": 2}, {"schemaname": "public", "tablename": "accounts", "indexname": "accounts_pkey", "idx_tup_read": 2, "idx_tup_fetch": 2, "idx_scan": 2}, {"schemaname": "public", "tablename": "payments", "indexname": "payments_mollieId_key", "idx_tup_read": 4, "idx_tup_fetch": 4, "idx_scan": 4}, {"schemaname": "public", "tablename": "file_type_rules", "indexname": "file_type_rules_pkey", "idx_tup_read": 4, "idx_tup_fetch": 4, "idx_scan": 4}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "idx_postfix_virtual_aliases_lookup", "idx_tup_read": 4, "idx_tup_fetch": 3, "idx_scan": 3}, {"schemaname": "public", "tablename": "notifications", "indexname": "notifications_pkey", "idx_tup_read": 5, "idx_tup_fetch": 1, "idx_scan": 3}, {"schemaname": "public", "tablename": "invoices", "indexname": "invoices_pkey", "idx_tup_read": 7, "idx_tup_fetch": 7, "idx_scan": 7}, {"schemaname": "public", "tablename": "two_factors", "indexname": "two_factors_pkey", "idx_tup_read": 10, "idx_tup_fetch": 10, "idx_scan": 10}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "idx_postfix_virtual_aliases_domain_join", "idx_tup_read": 16, "idx_tup_fetch": 11, "idx_scan": 12}, {"schemaname": "public", "tablename": "verifications", "indexname": "verifications_pkey", "idx_tup_read": 22, "idx_tup_fetch": 14, "idx_scan": 17}, {"schemaname": "public", "tablename": "_prisma_migrations", "indexname": "_prisma_migrations_pkey", "idx_tup_read": 22, "idx_tup_fetch": 22, "idx_scan": 22}, {"schemaname": "public", "tablename": "payments", "indexname": "payments_pkey", "idx_tup_read": 25, "idx_tup_fetch": 25, "idx_scan": 27}, {"schemaname": "public", "tablename": "subscriptions", "indexname": "subscriptions_pkey", "idx_tup_read": 27, "idx_tup_fetch": 27, "idx_scan": 27}, {"schemaname": "public", "tablename": "postfix_virtual_domains", "indexname": "idx_postfix_virtual_domains_spam_filtering", "idx_tup_read": 29, "idx_tup_fetch": 27, "idx_scan": 28}, {"schemaname": "public", "tablename": "verifications", "indexname": "verifications_identifier_value_key", "idx_tup_read": 30, "idx_tup_fetch": 0, "idx_scan": 30}, {"schemaname": "public", "tablename": "two_factors", "indexname": "two_factors_userId_key", "idx_tup_read": 40, "idx_tup_fetch": 40, "idx_scan": 55}, {"schemaname": "public", "tablename": "aliases", "indexname": "aliases_pkey", "idx_tup_read": 42, "idx_tup_fetch": 41, "idx_scan": 41}, {"schemaname": "public", "tablename": "api_keys", "indexname": "api_keys_pkey", "idx_tup_read": 48, "idx_tup_fetch": 48, "idx_scan": 48}, {"schemaname": "public", "tablename": "domains", "indexname": "domains_domain_key", "idx_tup_read": 58, "idx_tup_fetch": 58, "idx_scan": 59}, {"schemaname": "public", "tablename": "accounts", "indexname": "accounts_userId_idx", "idx_tup_read": 62, "idx_tup_fetch": 59, "idx_scan": 71}, {"schemaname": "public", "tablename": "attachment_files", "indexname": "attachment_files_pkey", "idx_tup_read": 67, "idx_tup_fetch": 61, "idx_scan": 62}, {"schemaname": "public", "tablename": "notifications", "indexname": "notifications_userId_isRead_idx", "idx_tup_read": 71, "idx_tup_fetch": 53, "idx_scan": 266}, {"schemaname": "public", "tablename": "users", "indexname": "users_email_key", "idx_tup_read": 85, "idx_tup_fetch": 80, "idx_scan": 90}, {"schemaname": "public", "tablename": "emails", "indexname": "emails_expiresAt_idx", "idx_tup_read": 93, "idx_tup_fetch": 24, "idx_scan": 13}, {"schemaname": "public", "tablename": "postfix_virtual_domains", "indexname": "postfix_virtual_domains_pkey", "idx_tup_read": 123, "idx_tup_fetch": 116, "idx_scan": 473}, {"schemaname": "public", "tablename": "accounts", "indexname": "accounts_providerId_accountId_key", "idx_tup_read": 238, "idx_tup_fetch": 238, "idx_scan": 42}, {"schemaname": "public", "tablename": "emails", "indexname": "emails_deliveryStatus_lastAttemptAt_idx", "idx_tup_read": 313, "idx_tup_fetch": 312, "idx_scan": 75}, {"schemaname": "public", "tablename": "emails", "indexname": "emails_pkey", "idx_tup_read": 359, "idx_tup_fetch": 161, "idx_scan": 258}, {"schemaname": "public", "tablename": "organizations", "indexname": "organizations_pkey", "idx_tup_read": 425, "idx_tup_fetch": 425, "idx_scan": 425}, {"schemaname": "public", "tablename": "subscriptions", "indexname": "subscriptions_mollieId_key", "idx_tup_read": 469, "idx_tup_fetch": 469, "idx_scan": 158}, {"schemaname": "public", "tablename": "postfix_virtual_aliases", "indexname": "postfix_virtual_aliases_pkey", "idx_tup_read": 484, "idx_tup_fetch": 481, "idx_scan": 3203}, {"schemaname": "public", "tablename": "file_type_rules", "indexname": "file_type_rules_userId_category_key", "idx_tup_read": 498, "idx_tup_fetch": 0, "idx_scan": 101}, {"schemaname": "public", "tablename": "emails", "indexname": "emails_messageId_key", "idx_tup_read": 628, "idx_tup_fetch": 318, "idx_scan": 474}, {"schemaname": "public", "tablename": "aliases", "indexname": "aliases_email_active_idx", "idx_tup_read": 724, "idx_tup_fetch": 724, "idx_scan": 724}, {"schemaname": "public", "tablename": "emails", "indexname": "emails_userId_createdAt_idx", "idx_tup_read": 842, "idx_tup_fetch": 407, "idx_scan": 22884}, {"schemaname": "public", "tablename": "webhooks", "indexname": "webhooks_pkey", "idx_tup_read": 1422, "idx_tup_fetch": 660, "idx_scan": 883}, {"schemaname": "public", "tablename": "aliases", "indexname": "aliases_email_domainId_key", "idx_tup_read": 4444, "idx_tup_fetch": 4437, "idx_scan": 4488}, {"schemaname": "public", "tablename": "user_settings", "indexname": "user_settings_userId_key", "idx_tup_read": 5545, "idx_tup_fetch": 1014, "idx_scan": 3444}, {"schemaname": "public", "tablename": "users", "indexname": "users_pkey", "idx_tup_read": 40943, "idx_tup_fetch": 40011, "idx_scan": 40347}, {"schemaname": "public", "tablename": "notifications", "indexname": "notifications_userId_createdAt_idx", "idx_tup_read": 51730, "idx_tup_fetch": 51707, "idx_scan": 18369}, {"schemaname": "public", "tablename": "domains", "indexname": "domains_pkey", "idx_tup_read": 208262, "idx_tup_fetch": 206207, "idx_scan": 208103}]
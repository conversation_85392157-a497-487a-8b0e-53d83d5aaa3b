[
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "messageId",
    "n_distinct": -1,
    "correlation": 0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "id",
    "n_distinct": -1,
    "correlation": -0.33333334,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "lastVerificationAttempt",
    "n_distinct": -1,
    "correlation": 0.5,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "updatedAt",
    "n_distinct": -1,
    "correlation": 0.5,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "url",
    "n_distinct": -1,
    "correlation": -0.0021978023,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "createdAt",
    "n_distinct": -1,
    "correlation": -0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "deliveredAt",
    "n_distinct": -1,
    "correlation": -0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "updatedAt",
    "n_distinct": -1,
    "correlation": 0.74505496,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "id",
    "n_distinct": -1,
    "correlation": 0.6527473,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "expiresAt",
    "n_distinct": -1,
    "correlation": -0.5,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "createdAt",
    "n_distinct": -1,
    "correlation": 0.6527473,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "updatedAt",
    "n_distinct": -1,
    "correlation": 0.25263157,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "id",
    "n_distinct": -1,
    "correlation": -0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "lastAttemptAt",
    "n_distinct": -1,
    "correlation": -0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "id",
    "n_distinct": -1,
    "correlation": 0.49122807,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "updatedAt",
    "n_distinct": -1,
    "correlation": -0.14285715,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "password",
    "n_distinct": -1,
    "correlation": 0.18245614,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "webhookPayload",
    "n_distinct": -1,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "createdAt",
    "n_distinct": -1,
    "correlation": 0.49122807,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "email",
    "n_distinct": -1,
    "correlation": 0.23333333,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "lastUsageReset",
    "n_distinct": -1,
    "correlation": 0.33157894,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "createdAt",
    "n_distinct": -1,
    "correlation": 0.20879121,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "id",
    "n_distinct": -1,
    "correlation": 0.20879121,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "updatedAt",
    "n_distinct": -1,
    "correlation": 0.7582418,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "createdAt",
    "n_distinct": -1,
    "correlation": -0.33333334,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "domain",
    "n_distinct": -1,
    "correlation": 0.5,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "name",
    "n_distinct": -0.94736844,
    "correlation": 0.0754386,
    "most_common_vals": "{\"Xander Groesbeek\"}",
    "most_common_freqs": "{0.10526316}"
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "email",
    "n_distinct": -0.9285714,
    "correlation": 0.0989011,
    "most_common_vals": "{*@in.emailconnect.eu}",
    "most_common_freqs": "{0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "nextVerificationCheck",
    "n_distinct": -0.875,
    "correlation": 1,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "subject",
    "n_distinct": -0.875,
    "correlation": -0.6666667,
    "most_common_vals": "{\"Test email from EmailConnect\"}",
    "most_common_freqs": "{0.25}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "name",
    "n_distinct": -0.85714287,
    "correlation": 0.23516484,
    "most_common_vals": "{\"n8n trigger webhook - EmailConnect Trigger\",test-webhook-created}",
    "most_common_freqs": "{0.14285715,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "userId",
    "n_distinct": -0.75,
    "correlation": -0.0952381,
    "most_common_vals": "{cmbv9lgzq0000lk1rcx1dethg}",
    "most_common_freqs": "{0.375}"
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "webhookId",
    "n_distinct": -0.71428573,
    "correlation": 0.22637363,
    "most_common_vals": "{cmdzskpae000dqj1rdh41su5a,cmdq3cx2c0005pd1qj1lu7vea}",
    "most_common_freqs": "{0.2857143,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "description",
    "n_distinct": -0.64285713,
    "correlation": -0.16363636,
    "most_common_vals": "{\"\",\"Auto-created webhook for n8n trigger node: EmailConnect Trigger (Production)\"}",
    "most_common_freqs": "{0.14285715,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "domainId",
    "n_distinct": -0.5714286,
    "correlation": 0.103296705,
    "most_common_vals": "{cmdzrc8h80005mw1lc6hjl6n6,cmd1s6xbr000doc1qkk9top15,cme14z4fd000ann1mpieqws3o}",
    "most_common_freqs": "{0.2857143,0.21428572,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "configuration",
    "n_distinct": -0.5,
    "correlation": 0.3181818,
    "most_common_vals": "{\"{\\"spamFiltering\\": {\\"enabled\\": false, \\"thresholds\\": {\\"red\\": 10, \\"green\\": 0}}, \\"includeEnvelope\\": false, \\"allowAttachments\\": true}\",\"{\\"includeEnvelope\\": false, \\"allowAttachments\\": false}\",\"{\\"s3Folder\\": \\"\\", \\"includeEnvelope\\": true, \\"allowAttachments\\": false, \\"attachmentHandling\\": \\"inline\\"}\"}",
    "most_common_freqs": "{0.21428572,0.14285715,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "userId",
    "n_distinct": -0.5,
    "correlation": -0.17802198,
    "most_common_vals": "{cmbv9lgzq0000lk1rcx1dethg,cme0ae6p8000aph1lxdic9usp,cmc59wbw00008qo1sd89fpu96}",
    "most_common_freqs": "{0.35714287,0.21428572,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "configuration",
    "n_distinct": -0.375,
    "correlation": -0.2,
    "most_common_vals": "{\"{\\"spamFiltering\\": {\\"enabled\\": true, \\"thresholds\\": {\\"red\\": 10, \\"green\\": 0}}, \\"includeEnvelope\\": false, \\"allowAttachments\\": true}\",\"{\\"spamFiltering\\": {\\"enabled\\": false, \\"thresholds\\": {\\"red\\": 10, \\"green\\": 0}}, \\"includeEnvelope\\": false, \\"allowAttachments\\": true}\"}",
    "most_common_freqs": "{0.375,0.25}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "toAddresses",
    "n_distinct": -0.375,
    "correlation": 0.5714286,
    "most_common_vals": "{\"{<EMAIL>}\",\"{<EMAIL>}\",\"{<EMAIL>}\"}",
    "most_common_freqs": "{0.375,0.375,0.25}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "webhookId",
    "n_distinct": -0.25,
    "correlation": -1,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "isTestWebhook",
    "n_distinct": -0.25,
    "correlation": 0.16666667,
    "most_common_vals": "{f,t}",
    "most_common_freqs": "{0.75,0.25}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "verificationFailureCount",
    "n_distinct": -0.25,
    "correlation": 0.97619045,
    "most_common_vals": "{0}",
    "most_common_freqs": "{0.875}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "verificationStatus",
    "n_distinct": -0.25,
    "correlation": 0.97619045,
    "most_common_vals": "{VERIFIED}",
    "most_common_freqs": "{0.875}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "verified",
    "n_distinct": -0.25,
    "correlation": 0.5,
    "most_common_vals": "{t}",
    "most_common_freqs": "{0.875}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "fromAddress",
    "n_distinct": -0.25,
    "correlation": 0.16666667,
    "most_common_vals": "{<EMAIL>,<EMAIL>}",
    "most_common_freqs": "{0.75,0.25}"
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "active",
    "n_distinct": -0.25,
    "correlation": 0.5,
    "most_common_vals": "{t}",
    "most_common_freqs": "{0.875}"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "currentMonthEmails",
    "n_distinct": -0.21052632,
    "correlation": 0.7614035,
    "most_common_vals": "{0}",
    "most_common_freqs": "{0.84210527}"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "mollieCustomerId",
    "n_distinct": -0.15789473,
    "correlation": 1,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "verified",
    "n_distinct": -0.14285715,
    "correlation": 1,
    "most_common_vals": "{t}",
    "most_common_freqs": "{0.9285714}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "active",
    "n_distinct": -0.14285715,
    "correlation": 1,
    "most_common_vals": "{t,f}",
    "most_common_freqs": "{0.85714287,0.14285715}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "userId",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{cmbv9lgzq0000lk1rcx1dethg}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "domainId",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{cmd1s6xbr000doc1qkk9top15}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "deliveryStatus",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{DELIVERED}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "hasAsyncUploads",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{f}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "aliasId",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{cmf54xh980003t92eij5m2lbn}",
    "most_common_freqs": "{0.375}"
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "deliveryAttempts",
    "n_distinct": -0.125,
    "correlation": 1,
    "most_common_vals": "{1}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "organizationId",
    "n_distinct": -0.10526317,
    "correlation": -1,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "role",
    "n_distinct": -0.10526316,
    "correlation": 0.99473685,
    "most_common_vals": "{user}",
    "most_common_freqs": "{0.94736844}"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "twoFactorEnabled",
    "n_distinct": -0.10526316,
    "correlation": 0.7614035,
    "most_common_vals": "{f}",
    "most_common_freqs": "{0.94736844}"
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "planType",
    "n_distinct": -0.10526316,
    "correlation": 0.81578946,
    "most_common_vals": "{free,pro}",
    "most_common_freqs": "{0.84210527,0.15789473}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "webhookSecret",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "trialActivatedBy",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "trialEndsAt",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "trialStartedAt",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "httpStatus",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "emails",
    "column_name": "errorMessage",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "domains",
    "column_name": "verificationToken",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "image",
    "n_distinct": 0,
    "correlation": null,
    "most_common_vals": null,
    "most_common_freqs": null
  },
  {
    "schemaname": "public",
    "tablename": "users",
    "column_name": "emailVerified",
    "n_distinct": 1,
    "correlation": 1,
    "most_common_vals": "{t}",
    "most_common_freqs": "{1}"
  },
  {
    "schemaname": "public",
    "tablename": "webhooks",
    "column_name": "customHeaders",
    "n_distinct": 1,
    "correlation": 1,
    "most_common_vals": "{\"null\"}",
    "most_common_freqs": "{0.64285713}"
  },
  {
    "schemaname": "public",
    "tablename": "aliases",
    "column_name": "active",
    "n_distinct": 1,
    "correlation": 1,
    "most_common_vals": "{t}",
    "most_common_freqs": "{1}"
  }
]
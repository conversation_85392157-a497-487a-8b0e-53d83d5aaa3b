# Development Docker Compose - Infrastructure Services Only
# Run the main app locally with: npm run dev
# This provides: PostgreSQL, Redis, and Postfix Manager

networks:
  emailconnect:
    driver: bridge

services:
  postgres:
    image: postgres:17-alpine
    networks:
      - emailconnect
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
      - POSTGRES_DB=${DB_NAME:-eu_email_webhook}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/utilities/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d eu_email_webhook"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    shm_size: 128mb

  # Redis Master
  redis-master:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_master_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-dev_redis_password_change_in_prod}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-dev_redis_password_change_in_prod}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD:-dev_redis_password_change_in_prod}

  # Redis Replica for failover
  redis-replica:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:6382:6379"
    volumes:
      - redis_replica_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-dev_redis_password_change_in_prod}
    depends_on:
      - redis-master
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-dev_redis_password_change_in_prod}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --requirepass ${REDIS_PASSWORD:-dev_redis_password_change_in_prod}
      --replicaof redis-master 6379
      --masterauth ${REDIS_PASSWORD:-dev_redis_password_change_in_prod}

  # Redis Sentinel for automatic failover
  redis-sentinel:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:26379:26379"
    depends_on:
      - redis-master
      - redis-replica
    volumes:
      - ./config/redis-sentinel.conf:/etc/redis/sentinel.conf:ro
      - ./config/sentinel-entrypoint.sh:/sentinel-entrypoint.sh:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "26379", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 15s
    restart: unless-stopped
    entrypoint: ["/bin/sh"]
    command: ["/sentinel-entrypoint.sh"]

  redis-test:
    image: redis:7-alpine
    networks:
      - emailconnect
    ports:
      - "127.0.0.1:6381:6379"  # Different port for test Redis (6380 used by replica)
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 256mb 
      --maxmemory-policy allkeys-lru
    profiles:
      - test

  ngrok:
      image: ngrok/ngrok:latest
      networks:
        - emailconnect
      restart: unless-stopped
      command:
        - "http"
        - "host.docker.internal:3000" 
        - "--domain=${NGROK_DOMAIN}"
      ports:
        - "4040:4040"
      environment:
        - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN}
      extra_hosts:
        - "host.docker.internal:host-gateway"

  # 🎯 SCRIPT INITIALIZATION SERVICE
  script-init:
    build: 
      context: .
      target: production
    volumes:
      - eu_email_scripts:/opt/emailconnect-app/scripts:rw
    command: sh -c "cp -r /app/scripts/* /opt/emailconnect-app/scripts/ && chmod +x /opt/emailconnect-app/scripts/*.js"
    restart: "no"
    profiles:
      - init

volumes:
  postgres_data:
    driver: local
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  redis_test_data:
    driver: local
  eu_email_data:
    driver: local
  eu_email_scripts:
    driver: local
